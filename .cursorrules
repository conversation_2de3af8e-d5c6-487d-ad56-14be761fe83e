{"frontend": {"styling": {"units": "rem", "avoidUnits": ["px"], "layout": ["flex", "grid"], "avoidLayout": ["fixed positioning", "px dimensions"]}, "components": {"preferred": ["el-button", "el-input", "el-table"], "styleOverride": "rem", "table": {"useMinWidth": true, "widthUnit": "rem"}}, "responsive": {"targetResolutions": ["1920px", "2560px", "3840px", "4096px"], "avoidTechniques": ["transform: scale", "media queries"], "preferredTechniques": ["rem units", "flexible layouts"]}, "structure": {"styleScope": "scoped", "scriptSetup": true, "typescript": true}}}
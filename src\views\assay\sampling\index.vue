<template>
  <ContentWrap title="采样执行管理">
    <el-tabs v-model="activeTab">
      <!-- 采样任务管理 -->
      <el-tab-pane label="采样任务管理" name="tasks">
        <el-card shadow="hover">
          <!-- 状态筛选器 -->
          <div class="mb-4">
            <el-tabs v-model="taskStatusFilter" type="card" @tab-change="handleStatusFilterChange">
              <el-tab-pane label="待采样" name="pending">
                <template #label>
                  <span>待采样 <el-badge :value="getTaskCountByStatus('pending')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="采样中" name="processing">
                <template #label>
                  <span>采样中 <el-badge :value="getTaskCountByStatus('processing')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="已完成" name="completed">
                <template #label>
                  <span>已完成 <el-badge :value="getTaskCountByStatus('completed')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="异常终止" name="abnormal">
                <template #label>
                  <span>异常终止 <el-badge :value="getTaskCountByStatus('abnormal')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.tasks" class="search-form">
              <el-form-item label="任务名称">
                <el-input v-model="searchForm.tasks.name" placeholder="请输入任务名称" clearable />
              </el-form-item>
              <el-form-item label="采样点">
                <el-input v-model="searchForm.tasks.samplingPoint" placeholder="请输入采样点" clearable />
              </el-form-item>
              <el-form-item label="执行人">
                <el-select v-model="searchForm.tasks.executor" placeholder="请选择执行人" clearable style="min-width: 8rem;">
                  <el-option label="张三" value="张三" />
                  <el-option label="李四" value="李四" />
                  <el-option label="王五" value="王五" />
                  <el-option label="赵六" value="赵六" />
                  <el-option label="钱七" value="钱七" />
                </el-select>
              </el-form-item>
              <el-form-item label="计划日期">
                <el-date-picker
                  v-model="searchForm.tasks.planDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('tasks')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('tasks')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button v-if="taskStatusFilter === 'pending'" type="success" @click="handleBatchConfirm" :disabled="selectedTasks.length === 0">
              <el-icon><Check /></el-icon>批量确认采样 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="taskStatusFilter === 'processing'" type="primary" @click="handleBatchComplete" :disabled="selectedTasks.length === 0">
              <el-icon><CircleCheck /></el-icon>批量完成采样 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="['completed', 'abnormal'].includes(taskStatusFilter)" type="info" @click="handleBatchExportReport" :disabled="selectedTasks.length === 0">
              <el-icon><Download /></el-icon>批量生成送检单 ({{ selectedTasks.length }})
            </el-button>
          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.tasks"
            :data="filteredTaskData"
            border
            style="width: 100%;"
            @selection-change="handleTaskSelectionChange"
            class="sampling-task-table"
            :table-layout="'fixed'"
          >
            <el-table-column type="selection" width="60" fixed="left" />
            <el-table-column prop="id" label="任务编号" width="120" fixed="left" />
            <el-table-column prop="name" label="任务名称" width="200" fixed="left" show-overflow-tooltip />
            <el-table-column prop="planName" label="关联计划" width="180" show-overflow-tooltip />
            <el-table-column prop="samplingPoint" label="采样点" width="120" />
            <el-table-column prop="planDate" label="计划日期" width="120" />
            <el-table-column label="采样人员" width="120">
              <template #default="{ row }">
                <div class="sampler-info">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span class="sampler-name">{{ row.samplerName || row.executor || '-' }}</span>
                </div>
              </template>
            </el-table-column>

            <!-- 预期采样信息字段 - 仅在待采样状态显示，为现场人员提供参考 -->
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedSampleQuantity" label="预期样品量(mL)" width="120" align="center">
              <template #default="{ row }">
                <span class="expected-info">{{ row.expectedSampleQuantity || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedSampleNature" label="预期样品性质" width="120">
              <template #default="{ row }">
                <el-tag size="small" type="info">{{ row.expectedSampleNature || row.sampleNature || '-' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedAppearance" label="预期样品外观" width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="expected-info">{{ row.expectedAppearance || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedSupernatant" label="预期上清液情况" width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="expected-info">{{ row.expectedSupernatant || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="samplingInstructions" label="采样说明" width="180" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="expected-info">{{ row.samplingInstructions || '-' }}</span>
              </template>
            </el-table-column>
            <!-- 采样地点和现场情况 - 仅在采样中和已完成状态显示（通过确认采样按钮填写） -->
            <el-table-column v-if="['processing', 'completed', 'abnormal'].includes(taskStatusFilter)" prop="samplingLocation" label="采样地点" width="160" />
            <el-table-column v-if="['processing', 'completed', 'abnormal'].includes(taskStatusFilter)" prop="samplingCondition" label="现场采样情况" width="220" show-overflow-tooltip />

            <!-- 实际采样时间 - 仅在采样中和已完成状态显示 -->
            <el-table-column v-if="['processing', 'completed', 'abnormal'].includes(taskStatusFilter)" prop="actualSamplingTime" label="实际采样时间" width="160" />

            <!-- 样品外观 - 仅在已完成状态显示（通过完成采样按钮填写） -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="actualSampleAppearance" label="实际样品外观" width="160">
              <template #default="{ row }">
                <span>{{ row.actualSampleAppearance || '待填写' }}</span>
                <el-tooltip v-if="!row.actualSampleAppearance" content="需要通过完成采样按钮填写" placement="top">
                  <el-icon class="ml-1 text-warning"><Warning /></el-icon>
                </el-tooltip>
              </template>
            </el-table-column>

            <!-- 实际样品数量 - 仅在已完成状态显示 -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="actualSampleQuantity" label="实际数量(mL)" width="120" align="center">
              <template #default="{ row }">
                <span>{{ row.actualSampleQuantity || '待填写' }}</span>
                <el-tooltip v-if="!row.actualSampleQuantity" content="需要通过完成采样按钮填写" placement="top">
                  <el-icon class="ml-1 text-warning"><Warning /></el-icon>
                </el-tooltip>
              </template>
            </el-table-column>

            <!-- 样品状态 - 仅在已完成状态显示 -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="sampleStatus" label="样品状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getSampleStatusType(row.sampleStatus)" size="small">
                  {{ row.sampleStatus || '待确定' }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 完成时间 - 仅在已完成状态显示 -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="completeTime" label="完成时间" width="160" />
            <el-table-column prop="status" label="任务状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusColor(row.status)" size="small">
                  {{ getTaskStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="320" fixed="right">
              <template #default="{ row }">
                <!-- 状态变更操作（包含信息填写） -->
                <el-button v-if="row.status === 'pending'" link type="primary" @click="handleConfirmSampling(row)">确认采样</el-button>
                <el-button v-if="row.status === 'processing'" link type="success" @click="handleCompleteSampling(row)">完成采样</el-button>
                <el-button v-if="row.status === 'processing'" link type="danger" @click="handleAbnormal(row)">记录异常</el-button>

                <!-- 单据操作 -->
                <el-button v-if="['completed', 'abnormal'].includes(row.status)" link type="warning" @click="handleCreateInspectionForm(row)">生成送检单</el-button>

                <el-divider direction="vertical" />
                <!-- 删除编辑按钮，其他操作保留 -->
                <el-button link type="success" @click="handleDetail('tasks', row)">详情</el-button>
                <el-button link type="danger" @click="handleDelete('tasks', row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.tasks.current"
              v-model:page-size="pagination.tasks.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.tasks.total"
              @size-change="handleSizeChange('tasks')"
              @current-change="handleCurrentChange('tasks')"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 送检记录管理 -->
      <el-tab-pane label="送检记录管理" name="submissionRecords">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.submissionRecords" class="search-form">
              <el-form-item label="送检单号">
                <el-input v-model="searchForm.submissionRecords.submissionCode" placeholder="请输入送检单号" clearable />
              </el-form-item>
              <el-form-item label="样品编号">
                <el-input v-model="searchForm.submissionRecords.sampleCode" placeholder="请输入样品编号" clearable />
              </el-form-item>
              <el-form-item label="送检状态">
                <el-select v-model="searchForm.submissionRecords.status" placeholder="请选择送检状态" clearable>
                  <el-option label="待送检" value="pending" />
                  <el-option label="已送检" value="submitted" />
                  <el-option label="已接收" value="received" />
                </el-select>
              </el-form-item>
              <el-form-item label="送检日期">
                <el-date-picker
                  v-model="searchForm.submissionRecords.submissionDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('submissionRecords')">
                  <Icon icon="ep:search" />查询
                </el-button>
                <el-button @click="handleReset('submissionRecords')">
                  <Icon icon="ep:refresh" />重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮 -->
          <div class="mb-4">
            <el-button type="success" @click="handleBatchSubmit" :disabled="selectedSubmissionRecords.length === 0">
              <Icon icon="ep:upload" />批量送检 ({{ selectedSubmissionRecords.length }})
            </el-button>
            <el-button type="info" @click="handleExportSubmissionRecords" :disabled="submissionRecords.length === 0">
              <Icon icon="ep:download" />导出Excel
            </el-button>
            <el-button type="warning" @click="handleExportSubmissionPDF" :disabled="submissionRecords.length === 0">
              <Icon icon="ep:document" />导出PDF
            </el-button>
          </div>

          <!-- 送检记录表格 -->
          <el-table
            :data="submissionRecords"
            v-loading="loading.submissionRecords"
            @selection-change="handleSubmissionRecordSelectionChange"
            stripe
            border
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="submissionCode" label="送检单号" width="160" />
            <el-table-column prop="sampleCode" label="样品编号" width="160" />
            <el-table-column prop="samplingPoint" label="采样点" width="120" />
            <el-table-column prop="testItems" label="检测项目" min-width="150" show-overflow-tooltip />
            <el-table-column prop="sampleType" label="样品类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getSampleTypeTagType(row.sampleType)">
                  {{ getSampleTypeText(row.sampleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="volume" label="样品体积(mL)" width="120" align="center" />
            <el-table-column prop="submissionDate" label="送检日期" width="120" />
            <el-table-column prop="submissionLab" label="送检实验室" width="120" />
            <el-table-column prop="submitterName" label="送检人" width="100" />
            <el-table-column prop="status" label="送检状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getSubmissionStatusTagType(row.status)">
                  {{ getSubmissionStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleSubmitRecord(row)"
                  :disabled="row.status !== 'pending'"
                >
                  送检
                </el-button>
                <el-button
                  link
                  type="info"
                  size="small"
                  @click="handleViewSubmissionDetail(row)"
                >
                  查看详情
                </el-button>
                <el-button
                  link
                  type="success"
                  size="small"
                  @click="handleDownloadSubmissionForm(row)"
                >
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.submissionRecords.page"
              v-model:page-size="pagination.submissionRecords.size"
              :total="pagination.submissionRecords.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange('submissionRecords')"
              @current-change="handleCurrentChange('submissionRecords')"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 样品管理 -->
      <el-tab-pane label="样品管理" name="sampleManagement">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.sampleManagement" class="search-form">
              <el-form-item label="样品编号">
                <el-input v-model="searchForm.sampleManagement.sampleCode" placeholder="请输入样品编号" clearable />
              </el-form-item>
              <el-form-item label="样品类型">
                <el-select v-model="searchForm.sampleManagement.sampleType" placeholder="请选择样品类型" clearable style="min-width: 10rem;">
                  <el-option label="进水样品" value="inlet" />
                  <el-option label="出水样品" value="outlet" />
                  <el-option label="污泥样品" value="sludge" />
                  <el-option label="中间过程样品" value="process" />
                </el-select>
              </el-form-item>
              <el-form-item label="样品状态">
                <el-select v-model="searchForm.sampleManagement.status" placeholder="请选择状态" clearable style="min-width: 10rem;">
                  <el-option label="已入库" value="stored" />
                  <el-option label="已送检" value="submitted" />
                  <el-option label="检测中" value="testing" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已销毁" value="destroyed" />
                </el-select>
              </el-form-item>
              <el-form-item label="采样日期">
                <el-date-picker
                  v-model="searchForm.sampleManagement.samplingDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item label="存储位置">
                <el-input v-model="searchForm.sampleManagement.storageLocation" placeholder="请输入存储位置" clearable />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('sampleManagement')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('sampleManagement')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>

          <!-- 表格操作栏 -->
          <div class="mb-4">

            <el-button type="success" @click="handleBatchStorage" :disabled="selectedSampleManagement.length === 0">
              <el-icon><Box /></el-icon>批量入库 ({{ selectedSampleManagement.length }})
            </el-button>
            <el-button type="warning" @click="handleBatchTransfer" :disabled="selectedSampleManagement.length === 0">
              <el-icon><Switch /></el-icon>批量转移 ({{ selectedSampleManagement.length }})
            </el-button>
            <el-button type="danger" @click="handleBatchDestroy" :disabled="selectedSampleManagement.length === 0">
              <el-icon><Delete /></el-icon>批量销毁 ({{ selectedSampleManagement.length }})
            </el-button>            
          </div>

          <!-- 统计信息 -->
          <div class="mb-4 status-summary">
            <div class="status-item">
              <span class="status-label">总样品数</span>
              <span class="status-count total">{{ getSampleTotalCount() }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">已入库</span>
              <span class="status-count stored">{{ getSampleStatusCount('stored') }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">检验中</span>
              <span class="status-count testing">{{ getSampleStatusCount('testing') }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">已完成</span>
              <span class="status-count completed">{{ getSampleStatusCount('completed') }}</span>
            </div>

            <div class="status-item">
              <span class="status-label">已销毁</span>
              <span class="status-count destroyed">{{ getSampleStatusCount('destroyed') }}</span>
            </div>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading.sampleManagement"
            :data="tableData.sampleManagement"
            border
            style="width: 100%"
            @selection-change="handleSampleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="sampleCode" label="样品编号" min-width="12rem" />
            <el-table-column prop="sampleType" label="样品类型" min-width="10rem">
              <template #default="{ row }">
                <el-tag :type="getSampleTypeColor(row.sampleType)">
                  {{ getSampleTypeText(row.sampleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="samplingDate" label="采样日期" min-width="12rem" />
            <el-table-column prop="samplingPersonName" label="采样人员" min-width="10rem" />
            <el-table-column prop="volume" label="样品体积(mL)" min-width="10rem" align="center" />
            <el-table-column prop="appearance" label="外观" min-width="12rem" />
            <el-table-column prop="testItemName" label="检测项目" min-width="12rem" show-overflow-tooltip />
            <el-table-column prop="preservationMethod" label="保存方法" min-width="10rem" />
            <el-table-column prop="storageLocation" label="存储位置" min-width="12rem" />
            <el-table-column prop="expiryDate" label="有效期至" min-width="12rem" />
            <el-table-column prop="status" label="样品状态" min-width="10rem">
              <template #default="{ row }">
                <el-tag :type="getSampleStatusTagType(row.status)">
                  {{ getSampleStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="storageTemperature" label="存储温度(°C)" min-width="10rem" />
            <el-table-column label="操作" min-width="25rem" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEdit('sampleManagement', row)">编辑</el-button>
                <el-button link type="success" @click="handleDetail('sampleManagement', row)">详情</el-button>
                <el-button v-if="row.status === 'collected'" link type="warning" @click="handleStorage(row)">入库</el-button>
                <el-button link type="danger" @click="handleDestroy(row)">销毁</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>


    </el-tabs>

    <!-- 工作流步骤对话框 -->
    <SamplingWorkflowDialog
      ref="workflowDialogRef"
      @success="handleWorkflowSuccess"
    />

    <!-- 送检单对话框 -->
    <SubmissionReportDialog
      ref="submissionReportDialogRef"
      @success="onSubmissionReportSuccess"
    />
  </ContentWrap>

  <!-- 采样任务对话框 -->
  <SamplingTaskDialog
    ref="samplingTaskDialogRef"
    @success="refreshTable('tasks')"
    @confirm="handleTaskDialogConfirm"
  />

  <!-- 状态变更对话框 -->
  <ConfirmSamplingDialog ref="confirmSamplingDialogRef" @success="refreshTable('tasks')" />
  <CompleteSamplingDialog ref="completeSamplingDialogRef" @success="refreshTable('tasks')" />
  <RecordAbnormalDialog ref="recordAbnormalDialogRef" @success="refreshTable('tasks')" />
  <CreateInspectionFormDialog ref="createInspectionFormDialogRef" @success="handleInspectionFormSuccess" />
  <SubmissionDialog ref="submissionDialogRef" @success="handleSubmissionSuccess" />

  <!-- 样品管理弹窗 -->
  <SampleManagementDialog
    ref="sampleManagementDialogRef"
    @confirm="handleSampleDialogConfirm"
  />

  <!-- 采样任务详情弹窗 -->
  <SamplingTaskDetailDialog
    ref="samplingTaskDetailDialogRef"
  />

  <!-- 样品详情弹窗 -->
  <SampleDetailDialog
    ref="sampleDetailDialogRef"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, h } from 'vue'
// 不需要单独导入图标，因为已在模板中使用
import { ElMessage, ElMessageBox } from 'element-plus'
import { exportSamplingForm, exportInspectionForm } from '@/utils/csvExport'

// 导入组件
import SamplingTaskDialog from './components/SamplingTaskDialog.vue'
import SamplingTaskDetailDialog from './components/SamplingTaskDetailDialog.vue'
import SampleManagementDialog from './components/SampleManagementDialog.vue'
import SampleDetailDialog from './components/SampleDetailDialog.vue'
import SamplingWorkflowDialog from './components/SamplingWorkflowDialog.vue'
import SubmissionReportDialog from './components/SubmissionReportDialog.vue'
// 状态变更对话框
import ConfirmSamplingDialog from './components/ConfirmSamplingDialog.vue'
import CompleteSamplingDialog from './components/CompleteSamplingDialog.vue'
import RecordAbnormalDialog from './components/RecordAbnormalDialog.vue'
import CreateInspectionFormDialog from './components/CreateInspectionFormDialog.vue'
import SubmissionDialog from './components/SubmissionDialog.vue'

defineOptions({ name: 'AssaySampling' })

// 当前激活的标签页
const activeTab = ref<'tasks' | 'submissionRecords' | 'sampleManagement'>('tasks')

// 任务状态筛选器
const taskStatusFilter = ref<'pending' | 'processing' | 'completed' | 'abnormal'>('pending')

// 加载状态
const loading = reactive({
  tasks: false,
  sampleManagement: false,
  submissionRecords: false
})

// 搜索表单
const searchForm = reactive({
  tasks: {
    name: '',
    samplingPoint: '',
    executor: '',
    planDate: [] as string[]
  },
  sampleManagement: {
    sampleCode: '',
    sampleType: '',
    status: '',
    samplingDate: [] as string[],
    storageLocation: ''
  },
  submissionRecords: {
    submissionCode: '',
    sampleCode: '',
    status: '',
    submissionDate: [] as string[],
    submissionLab: '',
    submitterName: ''
  }
})

// 分页配置
const pagination = reactive({
  tasks: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  sampleManagement: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  submissionRecords: {
    page: 1,
    size: 10,
    total: 0
  }
})

// 定义表格数据类型接口

interface SamplingTask {
  id: number;
  factoryId: number;   // ✅ v4.0新增：水厂ID
  name: string;
  planName: string;
  samplingPoint: string;
  planDate: string;
  executor: string;
  // ✅ v4.0修改：人员字段ID化
  samplerId?: number;    // 采样人员ID
  testerId?: number;     // 检测人员ID
  reviewerId?: number;   // 审核人员ID
  samplerName?: string;  // 采样人员姓名（用于显示）
  testerName?: string;   // 检测人员姓名（用于显示）
  reviewerName?: string; // 审核人员姓名（用于显示）
  status: string; // pending-待采样, processing-采样中, completed-已完成, abnormal-异常终止
  sampleId?: number; // 关联的样品ID
  // 从采样计划继承的字段
  sampleNature: string; // 性质 (来自采样计划)
  sampleQuantity: number; // 样品数量 (来自采样计划，现场可调整)
  needTest: boolean; // 是否检测 (来自采样计划)
  expectedAppearance?: string; // 预期样品外观 (来自采样计划)
  expectedSupernatant?: string; // 预期上清液情况 (来自采样计划)
  samplingInstructions?: string; // 采样说明 (来自采样计划)

  // 现场填写的字段
  samplingLocation: string; // 地点 (现场填写)
  samplingCondition: string; // 现场采样情况 (现场填写)
  sampleAppearance: string; // 实际样品外观 (现场填写)
  supernatant: string; // 实际上清液 (现场填写)
  sampleStatus: string; // 样品状态 (现场填写)
  actualSampleQuantity?: number; // 实际采样数量 (现场填写，如与计划不符)
  actualSamplingTime: string; // 采样时间 (现场填写)
  completedTime?: string; // 完成时间
}

// 送检记录接口
interface SubmissionRecord {
  id: number;
  factoryId: number;
  submissionCode: string;
  sampleCode: string;
  sampleId: number;
  executionId: number;
  samplingPoint: string;
  testItems: string;
  sampleType: string;
  volume: number;
  submissionDate: string;
  submissionTime?: string;
  submissionLab?: string;
  submitterName?: string;
  submitterId?: number;
  receiverName?: string;
  receiverId?: number;
  receiveTime?: string;
  status: 'pending' | 'submitted' | 'received';
  urgency: 'normal' | 'urgent';
  specialRequirements?: string;
  remark?: string;
  createTime: string;
  updateTime: string;
}

interface SampleManagement {
  id: number;
  factoryId: number;   // ✅ v4.0新增：水厂ID
  sampleCode: string;
  sampleType: string;
  samplingPoint: string;
  samplingDate: string;
  volume: number;
  preservationMethod: string;
  storageLocation: string;
  expiryDate: string;
  status: string;      // ✅ v4.0修改：5状态生命周期
  temperature: number;
  description?: string;
  // v4.0字段
  appearance: string; // 外观 (来自采样任务的sampleAppearance)
  testProgress: number; // 检测进度 (百分比，系统计算)
  samplingPersonId: number; // ✅ v4.0新增：采样人员ID
  samplingPersonName?: string; // 采样人员姓名（用于显示）
  executionId: number; // ✅ v4.0新增：关联采样执行ID
  testItems: number[]; // ✅ v4.0新增：检测项目ID列表
}



// 表格数据
const tableData = reactive({
  tasks: [] as SamplingTask[],
  sampleManagement: [] as SampleManagement[],
  submissionRecords: [] as SubmissionRecord[]
})

// 选择的行数据
const selectedTasks = ref<SamplingTask[]>([])
const selectedSampleManagement = ref<SampleManagement[]>([])
const selectedSubmissionRecords = ref<SubmissionRecord[]>([])

// 送检记录数据（从已完成的采样任务中获取）
const submissionRecords = computed(() => tableData.submissionRecords)

// 根据状态筛选的任务数据
const filteredTaskData = computed(() => {
  return tableData.tasks.filter(task => task.status === taskStatusFilter.value)
})

// 对话框引用
const samplingTaskDialogRef = ref<InstanceType<typeof SamplingTaskDialog> | null>(null)
const samplingTaskDetailDialogRef = ref(null)
const sampleManagementDialogRef = ref<InstanceType<typeof SampleManagementDialog> | null>(null)
const sampleDetailDialogRef = ref<InstanceType<typeof SampleDetailDialog> | null>(null)
const workflowDialogRef = ref<InstanceType<typeof SamplingWorkflowDialog> | null>(null)
const submissionReportDialogRef = ref(null)
// 状态变更对话框引用
const confirmSamplingDialogRef = ref<InstanceType<typeof ConfirmSamplingDialog> | null>(null)
const completeSamplingDialogRef = ref<InstanceType<typeof CompleteSamplingDialog> | null>(null)
const recordAbnormalDialogRef = ref<InstanceType<typeof RecordAbnormalDialog> | null>(null)
const createInspectionFormDialogRef = ref<InstanceType<typeof CreateInspectionFormDialog> | null>(null)
const submissionDialogRef = ref<InstanceType<typeof SubmissionDialog> | null>(null)

// 生命周期钩子
onMounted(() => {
  fetchTableData('tasks')
  fetchTableData('sampleManagement')
  fetchTableData('submissionRecords')
  // 监听来自检测数据管理模块的状态更新
  window.addEventListener('testStatusUpdated', handleTestStatusUpdated)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('testStatusUpdated', handleTestStatusUpdated)
})

// 处理检测状态更新
const handleTestStatusUpdated = (event: CustomEvent) => {
  const { sampleCode, status } = event.detail

  // 查找对应的样品并更新状态
  const sample = tableData.sampleManagement.find(s => s.sampleCode === sampleCode)
  if (sample) {
    // 根据检测状态更新样品状态
    if (status === 'reviewing' || status === 'entered') {
      sample.status = 'testing'
    } else if (status === 'approved' || status === 'completed') {
      sample.status = 'completed'
    }

    console.log(`样品 ${sampleCode} 状态已更新为: ${sample.status}`)
  }
}

// 监听标签页切换
watch(activeTab, (newVal: 'tasks' | 'sampleManagement') => {
  fetchTableData(newVal)
})

// 状态筛选变化处理
const handleStatusFilterChange = (status: string) => {
  taskStatusFilter.value = status as 'pending' | 'processing' | 'completed' | 'abnormal'
  pagination.tasks.current = 1
}

// 获取各状态任务数量
const getTaskCountByStatus = (status: string) => {
  return tableData.tasks.filter(task => task.status === status).length
}

// 获取任务状态颜色
const getTaskStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'abnormal': 'danger'
  }
  return colorMap[status] || 'info'
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  const textMap = {
    'pending': '待采样',
    'processing': '采样中',
    'completed': '已完成',
    'abnormal': '异常终止'
  }
  return textMap[status] || '未知'
}

// 获取样品状态颜色
const getSampleStatusColor = (status: string) => {
  const colorMap = {
    '待采样': 'info',
    '正常': 'success',
    '采样中': 'warning',
    '异常': 'danger'
  }
  return colorMap[status] || 'info'
}

// 获取样品状态文本
const getSampleStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'collected': '已采集',
    'stored': '已入库',
    'submitted': '已送检',
    'testing': '检验中',
    'completed': '已完成',
    'exception': '异常处理中',
    'destroyed': '已销毁'
  }
  return textMap[status] || status
}

// 获取表格数据
const fetchTableData = async (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  loading[type] = true
  try {
    // 模拟异步请求
    setTimeout(() => {
      if (type === 'tasks') {
        // ✅ 优化后的采样任务数据 - 与计划模块数据流保持一致
        tableData.tasks = [
          {
            id: 1,
            name: '进水水质日常检测采样任务',
            planName: '进水水质日常检测计划',  // 来源：计划模块ID=1
            samplingPoint: '进水总口',  // 来源：采样点ID=1
            planDate: '2024-01-15',
            executor: '张三',
            sampler: '张三',    // 来源：计划模块.sampler
            tester: '李四',     // 来源：计划模块.tester
            reviewer: '赵六',   // 来源：计划模块.reviewer
            status: 'completed',
            sampleId: 1,

            // 从采样计划继承的字段
            sampleNature: '液体',
            sampleQuantity: 2,
            needTest: true,
            expectedSampleQuantity: 2,
            expectedSampleNature: '液体',
            expectedAppearance: '无色透明',
            expectedSupernatant: '清澈',
            samplingInstructions: '每日上午9点采样，注意水样代表性',

            // 现场填写的字段
            samplingLocation: '进水总口A区域',
            samplingCondition: '天气晴朗，水流正常，无异常情况',
            sampleAppearance: '微黄色，略浑浊',
            supernatant: '清澈',
            sampleStatus: '正常',
            actualSampleQuantity: 2,
            actualSamplingTime: '2024-01-15 09:30:00'
          },
          {
            id: 2,
            name: '出水水质监测采样任务',
            planName: '出水水质监测计划',  // 来源：计划模块ID=2
            samplingPoint: '出水总口',  // 来源：采样点ID=5
            planDate: '2024-01-15',
            executor: '钱七',
            sampler: '钱七',
            tester: '孙八',
            reviewer: '赵六',
            status: 'processing',
            sampleId: 2,

            // 从采样计划继承的字段
            sampleNature: '液体',
            sampleQuantity: 1,
            needTest: true,
            expectedSampleQuantity: 1,
            expectedSampleNature: '液体',
            expectedAppearance: '微黄透明',
            expectedSupernatant: '清澈',
            samplingInstructions: '每日8:00、14:00、20:00三次采样',

            // 现场填写的字段（采样中状态 - 只有确认采样时填写的字段）
            samplingLocation: '出水总口B区域',
            samplingCondition: '设备运行正常，出水稳定',
            actualSamplingTime: '2024-01-15 14:15:00',
            // 完成采样时填写的字段（采样中状态下为空）
            sampleAppearance: '',
            supernatant: '',
            sampleStatus: '采样中',
            actualSampleQuantity: 0
          },
          {
            id: 3,
            name: '污泥浓度检测采样任务',
            planName: '污泥浓度检测计划',  // 来源：计划模块ID=3
            samplingPoint: '污泥浓缩池',  // 来源：采样点ID=6
            planDate: '2024-01-16',
            executor: '钱七',
            sampler: '钱七',
            tester: '周九',
            reviewer: '王五',
            status: 'completed',
            sampleId: 3,

            // 从采样计划继承的字段
            sampleNature: '半固体',
            sampleQuantity: 1,
            needTest: true,
            expectedSampleQuantity: 1,
            expectedSampleNature: '半固体',
            expectedAppearance: '棕色粘稠',
            expectedSupernatant: '浑浊',
            samplingInstructions: '每日上午10点和下午4点采样',

            // 现场填写的字段
            samplingLocation: '污泥浓缩池A池',
            samplingCondition: '生化池运行正常，污泥浓度适中',
            sampleAppearance: '棕褐色，粘稠状',
            supernatant: '略浑浊',
            sampleStatus: '正常',
            actualSampleQuantity: 1,
            actualSamplingTime: '2024-01-16 10:45:00'
          },
          {
            id: 4,
            name: '生化池工艺监测采样任务',
            planName: '生化池工艺监测计划',  // 来源：计划模块ID=4
            samplingPoint: '生化池进口',  // 来源：采样点ID=2
            planDate: '2024-01-16',
            executor: '张三',
            sampler: '张三',
            tester: '李四',
            reviewer: '王五',
            status: 'pending',
            sampleId: 4,

            // 从采样计划继承的字段
            sampleNature: '液体',
            sampleQuantity: 2,
            needTest: true,
            expectedSampleQuantity: 2,
            expectedSampleNature: '液体',
            expectedAppearance: '微黄浑浊',
            expectedSupernatant: '浑浊',
            samplingInstructions: '每日上午11点同时采集进出口样品',

            // 现场填写的字段（待采样）
            samplingLocation: '',
            samplingCondition: '',
            sampleAppearance: '',
            supernatant: '',
            sampleStatus: '待采样',
            actualSampleQuantity: 0,
            actualSamplingTime: ''
          },
          {
            id: 5,
            name: '重金属污染源排查采样任务',
            planName: '重金属污染源排查',  // 来源：临时计划ID=2
            samplingPoint: '进水总口',  // 来源：采样点ID=1
            planDate: '2024-01-20',
            executor: '钱七',
            sampler: '钱七',
            tester: '孙八',
            reviewer: '周九',
            status: 'processing',
            sampleId: 5,

            // 从采样计划继承的字段
            sampleNature: '液体',
            sampleQuantity: 2,
            needTest: true,
            expectedSampleQuantity: 2,
            expectedSampleNature: '液体',
            expectedAppearance: '正常透明',
            expectedSupernatant: '清澈',
            samplingInstructions: '同时采集各处理单元样品，确保时间同步',

            // 现场填写的字段（采样中状态 - 只有确认采样时填写的字段）
            samplingLocation: '进水总口监测点',
            samplingCondition: '紧急排查，水质异常',
            actualSamplingTime: '2024-01-20 09:30:00',
            // 完成采样时填写的字段（采样中状态下为空）
            sampleAppearance: '',
            supernatant: '',
            sampleStatus: '采样中',
            actualSampleQuantity: 0
          },
          {
            id: 6,
            name: '污泥脱水效果监测采样任务',
            planName: '污泥脱水效果监测计划',  // 来源：计划模块ID=6
            samplingPoint: '污泥脱水间',  // 来源：采样点ID=7
            planDate: '2024-01-16',
            executor: '钱七',
            sampler: '钱七',
            tester: '周九',
            reviewer: '王五',
            status: 'pending',
            sampleId: 6,

            // 从采样计划继承的字段
            sampleNature: '半固体',
            sampleQuantity: 1,
            needTest: true,
            expectedSampleQuantity: 1,
            expectedSampleNature: '半固体',
            expectedAppearance: '深棕色固体',
            expectedSupernatant: '-',
            samplingInstructions: '每周二、五脱水作业时采样',

            // 现场填写的字段（待采样）
            samplingLocation: '',
            samplingCondition: '',
            sampleAppearance: '',
            supernatant: '',
            sampleStatus: '待采样',
            actualSampleQuantity: 0,
            actualSamplingTime: ''
          },
          {
            id: 7,
            name: '营养盐全面监测采样任务',
            planName: '营养盐全面监测计划',  // 来源：计划模块ID=7
            samplingPoint: '进水总口',  // 来源：采样点ID=1
            planDate: '2024-01-17',
            executor: '张三',
            sampler: '张三',
            tester: '李四',
            reviewer: '赵六',
            status: 'pending',
            sampleId: 7,

            // 从采样计划继承的字段
            sampleNature: '液体',
            sampleQuantity: 1,
            needTest: true,
            expectedSampleQuantity: 1,
            expectedSampleNature: '液体',
            expectedAppearance: '透明至微黄',
            expectedSupernatant: '清澈',
            samplingInstructions: '每周三上午同时采集三个点位样品',

            // 现场填写的字段（待采样）
            samplingLocation: '',
            samplingCondition: '',
            sampleAppearance: '',
            supernatant: '',
            sampleStatus: '待采样',
            actualSampleQuantity: 0,
            actualSamplingTime: ''
          },
          {
            id: 8,
            name: '有机物去除效果监测采样任务',
            planName: '有机物去除效果监测计划',  // 来源：计划模块ID=8
            samplingPoint: '进水总口',  // 来源：采样点ID=1
            planDate: '2024-01-16',
            executor: '钱七',
            sampler: '钱七',
            tester: '孙八',
            reviewer: '周九',
            status: 'assigned',
            sampleId: 8,

            // 从采样计划继承的字段
            sampleNature: '液体',
            sampleQuantity: 1,
            needTest: true,
            expectedSampleQuantity: 1,
            expectedSampleNature: '液体',
            expectedAppearance: '无色至微黄',
            expectedSupernatant: '清澈',
            samplingInstructions: '每周二、五上午采样对比',

            // 现场填写的字段（待采样）
            samplingLocation: '',
            samplingCondition: '',
            sampleAppearance: '',
            supernatant: '',
            sampleStatus: '待采样',
            actualSampleQuantity: 0,
            actualSamplingTime: ''
          }
        ]
        pagination.tasks.total = 8
      } else if (type === 'sampleManagement') {
        // ✅ 优化后的样品管理数据 - 与采样任务数据流保持一致
        tableData.sampleManagement = [
          {
            id: 1,
            sampleCode: 'SP20240115001',
            sampleType: 'water',
            samplingPoint: '进水总口',  // 来源：采样任务ID=1
            samplingDate: '2024-01-15',  // 来源：采样任务.actualSamplingTime
            volume: 500,  // 来源：采样任务.actualSampleQuantity * 250ml
            preservationMethod: 'refrigerated',
            storageLocation: 'A区-01-001',
            expiryDate: '2024-01-22',
            status: 'stored',
            temperature: 4,
            description: '进水水质日常检测样品',
            appearance: '微黄色，略浑浊',  // 来源：采样任务.sampleAppearance
            testProgress: 100,  // 系统计算：检测完成进度
            remainingSampleDisposal: true  // 样品管理阶段设置
          },
          {
            id: 2,
            sampleCode: 'SP20240115002',
            sampleType: 'water',
            samplingPoint: '出水总口',  // 来源：采样任务ID=2
            samplingDate: '2024-01-15',  // 来源：采样任务.actualSamplingTime
            volume: 250,  // 来源：采样任务.actualSampleQuantity * 250ml
            preservationMethod: 'normal',
            storageLocation: 'B区-02-005',
            expiryDate: '2024-01-18',
            status: 'testing',
            temperature: 20,
            description: '出水水质监测样品',
            appearance: '无色透明',  // 来源：采样任务.sampleAppearance
            testProgress: 60,  // 系统计算：检测进度60%
            remainingSampleDisposal: false
          },
          {
            id: 3,
            sampleCode: 'SP20240116001',
            sampleType: 'sludge',
            samplingPoint: '污泥浓缩池',  // 来源：采样任务ID=3
            samplingDate: '2024-01-16',  // 来源：采样任务.actualSamplingTime
            volume: 200,  // 来源：采样任务.actualSampleQuantity * 200ml
            preservationMethod: 'frozen',
            storageLocation: 'C区-03-010',
            expiryDate: '2024-01-30',
            status: 'completed',
            temperature: -18,
            description: '污泥浓度检测样品',
            appearance: '棕褐色，粘稠状',  // 来源：采样任务.sampleAppearance
            testProgress: 100,  // 系统计算：检测完成进度
            remainingSampleDisposal: true  // 样品管理阶段设置
          },
          {
            id: 4,
            sampleCode: 'SP20240120001',
            sampleType: 'water',
            samplingPoint: '进水总口',  // 来源：采样任务ID=5
            samplingDate: '2024-01-20',  // 来源：采样任务.actualSamplingTime
            volume: 500,  // 来源：采样任务.actualSampleQuantity * 250ml
            preservationMethod: 'acid',
            storageLocation: 'A区-01-015',
            expiryDate: '2024-01-27',
            status: 'testing',
            temperature: 4,
            description: '重金属污染源排查样品',
            appearance: '微黄色，有异味',  // 来源：采样任务.sampleAppearance
            testProgress: 30,  // 系统计算：检测进度30%
            remainingSampleDisposal: false  // 样品管理阶段设置
          },
          {
            id: 5,
            sampleCode: 'SP20240114001',
            sampleType: 'water',
            samplingPoint: '出水总口',  // 历史样品
            samplingDate: '2024-01-14',
            volume: 250,
            preservationMethod: 'normal',
            storageLocation: 'B区-02-012',
            expiryDate: '2024-01-17',
            status: 'destroyed',
            temperature: 20,
            description: '出水口历史检测样品',
            appearance: '无色透明',
            testProgress: 100,  // 系统计算：已销毁，进度100%
            remainingSampleDisposal: true  // 样品管理阶段设置：已处置
          },
          {
            id: 6,
            sampleCode: 'SP20240113001',
            sampleType: 'water',
            samplingPoint: '生化池进口',
            samplingDate: '2024-01-13',
            volume: 500,
            preservationMethod: 'refrigerated',
            storageLocation: 'A区-01-020',
            expiryDate: '2024-01-20',
            status: 'stored',
            temperature: 4,
            description: '生化池工艺监测样品',
            appearance: '微黄浑浊',
            testProgress: 0,  // 系统计算：未开始检测
            remainingSampleDisposal: false
          },
          {
            id: 7,
            sampleCode: 'SP20240112001',
            sampleType: 'sludge',
            samplingPoint: '回流污泥',
            samplingDate: '2024-01-12',
            volume: 300,
            preservationMethod: 'refrigerated',
            storageLocation: 'C区-03-015',
            expiryDate: '2024-01-19',
            status: 'expired',
            temperature: 4,
            description: '回流污泥监测样品',
            appearance: '棕色粘稠',
            testProgress: 0,  // 系统计算：已过期，未检测
            remainingSampleDisposal: true
          },
          {
            id: 8,
            sampleCode: 'SP20240111001',
            sampleType: 'water',
            samplingPoint: '中间水池',
            samplingDate: '2024-01-11',
            volume: 250,
            preservationMethod: 'normal',
            storageLocation: 'B区-02-018',
            expiryDate: '2024-01-14',
            status: 'completed',
            temperature: 20,
            description: '中间处理工艺监测样品',
            appearance: '微黄透明',
            testProgress: 100,  // 系统计算：检测完成
            remainingSampleDisposal: true
          },
          {
            id: 7,
            sampleCode: 'SP20240116007',
            sampleType: 'water',
            samplingPoint: '进水总口',
            samplingDate: '2024-01-16',
            volume: 500,
            preservationMethod: 'refrigerated',
            storageLocation: '冷藏室A-07',
            expiryDate: '2024-01-23',
            status: 'exception',  // 异常处理中
            temperature: 4,
            description: '检测过程中发现异常，正在处理',
            appearance: '异常浑浊，有异味',
            testProgress: 60,
            remainingSampleDisposal: false
          },
          {
            id: 8,
            sampleCode: 'SP20240110008',
            sampleType: 'sludge',
            samplingPoint: '污泥浓缩池',
            samplingDate: '2024-01-10',
            volume: 200,
            preservationMethod: 'destroyed',
            storageLocation: '已销毁',
            expiryDate: '2024-01-17',
            status: 'destroyed',  // 已销毁
            temperature: 0,
            description: '检测完成后按规定销毁',
            appearance: '深褐色污泥',
            testProgress: 100,
            remainingSampleDisposal: false
          }
        ]
        pagination.sampleManagement.total = tableData.sampleManagement.length
      } else if (type === 'submissionRecords') {
        // 送检记录模拟数据（从已完成的采样任务生成）
        tableData.submissionRecords = [
          {
            id: 1,
            factoryId: 1,
            submissionCode: 'SUB-2024-001-001',
            sampleCode: 'SP20240115001',
            sampleId: 1,
            executionId: 1,
            samplingPoint: '进水总口',
            testItems: 'COD, BOD5, 氨氮, 总磷',
            sampleType: 'water',
            volume: 100,
            submissionDate: '2024-01-15',
            submissionTime: '2024-01-15 14:30:00',
            submissionLab: '第三方检测实验室A',
            submitterName: '张三',
            submitterId: 1001,
            receiverName: '李四',
            receiverId: 2001,
            receiveTime: '2024-01-15 15:00:00',
            status: 'received',
            urgency: 'normal',
            specialRequirements: '请在24小时内完成检测',
            remark: '进水口日常监测样品',
            createTime: '2024-01-15 10:00:00',
            updateTime: '2024-01-15 15:00:00'
          },
          {
            id: 2,
            factoryId: 1,
            submissionCode: 'SUB-2024-001-002',
            sampleCode: 'SP20240115002',
            sampleId: 2,
            executionId: 2,
            samplingPoint: '出水总口',
            testItems: 'COD, BOD5, 悬浮物',
            sampleType: 'water',
            volume: 150,
            submissionDate: '2024-01-15',
            submissionTime: '2024-01-15 16:00:00',
            submissionLab: '第三方检测实验室A',
            submitterName: '王五',
            submitterId: 1002,
            status: 'submitted',
            urgency: 'normal',
            remark: '出水口日常监测样品',
            createTime: '2024-01-15 11:00:00',
            updateTime: '2024-01-15 16:00:00'
          },
          {
            id: 3,
            factoryId: 1,
            submissionCode: 'SUB-2024-001-003',
            sampleCode: 'SP20240116003',
            sampleId: 3,
            executionId: 3,
            samplingPoint: '生化池',
            testItems: '重金属检测',
            sampleType: 'sludge',
            volume: 200,
            submissionDate: '2024-01-16',
            status: 'pending',
            urgency: 'urgent',
            specialRequirements: '紧急检测，需要加急处理',
            remark: '污泥重金属含量检测',
            createTime: '2024-01-16 09:00:00',
            updateTime: '2024-01-16 09:00:00'
          }
        ]
        pagination.submissionRecords.total = tableData.submissionRecords.length
      }
      loading[type] = false
    }, 500)
  } catch (error) {
    console.error(`获取${type}数据失败:`, error)
    loading[type] = false
  }
}



// 刷新表格
const refreshTable = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  fetchTableData(type)
}

// 确认采样（包含信息填写）
// 确认采样（使用专门的对话框）
const handleConfirmSampling = (row: SamplingTask) => {
  confirmSamplingDialogRef.value?.open(row)
}

// 完成采样（使用专门的对话框）
const handleCompleteSampling = (row: SamplingTask) => {
  completeSamplingDialogRef.value?.open(row)
}

// 记录异常（使用专门的对话框）
const handleAbnormal = (row: SamplingTask) => {
  recordAbnormalDialogRef.value?.open(row)
}



// 生成送检单（打开弹窗填写样品管理信息）
const handleCreateInspectionForm = (row: SamplingTask) => {
  createInspectionFormDialogRef.value?.open(row)
}

// 送检单生成成功回调
const handleInspectionFormSuccess = (data: any) => {
  // 更新任务状态为submitted（已提交送检）
  const task = filteredTaskData.value.find(t => t.id === data.id)
  if (task) {
    task.status = 'submitted'
  }

  // 刷新表格数据
  refreshTable('tasks')
  refreshTable('submissionRecords')
  ElMessage.success('送检单生成成功，送检记录已创建')
}

// 导出送检单（保留原有功能）
const handleExportReport = (row: SamplingTask) => {
  ElMessage.success(`正在导出 ${row.name} 的送检单...`)
  // 这里应该调用导出API
  setTimeout(() => {
    ElMessage.success(`送检单已导出: ${row.name}_送检单.csv`)
  }, 1000)
}

// 批量确认采样
const handleBatchConfirm = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要确认的采样任务')
    return
  }

  ElMessageBox.confirm(`确认开始选中的 ${selectedTasks.value.length} 个采样任务吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    selectedTasks.value.forEach(task => {
      task.status = 'processing'
      task.samplingLocation = task.samplingLocation || `${task.samplingPoint}默认区域`
      task.actualSamplingTime = new Date().toLocaleString()
    })
    ElMessage.success(`已确认 ${selectedTasks.value.length} 个采样任务`)
    refreshTable('tasks')
    selectedTasks.value = []
  }).catch(() => {
    // 用户取消操作
  })
}

// 批量完成采样
const handleBatchComplete = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要完成的采样任务')
    return
  }

  const processingTasks = selectedTasks.value.filter(task => task.status === 'processing')
  if (processingTasks.length === 0) {
    ElMessage.warning('选中的任务中没有正在采样中的任务')
    return
  }

  ElMessageBox.confirm(`确认完成选中的 ${processingTasks.length} 个采样任务吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    processingTasks.forEach(task => {
      task.status = 'completed'
    })
    ElMessage.success(`已完成 ${processingTasks.length} 个采样任务`)
    refreshTable('tasks')
    selectedTasks.value = []
  }).catch(() => {
    // 用户取消操作
  })
}

// 批量生成送检单
const handleBatchExportReport = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要生成送检单的任务')
    return
  }

  const completedTasks = selectedTasks.value.filter(task => ['completed', 'abnormal'].includes(task.status))
  if (completedTasks.length === 0) {
    ElMessage.warning('选中的任务中没有已完成或异常终止的任务')
    return
  }

  ElMessage.success(`正在生成 ${completedTasks.length} 个送检单...`)
  // 这里应该调用批量生成API
  setTimeout(() => {
    ElMessage.success(`已生成 ${completedTasks.length} 个送检单`)
  }, 1000)
}









// 送检单提交成功回调
const onSubmissionReportSuccess = (reportData: any) => {
  ElMessage.success('送检单已提交')
  refreshTable('tasks')
}

// ==================== 采样计划相关方法 ====================



// 采样任务选择变化
const handleTaskSelectionChange = (selection: SamplingTask[]) => {
  selectedTasks.value = selection
}









// 打印任务单




// 搜索
const handleSearch = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  if (type === 'submissionRecords') {
    pagination[type].page = 1
  } else {
    pagination[type].current = 1
  }
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  if (type === 'tasks') {
    searchForm.tasks = {
      name: '',
      samplingPoint: '',
      executor: '',
      planDate: []
    }
  } else if (type === 'sampleManagement') {
    searchForm.sampleManagement = {
      sampleCode: '',
      sampleType: '',
      status: '',
      samplingDate: [],
      storageLocation: ''
    }
  } else if (type === 'submissionRecords') {
    searchForm.submissionRecords = {
      submissionCode: '',
      sampleCode: '',
      status: '',
      submissionDate: [],
      submissionLab: '',
      submitterName: ''
    }
  }
  fetchTableData(type)
}

// 重置方法的别名（为了兼容模板中的调用）
const handleReset = resetSearch

// 新增/编辑
const handleAdd = (type: 'tasks' | 'sampleManagement') => {
  if (type === 'tasks') {
    samplingTaskDialogRef.value?.open('create')
  } else if (type === 'sampleManagement') {
    sampleManagementDialogRef.value?.open('create')
  }
}

const handleEdit = (type: 'tasks' | 'sampleManagement', row: any) => {
  if (type === 'tasks') {
    samplingTaskDialogRef.value?.open('update', row)
  } else if (type === 'sampleManagement') {
    sampleManagementDialogRef.value?.open('update', row)
  }
}

const handleDetail = (type: 'tasks' | 'sampleManagement', row: any) => {
  if (type === 'sampleManagement') {
    // 打开样品详情弹窗
    sampleDetailDialogRef.value?.open(row)
  } else if (type === 'tasks') {
    // 打开采样任务详情弹窗
    samplingTaskDetailDialogRef.value?.open(row)
  }
}

// 采样任务弹窗确认处理
const handleTaskDialogConfirm = (data: any) => {
  if (data.id) {
    // 更新现有任务
    const index = tableData.tasks.findIndex(item => item.id === data.id)
    if (index !== -1) {
      tableData.tasks[index] = { ...tableData.tasks[index], ...data }
    }
  } else {
    // 新增任务
    const newTask = {
      ...data,
      id: Date.now(), // 临时ID
      workflowStep: 1,
      conditionMet: false
    }
    tableData.tasks.unshift(newTask)
  }

  // 刷新表格
  fetchTableData('tasks')
}

// 样品管理弹窗确认处理
const handleSampleDialogConfirm = (data: any) => {
  if (data.id) {
    // 更新现有样品
    const index = tableData.sampleManagement.findIndex(item => item.id === data.id)
    if (index !== -1) {
      tableData.sampleManagement[index] = { ...tableData.sampleManagement[index], ...data }
    }
  } else {
    // 新增样品
    const newSample = {
      ...data,
      id: Date.now() // 临时ID
    }
    tableData.sampleManagement.unshift(newSample)
  }

  // 刷新表格
  fetchTableData('sampleManagement')
}

// 删除
const handleDelete = async (type: 'tasks', row: SamplingTask) => {
  try {
    await ElMessageBox.confirm('确认删除该采样任务吗?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    // 这里应该是调用删除API
    ElMessage.success('删除成功')
    fetchTableData(type)
  } catch {
    // 用户取消删除
  }
}

// 分页大小变化
const handleSizeChange = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  fetchTableData(type)
}

// 页码变化
const handleCurrentChange = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  fetchTableData(type)
}



// 工作流步骤成功回调
const handleWorkflowSuccess = (result: any) => {
  // 更新任务状态
  const task = tableData.tasks.find(t => t.id === result.taskId)
  if (task) {
    if (result.type === 'complete') {
      task.status = 'completed'
    } else if (result.type === 'abnormal') {
      task.status = 'abnormal'
      task.samplingCondition = result.abnormalReason || '采样条件异常'
    }
    refreshTable('tasks')
  }
}




// 获取工作流程当前步骤
const getWorkflowActiveStep = (row: SamplingTask): number => {
  const status = row.status

  if (status === 'pending') return 0
  if (status === 'abnormal') return 0
  if (status === 'completed') return 5
  return 0
}

// 获取步骤描述
const getStepDescription = (row: SamplingTask, step: number): string => {
  const currentStep = getWorkflowActiveStep(row)
  const status = row.status

  if (step > currentStep) {
    return '待执行'
  } else if (step === currentStep && status === 'processing') {
    return '进行中'
  } else if (step <= currentStep) {
    // 根据步骤返回具体的完成信息
    switch (step) {
      case 1:
        return row.executor ? `${row.executor} 已接收` : '已完成'
      case 2:
        return '已到达现场'
      case 3:
        return '条件符合'
      case 4:
        return status === 'abnormal' ? '异常终止' : '采样完成'
      case 5:
        return '已送检'
      default:
        return '已完成'
    }
  }
  return ''
}

// ==================== 样品管理相关方法 ====================

// 样品选择变化
const handleSampleSelectionChange = (selection: SampleManagement[]) => {
  selectedSampleManagement.value = selection
}

// 获取样品类型颜色
const getSampleTypeColor = (type: string) => {
  const colorMap = {
    'inlet': 'primary',
    'outlet': 'success',
    'sludge': 'warning',
    'process': 'info'
  }
  return colorMap[type] || 'info'
}

// 获取样品类型文本（统一处理所有样品类型）
const getSampleTypeText = (type: string) => {
  const textMap = {
    // 样品管理模块的类型
    'inlet': '进水样品',
    'outlet': '出水样品',
    'sludge': '污泥样品',
    'process': '中间过程样品',
    // 送检记录模块的类型
    'water': '水样',
    'gas': '气体'
  }
  return textMap[type] || type
}

// 获取样品总数量
const getSampleTotalCount = () => {
  return tableData.sampleManagement.length
}

// 获取样品状态数量
const getSampleStatusCount = (status: string) => {
  return tableData.sampleManagement.filter(item => item.status === status).length
}

// 获取样品状态标签类型
const getSampleStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'collected': 'info',
    'stored': 'primary',
    'submitted': 'warning',
    'testing': 'warning',
    'completed': 'success',
    'exception': 'danger',
    'destroyed': 'info'
  }
  return typeMap[status] || 'info'
}



// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 样品入库
const handleStorage = (row: SampleManagement) => {
  ElMessageBox.confirm(`确认将样品 "${row.sampleCode}" 入库吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = 'stored'
    ElMessage.success(`样品 ${row.sampleCode} 已入库`)
    fetchTableData('sampleManagement')
  }).catch(() => {
    // 用户取消入库
  })
}

// 样品转移
const handleTransfer = (row: SampleManagement) => {
  ElMessage.info(`转移样品 ${row.sampleCode} 功能开发中...`)
}

// 样品销毁
const handleDestroy = (row: SampleManagement) => {
  ElMessageBox.confirm(`确认销毁样品 "${row.sampleCode}" 吗? 此操作不可恢复!`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    row.status = 'destroyed'
    ElMessage.success(`样品 ${row.sampleCode} 已销毁`)
    fetchTableData('sampleManagement')
  }).catch(() => {
    // 用户取消销毁
  })
}



// 批量入库
const handleBatchStorage = () => {
  if (selectedSampleManagement.value.length === 0) {
    ElMessage.warning('请先选择要入库的样品')
    return
  }

  const collectedSamples = selectedSampleManagement.value.filter(sample => sample.status === 'collected')
  if (collectedSamples.length === 0) {
    ElMessage.warning('选中的样品中没有可入库的样品')
    return
  }

  ElMessageBox.confirm(`确认将选中的 ${collectedSamples.length} 个样品入库吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    collectedSamples.forEach(sample => {
      sample.status = 'stored'
    })
    ElMessage.success(`已入库 ${collectedSamples.length} 个样品`)
    fetchTableData('sampleManagement')
    selectedSampleManagement.value = []
  }).catch(() => {
    // 用户取消入库
  })
}

// 批量转移
const handleBatchTransfer = () => {
  ElMessage.info('批量转移功能开发中...')
}

// 批量销毁
const handleBatchDestroy = () => {
  if (selectedSampleManagement.value.length === 0) {
    ElMessage.warning('请先选择要销毁的样品')
    return
  }

  ElMessageBox.confirm(`确认销毁选中的 ${selectedSampleManagement.value.length} 个样品吗? 此操作不可恢复!`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    selectedSampleManagement.value.forEach(sample => {
      sample.status = 'destroyed'
    })
    ElMessage.success(`已销毁 ${selectedSampleManagement.value.length} 个样品`)
    fetchTableData('sampleManagement')
    selectedSampleManagement.value = []
  }).catch(() => {
    // 用户取消销毁
  })
}



// 获取样品状态类型
const getSampleStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '正常': 'success',
    '异常': 'danger',
    '需复检': 'warning'
  }
  return typeMap[status] || 'info'
}

// ==================== 送检记录管理相关方法 ====================

// 送检记录选择变更
const handleSubmissionRecordSelectionChange = (selection: SubmissionRecord[]) => {
  selectedSubmissionRecords.value = selection
}

// 获取样品类型标签类型
const getSampleTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    'water': 'primary',
    'sludge': 'warning',
    'gas': 'success'
  }
  return typeMap[type] || 'info'
}



// 获取送检状态标签类型
const getSubmissionStatusTagType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'warning',
    'submitted': 'primary',
    'received': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取送检状态文本
const getSubmissionStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待送检',
    'submitted': '已送检',
    'received': '已接收'
  }
  return textMap[status] || status
}

// 送检记录
const handleSubmitRecord = (record: SubmissionRecord) => {
  // 打开送检弹窗，填写送检人、送检时间、送检实验室信息
  submissionDialogRef.value?.open(record)
}

// 送检成功回调
const handleSubmissionSuccess = (data: any) => {
  // 更新送检记录状态
  const record = submissionRecords.value.find(r => r.id === data.recordId)
  if (record) {
    record.status = 'submitted'
    record.submissionLab = data.submissionLab
    record.submissionTime = data.submissionTime
    record.submitterName = data.submitterName
  }

  // 刷新表格数据
  refreshTable('submissionRecords')
  ElMessage.success('送检成功，样品状态已更新')
}

// 查看送检详情
const handleViewSubmissionDetail = (record: SubmissionRecord) => {
  ElMessage.info(`查看送检记录详情：${record.submissionCode}`)
  // 这里应该打开详情弹窗
}

// 下载送检单
const handleDownloadSubmissionForm = (record: SubmissionRecord) => {
  ElMessage.success(`正在下载送检单：${record.submissionCode}`)
  // 这里应该调用下载API
}

// 批量送检
const handleBatchSubmit = () => {
  if (selectedSubmissionRecords.value.length === 0) {
    ElMessage.warning('请先选择要送检的记录')
    return
  }

  ElMessageBox.confirm(
    `确认要批量送检选中的 ${selectedSubmissionRecords.value.length} 条记录吗？`,
    '批量送检确认',
    {
      confirmButtonText: '确认送检',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 批量更新状态
    selectedSubmissionRecords.value.forEach(record => {
      if (record.status === 'pending') {
        record.status = 'submitted'
        record.submissionTime = new Date().toISOString().slice(0, 19).replace('T', ' ')
        record.submitterName = '当前用户'
      }
    })

    ElMessage.success(`成功送检 ${selectedSubmissionRecords.value.length} 条记录`)
    selectedSubmissionRecords.value = []
    // 这里应该调用API批量更新
  }).catch(() => {
    ElMessage.info('已取消批量送检')
  })
}

// 导出送检记录Excel
const handleExportSubmissionRecords = () => {
  ElMessage.success('正在导出送检记录Excel...')
  // 这里应该调用导出API
}

// 导出送检记录PDF
const handleExportSubmissionPDF = () => {
  ElMessage.success('正在导出送检记录PDF...')
  // 这里应该调用导出API
}

</script>

<style lang="scss" scoped>
// 采样任务表格样式
.sampling-task-table {
  // 设置表格最小宽度以触发水平滚动
  min-width: 100%;

  // 表格容器样式
  :deep(.el-table) {
    // 计算总列宽约为: 60+120+200+180+120+120+100+160+220+160+120+100+130+110+100+170+100+120+450+380 = 2830px
    // 这个宽度会触发水平滚动
    width: 100%;
  }

  // 确保表格能够正确显示所有列
  :deep(.el-table__body-wrapper) {
    overflow-x: auto;
  }

  :deep(.el-table__header-wrapper) {
    overflow-x: hidden;
  }

  // 自定义滚动条样式
  :deep(.el-table__body-wrapper)::-webkit-scrollbar {
    height: 8px;
  }

  :deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  :deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 确保固定列正确显示
  :deep(.el-table__fixed-right) {
    right: 0 !important;
    z-index: 3;
  }

  :deep(.el-table__fixed-left) {
    left: 0 !important;
    z-index: 3;
  }

  // 固定列阴影效果
  :deep(.el-table__fixed-right::before) {
    box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-table__fixed-left::after) {
    box-shadow: 1px 0 8px rgba(0, 0, 0, 0.1);
  }
}

// 搜索表单样式优化
.search-form {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  gap: 0.5rem;
  
  .el-form-item {
    margin-bottom: 0.75rem;
    margin-right: 1.5rem;
  }
}

// 表格容器优化
.el-card {
  margin-bottom: 1.5rem;
  
  :deep(.el-card__body) {
    padding: 1.5rem;
  }
}

// 按钮间距优化
.el-button + .el-button {
  margin-left: 0.75rem;
}

// 分页样式优化
.mt-4 {
  margin-top: 1rem;
}

.flex {
  display: flex;
}

.justify-end {
  justify-content: flex-end;
}

.mb-4 {
  margin-bottom: 1rem;
}

// 表格内按钮组样式优化
.el-table {
  :deep(.el-button--link) {
    padding: 0.25rem 0.5rem;
  }
  
  :deep(.el-divider--vertical) {
    margin: 0 0.5rem;
  }
}

// 表格标题行样式优化
:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

// 表格行高亮效果
:deep(.el-table tr:hover > td) {
  background-color: #ecf5ff;
}

// 标签页样式优化
:deep(.el-tabs__item) {
  font-size: 1rem;
  padding: 0 1.25rem;
}

:deep(.el-tabs__active-bar) {
  height: 0.125rem;
}

// 工作流步骤样式优化
:deep(.el-steps) {
  margin: 0.5rem 0;

  .el-step__title {
    font-size: 0.875rem;
  }

  .el-step__head {
    padding-right: 0.5rem;
  }
}

/* 工作流程样式优化 */
.workflow-container {
  padding: 8px 0;
}

.workflow-steps {
  margin-bottom: 8px;
}

.workflow-steps :deep(.el-step__title) {
  font-size: 12px;
  line-height: 1.2;
}

.workflow-steps :deep(.el-step__description) {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.workflow-steps :deep(.el-step__head) {
  width: 20px;
  height: 20px;
}

.workflow-steps :deep(.el-step__icon) {
  width: 16px;
  height: 16px;
  font-size: 12px;
}

.workflow-error {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #fef0f0;
  border-radius: 4px;
  border-left: 3px solid #f56c6c;
}

.workflow-error .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

// 样品管理状态统计样式
.status-summary {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;

  .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .status-label {
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 0.25rem;
    }

    .status-count {
      font-size: 1.5rem;
      font-weight: bold;

      &.total { color: #3b82f6; }
      &.stored { color: #10b981; }
      &.testing { color: #f59e0b; }
      &.completed { color: #059669; }
      &.warning { color: #ef4444; }
      &.destroyed { color: #6b7280; }
    }
  }
}

/* 预期与实际值对比样式 */
.comparison-cell {
  font-size: 12px;
  line-height: 1.2;
}

.comparison-cell .expected {
  color: #909399;
  margin-bottom: 2px;
}

.comparison-cell .actual {
  color: #303133;
  font-weight: 500;
}

/* 预期信息样式 */
.expected-info {
  color: #909399;
  font-size: 13px;
  font-style: italic;
}

.comparison-cell .actual.different {
  color: #f56c6c;
  font-weight: bold;
}

.comparison-cell .actual.different::before {
  content: "⚠ ";
  color: #e6a23c;
}

/* 执行人员角色样式 */
.executor-roles {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;

  .el-tag {
    min-width: 32px;
    text-align: center;
  }

  span {
    color: #606266;
    font-weight: 500;
  }
}

/* 采样人员样式 */
.sampler-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-tag {
    min-width: 40px;
    text-align: center;
  }

  .sampler-name {
    color: #303133;
    font-weight: 500;
    font-size: 13px;
  }
}
</style>
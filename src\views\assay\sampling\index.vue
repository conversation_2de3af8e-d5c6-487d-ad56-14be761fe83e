<template>
  <ContentWrap title="采样执行管理">
    <el-tabs v-model="activeTab">
      <!-- 采样任务管理 -->
      <el-tab-pane label="采样任务管理" name="tasks">
        <el-card shadow="hover">
          <!-- 状态筛选器 -->
          <div class="mb-4">
            <el-tabs v-model="taskStatusFilter" type="card" @tab-change="handleStatusFilterChange">
              <el-tab-pane label="待采样" name="pending">
                <template #label>
                  <span>待采样 <el-badge :value="getTaskCountByStatus('pending')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="采样中" name="processing">
                <template #label>
                  <span>采样中 <el-badge :value="getTaskCountByStatus('processing')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="已完成" name="completed">
                <template #label>
                  <span>已完成 <el-badge :value="getTaskCountByStatus('completed')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="异常终止" name="abnormal">
                <template #label>
                  <span>异常终止 <el-badge :value="getTaskCountByStatus('abnormal')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.tasks" class="search-form">
              <el-form-item label="任务名称">
                <el-input v-model="searchForm.tasks.name" placeholder="请输入任务名称" clearable />
              </el-form-item>
              <el-form-item label="采样点">
                <el-input v-model="searchForm.tasks.samplingPoint" placeholder="请输入采样点" clearable />
              </el-form-item>
              <el-form-item label="执行人">
                <el-select v-model="searchForm.tasks.executor" placeholder="请选择执行人" clearable style="min-width: 8rem;">
                  <el-option label="张三" value="张三" />
                  <el-option label="李四" value="李四" />
                  <el-option label="王五" value="王五" />
                  <el-option label="赵六" value="赵六" />
                  <el-option label="钱七" value="钱七" />
                </el-select>
              </el-form-item>
              <el-form-item label="计划日期">
                <el-date-picker
                  v-model="searchForm.tasks.planDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('tasks')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('tasks')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button v-if="taskStatusFilter === 'pending'" type="success" @click="handleBatchConfirm" :disabled="selectedTasks.length === 0">
              <el-icon><Check /></el-icon>批量确认采样 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="taskStatusFilter === 'processing'" type="primary" @click="handleBatchComplete" :disabled="selectedTasks.length === 0">
              <el-icon><CircleCheck /></el-icon>批量完成采样 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="['completed', 'abnormal'].includes(taskStatusFilter)" type="info" @click="handleBatchExportReport" :disabled="selectedTasks.length === 0">
              <el-icon><Download /></el-icon>批量生成送检单 ({{ selectedTasks.length }})
            </el-button>
          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.tasks"
            :data="filteredTaskData"
            border
            style="width: 100%;"
            @selection-change="handleTaskSelectionChange"
            class="sampling-task-table"
            :table-layout="'fixed'"
          >
            <el-table-column type="selection" width="60" fixed="left" />
            <el-table-column prop="id" label="任务编号" width="120" fixed="left" />
            <el-table-column prop="name" label="任务名称" width="200" fixed="left" show-overflow-tooltip />
            <el-table-column prop="planName" label="关联计划" width="180" show-overflow-tooltip />
            <el-table-column prop="samplingPoint" label="采样点" width="120" />
            <el-table-column prop="planDate" label="计划日期" width="120" />
            <el-table-column label="采样人员" width="120">
              <template #default="{ row }">
                <div class="sampler-info">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span class="sampler-name">{{ row.samplerName || row.executor || '-' }}</span>
                </div>
              </template>
            </el-table-column>

            <!-- 预期采样信息字段 - 仅在待采样状态显示，为现场人员提供参考 -->
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedSampleQuantity" label="预期样品量(mL)" width="120" align="center">
              <template #default="{ row }">
                <span class="expected-info">{{ row.expectedSampleQuantity || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedSampleNature" label="预期样品性质" width="120">
              <template #default="{ row }">
                <el-tag size="small" type="info">{{ row.expectedSampleNature || row.sampleNature || '-' }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedAppearance" label="预期样品外观" width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="expected-info">{{ row.expectedAppearance || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="expectedSupernatant" label="预期上清液情况" width="140" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="expected-info">{{ row.expectedSupernatant || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column v-if="taskStatusFilter === 'pending'" prop="samplingInstructions" label="采样说明" width="180" show-overflow-tooltip>
              <template #default="{ row }">
                <span class="expected-info">{{ row.samplingInstructions || '-' }}</span>
              </template>
            </el-table-column>
            <!-- 采样地点和现场情况 - 仅在采样中和已完成状态显示（通过确认采样按钮填写） -->
            <el-table-column v-if="['processing', 'completed', 'abnormal'].includes(taskStatusFilter)" prop="samplingLocation" label="采样地点" width="160" />
            <el-table-column v-if="['processing', 'completed', 'abnormal'].includes(taskStatusFilter)" prop="samplingCondition" label="现场采样情况" width="220" show-overflow-tooltip />

            <!-- 实际采样时间 - 仅在采样中和已完成状态显示 -->
            <el-table-column v-if="['processing', 'completed', 'abnormal'].includes(taskStatusFilter)" prop="actualSamplingTime" label="实际采样时间" width="160" />

            <!-- 样品外观 - 仅在已完成状态显示（通过完成采样按钮填写） -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="actualSampleAppearance" label="实际样品外观" width="160">
              <template #default="{ row }">
                <span>{{ row.actualSampleAppearance || '待填写' }}</span>
                <el-tooltip v-if="!row.actualSampleAppearance" content="需要通过完成采样按钮填写" placement="top">
                  <el-icon class="ml-1 text-warning"><Warning /></el-icon>
                </el-tooltip>
              </template>
            </el-table-column>

            <!-- 实际样品数量 - 仅在已完成状态显示 -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="actualSampleQuantity" label="实际数量(mL)" width="120" align="center">
              <template #default="{ row }">
                <span>{{ row.actualSampleQuantity || '待填写' }}</span>
                <el-tooltip v-if="!row.actualSampleQuantity" content="需要通过完成采样按钮填写" placement="top">
                  <el-icon class="ml-1 text-warning"><Warning /></el-icon>
                </el-tooltip>
              </template>
            </el-table-column>

            <!-- 样品状态 - 仅在已完成状态显示 -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="sampleStatus" label="样品状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getSampleStatusType(row.sampleStatus)" size="small">
                  {{ row.sampleStatus || '待确定' }}
                </el-tag>
              </template>
            </el-table-column>

            <!-- 完成时间 - 仅在已完成状态显示 -->
            <el-table-column v-if="['completed', 'abnormal'].includes(taskStatusFilter)" prop="completeTime" label="完成时间" width="160" />
            <el-table-column prop="status" label="任务状态" width="100">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusColor(row.status)" size="small">
                  {{ getTaskStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="320" fixed="right">
              <template #default="{ row }">
                <!-- 状态变更操作（包含信息填写） -->
                <el-button v-if="row.status === 'pending'" link type="primary" @click="handleConfirmSampling(row)">确认采样</el-button>
                <el-button v-if="row.status === 'processing'" link type="success" @click="handleCompleteSampling(row)">完成采样</el-button>
                <el-button v-if="row.status === 'processing'" link type="danger" @click="handleAbnormal(row)">记录异常</el-button>

                <!-- 单据操作 -->
                <el-button v-if="['completed', 'abnormal'].includes(row.status)" link type="warning" @click="handleCreateInspectionForm(row)">生成送检单</el-button>

                <el-divider direction="vertical" />
                <!-- 详情按钮 -->
                <el-button link type="success" @click="handleDetail('tasks', row)">详情</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.tasks.current"
              v-model:page-size="pagination.tasks.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.tasks.total"
              @size-change="handleSizeChange('tasks')"
              @current-change="handleCurrentChange('tasks')"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 送检记录管理 -->
      <el-tab-pane label="送检记录管理" name="submissionRecords">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.submissionRecords" class="search-form">
              <el-form-item label="送检单号">
                <el-input v-model="searchForm.submissionRecords.submissionCode" placeholder="请输入送检单号" clearable />
              </el-form-item>
              <el-form-item label="样品编号">
                <el-input v-model="searchForm.submissionRecords.sampleCode" placeholder="请输入样品编号" clearable />
              </el-form-item>
              <el-form-item label="送检状态">
                <el-select v-model="searchForm.submissionRecords.status" placeholder="请选择送检状态" clearable>
                  <el-option label="待送检" value="pending" />
                  <el-option label="已送检" value="submitted" />
                  <el-option label="已接收" value="received" />
                </el-select>
              </el-form-item>
              <el-form-item label="送检日期">
                <el-date-picker
                  v-model="searchForm.submissionRecords.submissionDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  format="YYYY-MM-DD"
                  value-format="YYYY-MM-DD"
                />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('submissionRecords')">
                  <Icon icon="ep:search" />查询
                </el-button>
                <el-button @click="handleReset('submissionRecords')">
                  <Icon icon="ep:refresh" />重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>

          <!-- 操作按钮 -->
          <div class="mb-4">
            <el-button type="success" @click="handleBatchSubmit" :disabled="selectedSubmissionRecords.length === 0">
              <Icon icon="ep:upload" />批量送检 ({{ selectedSubmissionRecords.length }})
            </el-button>
            <el-button type="info" @click="handleExportSubmissionRecords" :disabled="submissionRecords.length === 0">
              <Icon icon="ep:download" />导出Excel
            </el-button>
            <el-button type="warning" @click="handleExportSubmissionPDF" :disabled="submissionRecords.length === 0">
              <Icon icon="ep:document" />导出PDF
            </el-button>
          </div>

          <!-- 送检记录表格 -->
          <el-table
            :data="submissionRecords"
            v-loading="loading.submissionRecords"
            @selection-change="handleSubmissionRecordSelectionChange"
            stripe
            border
            style="width: 100%"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="submissionCode" label="送检单号" width="160" />
            <el-table-column prop="sampleCode" label="样品编号" width="160" />
            <el-table-column prop="samplingPoint" label="采样点" width="120" />
            <el-table-column prop="testItems" label="检测项目" min-width="150" show-overflow-tooltip />
            <el-table-column prop="sampleType" label="样品类型" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getSampleTypeTagType(row.sampleType)">
                  {{ getSampleTypeText(row.sampleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="volume" label="样品体积(mL)" width="120" align="center" />
            <el-table-column prop="submissionDate" label="送检日期" width="120" />
            <el-table-column prop="submissionLab" label="送检实验室" width="120" />
            <el-table-column prop="submitterName" label="送检人" width="100" />
            <el-table-column prop="status" label="送检状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getSubmissionStatusTagType(row.status)">
                  {{ getSubmissionStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="180" fixed="right">
              <template #default="{ row }">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleSubmitRecord(row)"
                  :disabled="row.status !== 'pending'"
                >
                  送检
                </el-button>
                <el-button
                  link
                  type="info"
                  size="small"
                  @click="handleViewSubmissionDetail(row)"
                >
                  查看详情
                </el-button>
                <el-button
                  link
                  type="success"
                  size="small"
                  @click="handleDownloadSubmissionForm(row)"
                >
                  下载
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.submissionRecords.current"
              v-model:page-size="pagination.submissionRecords.pageSize"
              :total="pagination.submissionRecords.total"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange('submissionRecords')"
              @current-change="handleCurrentChange('submissionRecords')"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 样品管理 -->
      <el-tab-pane label="样品管理" name="sampleManagement">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.sampleManagement" class="search-form">
              <el-form-item label="样品编号">
                <el-input v-model="searchForm.sampleManagement.sampleCode" placeholder="请输入样品编号" clearable />
              </el-form-item>
              <el-form-item label="样品类型">
                <el-select v-model="searchForm.sampleManagement.sampleType" placeholder="请选择样品类型" clearable style="min-width: 10rem;">
                  <el-option label="进水样品" value="inlet" />
                  <el-option label="出水样品" value="outlet" />
                  <el-option label="污泥样品" value="sludge" />
                  <el-option label="中间过程样品" value="process" />
                </el-select>
              </el-form-item>
              <el-form-item label="样品状态">
                <el-select v-model="searchForm.sampleManagement.status" placeholder="请选择状态" clearable style="min-width: 10rem;">
                  <el-option label="已入库" value="stored" />
                  <el-option label="已送检" value="submitted" />
                  <el-option label="检测中" value="testing" />
                  <el-option label="已完成" value="completed" />
                  <el-option label="已销毁" value="destroyed" />
                </el-select>
              </el-form-item>
              <el-form-item label="采样日期">
                <el-date-picker
                  v-model="searchForm.sampleManagement.samplingDate"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item label="存储位置">
                <el-input v-model="searchForm.sampleManagement.storageLocation" placeholder="请输入存储位置" clearable />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('sampleManagement')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('sampleManagement')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>

          <!-- 表格操作栏 -->
          <div class="mb-4">

            <el-button type="success" @click="handleBatchStorage" :disabled="selectedSampleManagement.length === 0">
              <el-icon><Box /></el-icon>批量入库 ({{ selectedSampleManagement.length }})
            </el-button>
            <el-button type="warning" @click="handleBatchTransfer" :disabled="selectedSampleManagement.length === 0">
              <el-icon><Switch /></el-icon>批量转移 ({{ selectedSampleManagement.length }})
            </el-button>
            <el-button type="danger" @click="handleBatchDestroy" :disabled="selectedSampleManagement.length === 0">
              <el-icon><Delete /></el-icon>批量销毁 ({{ selectedSampleManagement.length }})
            </el-button>            
          </div>

          <!-- 统计信息 -->
          <div class="mb-4 status-summary">
            <div class="status-item">
              <span class="status-label">总样品数</span>
              <span class="status-count total">{{ getSampleTotalCount() }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">已入库</span>
              <span class="status-count stored">{{ getSampleStatusCount('stored') }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">检验中</span>
              <span class="status-count testing">{{ getSampleStatusCount('testing') }}</span>
            </div>
            <div class="status-item">
              <span class="status-label">已完成</span>
              <span class="status-count completed">{{ getSampleStatusCount('completed') }}</span>
            </div>

            <div class="status-item">
              <span class="status-label">已销毁</span>
              <span class="status-count destroyed">{{ getSampleStatusCount('destroyed') }}</span>
            </div>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading.sampleManagement"
            :data="tableData.sampleManagement"
            border
            style="width: 100%"
            @selection-change="handleSampleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="sampleCode" label="样品编号" min-width="12rem" />
            <el-table-column prop="sampleType" label="样品类型" min-width="10rem">
              <template #default="{ row }">
                <el-tag :type="getSampleTypeColor(row.sampleType)">
                  {{ getSampleTypeText(row.sampleType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="samplingDate" label="采样日期" min-width="12rem" />
            <el-table-column prop="samplingPersonName" label="采样人员" min-width="10rem" />
            <el-table-column prop="volume" label="样品体积(mL)" min-width="10rem" align="center" />
            <el-table-column prop="appearance" label="外观" min-width="12rem" />
            <el-table-column prop="testItemName" label="检测项目" min-width="12rem" show-overflow-tooltip />
            <el-table-column prop="preservationMethod" label="保存方法" min-width="10rem" />
            <el-table-column prop="storageLocation" label="存储位置" min-width="12rem" />
            <el-table-column prop="expiryDate" label="有效期至" min-width="12rem" />
            <el-table-column prop="status" label="样品状态" min-width="10rem">
              <template #default="{ row }">
                <el-tag :type="getSampleStatusTagType(row.status)">
                  {{ getSampleStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="storageTemperature" label="存储温度(°C)" min-width="10rem" />
            <el-table-column label="操作" min-width="25rem" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEdit('sampleManagement', row)">编辑</el-button>
                <el-button link type="success" @click="handleDetail('sampleManagement', row)">详情</el-button>
                <el-button v-if="row.status === 'collected'" link type="warning" @click="handleStorage(row)">入库</el-button>
                <el-button link type="danger" @click="handleDestroy(row)">销毁</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-tab-pane>


    </el-tabs>

    <!-- 工作流步骤对话框 -->
    <SamplingWorkflowDialog
      ref="workflowDialogRef"
      @success="handleWorkflowSuccess"
    />

    <!-- 送检单对话框 -->
    <SubmissionReportDialog
      ref="submissionReportDialogRef"
      @success="onSubmissionReportSuccess"
    />
  </ContentWrap>

  <!-- 采样任务对话框 -->
  <SamplingTaskDialog
    ref="samplingTaskDialogRef"
    @success="refreshTable('tasks')"
    @confirm="handleTaskDialogConfirm"
  />

  <!-- 状态变更对话框 -->
  <ConfirmSamplingDialog ref="confirmSamplingDialogRef" @success="handleConfirmSamplingSuccess" />
  <CompleteSamplingDialog ref="completeSamplingDialogRef" @success="handleCompleteSamplingSuccess" />
  <RecordAbnormalDialog ref="recordAbnormalDialogRef" @success="handleAbnormalSuccess" />
  <CreateInspectionFormDialog ref="createInspectionFormDialogRef" @success="handleInspectionFormSuccess" />
  <SubmissionDialog ref="submissionDialogRef" @success="handleSubmissionSuccess" />

  <!-- 样品管理弹窗 -->
  <SampleManagementDialog
    ref="sampleManagementDialogRef"
    @confirm="handleSampleDialogConfirm"
  />

  <!-- 采样任务详情弹窗 -->
  <SamplingTaskDetailDialog
    ref="samplingTaskDetailDialogRef"
  />

  <!-- 样品详情弹窗 -->
  <SampleDetailDialog
    ref="sampleDetailDialogRef"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, onUnmounted, watch, h } from 'vue'
// 不需要单独导入图标，因为已在模板中使用
import { ElMessage, ElMessageBox } from 'element-plus'
import { exportSamplingForm, exportInspectionForm } from '@/utils/csvExport'

// 导入API
import {
  AssaySamplingApi,
  type AssaySamplingExecutionVO,
  type AssaySampleManagementVO,
  type AssaySubmissionRecordVO,
  type SamplingExecutionConfirmReqVO,
  type SamplingExecutionCompleteReqVO,
  type SamplingExecutionAbnormalReqVO,
  type GenerateInspectionFormReqVO
} from '@/api/assay/sampling'

// 导入组件
import SamplingTaskDialog from './components/SamplingTaskDialog.vue'
import SamplingTaskDetailDialog from './components/SamplingTaskDetailDialog.vue'
import SampleManagementDialog from './components/SampleManagementDialog.vue'
import SampleDetailDialog from './components/SampleDetailDialog.vue'
import SamplingWorkflowDialog from './components/SamplingWorkflowDialog.vue'
import SubmissionReportDialog from './components/SubmissionReportDialog.vue'
// 状态变更对话框
import ConfirmSamplingDialog from './components/ConfirmSamplingDialog.vue'
import CompleteSamplingDialog from './components/CompleteSamplingDialog.vue'
import RecordAbnormalDialog from './components/RecordAbnormalDialog.vue'
import CreateInspectionFormDialog from './components/CreateInspectionFormDialog.vue'
import SubmissionDialog from './components/SubmissionDialog.vue'

defineOptions({ name: 'AssaySampling' })

// 当前激活的标签页
const activeTab = ref<'tasks' | 'submissionRecords' | 'sampleManagement'>('tasks')

// 任务状态筛选器
const taskStatusFilter = ref<'pending' | 'processing' | 'completed' | 'abnormal'>('pending')

// 加载状态
const loading = reactive({
  tasks: false,
  sampleManagement: false,
  submissionRecords: false
})

// 搜索表单
const searchForm = reactive({
  tasks: {
    name: '',
    samplingPoint: '',
    executor: '',
    planDate: [] as string[]
  },
  sampleManagement: {
    sampleCode: '',
    sampleType: '',
    status: '',
    samplingDate: [] as string[],
    storageLocation: ''
  },
  submissionRecords: {
    submissionCode: '',
    sampleCode: '',
    status: '',
    submissionDate: [] as string[],
    submissionLab: '',
    submitterName: ''
  }
})

// 分页配置
const pagination = reactive({
  tasks: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  sampleManagement: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  submissionRecords: {
    current: 1,
    pageSize: 10,
    total: 0
  }
})

// 使用API中定义的类型
type SamplingTask = AssaySamplingExecutionVO

type SubmissionRecord = AssaySubmissionRecordVO
type SampleManagement = AssaySampleManagementVO



// 表格数据
const tableData = reactive({
  tasks: [] as SamplingTask[],
  sampleManagement: [] as SampleManagement[],
  submissionRecords: [] as SubmissionRecord[]
})

// 选择的行数据
const selectedTasks = ref<SamplingTask[]>([])
const selectedSampleManagement = ref<SampleManagement[]>([])
const selectedSubmissionRecords = ref<SubmissionRecord[]>([])

// 送检记录数据（从已完成的采样任务中获取）
const submissionRecords = computed(() => tableData.submissionRecords)

// 根据状态筛选的任务数据
const filteredTaskData = computed(() => {
  return tableData.tasks.filter(task => task.status === taskStatusFilter.value)
})

// 对话框引用
const samplingTaskDialogRef = ref<InstanceType<typeof SamplingTaskDialog> | null>(null)
const samplingTaskDetailDialogRef = ref(null)
const sampleManagementDialogRef = ref<InstanceType<typeof SampleManagementDialog> | null>(null)
const sampleDetailDialogRef = ref<InstanceType<typeof SampleDetailDialog> | null>(null)
const workflowDialogRef = ref<InstanceType<typeof SamplingWorkflowDialog> | null>(null)
const submissionReportDialogRef = ref(null)
// 状态变更对话框引用
const confirmSamplingDialogRef = ref<InstanceType<typeof ConfirmSamplingDialog> | null>(null)
const completeSamplingDialogRef = ref<InstanceType<typeof CompleteSamplingDialog> | null>(null)
const recordAbnormalDialogRef = ref<InstanceType<typeof RecordAbnormalDialog> | null>(null)
const createInspectionFormDialogRef = ref<InstanceType<typeof CreateInspectionFormDialog> | null>(null)
const submissionDialogRef = ref<InstanceType<typeof SubmissionDialog> | null>(null)

// 生命周期钩子
onMounted(() => {
  fetchTableData('tasks')
  fetchTableData('sampleManagement')
  fetchTableData('submissionRecords')
  // 监听来自检测数据管理模块的状态更新
  window.addEventListener('testStatusUpdated', handleTestStatusUpdated)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('testStatusUpdated', handleTestStatusUpdated)
})

// 处理检测状态更新
const handleTestStatusUpdated = (event: CustomEvent) => {
  const { sampleCode, status } = event.detail

  // 查找对应的样品并更新状态
  const sample = tableData.sampleManagement.find(s => s.sampleCode === sampleCode)
  if (sample) {
    // 根据检测状态更新样品状态
    if (status === 'reviewing' || status === 'entered') {
      sample.status = 'testing'
    } else if (status === 'approved' || status === 'completed') {
      sample.status = 'completed'
    }

    console.log(`样品 ${sampleCode} 状态已更新为: ${sample.status}`)
  }
}

// 监听标签页切换
watch(activeTab, (newVal: 'tasks' | 'sampleManagement') => {
  fetchTableData(newVal)
})

// 状态筛选变化处理
const handleStatusFilterChange = (status: string) => {
  taskStatusFilter.value = status as 'pending' | 'processing' | 'completed' | 'abnormal'
  pagination.tasks.current = 1
}

// 获取各状态任务数量
const getTaskCountByStatus = (status: string) => {
  return tableData.tasks.filter(task => task.status === status).length
}

// 获取任务状态颜色
const getTaskStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'abnormal': 'danger'
  }
  return colorMap[status] || 'info'
}

// 获取任务状态文本
const getTaskStatusText = (status: string) => {
  const textMap = {
    'pending': '待采样',
    'processing': '采样中',
    'completed': '已完成',
    'abnormal': '异常终止'
  }
  return textMap[status] || '未知'
}

// 获取样品状态颜色
const getSampleStatusColor = (status: string) => {
  const colorMap = {
    '待采样': 'info',
    '正常': 'success',
    '采样中': 'warning',
    '异常': 'danger'
  }
  return colorMap[status] || 'info'
}

// 获取样品状态文本
const getSampleStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'collected': '已采集',
    'stored': '已入库',
    'submitted': '已送检',
    'testing': '检验中',
    'completed': '已完成',
    'exception': '异常处理中',
    'destroyed': '已销毁'
  }
  return textMap[status] || status
}

// 获取表格数据
const fetchTableData = async (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  loading[type] = true
  try {
    if (type === 'tasks') {
      // 调用采样执行管理API - 修正参数以匹配后端接口
      const params = {
        pageNo: pagination.tasks.current,
        pageSize: pagination.tasks.pageSize,
        status: taskStatusFilter.value,
        startDate: searchForm.tasks.planDate[0],
        endDate: searchForm.tasks.planDate[1]
        // 注意：后端接口不支持name、samplingPoint、executor参数
        // 这些筛选需要在前端进行或者后端需要添加支持
      }
      const res = await AssaySamplingApi.getSamplingExecutionPage(params)
      if (res.data) {
        tableData.tasks = res.data.list || []
        pagination.tasks.total = res.data.total || 0
      }
    } else if (type === 'sampleManagement') {
      // 调用样品管理API - 修正参数以匹配后端接口
      const params = {
        pageNo: pagination.sampleManagement.current,
        pageSize: pagination.sampleManagement.pageSize,
        sampleCode: searchForm.sampleManagement.sampleCode,
        sampleType: searchForm.sampleManagement.sampleType,
        status: searchForm.sampleManagement.status,
        startDate: searchForm.sampleManagement.samplingDate[0],
        endDate: searchForm.sampleManagement.samplingDate[1]
        // 注意：后端接口不支持samplingPoint参数，使用executionId和samplingPersonId
      }
      const res = await AssaySamplingApi.getSampleManagementPage(params)
      if (res.data) {
        tableData.sampleManagement = res.data.list || []
        pagination.sampleManagement.total = res.data.total || 0
      }
    } else if (type === 'submissionRecords') {
      // 调用送检记录API - 修正参数以匹配后端接口
      const params = {
        pageNo: pagination.submissionRecords.current,
        pageSize: pagination.submissionRecords.pageSize,
        recordCode: searchForm.submissionRecords.submissionCode, // 后端字段名为recordCode
        status: searchForm.submissionRecords.status,
        startDate: searchForm.submissionRecords.submissionDate[0],
        endDate: searchForm.submissionRecords.submissionDate[1]
        // 注意：后端接口不支持submissionLab、submitterName参数，使用submissionPersonId
      }
      const res = await AssaySamplingApi.getSubmissionRecordPage(params)
      if (res.data) {
        tableData.submissionRecords = res.data.list || []
        pagination.submissionRecords.total = res.data.total || 0
      }
    }
  } catch (error) {
    console.error(`获取${type}数据失败:`, error)
    ElMessage.error(`获取数据失败，请稍后重试`)
  } finally {
    loading[type] = false
  }
}











// 刷新表格



// 刷新表格
const refreshTable = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  fetchTableData(type)
}

// 确认采样（使用专门的对话框）
const handleConfirmSampling = (row: SamplingTask) => {
  confirmSamplingDialogRef.value?.open(row)
}

// 确认采样成功回调
const handleConfirmSamplingSuccess = async (data: SamplingExecutionConfirmReqVO) => {
  try {
    const res = await AssaySamplingApi.confirmSampling(data)
    if (res.code === 0) {
      ElMessage.success('确认采样成功')
      refreshTable('tasks')
    } else {
      ElMessage.error(res.msg || '确认采样失败')
    }
  } catch (error) {
    console.error('确认采样失败:', error)
    ElMessage.error('确认采样失败，请稍后重试')
  }
}

// 完成采样（使用专门的对话框）
const handleCompleteSampling = (row: SamplingTask) => {
  completeSamplingDialogRef.value?.open(row)
}

// 完成采样成功回调
const handleCompleteSamplingSuccess = async (data: SamplingExecutionCompleteReqVO) => {
  try {
    const res = await AssaySamplingApi.completeSampling(data)
    if (res.code === 0) {
      ElMessage.success('完成采样成功')
      refreshTable('tasks')
    } else {
      ElMessage.error(res.msg || '完成采样失败')
    }
  } catch (error) {
    console.error('完成采样失败:', error)
    ElMessage.error('完成采样失败，请稍后重试')
  }
}

// 记录异常（使用专门的对话框）
const handleAbnormal = (row: SamplingTask) => {
  recordAbnormalDialogRef.value?.open(row)
}

// 记录异常成功回调
const handleAbnormalSuccess = async (data: SamplingExecutionAbnormalReqVO) => {
  try {
    const res = await AssaySamplingApi.recordAbnormal(data)
    if (res.code === 0) {
      ElMessage.success('记录异常成功')
      refreshTable('tasks')
    } else {
      ElMessage.error(res.msg || '记录异常失败')
    }
  } catch (error) {
    console.error('记录异常失败:', error)
    ElMessage.error('记录异常失败，请稍后重试')
  }
}



// 生成送检单（打开弹窗填写样品管理信息）
const handleCreateInspectionForm = (row: SamplingTask) => {
  createInspectionFormDialogRef.value?.open(row)
}

// 送检单生成成功回调
const handleInspectionFormSuccess = async (data: GenerateInspectionFormReqVO) => {
  try {
    const res = await AssaySamplingApi.generateInspectionForm(data)
    if (res.code === 0) {
      ElMessage.success('送检单生成成功，送检记录已创建')
      refreshTable('tasks')
      refreshTable('submissionRecords')
    } else {
      ElMessage.error(res.msg || '送检单生成失败')
    }
  } catch (error) {
    console.error('送检单生成失败:', error)
    ElMessage.error('送检单生成失败，请稍后重试')
  }
}

// 导出送检单（保留原有功能）
const handleExportReport = (row: SamplingTask) => {
  ElMessage.success(`正在导出 ${row.name} 的送检单...`)
  // 这里应该调用导出API
  setTimeout(() => {
    ElMessage.success(`送检单已导出: ${row.name}_送检单.csv`)
  }, 1000)
}

// 批量确认采样
const handleBatchConfirm = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要确认的采样任务')
    return
  }

  ElMessageBox.confirm(`确认开始选中的 ${selectedTasks.value.length} 个采样任务吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    selectedTasks.value.forEach(task => {
      task.status = 'processing'
      task.samplingLocation = task.samplingLocation || `${task.samplingPoint}默认区域`
      task.actualSamplingTime = new Date().toLocaleString()
    })
    ElMessage.success(`已确认 ${selectedTasks.value.length} 个采样任务`)
    refreshTable('tasks')
    selectedTasks.value = []
  }).catch(() => {
    // 用户取消操作
  })
}

// 批量完成采样
const handleBatchComplete = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要完成的采样任务')
    return
  }

  const processingTasks = selectedTasks.value.filter(task => task.status === 'processing')
  if (processingTasks.length === 0) {
    ElMessage.warning('选中的任务中没有正在采样中的任务')
    return
  }

  ElMessageBox.confirm(`确认完成选中的 ${processingTasks.length} 个采样任务吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'success'
  }).then(() => {
    processingTasks.forEach(task => {
      task.status = 'completed'
    })
    ElMessage.success(`已完成 ${processingTasks.length} 个采样任务`)
    refreshTable('tasks')
    selectedTasks.value = []
  }).catch(() => {
    // 用户取消操作
  })
}

// 批量生成送检单
const handleBatchExportReport = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要生成送检单的任务')
    return
  }

  const completedTasks = selectedTasks.value.filter(task => ['completed', 'abnormal'].includes(task.status))
  if (completedTasks.length === 0) {
    ElMessage.warning('选中的任务中没有已完成或异常终止的任务')
    return
  }

  ElMessage.success(`正在生成 ${completedTasks.length} 个送检单...`)
  // 这里应该调用批量生成API
  setTimeout(() => {
    ElMessage.success(`已生成 ${completedTasks.length} 个送检单`)
  }, 1000)
}









// 送检单提交成功回调
const onSubmissionReportSuccess = (reportData: any) => {
  ElMessage.success('送检单已提交')
  refreshTable('tasks')
}

// ==================== 采样计划相关方法 ====================



// 采样任务选择变化
const handleTaskSelectionChange = (selection: SamplingTask[]) => {
  selectedTasks.value = selection
}









// 打印任务单




// 搜索
const handleSearch = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  pagination[type].current = 1
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  if (type === 'tasks') {
    searchForm.tasks = {
      name: '',
      samplingPoint: '',
      executor: '',
      planDate: []
    }
  } else if (type === 'sampleManagement') {
    searchForm.sampleManagement = {
      sampleCode: '',
      sampleType: '',
      status: '',
      samplingDate: [],
      storageLocation: ''
    }
  } else if (type === 'submissionRecords') {
    searchForm.submissionRecords = {
      submissionCode: '',
      sampleCode: '',
      status: '',
      submissionDate: [],
      submissionLab: '',
      submitterName: ''
    }
  }
  fetchTableData(type)
}

// 重置方法的别名（为了兼容模板中的调用）
const handleReset = resetSearch

// 新增/编辑
const handleAdd = (type: 'tasks' | 'sampleManagement') => {
  if (type === 'tasks') {
    samplingTaskDialogRef.value?.open('create')
  } else if (type === 'sampleManagement') {
    sampleManagementDialogRef.value?.open('create')
  }
}

const handleEdit = (type: 'tasks' | 'sampleManagement', row: any) => {
  if (type === 'tasks') {
    samplingTaskDialogRef.value?.open('update', row)
  } else if (type === 'sampleManagement') {
    sampleManagementDialogRef.value?.open('update', row)
  }
}

const handleDetail = (type: 'tasks' | 'sampleManagement', row: any) => {
  if (type === 'sampleManagement') {
    // 打开样品详情弹窗
    sampleDetailDialogRef.value?.open(row)
  } else if (type === 'tasks') {
    // 打开采样任务详情弹窗
    samplingTaskDetailDialogRef.value?.open(row)
  }
}

// 采样任务弹窗确认处理
const handleTaskDialogConfirm = (data: any) => {
  if (data.id) {
    // 更新现有任务
    const index = tableData.tasks.findIndex(item => item.id === data.id)
    if (index !== -1) {
      tableData.tasks[index] = { ...tableData.tasks[index], ...data }
    }
  } else {
    // 新增任务
    const newTask = {
      ...data,
      id: Date.now(), // 临时ID
      workflowStep: 1,
      conditionMet: false
    }
    tableData.tasks.unshift(newTask)
  }

  // 刷新表格
  fetchTableData('tasks')
}

// 样品管理弹窗确认处理
const handleSampleDialogConfirm = async (data: any) => {
  try {
    if (data.id) {
      // 更新现有样品
      const res = await AssaySamplingApi.updateSampleManagement(data)
      if (res.code === 0) {
        ElMessage.success('样品信息更新成功')
        refreshTable('sampleManagement')
      } else {
        ElMessage.error(res.msg || '样品信息更新失败')
      }
    } else {
      // 新增样品
      const res = await AssaySamplingApi.createSampleManagement(data)
      if (res.code === 0) {
        ElMessage.success('样品信息添加成功')
        refreshTable('sampleManagement')
      } else {
        ElMessage.error(res.msg || '样品信息添加失败')
      }
    }
  } catch (error) {
    console.error('样品管理操作失败:', error)
    ElMessage.error('操作失败，请稍后重试')
  }
}

// 删除样品管理记录
const handleDeleteSample = async (row: SampleManagement) => {
  try {
    await ElMessageBox.confirm('确认删除该样品记录吗?', '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const res = await AssaySamplingApi.deleteSampleManagement(row.id!)
    if (res.code === 0) {
      ElMessage.success('删除成功')
      refreshTable('sampleManagement')
    } else {
      ElMessage.error(res.msg || '删除失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除样品失败:', error)
      ElMessage.error('删除失败，请稍后重试')
    }
  }
}

// 分页大小变化
const handleSizeChange = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  fetchTableData(type)
}

// 页码变化
const handleCurrentChange = (type: 'tasks' | 'sampleManagement' | 'submissionRecords') => {
  fetchTableData(type)
}



// 工作流步骤成功回调
const handleWorkflowSuccess = (result: any) => {
  // 更新任务状态
  const task = tableData.tasks.find(t => t.id === result.taskId)
  if (task) {
    if (result.type === 'complete') {
      task.status = 'completed'
    } else if (result.type === 'abnormal') {
      task.status = 'abnormal'
      task.samplingCondition = result.abnormalReason || '采样条件异常'
    }
    refreshTable('tasks')
  }
}




// 获取工作流程当前步骤
const getWorkflowActiveStep = (row: SamplingTask): number => {
  const status = row.status

  if (status === 'pending') return 0
  if (status === 'abnormal') return 0
  if (status === 'completed') return 5
  return 0
}

// 获取步骤描述
const getStepDescription = (row: SamplingTask, step: number): string => {
  const currentStep = getWorkflowActiveStep(row)
  const status = row.status

  if (step > currentStep) {
    return '待执行'
  } else if (step === currentStep && status === 'processing') {
    return '进行中'
  } else if (step <= currentStep) {
    // 根据步骤返回具体的完成信息
    switch (step) {
      case 1:
        return row.executor ? `${row.executor} 已接收` : '已完成'
      case 2:
        return '已到达现场'
      case 3:
        return '条件符合'
      case 4:
        return status === 'abnormal' ? '异常终止' : '采样完成'
      case 5:
        return '已送检'
      default:
        return '已完成'
    }
  }
  return ''
}

// ==================== 样品管理相关方法 ====================

// 样品选择变化
const handleSampleSelectionChange = (selection: SampleManagement[]) => {
  selectedSampleManagement.value = selection
}

// 获取样品类型颜色
const getSampleTypeColor = (type: string) => {
  const colorMap = {
    'inlet': 'primary',
    'outlet': 'success',
    'sludge': 'warning',
    'process': 'info'
  }
  return colorMap[type] || 'info'
}

// 获取样品类型文本（统一处理所有样品类型）
const getSampleTypeText = (type: string) => {
  const textMap = {
    // 样品管理模块的类型
    'inlet': '进水样品',
    'outlet': '出水样品',
    'sludge': '污泥样品',
    'process': '中间过程样品',
    // 送检记录模块的类型
    'water': '水样',
    'gas': '气体'
  }
  return textMap[type] || type
}

// 获取样品总数量
const getSampleTotalCount = () => {
  return tableData.sampleManagement.length
}

// 获取样品状态数量
const getSampleStatusCount = (status: string) => {
  return tableData.sampleManagement.filter(item => item.status === status).length
}

// 获取样品状态标签类型
const getSampleStatusTagType = (status: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    'stored': 'primary',
    'testing': 'warning',
    'completed': 'success',
    'exception': 'danger',
    'destroyed': 'info'
  }
  return typeMap[status] || 'info'
}



// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 样品入库
const handleStorage = (row: SampleManagement) => {
  ElMessageBox.confirm(`确认将样品 "${row.sampleCode}" 入库吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    row.status = 'stored'
    ElMessage.success(`样品 ${row.sampleCode} 已入库`)
    fetchTableData('sampleManagement')
  }).catch(() => {
    // 用户取消入库
  })
}

// 样品转移
const handleTransfer = (row: SampleManagement) => {
  ElMessage.info(`转移样品 ${row.sampleCode} 功能开发中...`)
}

// 样品销毁
const handleDestroy = (row: SampleManagement) => {
  ElMessageBox.confirm(`确认销毁样品 "${row.sampleCode}" 吗? 此操作不可恢复!`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    row.status = 'destroyed'
    ElMessage.success(`样品 ${row.sampleCode} 已销毁`)
    fetchTableData('sampleManagement')
  }).catch(() => {
    // 用户取消销毁
  })
}



// 批量入库
const handleBatchStorage = () => {
  if (selectedSampleManagement.value.length === 0) {
    ElMessage.warning('请先选择要入库的样品')
    return
  }

  const collectedSamples = selectedSampleManagement.value.filter(sample => sample.status === 'stored')
  if (collectedSamples.length === 0) {
    ElMessage.warning('选中的样品中没有可入库的样品')
    return
  }

  ElMessageBox.confirm(`确认将选中的 ${collectedSamples.length} 个样品入库吗?`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    collectedSamples.forEach(sample => {
      sample.status = 'stored'
    })
    ElMessage.success(`已入库 ${collectedSamples.length} 个样品`)
    fetchTableData('sampleManagement')
    selectedSampleManagement.value = []
  }).catch(() => {
    // 用户取消入库
  })
}

// 批量转移
const handleBatchTransfer = () => {
  ElMessage.info('批量转移功能开发中...')
}

// 批量销毁
const handleBatchDestroy = () => {
  if (selectedSampleManagement.value.length === 0) {
    ElMessage.warning('请先选择要销毁的样品')
    return
  }

  ElMessageBox.confirm(`确认销毁选中的 ${selectedSampleManagement.value.length} 个样品吗? 此操作不可恢复!`, '警告', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'error'
  }).then(() => {
    selectedSampleManagement.value.forEach(sample => {
      sample.status = 'destroyed'
    })
    ElMessage.success(`已销毁 ${selectedSampleManagement.value.length} 个样品`)
    fetchTableData('sampleManagement')
    selectedSampleManagement.value = []
  }).catch(() => {
    // 用户取消销毁
  })
}



// 获取样品状态类型
const getSampleStatusType = (status: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    '正常': 'success',
    '异常': 'danger',
    '需复检': 'warning'
  }
  return typeMap[status] || 'info'
}

// ==================== 送检记录管理相关方法 ====================

// 送检记录选择变更
const handleSubmissionRecordSelectionChange = (selection: SubmissionRecord[]) => {
  selectedSubmissionRecords.value = selection
}

// 获取样品类型标签类型
const getSampleTypeTagType = (type: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    'water': 'primary',
    'sludge': 'warning',
    'gas': 'success'
  }
  return typeMap[type] || 'info'
}



// 获取送检状态标签类型
const getSubmissionStatusTagType = (status: string): 'success' | 'info' | 'warning' | 'danger' | 'primary' => {
  const typeMap: Record<string, 'success' | 'info' | 'warning' | 'danger' | 'primary'> = {
    'pending': 'warning',
    'submitted': 'primary',
    'received': 'success'
  }
  return typeMap[status] || 'info'
}

// 获取送检状态文本
const getSubmissionStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待送检',
    'submitted': '已送检',
    'received': '已接收'
  }
  return textMap[status] || status
}

// 送检记录
const handleSubmitRecord = (record: SubmissionRecord) => {
  // 打开送检弹窗，填写送检人、送检时间、送检实验室信息
  submissionDialogRef.value?.open(record)
}

// 送检成功回调
const handleSubmissionSuccess = async (data: any) => {
  try {
    const res = await AssaySamplingApi.submitRecord(data.recordId)
    if (res.data) {
      ElMessage.success('送检成功，样品状态已更新')
      refreshTable('submissionRecords')
    } else {
      ElMessage.error('送检失败')
    }
  } catch (error) {
    console.error('送检失败:', error)
    ElMessage.error('送检失败，请稍后重试')
  }
}

// 查看送检详情
const handleViewSubmissionDetail = (record: SubmissionRecord) => {
  ElMessage.info(`查看送检记录详情：${record.submissionCode}`)
  // 这里应该打开详情弹窗
}

// 下载送检单
const handleDownloadSubmissionForm = (record: SubmissionRecord) => {
  ElMessage.success(`正在下载送检单：${record.submissionCode}`)
  // 这里应该调用下载API
}

// 批量送检
const handleBatchSubmit = () => {
  if (selectedSubmissionRecords.value.length === 0) {
    ElMessage.warning('请先选择要送检的记录')
    return
  }

  ElMessageBox.confirm(
    `确认要批量送检选中的 ${selectedSubmissionRecords.value.length} 条记录吗？`,
    '批量送检确认',
    {
      confirmButtonText: '确认送检',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 批量更新状态
    selectedSubmissionRecords.value.forEach(record => {
      if (record.status === 'pending') {
        record.status = 'submitted'
        record.submitterName = '当前用户'
      }
    })

    ElMessage.success(`成功送检 ${selectedSubmissionRecords.value.length} 条记录`)
    selectedSubmissionRecords.value = []
    // 这里应该调用API批量更新
  }).catch(() => {
    ElMessage.info('已取消批量送检')
  })
}

// 导出送检记录Excel
const handleExportSubmissionRecords = () => {
  ElMessage.success('正在导出送检记录Excel...')
  // 这里应该调用导出API
}

// 导出送检记录PDF
const handleExportSubmissionPDF = () => {
  ElMessage.success('正在导出送检记录PDF...')
  // 这里应该调用导出API
}

</script>

<style lang="scss" scoped>
// 采样任务表格样式
.sampling-task-table {
  // 设置表格最小宽度以触发水平滚动
  min-width: 100%;

  // 表格容器样式
  :deep(.el-table) {
    // 计算总列宽约为: 60+120+200+180+120+120+100+160+220+160+120+100+130+110+100+170+100+120+450+380 = 2830px
    // 这个宽度会触发水平滚动
    width: 100%;
  }

  // 确保表格能够正确显示所有列
  :deep(.el-table__body-wrapper) {
    overflow-x: auto;
  }

  :deep(.el-table__header-wrapper) {
    overflow-x: hidden;
  }

  // 自定义滚动条样式
  :deep(.el-table__body-wrapper)::-webkit-scrollbar {
    height: 8px;
  }

  :deep(.el-table__body-wrapper)::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }

  :deep(.el-table__body-wrapper)::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;

    &:hover {
      background: #a8a8a8;
    }
  }

  // 确保固定列正确显示
  :deep(.el-table__fixed-right) {
    right: 0 !important;
    z-index: 3;
  }

  :deep(.el-table__fixed-left) {
    left: 0 !important;
    z-index: 3;
  }

  // 固定列阴影效果
  :deep(.el-table__fixed-right::before) {
    box-shadow: -1px 0 8px rgba(0, 0, 0, 0.1);
  }

  :deep(.el-table__fixed-left::after) {
    box-shadow: 1px 0 8px rgba(0, 0, 0, 0.1);
  }
}

// 搜索表单样式优化
.search-form {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 1.5rem;
  gap: 0.5rem;
  
  .el-form-item {
    margin-bottom: 0.75rem;
    margin-right: 1.5rem;
  }
}

// 表格容器优化
.el-card {
  margin-bottom: 1.5rem;
  
  :deep(.el-card__body) {
    padding: 1.5rem;
  }
}

// 按钮间距优化
.el-button + .el-button {
  margin-left: 0.75rem;
}

// 分页样式优化
.mt-4 {
  margin-top: 1rem;
}

.flex {
  display: flex;
}

.justify-end {
  justify-content: flex-end;
}

.mb-4 {
  margin-bottom: 1rem;
}

// 表格内按钮组样式优化
.el-table {
  :deep(.el-button--link) {
    padding: 0.25rem 0.5rem;
  }
  
  :deep(.el-divider--vertical) {
    margin: 0 0.5rem;
  }
}

// 表格标题行样式优化
:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
}

// 表格行高亮效果
:deep(.el-table tr:hover > td) {
  background-color: #ecf5ff;
}

// 标签页样式优化
:deep(.el-tabs__item) {
  font-size: 1rem;
  padding: 0 1.25rem;
}

:deep(.el-tabs__active-bar) {
  height: 0.125rem;
}

// 工作流步骤样式优化
:deep(.el-steps) {
  margin: 0.5rem 0;

  .el-step__title {
    font-size: 0.875rem;
  }

  .el-step__head {
    padding-right: 0.5rem;
  }
}

/* 工作流程样式优化 */
.workflow-container {
  padding: 8px 0;
}

.workflow-steps {
  margin-bottom: 8px;
}

.workflow-steps :deep(.el-step__title) {
  font-size: 12px;
  line-height: 1.2;
}

.workflow-steps :deep(.el-step__description) {
  font-size: 11px;
  color: #909399;
  margin-top: 2px;
}

.workflow-steps :deep(.el-step__head) {
  width: 20px;
  height: 20px;
}

.workflow-steps :deep(.el-step__icon) {
  width: 16px;
  height: 16px;
  font-size: 12px;
}

.workflow-error {
  margin-top: 8px;
  padding: 4px 8px;
  background-color: #fef0f0;
  border-radius: 4px;
  border-left: 3px solid #f56c6c;
}

.workflow-error .el-text {
  display: flex;
  align-items: center;
  gap: 4px;
}

// 样品管理状态统计样式
.status-summary {
  display: flex;
  gap: 1rem;
  padding: 1rem;
  background-color: #f8f9fa;
  border-radius: 0.5rem;

  .status-item {
    display: flex;
    flex-direction: column;
    align-items: center;

    .status-label {
      font-size: 0.875rem;
      color: #6b7280;
      margin-bottom: 0.25rem;
    }

    .status-count {
      font-size: 1.5rem;
      font-weight: bold;

      &.total { color: #3b82f6; }
      &.stored { color: #10b981; }
      &.testing { color: #f59e0b; }
      &.completed { color: #059669; }
      &.warning { color: #ef4444; }
      &.destroyed { color: #6b7280; }
    }
  }
}

/* 预期与实际值对比样式 */
.comparison-cell {
  font-size: 12px;
  line-height: 1.2;
}

.comparison-cell .expected {
  color: #909399;
  margin-bottom: 2px;
}

.comparison-cell .actual {
  color: #303133;
  font-weight: 500;
}

/* 预期信息样式 */
.expected-info {
  color: #909399;
  font-size: 13px;
  font-style: italic;
}

.comparison-cell .actual.different {
  color: #f56c6c;
  font-weight: bold;
}

.comparison-cell .actual.different::before {
  content: "⚠ ";
  color: #e6a23c;
}

/* 执行人员角色样式 */
.executor-roles {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;

  .el-tag {
    min-width: 32px;
    text-align: center;
  }

  span {
    color: #606266;
    font-weight: 500;
  }
}

/* 采样人员样式 */
.sampler-info {
  display: flex;
  align-items: center;
  gap: 8px;

  .el-tag {
    min-width: 40px;
    text-align: center;
  }

  .sampler-name {
    color: #303133;
    font-weight: 500;
    font-size: 13px;
  }
}
</style>
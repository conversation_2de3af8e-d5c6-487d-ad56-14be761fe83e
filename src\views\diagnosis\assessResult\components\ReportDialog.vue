<template>
  <el-dialog 
    v-model="visible" 
    title="评估报告" 
    width="80%" 
    :close-on-click-modal="false"
    @update:model-value="handleVisibleChange"
  >
    <div class="report-container">
      <div class="report-header">
        <h3>{{ reportData.objectName }} 评估报告</h3>
        <div class="report-info">
          <div>评估周期: {{ reportData.period }}</div>
          <div>评估方案: {{ reportData.schemeName }}</div>
          <div>生成时间: {{ reportData.generateTime }}</div>
        </div>
      </div>
      
      <div class="report-content">
        <!-- 基本信息 -->
        <div class="report-section">
          <h4>基本信息</h4>
          <el-descriptions :column="2" border>
            <el-descriptions-item label="评估对象">{{ reportData.objectName }}</el-descriptions-item>
            <el-descriptions-item label="评估周期">{{ reportData.period }}</el-descriptions-item>
            <el-descriptions-item label="方案名称">{{ reportData.schemeName }}</el-descriptions-item>
            <el-descriptions-item label="综合得分">
              <el-tag :type="getScoreType(reportData.totalScore)" size="large">
                {{ reportData.totalScore }}分 ({{ reportData.level }})
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>

        <!-- 项目分析 -->
        <div class="report-section">
          <h4>项目分析</h4>
          <el-table :data="reportData.projectAnalysis" border>
            <el-table-column prop="projectName" label="项目名称" width="150" />
            <el-table-column prop="weight" label="权重" width="80" align="center">
              <template #default="{ row }">{{ row.weight }}%</template>
            </el-table-column>
            <el-table-column prop="score" label="得分" width="80" align="center">
              <template #default="{ row }">
                <el-tag :type="getScoreType(row.score)">{{ row.score }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="remark" label="分析说明" min-width="200" />
          </el-table>
        </div>

        <!-- 结论与建议 -->
        <div class="report-section">
          <h4>结论与建议</h4>
          <div class="conclusion-content">
            <div class="conclusion-item">
              <strong>总体评价:</strong>
              <p>{{ reportData.conclusion.overall }}</p>
            </div>
            <div class="conclusion-item">
              <strong>主要优势:</strong>
              <ul>
                <li v-for="advantage in reportData.conclusion.advantages" :key="advantage">
                  {{ advantage }}
                </li>
              </ul>
            </div>
            <div class="conclusion-item">
              <strong>改进建议:</strong>
              <ul>
                <li v-for="suggestion in reportData.conclusion.suggestions" :key="suggestion">
                  {{ suggestion }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handlePrintReport">打印报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed, defineEmits } from 'vue'
import { ElMessage } from 'element-plus'
import type { ReportData } from '../types'

// Props
interface Props {
  modelValue: boolean
  reportData: ReportData
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  printReport: []
}>()

// 计算属性
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value)
})

// 工具方法
const getScoreType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'warning'
  if (score >= 70) return ''
  return 'danger'
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '优秀': 'success',
    '正常': '',
    '偏高': 'warning',
    '偏低': 'warning',
    '异常': 'danger'
  }
  return typeMap[status] || ''
}

// 事件处理方法
const handleVisibleChange = (value: boolean) => {
  emit('update:modelValue', value)
}

const handleClose = () => {
  emit('update:modelValue', false)
}

const handlePrintReport = () => {
  emit('printReport')
}
</script>

<style scoped lang="scss">
// 报告样式
.report-container {
  .report-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
    border-bottom: 1px solid #e5e7eb;

    h3 {
      margin: 0 0 12px 0;
      font-size: 20px;
      color: #2c3e50;
    }

    .report-info {
      display: flex;
      gap: 24px;
      font-size: 14px;
      color: #6b7280;
    }
  }

  .report-section {
    margin-bottom: 24px;

    h4 {
      margin: 0 0 16px 0;
      font-size: 16px;
      color: #374151;
      padding-left: 8px;
      border-left: 4px solid #3b82f6;
    }
  }

  .conclusion-content {
    .conclusion-item {
      margin-bottom: 16px;

      strong {
        color: #374151;
        font-size: 14px;
      }

      p {
        margin: 8px 0;
        line-height: 1.6;
        color: #6b7280;
      }

      ul {
        margin: 8px 0;
        padding-left: 20px;

        li {
          margin-bottom: 4px;
          line-height: 1.6;
          color: #6b7280;
        }
      }
    }
  }
}
</style>

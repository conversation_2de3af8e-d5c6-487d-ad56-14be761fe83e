<template>
  <ContentWrap title="采样计划管理">
    <div class="operation-bar">
      <el-button type="primary" @click="createRegularPlan">新建常规采样计划</el-button>
      <el-button type="primary" @click="createTempPlan">新建临时采样计划</el-button>
      <el-input v-model="searchKeyword" placeholder="搜索计划名称" style="width: 15rem; margin-left: 1rem" clearable @clear="handleSearchClear" @keyup.enter="handleSearchEnter" />
      <el-button type="primary" @click="refreshData">
        <el-icon><Refresh /></el-icon>
      </el-button>
    </div>
    
    <el-tabs v-model="activeTab" @tab-change="handleTabChange">
      <!-- 常规采样计划 -->
      <el-tab-pane label="常规采样计划" name="regular">
        <el-table
          v-loading="loading"
          :data="filteredRegularPlans"
          border
          style="width: 100%;"
          class="plan-table responsive-table"
        >
          <el-table-column prop="id" label="ID" min-width="60" width="80" align="center" />
          <el-table-column prop="name" label="采样计划名称" min-width="160" show-overflow-tooltip />
          <el-table-column prop="planCode" label="计划编号" min-width="120" align="center" />
          <el-table-column label="执行人员" width="180">
            <template #default="{ row }">
              <div class="executor-roles">
                <div class="role-item">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span>{{ row.samplerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="warning">检测</el-tag>
                  <span>{{ row.testerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="info">审核</el-tag>
                  <span>{{ row.reviewerName || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="检测项目" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.testItemName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采样点" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.samplingPointName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="frequency" label="采样频率" min-width="100" align="center">
            <template #default="{ row }">
              <el-tag size="small" type="info">{{ getFrequencyText(row.frequency) }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="startDate" label="开始日期" min-width="100" width="120" align="center" />
          <el-table-column prop="endDate" label="结束日期" min-width="100" width="120" align="center" />
          <el-table-column prop="priority" label="优先级" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="expectedSampleQuantity" label="样品数量" min-width="100" align="center">
            <template #default="{ row }">
              {{ row.expectedSampleQuantity || 100 }}mL
            </template>
          </el-table-column>
          <el-table-column prop="expectedSampleNature" label="样品性质" min-width="80" align="center">
            <template #default="{ row }">
              {{ getSampleNatureText(row.expectedSampleNature) }}
            </template>
          </el-table-column>
          <el-table-column prop="expectedSupernatant" label="预期上清液" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="expected-info">{{ row.expectedSupernatant || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isEnabled" label="启用状态" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.isEnabled ? 'success' : 'info'" size="small">
                {{ row.isEnabled ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="200" width="240" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click="handleEdit('regular', row)">编辑</el-button>
                <el-button link type="success" size="small" @click="handleViewTasks(row)">查看任务</el-button>
                <el-dropdown trigger="click">
                  <el-button link type="primary" size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleChangePlanStatus(row)">
                        {{ row.isEnabled ? '停用计划' : '启用计划' }}
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleCheckConflicts(row)">检查冲突</el-dropdown-item>
                      <el-dropdown-item @click="handleDelete('regular', row)" class="danger-item">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="regularPagination.currentPage"
            v-model:page-size="regularPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="regularPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="(pageSize: number) => handleSizeChange(pageSize, 'regular')"
            @current-change="(currentPage: number) => handleCurrentChange(currentPage, 'regular')"
          />
        </div>
      </el-tab-pane>
      
      <!-- 临时采样计划 -->
      <el-tab-pane label="临时采样计划" name="temporary">
        <el-table
          v-loading="loading"
          :data="filteredTempPlans"
          border
          style="width: 100%;"
          class="plan-table"
        >
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="planCode" label="计划编号" min-width="120" align="center" />
          <el-table-column prop="name" label="采样计划名称" min-width="180" show-overflow-tooltip />
          <el-table-column prop="planDatetime" label="执行时间" width="150" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.planDatetime) }}
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" effect="plain" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="执行人员" width="180">
            <template #default="{ row }">
              <div class="executor-roles">
                <div class="role-item">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span>{{ row.samplerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="warning">检测</el-tag>
                  <span>{{ row.testerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="info">审核</el-tag>
                  <span>{{ row.reviewerName || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="检测项目" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.testItemName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column label="采样点" min-width="140" show-overflow-tooltip>
            <template #default="{ row }">
              <span>{{ row.samplingPointName || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="reason" label="创建原因" min-width="150" show-overflow-tooltip />
          <el-table-column prop="expectedSampleQuantity" label="样品数量" min-width="100" align="center">
            <template #default="{ row }">
              {{ row.expectedSampleQuantity || 100 }}mL
            </template>
          </el-table-column>
          <el-table-column prop="expectedSampleNature" label="样品性质" min-width="80" align="center">
            <template #default="{ row }">
              {{ getSampleNatureText(row.expectedSampleNature) }}
            </template>
          </el-table-column>
          <el-table-column prop="expectedSupernatant" label="预期上清液" min-width="120" show-overflow-tooltip>
            <template #default="{ row }">
              <span class="expected-info">{{ row.expectedSupernatant || '-' }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="isEnabled" label="启用状态" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="row.isEnabled ? 'success' : 'info'" size="small">
                {{ row.isEnabled ? '启用' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="220" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button link type="primary" size="small" @click="handleEdit('temporary', row)">编辑</el-button>
                <el-button link type="success" size="small" @click="handleViewTasks(row)">查看任务</el-button>
                <el-dropdown trigger="click">
                  <el-button link type="primary" size="small">
                    更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item @click="handleChangePlanStatus(row)">
                        {{ row.isEnabled ? '停用计划' : '启用计划' }}
                      </el-dropdown-item>
                      <el-dropdown-item @click="handleCheckConflicts(row)">检查冲突</el-dropdown-item>
                      <el-dropdown-item @click="handleDelete('temporary', row)" class="danger-item">删除</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="tempPagination.currentPage"
            v-model:page-size="tempPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="tempPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="(pageSize: number) => handleSizeChange(pageSize, 'temporary')"
            @current-change="(currentPage: number) => handleCurrentChange(currentPage, 'temporary')"
          />
        </div>
      </el-tab-pane>
      
      <!-- 任务执行 -->
      <el-tab-pane label="任务执行" name="tasks">
        <!-- 操作栏 -->
        <div class="mb-4">

          <el-button type="success" @click="handleBatchAssign" :disabled="selectedTasks.length === 0">
            <el-icon><User /></el-icon>批量分配 ({{ selectedTasks.length }})
          </el-button>
          <el-button @click="handleRefreshTasks">
            <el-icon><Refresh /></el-icon>刷新
          </el-button>
        </div>

        <el-table
          v-loading="loading"
          :data="filteredTasks"
          border
          style="width: 100%;"
          class="plan-table"
          @selection-change="handleTaskSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="id" label="ID" width="80" align="center" />
          <el-table-column prop="taskCode" label="任务编号" min-width="140" align="center" />
          <el-table-column prop="planName" label="关联采样计划" min-width="180" show-overflow-tooltip />
          <el-table-column prop="taskDatetime" label="执行时间" width="150" align="center">
            <template #default="{ row }">
              {{ formatDateTime(row.taskDatetime) }}
            </template>
          </el-table-column>
          <el-table-column prop="samplingPointName" label="采样点" min-width="120" align="center" />
          <el-table-column prop="testItemName" label="检测项目" min-width="140" show-overflow-tooltip />
          <el-table-column label="执行人员" width="180">
            <template #default="{ row }">
              <div class="executor-roles">
                <div class="role-item">
                  <el-tag size="small" type="success">采样</el-tag>
                  <span>{{ row.samplerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="warning">检测</el-tag>
                  <span>{{ row.testerName || '-' }}</span>
                </div>
                <div class="role-item">
                  <el-tag size="small" type="info">审核</el-tag>
                  <span>{{ row.reviewerName || '-' }}</span>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="priority" label="优先级" min-width="80" align="center">
            <template #default="{ row }">
              <el-tag :type="getPriorityType(row.priority)" size="small">
                {{ getPriorityText(row.priority) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="120" align="center">
            <template #default="{ row }">
              <el-tag :type="getTaskStatusType(row.status)" size="small">
                {{ getTaskStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="260" fixed="right">
            <template #default="{ row }">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  size="small"
                  @click="handleAssignTask(row)"
                  :disabled="row.status !== 'pending'"
                >
                  分配任务
                </el-button>
                <el-button
                  link
                  type="info"
                  size="small"
                  @click="handleViewTaskDetail(row)"
                >
                  查看详情
                </el-button>
                <el-button
                  link
                  type="danger"
                  size="small"
                  @click="handleCancelTask(row)"
                  v-if="row.status !== 'completed' && row.status !== 'cancelled'"
                >
                  取消任务
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="taskPagination.currentPage"
            v-model:page-size="taskPagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="taskPagination.total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="(pageSize: number) => handleSizeChange(pageSize, 'tasks')"
            @current-change="(currentPage: number) => handleCurrentChange(currentPage, 'tasks')"
          />
        </div>
      </el-tab-pane>
    </el-tabs>
    
    <!-- 弹窗组件 -->
    <RegularPlanDialog ref="regularPlanDialogRef" @success="handleDialogSuccess" />
    <TempPlanDialog ref="tempPlanDialogRef" @success="handleDialogSuccess" />
    

    
    <!-- 冲突检查结果弹窗 -->
    <el-dialog v-model="conflictDialogVisible" title="计划冲突检查结果" width="50%">
      <div v-if="conflicts.length > 0">
        <el-alert type="warning" title="检测到以下冲突，请调整计划:" :closable="false" show-icon />
        <el-table :data="conflicts" border style="width: 100%; margin-top: 1rem;" class="conflict-table">
          <el-table-column prop="type" label="冲突类型" min-width="100" width="120" align="center" />
          <el-table-column prop="description" label="冲突描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="suggestion" label="建议操作" min-width="150" show-overflow-tooltip />
        </el-table>
      </div>
      <div v-else>
        <el-alert type="success" title="未检测到任何冲突，可以安排执行" :closable="false" show-icon />
      </div>
      <template #footer>
        <el-button @click="conflictDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="handleAdjustPlan" v-if="conflicts.length > 0">调整计划</el-button>
        <el-button type="success" @click="handleConfirmPlan" v-if="conflicts.length === 0">确认计划</el-button>
      </template>
    </el-dialog>

    <!-- 任务详情弹窗 -->
    <el-dialog v-model="taskDetailDialogVisible" title="任务详情" width="60%" class="task-detail-dialog">
      <div v-if="currentTask" class="task-detail-content">
        <!-- 基本信息 -->
        <el-card class="detail-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">基本信息</span>
              <el-tag :type="getTaskStatusType(currentTask.status)" size="small">
                {{ getTaskStatusText(currentTask.status) }}
              </el-tag>
            </div>
          </template>
          <el-row :gutter="20">
            <el-col :span="8">
              <div class="detail-item">
                <label>任务ID：</label>
                <span>{{ currentTask.id }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>关联计划：</label>
                <span>{{ currentTask.planName }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>执行日期：</label>
                <span>{{ currentTask.taskDate }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px;">
            <el-col :span="8">
              <div class="detail-item">
                <label>采样点：</label>
                <span>{{ currentTask.samplingPoint }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>采样人员：</label>
                <span>{{ currentTask.sampler || '未分配' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>检测人员：</label>
                <span>{{ currentTask.tester || '未分配' }}</span>
              </div>
            </el-col>
          </el-row>
          <el-row :gutter="20" style="margin-top: 16px;">
            <el-col :span="8">
              <div class="detail-item">
                <label>审核人员：</label>
                <span>{{ currentTask.reviewer || '未分配' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>创建时间：</label>
                <span>{{ currentTask.createTime || '2024-01-15 10:30:00' }}</span>
              </div>
            </el-col>
            <el-col :span="8">
              <div class="detail-item">
                <label>备注：</label>
                <span>{{ currentTask.remark || '-' }}</span>
              </div>
            </el-col>
          </el-row>
        </el-card>

        <!-- 检测项目信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
          <template #header>
            <span class="card-title">检测项目</span>
          </template>
          <el-table :data="currentTaskItems" border class="task-detail-table">
            <el-table-column prop="name" label="项目名称" min-width="120" />
            <el-table-column prop="category" label="类别" min-width="80" width="100" align="center" />
            <el-table-column prop="method" label="检测方法" min-width="150" show-overflow-tooltip />
            <el-table-column prop="standardValue" label="标准值" min-width="100" width="120" align="center">
              <template #default="{ row }">
                {{ row.standardValue }} {{ row.unit }}
              </template>
            </el-table-column>
            <el-table-column prop="priority" label="优先级" min-width="80" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="row.priority === 'high' ? 'danger' : row.priority === 'medium' ? 'warning' : 'info'" size="small">
                  {{ row.priority === 'high' ? '高' : row.priority === 'medium' ? '中' : '低' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <!-- 执行记录 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
          <template #header>
            <span class="card-title">执行记录</span>
          </template>
          <el-timeline>
            <el-timeline-item
              v-for="record in taskExecutionRecords"
              :key="record.id"
              :timestamp="record.time"
              :type="record.type"
            >
              <div class="timeline-content">
                <div class="timeline-title">{{ record.action }}</div>
                <div class="timeline-desc" v-if="record.description">{{ record.description }}</div>
                <div class="timeline-operator" v-if="record.operator">操作人：{{ record.operator }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
        </el-card>

        <!-- 备注信息 -->
        <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="currentTask.remark">
          <template #header>
            <span class="card-title">备注信息</span>
          </template>
          <p class="remark-content">{{ currentTask.remark || '暂无备注' }}</p>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="taskDetailDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEditTask" v-if="currentTask?.status === 'pending'">编辑任务</el-button>
        </div>
      </template>
    </el-dialog>

  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, computed, onMounted, onBeforeUnmount } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ArrowDown, Refresh } from '@element-plus/icons-vue'
import RegularPlanDialog from './components/RegularPlanDialog.vue'
import TempPlanDialog from './components/TempPlanDialog.vue'
import { AssayTestPlanApi, type AssaySamplingPlanRespVO, type AssaySamplingTaskRespVO } from '@/api/assay/testPlan'
import { useAppStore } from '@/store/modules/app'

// 数据类型定义 - 使用API文档中的类型
type RegularPlan = AssaySamplingPlanRespVO & { type: 'regular' }
type TempPlan = AssaySamplingPlanRespVO & { type: 'temporary' }
type Task = AssaySamplingTaskRespVO

interface Conflict {
  type: string;
  description: string;
  suggestion: string;
}

interface TestItem {
  id: number;
  name: string;
  category?: string;
  method?: string;
  standardValue?: string;
  unit?: string;
  priority?: 'high' | 'medium' | 'low';
}

interface SamplingPoint {
  id: number;
  name: string;
}

interface Staff {
  id: number;
  name: string;
  role: string;
}

// 页面状态
const activeTab = ref('regular')
const loading = ref(false)
const searchKeyword = ref('')

// 弹窗引用
const regularPlanDialogRef = ref()
const tempPlanDialogRef = ref()

// 获取全局状态管理
const appStore = useAppStore()
const currentFactoryId = computed(() => appStore.currentStation?.id || 1)



// 冲突检查弹窗
const conflictDialogVisible = ref(false)
const conflicts = ref<Conflict[]>([])
const currentPlan = ref<RegularPlan | TempPlan | null>(null)

// 任务详情弹窗
const taskDetailDialogVisible = ref(false)
const currentTask = ref<Task | null>(null)

// 当前任务的检测项目
const currentTaskItems = ref<TestItem[]>([])

// 任务执行记录
const taskExecutionRecords = ref<ExecutionRecord[]>([])

// 选择的任务
const selectedTasks = ref<Task[]>([])

// 定义执行记录类型
interface ExecutionRecord {
  id: number
  action: string
  description?: string
  operator?: string
  time: string
  type: 'primary' | 'success' | 'warning' | 'danger' | 'info'
}



// 检测项目数据 (与基础信息模块保持一致)
const testItems: TestItem[] = [
  { id: 111, name: 'COD', category: '有机物', method: '重铬酸钾法', standardValue: '≤50', unit: 'mg/L', priority: 'high' },
  { id: 112, name: 'BOD5', category: '有机物', method: '稀释接种法', standardValue: '≤10', unit: 'mg/L', priority: 'high' },
  { id: 113, name: 'TOC', category: '有机物', method: '燃烧氧化法', standardValue: '≤30', unit: 'mg/L', priority: 'medium' },
  { id: 121, name: '氨氮', category: '营养盐', method: '纳氏试剂比色法', standardValue: '≤5', unit: 'mg/L', priority: 'high' },
  { id: 122, name: '总磷', category: '营养盐', method: '钼酸铵分光光度法', standardValue: '≤0.5', unit: 'mg/L', priority: 'high' },
  { id: 123, name: '总氮', category: '营养盐', method: '碱性过硫酸钾消解法', standardValue: '≤15', unit: 'mg/L', priority: 'high' },
  { id: 124, name: '硝酸盐氮', category: '营养盐', method: '紫外分光光度法', standardValue: '≤10', unit: 'mg/L', priority: 'medium' },
  { id: 131, name: 'pH值', category: '物理指标', method: '玻璃电极法', standardValue: '6-9', unit: '无量纲', priority: 'high' },
  { id: 132, name: '悬浮物', category: '物理指标', method: '重量法', standardValue: '≤10', unit: 'mg/L', priority: 'high' },
  { id: 133, name: '浊度', category: '物理指标', method: '散射法', standardValue: '≤5', unit: 'NTU', priority: 'medium' },
  { id: 211, name: '含水率', category: '污泥性质', method: '重量法', standardValue: '≤80', unit: '%', priority: 'medium' },
  { id: 212, name: '污泥浓度', category: '污泥性质', method: '重量法', standardValue: '2000-4000', unit: 'mg/L', priority: 'high' },
  { id: 213, name: '污泥沉降比', category: '污泥性质', method: '静置沉降法', standardValue: '15-30', unit: '%', priority: 'medium' },
  { id: 311, name: '铜', category: '重金属', method: '原子吸收分光光度法', standardValue: '≤0.5', unit: 'mg/L', priority: 'low' },
  { id: 312, name: '锌', category: '重金属', method: '原子吸收分光光度法', standardValue: '≤1.0', unit: 'mg/L', priority: 'low' }
]

// 采样点数据 (与基础信息模块保持一致)
const samplingPoints: SamplingPoint[] = [
  { id: 1, name: '进水总口' },
  { id: 2, name: '生化池进口' },
  { id: 3, name: '生化池出口' },
  { id: 4, name: '二沉池出水' },
  { id: 5, name: '出水总口' },
  { id: 6, name: '污泥浓缩池' },
  { id: 7, name: '污泥脱水间' },
  { id: 8, name: '回流污泥' },
  { id: 9, name: '中间水池' },
  { id: 10, name: '应急排放口' }
]

// 人员数据 (扩展更多人员)
const staffList: Staff[] = [
  { id: 1, name: '张三', role: '采样员' },
  { id: 2, name: '李四', role: '检测员' },
  { id: 3, name: '王五', role: '质控员' },
  { id: 4, name: '赵六', role: '审核员' },
  { id: 5, name: '钱七', role: '采样员' },
  { id: 6, name: '孙八', role: '检测员' },
  { id: 7, name: '周九', role: '质控员' },
  { id: 8, name: '吴十', role: '技术员' }
]

// 建立ID-对象映射，方便快速查找
const testItemMap = testItems.reduce((acc, item) => {
  acc[item.id] = item
  return acc
}, {} as Record<number, TestItem>)

const samplingPointMap = samplingPoints.reduce((acc, point) => {
  acc[point.id] = point
  return acc
}, {} as Record<number, SamplingPoint>)

// 数据状态
const regularPlans = ref<RegularPlan[]>([])
const tempPlans = ref<TempPlan[]>([])
const tasks = ref<Task[]>([])

// 分页状态
const regularPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const tempPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

const taskPagination = ref({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// 计算过滤后的列表数据
const filteredRegularPlans = computed(() => {
  const filtered = regularPlans.value.filter(plan =>
    plan.type === 'regular' &&
    (!searchKeyword.value || plan.name.toLowerCase().includes(searchKeyword.value.toLowerCase()))
  )

  const start = (regularPagination.value.currentPage - 1) * regularPagination.value.pageSize
  const end = start + regularPagination.value.pageSize

  return filtered.slice(start, end)
})

// 临时计划列表（直接返回API数据，不进行客户端分页）
const filteredTempPlans = computed(() => {
  return tempPlans.value
})

// 任务列表（直接返回API数据，不进行客户端分页）
const filteredTasks = computed(() => {
  return tasks.value
})

// 初始化
onMounted(() => {
  // 检查用户是否已登录并有水厂ID
  if (!currentFactoryId.value) {
    ElMessage.warning('请先登录并选择水厂')
    return
  }
  refreshData()
})

// 组件卸载前清理
onBeforeUnmount(() => {
  // 清理数据，防止内存泄漏
  regularPlans.value = []
  tempPlans.value = []
  tasks.value = []
})

// 刷新数据
const refreshData = async () => {
  loading.value = true
  try {
    await Promise.all([
      loadRegularPlans(),
      loadTempPlans(),
      loadTasks()
    ])
  } catch (error) {
    console.error('数据加载失败:', error)
    ElMessage.error('数据加载失败')
  } finally {
    loading.value = false
  }
}

// 加载常规计划数据
const loadRegularPlans = async () => {
  try {
    const params = {
      pageNo: regularPagination.value.currentPage,
      pageSize: regularPagination.value.pageSize,
      factoryId: currentFactoryId.value,
      type: 'regular',
      name: searchKeyword.value || undefined
    }

    const res = await AssayTestPlanApi.getSamplingPlanPage(params)
    if (res.data) {
      // 处理返回的数据，确保字段映射正确
      regularPlans.value = (res.data.list || []).map((plan: any) => ({
        ...plan,
        type: 'regular',
        // 确保显示字段存在
        testItemName: plan.testItemName || testItemMap[plan.testItem]?.name || '-',
        samplingPointName: plan.samplingPointName || samplingPointMap[plan.samplingPoint]?.name || '-'
      }))
      regularPagination.value.total = res.data.total || 0
    }
  } catch (error) {
    console.error('加载常规计划失败:', error)
    ElMessage.error('加载常规计划失败')
  }
}

// 加载临时计划数据
const loadTempPlans = async () => {
  try {
    const params = {
      pageNo: tempPagination.value.currentPage,
      pageSize: tempPagination.value.pageSize,
      factoryId: currentFactoryId.value,
      type: 'temporary',
      name: searchKeyword.value || undefined
    }

    const res = await AssayTestPlanApi.getSamplingPlanPage(params)
    if (res.data) {
      // 处理返回的数据，确保字段映射正确
      tempPlans.value = (res.data.list || []).map((plan: any) => ({
        ...plan,
        type: 'temporary',
        // 确保显示字段存在
        testItemName: plan.testItemName || testItemMap[plan.testItem]?.name || '-',
        samplingPointName: plan.samplingPointName || samplingPointMap[plan.samplingPoint]?.name || '-'
      }))
      tempPagination.value.total = res.data.total || 0
    }
  } catch (error) {
    console.error('加载临时计划失败:', error)
    ElMessage.error('加载临时计划失败')
  }
}

// 加载任务数据
const loadTasks = async () => {
  try {
    const params = {
      pageNo: taskPagination.value.currentPage,
      pageSize: taskPagination.value.pageSize,
      factoryId: currentFactoryId.value
    }

    const res = await AssayTestPlanApi.getSamplingTaskPage(params)
    if (res.data) {
      // 处理返回的数据，确保字段映射正确
      tasks.value = (res.data.list || []).map((task: any) => ({
        ...task,
        // 确保显示字段存在
        testItemName: task.testItemName || testItemMap[task.testItem]?.name || '-',
        samplingPointName: task.samplingPointName || samplingPointMap[task.samplingPoint]?.name || '-'
      }))
      taskPagination.value.total = res.data.total || 0
    }
  } catch (error) {
    console.error('加载任务失败:', error)
    ElMessage.error('加载任务失败')
  }
}

// 分页处理
// 分页大小改变处理
const handleSizeChange = (pageSize: number, tabType?: string) => {
  const currentTab = tabType || activeTab.value
  if (currentTab === 'regular') {
    regularPagination.value.pageSize = pageSize
    regularPagination.value.currentPage = 1
    loadRegularPlans()
  } else if (currentTab === 'temporary') {
    tempPagination.value.pageSize = pageSize
    tempPagination.value.currentPage = 1
    loadTempPlans()
  } else if (currentTab === 'tasks') {
    taskPagination.value.pageSize = pageSize
    taskPagination.value.currentPage = 1
    loadTasks()
  }
}

// 当前页改变处理
const handleCurrentChange = (currentPage: number, tabType?: string) => {
  const currentTab = tabType || activeTab.value
  if (currentTab === 'regular') {
    regularPagination.value.currentPage = currentPage
    loadRegularPlans()
  } else if (currentTab === 'temporary') {
    tempPagination.value.currentPage = currentPage
    loadTempPlans()
  } else if (currentTab === 'tasks') {
    taskPagination.value.currentPage = currentPage
    loadTasks()
  }
}

// 标签页切换处理
const handleTabChange = () => {
  searchKeyword.value = ''
  if (activeTab.value === 'regular') {
    loadRegularPlans()
  } else if (activeTab.value === 'temporary') {
    loadTempPlans()
  } else if (activeTab.value === 'tasks') {
    loadTasks()
  }
}

// 搜索处理
const handleSearchClear = () => {
  searchKeyword.value = ''
  if (activeTab.value === 'regular') {
    regularPagination.value.currentPage = 1
    loadRegularPlans()
  } else if (activeTab.value === 'temporary') {
    tempPagination.value.currentPage = 1
    loadTempPlans()
  } else if (activeTab.value === 'tasks') {
    taskPagination.value.currentPage = 1
    loadTasks()
  }
}

const handleSearchEnter = () => {
  if (activeTab.value === 'regular') {
    regularPagination.value.currentPage = 1
    loadRegularPlans()
  } else if (activeTab.value === 'temporary') {
    tempPagination.value.currentPage = 1
    loadTempPlans()
  } else if (activeTab.value === 'tasks') {
    taskPagination.value.currentPage = 1
    loadTasks()
  }
}

// 获取状态类型和文本
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending': return 'info'
    case 'processing': return 'primary'
    case 'completed': return 'success'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '未开始'
    case 'processing': return '进行中'
    case 'completed': return '已完成'
    default: return '未知'
  }
}

// 获取优先级类型和文本
const getPriorityType = (priority: string) => {
  switch (priority) {
    case 'normal': return 'info'
    case 'high': return 'warning'
    case 'urgent': return 'danger'
    default: return 'info'
  }
}

const getPriorityText = (priority: string) => {
  switch (priority) {
    case 'normal': return '普通'
    case 'high': return '高'
    case 'urgent': return '紧急'
    default: return '普通'
  }
}

// 获取采样频率文本
const getFrequencyText = (frequency: string) => {
  switch (frequency) {
    case 'daily': return '每日'
    case 'weekly': return '每周'
    case 'monthly': return '每月'
    case 'quarterly': return '季度'
    default: return frequency || '-'
  }
}

// 获取样品性质文本
const getSampleNatureText = (nature: string) => {
  switch (nature) {
    case 'liquid': return '液体'
    case 'solid': return '固体'
    case 'semi-solid': return '半固体'
    case 'gas': return '气体'
    default: return nature || '-'
  }
}

// 格式化日期时间
const formatDateTime = (datetime: any) => {
  if (!datetime) return '-'

  // 如果是字符串类型
  if (typeof datetime === 'string') {
    return datetime.replace('T', ' ').substring(0, 19)
  }

  // 如果是Date对象
  if (datetime instanceof Date) {
    return datetime.toISOString().replace('T', ' ').substring(0, 19)
  }

  // 如果是时间戳
  if (typeof datetime === 'number') {
    return new Date(datetime).toISOString().replace('T', ' ').substring(0, 19)
  }

  // 其他情况返回原值的字符串形式
  return String(datetime)
}

// 获取任务状态类型和文本
const getTaskStatusType = (status: string) => {
  switch (status) {
    case 'unassigned': return 'info'
    case 'assigned': return 'warning'
    case 'processing': return 'primary'
    case 'completed': return 'success'
    default: return 'info'
  }
}

const getTaskStatusText = (status: string) => {
  switch (status) {
    case 'unassigned': return '未分配'
    case 'assigned': return '已分配'
    case 'processing': return '执行中'
    case 'completed': return '已完成'
    default: return '未知'
  }
}

// 创建常规计划
const createRegularPlan = () => {
  regularPlanDialogRef.value?.open('create')
}

// 创建临时计划
const createTempPlan = () => {
  tempPlanDialogRef.value?.open('create')
}

// 编辑计划
const handleEdit = (type: string, row: RegularPlan | TempPlan) => {
  if (type === 'regular') {
    regularPlanDialogRef.value?.open('update', row)
  } else {
    tempPlanDialogRef.value?.open('update', row)
  }
}

// 删除计划
const handleDelete = (type: string, row: RegularPlan | TempPlan) => {
  ElMessageBox.confirm(`确定要删除「${row.name}」吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      await AssayTestPlanApi.deleteSamplingPlan(row.id, currentFactoryId.value)
      ElMessage.success('删除成功')

      // 重新加载数据
      if (type === 'regular') {
        await loadRegularPlans()
      } else {
        await loadTempPlans()
      }
    } catch (error) {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }).catch(() => {})
}

// 弹窗成功回调
const handleDialogSuccess = () => {
  if (activeTab.value === 'regular') {
    loadRegularPlans()
  } else if (activeTab.value === 'temporary') {
    loadTempPlans()
  } else {
    refreshData()
  }
}

// 查看任务
const handleViewTasks = async (row: RegularPlan | TempPlan) => {
  try {
    // 先获取该计划的任务列表
    const res = await AssayTestPlanApi.getTasksByPlan(row.id)

    if (res.data && res.data.length > 0) {
      // 如果有任务，切换到任务标签页并过滤显示
      activeTab.value = 'tasks'
      searchKeyword.value = row.name
      await loadTasks()
    } else {
      // 如果没有任务，询问是否生成任务
      ElMessageBox.confirm(`计划「${row.name}」暂无任务，是否生成任务？`, '提示', {
        confirmButtonText: '生成任务',
        cancelButtonText: '取消',
        type: 'info'
      }).then(async () => {
        await handleGenerateTasks(row)
      }).catch(() => {})
    }
  } catch (error) {
    console.error('查看任务失败:', error)
    ElMessage.error('查看任务失败')
  }
}

// 生成任务
const handleGenerateTasks = async (row: RegularPlan | TempPlan) => {
  try {
    const generateData = {
      factoryId: currentFactoryId.value,
      planId: row.id,
      generateType: 'auto',
      startDate: new Date().toISOString().split('T')[0],
      endDate: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 30天后
    }

    await AssayTestPlanApi.generateTasksFromPlan(generateData)
    ElMessage.success('任务生成成功')

    // 重新加载任务数据并切换到任务标签页
    await loadTasks()
    activeTab.value = 'tasks'
    searchKeyword.value = row.name
  } catch (error) {
    console.error('生成任务失败:', error)
    ElMessage.error('生成任务失败')
  }
}

// 检查计划冲突
const handleCheckConflicts = async (row: RegularPlan | TempPlan) => {
  loading.value = true
  currentPlan.value = row

  try {
    const conflictData = {
      planId: row.id,
      factoryId: currentFactoryId.value,
      samplingPoint: row.samplingPoint,
      dateRange: row.type === 'regular' ? {
        startDate: row.startDate,
        endDate: row.endDate
      } : {
        startDate: row.planDatetime?.split('T')[0],
        endDate: row.planDatetime?.split('T')[0]
      },
      samplerId: row.samplerId,
      testerId: row.testerId,
      reviewerId: row.reviewerId
    }

    const res = await AssayTestPlanApi.checkPlanConflicts(conflictData)

    if (res.data?.hasConflicts) {
      conflicts.value = res.data.conflicts.map((conflict: any) => ({
        type: conflict.type === 'sampling_point' ? '采样点冲突' :
              conflict.type === 'sampler' ? '采样人员冲突' :
              conflict.type === 'tester' ? '检测人员冲突' : '其他冲突',
        description: conflict.message,
        suggestion: '请调整计划时间或人员安排'
      }))
    } else {
      conflicts.value = []
    }

    conflictDialogVisible.value = true
  } catch (error) {
    console.error('检查冲突失败:', error)
    ElMessage.error('检查冲突失败')
  } finally {
    loading.value = false
  }
}

// 调整计划
const handleAdjustPlan = () => {
  conflictDialogVisible.value = false
  
  // 根据计划类型打开编辑弹窗
  if (currentPlan.value) {
    if ('frequency' in currentPlan.value) {
      regularPlanDialogRef.value.open('update', currentPlan.value)
    } else {
      tempPlanDialogRef.value.open('update', currentPlan.value)
    }
  }
}

// 确认计划
const handleConfirmPlan = async () => {
  conflictDialogVisible.value = false

  if (currentPlan.value) {
    try {
      // 直接调用生成任务的API
      await handleGenerateTasks(currentPlan.value)

      ElMessage.success('计划已确认，任务已生成')
    } catch (error) {
      console.error('确认计划失败:', error)
      ElMessage.error('确认计划失败')
    }
  }
}

// 更改计划状态
const handleChangePlanStatus = async (row: RegularPlan | TempPlan) => {
  try {
    let message: string

    // 根据isEnabled状态切换
    if (row.isEnabled) {
      message = '计划已停用'
      row.isEnabled = false
    } else {
      message = '计划已启用'
      row.isEnabled = true
    }

    // 调用API更新状态
    await AssayTestPlanApi.updateSamplingPlan({
      ...row,
      isEnabled: row.isEnabled
    })

    ElMessage.success(message)
  } catch (error) {
    console.error('更新计划状态失败:', error)
    ElMessage.error('更新计划状态失败')
  }
}

// 任务分配 - 带二次确认弹窗
const handleAssignTask = async (task: Task) => {
  // 构建确认信息
  const confirmContent = `
    <div style="line-height: 1.6;">
      <p><strong>任务信息：</strong></p>
      <p>计划名称：${task.planName}</p>
      <p>采样点位：${task.samplingPointName}</p>
      <p>检测项目：${task.testItemName}</p>
      <p>计划日期：${task.taskDatetime}</p>
      <br/>
      <p><strong>分配信息：</strong></p>
      <p>采样人员：${task.samplerName || '未指定'}</p>
      <p>检测人员：${task.testerName || '未指定'}</p>
      <p>审核人员：${task.reviewerName || '未指定'}</p>
      <br/>
      <p style="color: #E6A23C;"><strong>确认分配此任务？</strong></p>
    </div>
  `

  ElMessageBox.confirm(confirmContent, '分配任务确认', {
    confirmButtonText: '确认分配',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true,
    customClass: 'assign-task-confirm'
  }).then(async () => {
    try {
      const assignData = {
        taskId: task.id,
        samplerId: task.samplerId,
        testerId: task.testerId,
        reviewerId: task.reviewerId,
        assignReason: '手动分配任务'
      }

      await AssayTestPlanApi.assignTask(assignData)
      await AssayTestPlanApi.updateTaskStatus(task.id, 'sampling', '任务已分配，开始采样')

      ElMessage.success('任务已分配，状态更新为采样中')

      // 重新加载任务数据
      await loadTasks()
    } catch (error) {
      console.error('分配任务失败:', error)
      ElMessage.error('分配任务失败')
    }
  }).catch(() => {
    ElMessage.info('已取消分配')
  })
}





// 查看任务详情
const handleViewTaskDetail = (task: Task) => {
  currentTask.value = task

  // 模拟获取任务相关的检测项目
  currentTaskItems.value = [
    {
      id: 1,
      name: 'COD',
      category: '水质',
      method: '重铬酸钾法',
      standardValue: '≤50',
      unit: 'mg/L',
      priority: 'high'
    },
    {
      id: 2,
      name: 'BOD5',
      category: '水质',
      method: '稀释与接种法',
      standardValue: '≤10',
      unit: 'mg/L',
      priority: 'medium'
    },
    {
      id: 3,
      name: 'SS',
      category: '水质',
      method: '重量法',
      standardValue: '≤10',
      unit: 'mg/L',
      priority: 'low'
    }
  ]

  // 模拟获取任务执行记录
  taskExecutionRecords.value = [
    {
      id: 1,
      action: '任务创建',
      description: '系统自动创建任务',
      operator: '系统',
      time: '2024-01-15 09:00:00',
      type: 'info'
    },
    {
      id: 2,
      action: '任务分配',
      description: `任务已分配给 ${task.samplerName || '张三'}`,
      operator: '李主管',
      time: '2024-01-15 09:30:00',
      type: 'primary'
    }
  ]

  // 根据任务状态添加更多记录
  if (task.status === 'processing') {
    taskExecutionRecords.value.push({
      id: 3,
      action: '开始执行',
      description: '任务执行人员开始采样工作',
      operator: task.samplerName || '张三',
      time: '2024-01-15 10:00:00',
      type: 'success'
    })
  } else if (task.status === 'completed') {
    taskExecutionRecords.value.push(
      {
        id: 3,
        action: '开始执行',
        description: '任务执行人员开始采样工作',
        operator: task.samplerName || '张三',
        time: '2024-01-15 10:00:00',
        type: 'success'
      },
      {
        id: 4,
        action: '任务完成',
        description: '采样工作已完成，样品已送检',
        operator: task.samplerName || '张三',
        time: '2024-01-15 11:30:00',
        type: 'success'
      }
    )
  }

  taskDetailDialogVisible.value = true
}

// 从详情弹窗编辑任务
const handleEditTask = () => {
  if (currentTask.value) {
    taskDetailDialogVisible.value = false
    // 这里可以打开编辑弹窗或跳转到编辑页面
    ElMessage.info('跳转到任务编辑页面')
  }
}



// 取消任务
const handleCancelTask = (task: Task) => {
  ElMessageBox.confirm(`确定要取消任务「${task.id}」吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    const index = tasks.value.findIndex(t => t.id === task.id)
    if (index !== -1) {
      tasks.value.splice(index, 1)
      ElMessage.success('任务已取消')
    }
  }).catch(() => {})
}

// ==================== 新增执行功能 ====================

// 任务选择变化
const handleTaskSelectionChange = (selection: Task[]) => {
  selectedTasks.value = selection
}



// 批量分配任务
const handleBatchAssign = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要分配的任务')
    return
  }

  ElMessage.info('批量分配功能开发中...')
}



// 刷新任务
const handleRefreshTasks = () => {
  ElMessage.success('任务列表已刷新')
  // 这里可以重新获取数据
}





</script>

<script lang="ts">
export default {
  name: 'AssayTestPlan'
}
</script>

<style scoped>
.operation-bar {
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.pagination-container {
  margin-top: 1.5rem;
  text-align: right;
  padding: 0.5rem 0;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
}

:deep(.el-table) {
  border-collapse: collapse;
  width: 100%;
  border-radius: 0.25rem;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

:deep(.el-table th) {
  background-color: #f5f7fa;
  color: #606266;
  font-weight: 600;
  padding: 0.85rem 0.75rem;
  border: 1px solid var(--el-table-border-color);
}

:deep(.el-table td) {
  padding: 0.85rem 0.75rem;
  border: 1px solid var(--el-table-border-color);
}

:deep(.el-table--border .el-table__cell) {
  border-right: 1px solid var(--el-table-border-color);
}

:deep(.el-table-column--selection .cell) {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}

:deep(.el-dropdown-link) {
  cursor: pointer;
  display: flex;
  align-items: center;
}

:deep(.el-tabs__item) {
  font-size: 1rem;
  font-weight: 500;
  padding: 0 1.25rem;
  height: 2.5rem;
  line-height: 2.5rem;
}

:deep(.el-tabs__nav-wrap::after) {
  height: 1px;
}

:deep(.el-tabs__active-bar) {
  height: 2px;
}

:deep(.el-button) {
  font-weight: 500;
}

:deep(.el-tag) {
  font-weight: 500;
  padding: 0 0.5rem;
  height: 1.5rem;
  line-height: 1.5rem;
}

:deep(.el-dialog__body) {
  padding: 1.5rem;
}

:deep(.el-alert) {
  margin-bottom: 1rem;
}

:deep(.el-form-item) {
  margin-bottom: 1.25rem;
}

:deep(.el-input), 
:deep(.el-select),
:deep(.el-date-picker) {
  width: 100%;
}

:deep(.el-dialog__footer) {
  padding: 1rem 1.5rem 1.5rem;
  text-align: right;
}

/* 表格优化样式 */
.plan-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.plan-table :deep(.el-table__header) {
  background-color: #f8fafc;
}

.plan-table :deep(.el-table__header th) {
  background-color: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

.plan-table :deep(.el-table__body tr:hover) {
  background-color: #f9fafb;
}

.plan-table :deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid #f3f4f6;
}

.text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.action-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-buttons .el-button {
  margin: 0;
  padding: 4px 8px;
}

:deep(.danger-item) {
  color: var(--el-color-danger) !important;
}

:deep(.danger-item:hover) {
  background-color: var(--el-color-danger-light-9) !important;
  color: var(--el-color-danger) !important;
}

/* 响应式表格优化 */
.responsive-table {
  /* 基础响应式设置 */
  table-layout: auto;
}

/* 大屏适配 (≥1920px) */
@media (min-width: 1920px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 16px 12px;
    font-size: 14px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 14px;
    font-weight: 600;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 12px;
    padding: 4px 8px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 13px;
    padding: 6px 12px;
  }
}

/* 超大屏适配 (≥2560px) */
@media (min-width: 2560px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 20px 16px;
    font-size: 16px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 16px;
    font-weight: 600;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 14px;
    padding: 6px 12px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 14px;
    padding: 8px 16px;
  }
}

/* 中等屏幕适配 (1366px-1919px) */
@media (max-width: 1919px) and (min-width: 1366px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 10px 8px;
    font-size: 13px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 13px;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 11px;
    padding: 2px 6px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 12px;
    padding: 4px 8px;
  }
}

/* 小屏幕适配 (≤1365px) */
@media (max-width: 1365px) {
  .responsive-table :deep(.el-table__cell) {
    padding: 8px 6px;
    font-size: 12px;
  }

  .responsive-table :deep(.el-table__header th) {
    font-size: 12px;
  }

  .responsive-table :deep(.el-tag) {
    font-size: 10px;
    padding: 2px 4px;
  }

  .responsive-table :deep(.el-button) {
    font-size: 11px;
    padding: 3px 6px;
  }
}

/* 冲突检查表格样式 */
.conflict-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.conflict-table :deep(.el-table__header) {
  background-color: #fef7e0;
}

.conflict-table :deep(.el-table__header th) {
  background-color: #fef7e0 !important;
  color: #b45309;
  font-weight: 600;
  border-bottom: 2px solid #f59e0b;
}

.conflict-table :deep(.el-table__body tr:hover) {
  background-color: #fffbeb;
}

.conflict-table :deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid #fed7aa;
}

/* 任务详情弹窗样式 */
.task-detail-dialog {
  border-radius: 12px;
  overflow: hidden;
}

.task-detail-dialog :deep(.el-dialog__header) {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px 24px;
  margin: 0;
}

.task-detail-dialog :deep(.el-dialog__title) {
  color: white;
  font-size: 18px;
  font-weight: 600;
}

.task-detail-dialog :deep(.el-dialog__headerbtn .el-dialog__close) {
  color: white;
  font-size: 20px;
}

.task-detail-dialog :deep(.el-dialog__body) {
  padding: 24px;
  background-color: #f8fafc;
}

.task-detail-content {
  max-height: 70vh;
  overflow-y: auto;
}

.detail-card {
  border-radius: 8px;
  border: 1px solid #e5e7eb;
}

.detail-card :deep(.el-card__header) {
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  padding: 16px 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
}

.detail-item {
  margin-bottom: 12px;
}

.detail-item label {
  font-weight: 600;
  color: #6b7280;
  margin-right: 8px;
  min-width: 80px;
  display: inline-block;
}

.detail-item span {
  color: #374151;
}

.task-detail-table {
  border-radius: 6px;
  overflow: hidden;
}

.task-detail-table :deep(.el-table__header) {
  background-color: #f3f4f6;
}

.task-detail-table :deep(.el-table__header th) {
  background-color: #f3f4f6 !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #d1d5db;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.timeline-desc {
  color: #6b7280;
  font-size: 14px;
  margin-bottom: 4px;
}

.timeline-operator {
  color: #9ca3af;
  font-size: 12px;
}

.remark-content {
  color: #374151;
  line-height: 1.6;
  margin: 0;
  padding: 16px;
  background-color: #f9fafb;
  border-radius: 6px;
  border-left: 4px solid #3b82f6;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* 执行人员角色样式 */
.executor-roles {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.role-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;

  .el-tag {
    min-width: 32px;
    text-align: center;
  }

  span {
    color: #606266;
    font-weight: 500;
  }
}

/* 分配任务确认弹窗样式 */
:deep(.assign-task-confirm) {
  .el-message-box__content {
    padding: 20px 24px;
  }

  .el-message-box__message {
    line-height: 1.6;

    p {
      margin: 8px 0;
    }

    strong {
      color: #303133;
      font-weight: 600;
    }
  }
}
</style>
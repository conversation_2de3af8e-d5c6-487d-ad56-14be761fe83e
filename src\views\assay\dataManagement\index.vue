<template>
  <ContentWrap title="检验执行管理">
    <el-tabs v-model="activeTab">
      <!-- 检验任务管理 -->
      <el-tab-pane label="检验任务管理" name="testTask">
        <el-card shadow="hover">
          <!-- 状态筛选器 -->
          <div class="mb-4">
            <el-tabs v-model="taskStatusFilter" type="card" @tab-change="handleStatusFilterChange">
              <el-tab-pane label="待下发" name="pending">
                <template #label>
                  <span>待下发 <el-badge :value="getTaskCountByStatus('pending')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="执行中" name="processing">
                <template #label>
                  <span>执行中 <el-badge :value="getTaskCountByStatus('processing')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="已完成" name="completed">
                <template #label>
                  <span>已完成 <el-badge :value="getTaskCountByStatus('completed')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.testTask" class="search-form">

              <el-form-item label="任务编号">
                <el-input v-model="searchForm.testTask.taskCode" placeholder="请输入任务编号" clearable />
              </el-form-item>
              <el-form-item label="复检筛选">
                <el-select v-model="searchForm.testTask.isRecheck" placeholder="请选择" clearable style="min-width: 8rem;">
                  <el-option label="正常任务" :value="false" />
                  <el-option label="复检任务" :value="true" />
                </el-select>
              </el-form-item>
              <el-form-item label="检验员">
                <el-select v-model="searchForm.testTask.assignedToId" placeholder="请选择检验员" clearable style="min-width: 10rem;">
                  <el-option label="张三" :value="10" />
                  <el-option label="李四" :value="11" />
                  <el-option label="王五" :value="12" />
                </el-select>
              </el-form-item>
              <el-form-item label="优先级">
                <el-select v-model="searchForm.testTask.priority" placeholder="请选择优先级" clearable style="min-width: 8rem;">
                  <el-option label="低" value="low" />
                  <el-option label="正常" value="normal" />
                  <el-option label="高" value="high" />
                  <el-option label="紧急" value="urgent" />
                </el-select>
              </el-form-item>
              <el-form-item label="创建日期">
                <el-date-picker
                  v-model="searchForm.testTask.createTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('testTask')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('testTask')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>


          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button type="primary" @click="handleImportFromSubmission">
              <el-icon><Import /></el-icon>从送检单导入任务
            </el-button>
            <el-button v-if="taskStatusFilter === 'pending'" type="success" @click="handleBatchDispatch" :disabled="selectedTasks.length === 0">
              <el-icon><Promotion /></el-icon>批量下发 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="['completed'].includes(taskStatusFilter)" type="info" @click="handleBatchGenerateOrder" :disabled="selectedTasks.length === 0">
              <el-icon><Printer /></el-icon>批量生成检验单 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="taskStatusFilter === 'processing'" type="warning" @click="handleBatchComplete" :disabled="selectedTasks.length === 0">
              <el-icon><Check /></el-icon>批量完成 ({{ selectedTasks.length }})
            </el-button>
          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.testTask"
            :data="filteredTaskData"
            border
            style="width: 100%"
            @selection-change="handleTaskSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="taskCode" label="任务编号" min-width="12rem" />
            <el-table-column prop="testIndicator" label="检测指标" min-width="12rem">
              <template #default="{ row }">
                <div>
                  <div class="font-medium">{{ row.testIndicator?.name || '-' }}</div>
                  <div class="text-xs text-gray-500">{{ row.testIndicator?.unit || '-' }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="assignedToName" label="检验员" min-width="10rem" />
            <el-table-column prop="priority" label="优先级" min-width="8rem">
              <template #default="{ row }">
                <el-tag :type="getPriorityColor(row.priority)">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dueDate" label="截止日期" min-width="12rem" />
            <el-table-column prop="isRecheck" label="任务类型" min-width="10rem">
              <template #default="{ row }">
                <el-tag v-if="row.isRecheck" type="warning" size="small">
                  复检任务
                </el-tag>
                <el-tag v-else type="info" size="small">
                  正常任务
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="10rem">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusColor(row.status)">
                  {{ getTaskStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="25rem" fixed="right">
              <template #default="{ row }">
                <el-button v-if="row.status === 'pending'" link type="primary" @click="handleDispatchTask(row)">
                  下发
                </el-button>
                <el-button v-if="row.status === 'processing'" link type="success" @click="handleCompleteTask(row)">
                  完成
                </el-button>
                <el-button v-if="['completed'].includes(row.status)" link type="info" @click="handleGenerateOrder(row)">
                  生成检验单
                </el-button>
                <el-button v-if="['completed'].includes(row.status) && !row.isRecheck" link type="warning" @click="handleCreateRecheck(row)">
                  复检
                </el-button>
                <el-button link type="info" @click="handleTaskDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.testTask.current"
              v-model:page-size="pagination.testTask.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.testTask.total"
              @size-change="(size: number) => handleSizeChange('testTask', size)"
              @current-change="(current: number) => handleCurrentChange('testTask', current)"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 检验单记录管理 -->
      <el-tab-pane label="检验单记录管理" name="testRecord">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.testRecord" class="search-form">

              <el-form-item label="记录编号">
                <el-input v-model="searchForm.testRecord.recordCode" placeholder="请输入记录编号" clearable />
              </el-form-item>
              <el-form-item label="检测任务">
                <el-input v-model="searchForm.testRecord.testId" placeholder="请输入任务ID" clearable />
              </el-form-item>
              <el-form-item label="检验日期">
                <el-date-picker
                  v-model="searchForm.testRecord.testDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('testRecord')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('testRecord')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading.testRecord"
            :data="tableData.testRecord"
            border
            style="width: 100%"
          >
            <el-table-column prop="recordCode" label="记录编号" min-width="12rem" />
            <el-table-column prop="testId" label="任务ID" min-width="8rem" />
            <el-table-column prop="testDate" label="检验日期" min-width="10rem" />
            <el-table-column prop="testPersonName" label="检验人员" min-width="10rem" />
            <el-table-column prop="indicatorName" label="检测指标" min-width="10rem" />
            <el-table-column prop="testValue" label="检测值" min-width="10rem">
              <template #default="{ row }">
                {{ row.testValue }} {{ row.unit || '' }}
              </template>
            </el-table-column>
            <el-table-column prop="fileUrl" label="检验单文件" min-width="12rem">
              <template #default="{ row }">
                <el-button v-if="row.fileUrl" link type="primary" @click="handlePreviewFile(row.fileUrl)">
                  <el-icon><Document /></el-icon>查看文件
                </el-button>
                <span v-else class="text-gray-400">暂无文件</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="10rem" fixed="right">
              <template #default="{ row }">
                <el-button link type="info" @click="handleRecordDetail(row)">
                  <el-icon><View /></el-icon>详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.testRecord.current"
              v-model:page-size="pagination.testRecord.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.testRecord.total"
              @size-change="(size: number) => handleSizeChange('testRecord', size)"
              @current-change="(current: number) => handleCurrentChange('testRecord', current)"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 数据录入与管理 -->
      <el-tab-pane label="数据录入与管理" name="dataEntry">
        <el-card shadow="hover">
          <!-- 操作栏 -->
          <div class="mb-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <el-button type="info" @click="handleImportFromTestRecord">
                <el-icon><Import /></el-icon>从检验单导入
              </el-button>
              <el-button @click="handleBatchExport" :disabled="selectedRows.length === 0">
                <el-icon><Download /></el-icon>批量导出 ({{ selectedRows.length }})
              </el-button>
            </div>
            <div class="status-summary">
              <div class="status-item">
                <span class="status-label">待录入</span>
                <span class="status-count pending">{{ getStatusCount('pending') }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">已录入</span>
                <span class="status-count entered">{{ getStatusCount('entered') }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">已审核</span>
                <span class="status-count reviewed">{{ getStatusCount('reviewed') }}</span>
              </div>
            </div>
          </div>

          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.dataEntry" class="search-form">

              <el-form-item label="数据编号">
                <el-input v-model="searchForm.dataEntry.dataCode" placeholder="请输入数据编号" clearable />
              </el-form-item>
              <el-form-item label="检测指标">
                <el-select style="min-width: 10rem;" v-model="searchForm.dataEntry.indicatorId" placeholder="请选择检测指标" clearable>
                  <el-option label="COD" :value="111" />
                  <el-option label="BOD5" :value="112" />
                  <el-option label="氨氮" :value="113" />
                  <el-option label="总磷" :value="114" />
                  <el-option label="总氮" :value="115" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select style="min-width: 8rem;" v-model="searchForm.dataEntry.status" placeholder="请选择状态" clearable>
                  <el-option label="待录入" value="pending" />
                  <el-option label="已录入" value="entered" />
                  <el-option label="已审核" value="reviewed" />
                </el-select>
              </el-form-item>
              <el-form-item label="检测日期">
                <el-date-picker
                  v-model="searchForm.dataEntry.testDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item label="检测人员">
                <el-select v-model="searchForm.dataEntry.testerId" placeholder="请选择检测人员" clearable style="min-width: 10rem;">
                  <el-option label="张三" :value="10" />
                  <el-option label="李四" :value="11" />
                  <el-option label="王五" :value="12" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('dataEntry')">
                  <Icon icon="ep:search" />搜索
                </el-button>
                <el-button @click="resetSearch('dataEntry')">
                  <Icon icon="ep:refresh" />重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.dataEntry"
            :data="tableData.dataEntry"
            border
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="dataCode" label="数据编号" min-width="12rem" />
            <el-table-column prop="indicatorName" label="检测指标" min-width="10rem" />
            <el-table-column prop="testValue" label="检测值" min-width="10rem">
              <template #default="{ row }">
                <span v-if="row.testValue !== null && row.testValue !== undefined">
                  {{ row.testValue }} {{ row.unit }}
                </span>
                <span v-else class="text-gray-400">待录入</span>
              </template>
            </el-table-column>
            <el-table-column prop="testDate" label="检测日期" min-width="10rem" />
            <el-table-column prop="testerName" label="检测人员" min-width="10rem" />
            <el-table-column prop="method" label="检测方法" min-width="12rem" show-overflow-tooltip />
            <el-table-column prop="instrument" label="检测仪器" min-width="12rem" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" min-width="8rem">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="10rem" fixed="right">
              <template #default="{ row }">
                <el-button v-if="row.status === 'pending'" link type="danger" @click="handleDeleteData(row)">
                  <el-icon><Delete /></el-icon>删除
                </el-button>
                <el-button link type="info" @click="handleViewData(row)">
                  <el-icon><View /></el-icon>详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.dataEntry.current"
              v-model:page-size="pagination.dataEntry.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.dataEntry.total"
              @size-change="(size: number) => handleSizeChange('dataEntry', size)"
              @current-change="(current: number) => handleCurrentChange('dataEntry', current)"
            />
          </div>
        </el-card>
      </el-tab-pane>


    </el-tabs>
  </ContentWrap>

  <!-- 数据录入对话框 -->


  <!-- 数据录入对话框 -->
  <DataEntryDialog
    ref="dataEntryDialogRef"
    @success="refreshTable('dataEntry')"
  />

  <!-- 数据详情对话框 -->
  <DataDetailDialog
    ref="dataDetailDialogRef"
  />

  <!-- 任务详情对话框 -->
  <TaskDetailDialog
    ref="taskDetailDialogRef"
  />

  <!-- 异常处理对话框 -->
  <ExceptionDialog
    ref="exceptionDialogRef"
    @success="refreshTable('dataEntry')"
  />

  <!-- 流程说明对话框 -->
  <ProcessFlowDialog
    ref="processFlowDialogRef"
  />

  <!-- 检验下发对话框 -->
  <TestDispatchDialog
    ref="testDispatchDialogRef"
  />

  <!-- 数据分析对话框 -->
  <DataAnalysisDialog
    ref="dataAnalysisDialogRef"
  />

  <!-- 数据对比对话框 -->
  <DataCompareDialog
    ref="dataCompareDialogRef"
  />

  <!-- 从检验报告创建任务对话框 -->
  <CreateTaskFromReportDialog
    ref="createTaskFromReportDialogRef"
    @success="handleCreateTaskSuccess"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { exportTestForm } from '@/utils/csvExport'
// 导入组件
import DataEntryDialog from './components/DataEntryDialog.vue'
import DataDetailDialog from './components/DataDetailDialog.vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import ExceptionDialog from './components/ExceptionDialog.vue'
import ProcessFlowDialog from './components/ProcessFlowDialog.vue'
import TestDispatchDialog from './components/TestDispatchDialog.vue'
import CreateTaskFromReportDialog from './components/CreateTaskFromReportDialog.vue'

defineOptions({ name: 'AssayDataManagement' })

// 当前激活的标签页
const activeTab = ref('testTask')

// 任务状态筛选器
const taskStatusFilter = ref('pending')

// 加载状态
const loading = reactive({
  testTask: false,
  dataEntry: false,
  testRecord: false
})

// 搜索表单
const searchForm = reactive({
  testTask: {
    taskCode: '',
    isRecheck: undefined as boolean | undefined,
    assignedToId: undefined as number | undefined,
    priority: '',
    createTimeRange: []
  },
  dataEntry: {
    dataCode: '',
    indicatorId: undefined as number | undefined,
    status: '',
    testDateRange: [],
    testerId: undefined as number | undefined
  },
  testRecord: {
    recordCode: '',
    testId: '',
    testDateRange: []
  }
})

// 选中的行
const selectedTasks = ref<any[]>([])
const selectedRows = ref<any[]>([])

// 分页配置
const pagination = reactive({
  testTask: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  dataEntry: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  testRecord: {
    current: 1,
    pageSize: 10,
    total: 0
  }
})

// 定义表格数据类型接口
interface TestTask {
  id: number;
  taskCode: string;
  submissionReportId?: number;
  submissionReportCode?: string;
  sampleCode: string;
  sampleId: number;
  testIndicators: string[]; // 检测指标列表
  assignedTo: string;
  priority: string;
  dueDate: string;
  specialRequirements?: string;
  status: string;
  acceptTime?: string;
  startTime?: string;
  completeTime?: string;
  actualDuration?: number;
  progress?: number;
  createTime: string;
}

interface DataEntry {
  id: number;
  sampleCode: string;
  testIndicator: string; // 改为检测指标
  indicatorId?: number; // 检测指标ID
  samplingPoint: string;
  samplingDate: string;
  status: string;
  priority?: string;
  // 从送检单/采样任务继承的字段
  sampleNature?: string; // 样品性质
  sampleQuantity?: number; // 样品数量
  submissionReportCode?: string; // 送检单编号
  testMethod?: string; // 检测方法
  testInstrument?: string; // 检测仪器
  testConditions?: string; // 检测条件
  // 检测指标相关字段
  unit?: string; // 数据单位
  standardRange?: string; // 标准范围
  equipment?: string; // 检测设备
}





// 表格数据
const tableData = reactive({
  testTask: [] as TestTask[],
  dataEntry: [] as DataEntry[],
  testRecord: [] as any[]
})

// 根据状态筛选的任务数据
const filteredTaskData = computed(() => {
  return tableData.testTask.filter(task => task.status === taskStatusFilter.value)
})

// 获取指定状态的任务数量
const getTaskCountByStatus = (status: string) => {
  return tableData.testTask.filter(task => task.status === status).length
}

// 对话框引用
const dataEntryDialogRef = ref()
const dataDetailDialogRef = ref()
const taskDetailDialogRef = ref()
const exceptionDialogRef = ref()
const processFlowDialogRef = ref()
const testDispatchDialogRef = ref()
const createTaskFromReportDialogRef = ref()

// 生命周期钩子
onMounted(() => {
  fetchTableData('testTask')
  fetchTableData('dataEntry')
  fetchTableData('testRecord')
  // 监听来自采样模块的样品送检事件
  window.addEventListener('sampleSentForTesting', handleSampleSentForTesting)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('sampleSentForTesting', handleSampleSentForTesting)
})

// 监听标签页切换
watch(activeTab, (newVal) => {
  if (['testTask', 'dataEntry', 'testRecord'].includes(newVal)) {
    fetchTableData(newVal)
  }
})

// 监听状态筛选器变化
watch(taskStatusFilter, () => {
  // 筛选数据已通过computed自动处理
})

// 获取状态统计
const getStatusCount = (status: string) => {
  return tableData.dataEntry.filter(item => item.status === status).length
}



// 处理任务表格选择变化
const handleTaskSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

// 处理数据录入表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 状态筛选器变化处理
const handleStatusFilterChange = (status: string) => {
  taskStatusFilter.value = status
}

// 新增的任务管理方法
const handleCreateTask = () => {
  ElMessage.info('新增任务功能开发中...')
}

const handleCreateRecheck = (row: any) => {
  ElMessageBox.prompt('请输入复检原因', '创建复检任务', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '复检原因不能为空'
  }).then(({ value }) => {
    ElMessage.success(`已为任务 ${row.taskCode} 创建复检任务，原因：${value}`)
    fetchTableData('testTask')
  }).catch(() => {})
}

const handleBatchDispatch = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要下发的任务')
    return
  }
  ElMessage.success(`已批量下发 ${selectedTasks.value.length} 个任务`)
}

const handleBatchGenerateOrder = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要生成检验单的任务')
    return
  }
  ElMessage.success(`已批量生成 ${selectedTasks.value.length} 个检验单`)
}

const handleBatchComplete = () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要完成的任务')
    return
  }
  ElMessage.success(`已批量完成 ${selectedTasks.value.length} 个任务`)
}

const handleDispatchTask = (row: any) => {
  // 创建弹窗表单
  ElMessageBox({
    title: '下发任务',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666;' }, `状态变更：待下发 → 执行中`),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '任务编号：'),
        h('span', row.taskCode)
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测指标：'),
        h('span', row.testIndicator?.name || '-')
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检验员：'),
        h('span', row.assignedToName || '-')
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '优先级：'),
        h('span', { style: `color: ${getPriorityColor(row.priority) === 'danger' ? '#f56c6c' : getPriorityColor(row.priority) === 'warning' ? '#e6a23c' : '#909399'}` },
          getPriorityText(row.priority))
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '截止日期：'),
        h('span', row.dueDate || '-')
      ]),
      h('p', { style: 'margin-top: 16px; color: #409eff;' }, '确认下发后，任务将分配给检验员开始执行')
    ]),
    showCancelButton: true,
    confirmButtonText: '确定下发',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true
  }).then(() => {
    row.status = 'processing'
    row.startTime = new Date().toISOString().split('T')[0]
    ElMessage.success(`任务 ${row.taskCode} 已下发`)
    fetchTableData('testTask')
  }).catch(() => {})
}

const handleCompleteTask = (row: any) => {
  // 创建检验结果输入弹窗
  let testValue = ''
  let testMethod = row.testIndicator?.method || ''
  let testInstrument = row.testIndicator?.equipment || ''
  let remark = ''

  ElMessageBox({
    title: '完成任务 - 录入检验结果',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666;' }, `状态变更：执行中 → 已完成`),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '任务编号：'),
        h('span', row.taskCode)
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测指标：'),
        h('span', `${row.testIndicator?.name || '-'} (${row.testIndicator?.unit || '-'})`)
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold; color: #f56c6c;' }, '*检测值：'),
        h('input', {
          type: 'number',
          placeholder: '请输入检测值',
          style: 'width: 200px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px;',
          oninput: (e: any) => { testValue = e.target.value }
        })
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测方法：'),
        h('input', {
          type: 'text',
          value: testMethod,
          placeholder: '请输入检测方法',
          style: 'width: 200px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px;',
          oninput: (e: any) => { testMethod = e.target.value }
        })
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测仪器：'),
        h('input', {
          type: 'text',
          value: testInstrument,
          placeholder: '请输入检测仪器',
          style: 'width: 200px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px;',
          oninput: (e: any) => { testInstrument = e.target.value }
        })
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '备注：'),
        h('textarea', {
          placeholder: '请输入备注信息',
          style: 'width: 200px; height: 60px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px; resize: vertical;',
          oninput: (e: any) => { remark = e.target.value }
        })
      ])
    ]),
    showCancelButton: true,
    confirmButtonText: '确定完成',
    cancelButtonText: '取消',
    type: 'success',
    dangerouslyUseHTMLString: true,
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        if (!testValue) {
          ElMessage.error('请输入检测值')
          return
        }
      }
      done()
    }
  }).then(() => {
    row.status = 'completed'
    row.completeTime = new Date().toISOString().split('T')[0]
    row.progress = 100
    // 保存检验结果
    row.testResult = {
      testValue: parseFloat(testValue),
      testMethod,
      testInstrument,
      remark
    }
    ElMessage.success(`任务 ${row.taskCode} 已完成，检验结果已保存`)
    fetchTableData('testTask')
  }).catch(() => {})
}

const handleGenerateOrder = (row: any) => {
  // 创建生成检验单弹窗，回显检验结果
  const testResult = row.testResult || {}

  ElMessageBox({
    title: '生成检验单',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666; font-weight: bold;' }, '检验单详细信息：'),

      h('div', { style: 'background: #f5f7fa; padding: 16px; border-radius: 4px; margin-bottom: 16px;' }, [
        h('h4', { style: 'margin: 0 0 12px 0; color: #303133;' }, '基本信息'),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '任务编号：'),
          h('span', row.taskCode)
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测指标：'),
          h('span', `${row.testIndicator?.name || '-'} (${row.testIndicator?.unit || '-'})`)
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检验员：'),
          h('span', row.assignedToName || '-')
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '完成时间：'),
          h('span', row.completeTime || '-')
        ])
      ]),

      h('div', { style: 'background: #f0f9ff; padding: 16px; border-radius: 4px; margin-bottom: 16px;' }, [
        h('h4', { style: 'margin: 0 0 12px 0; color: #303133;' }, '检验结果'),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测值：'),
          h('span', { style: 'color: #409eff; font-weight: bold;' },
            testResult.testValue ? `${testResult.testValue} ${row.testIndicator?.unit || ''}` : '未录入')
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测方法：'),
          h('span', testResult.testMethod || row.testIndicator?.method || '-')
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测仪器：'),
          h('span', testResult.testInstrument || row.testIndicator?.equipment || '-')
        ]),
        h('div', [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '备注：'),
          h('span', testResult.remark || '-')
        ])
      ]),

      h('div', { style: 'background: #fef0f0; padding: 16px; border-radius: 4px;' }, [
        h('h4', { style: 'margin: 0 0 12px 0; color: #303133;' }, 'Excel文件将包含'),
        h('ul', { style: 'margin: 0; padding-left: 20px; color: #666;' }, [
          h('li', '任务基本信息（编号、指标、检验员等）'),
          h('li', '检测数据详情（检测值、单位、方法、仪器）'),
          h('li', '检验过程记录和备注信息'),
          h('li', '质量控制和审核信息')
        ])
      ])
    ]),
    showCancelButton: true,
    confirmButtonText: '确认生成并下载',
    cancelButtonText: '取消',
    type: 'info',
    dangerouslyUseHTMLString: true
  }).then(() => {
    ElMessage.success(`检验单生成成功，正在下载...`)
    // 这里后端会处理下载逻辑
  }).catch(() => {})
}

const handleTaskDetail = (row: any) => {
  taskDetailDialogRef.value?.open(row)
}

// 优先级相关方法
const getPriorityColor = (priority: string) => {
  const colorMap = {
    'low': 'info',
    'normal': 'primary',
    'high': 'warning',
    'urgent': 'danger'
  }
  return colorMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    'low': '低',
    'normal': '正常',
    'high': '高',
    'urgent': '紧急'
  }
  return textMap[priority] || priority
}

// 任务状态相关方法
const getTaskStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return colorMap[status] || 'info'
}

const getTaskStatusText = (status: string) => {
  const textMap = {
    'pending': '待下发',
    'processing': '执行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}



// 处理来自采样模块的样品送检事件
const handleSampleSentForTesting = (event: CustomEvent) => {
  const { sampleCode, sampleId, testIndicators } = event.detail

  // 为每个检测指标创建检测数据记录
  const newTestDataRecords = testIndicators.map((testIndicator: string, index: number) => ({
    id: Date.now() + index,
    sampleCode: sampleCode,
    sampleId: sampleId,
    testIndicator: testIndicator,
    samplingPoint: getSamplingPointBySampleCode(sampleCode),
    samplingDate: getSamplingDateBySampleCode(sampleCode),
    status: 'pending',
    priority: 'normal',
    // 从送检单继承的字段
    sampleNature: getSampleNatureBySampleCode(sampleCode),
    sampleQuantity: getSampleQuantityBySampleCode(sampleCode),
    submissionReportCode: generateSubmissionReportCode(sampleCode),
    testMethod: getTestMethodByItem(testIndicator),
    testInstrument: getTestInstrumentByItem(testIndicator),
    testConditions: getTestConditionsByItem(testIndicator),
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }))

  // 添加到数据录入表格
  tableData.dataEntry.unshift(...newTestDataRecords)
  pagination.dataEntry.total += newTestDataRecords.length

  ElMessage.success(`已接收样品 ${sampleCode} 的 ${testItems.length} 个检测项目`)

  // 刷新当前表格
  refreshTable('dataEntry')
}

// 根据样品编号获取采样点（模拟函数）
const getSamplingPointBySampleCode = (sampleCode: string): string => {
  // 实际项目中应该通过API查询
  const pointMap = {
    'SP20230715': '进水总口',
    'SP20230716': '出水总口',
    'SP20230717': '生化池',
    'SP20230718': '出水总口',
    'SP20230720': '进水总口'
  }
  const prefix = sampleCode.slice(0, 11)
  return pointMap[prefix] || '未知采样点'
}

// 根据样品编号获取采样日期（模拟函数）
const getSamplingDateBySampleCode = (sampleCode: string): string => {
  // 从样品编号中提取日期
  const dateStr = sampleCode.slice(2, 10) // SP20230715001 -> 20230715
  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
}

// 根据样品编号获取样品性质（模拟函数）
const getSampleNatureBySampleCode = (sampleCode: string): string => {
  // 根据样品编号前缀判断样品性质
  if (sampleCode.includes('SP')) return '液体'
  if (sampleCode.includes('SS')) return '半固体'
  if (sampleCode.includes('SG')) return '气体'
  return '液体' // 默认
}

// 根据样品编号获取样品数量（模拟函数）
const getSampleQuantityBySampleCode = (sampleCode: string): number => {
  // 根据样品编号后缀判断数量
  const suffix = sampleCode.slice(-3)
  return parseInt(suffix) % 3 + 1 // 1-3个样品
}

// 生成送检单编号（模拟函数）
const generateSubmissionReportCode = (sampleCode: string): string => {
  return sampleCode.replace('SP', 'SR')
}

// 根据检测项目获取检测方法（模拟函数）
const getTestMethodByItem = (testItem: string): string => {
  const methodMap: Record<string, string> = {
    'COD': '重铬酸钾法',
    'BOD5': '稀释接种法',
    '氨氮': '纳氏试剂分光光度法',
    '总磷': '钼酸铵分光光度法',
    '总氮': '碱性过硫酸钾消解紫外分光光度法',
    'pH': '玻璃电极法',
    'SS': '重量法'
  }
  return methodMap[testItem] || '标准方法'
}

// 根据检测项目获取检测仪器（模拟函数）
const getTestInstrumentByItem = (testItem: string): string => {
  const instrumentMap: Record<string, string> = {
    'COD': 'COD测定仪',
    'BOD5': 'BOD培养箱',
    '氨氮': '分光光度计',
    '总磷': '分光光度计',
    '总氮': '紫外分光光度计',
    'pH': 'pH计',
    'SS': '分析天平'
  }
  return instrumentMap[testItem] || '通用仪器'
}

// 根据检测项目获取检测条件（模拟函数）
const getTestConditionsByItem = (testItem: string): string => {
  const conditionsMap: Record<string, string> = {
    'COD': '常温常压',
    'BOD5': '20℃±1℃，5天培养',
    '氨氮': '常温，避光',
    '总磷': '常温，消解后测定',
    '总氮': '高温高压消解',
    'pH': '常温',
    'SS': '105℃烘干'
  }
  return conditionsMap[testItem] || '标准条件'
}

// 数据录入相关方法
const handleCreateData = () => {
  ElMessage.info('录入数据功能开发中...')
}

const handleImportFromSubmission = () => {
  // 弹窗显示当前服务器文件夹下的所有送检单文件
  ElMessageBox({
    title: '从送检单导入任务',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666;' }, '请选择要导入的送检单文件：'),
      h('div', { style: 'max-height: 300px; overflow-y: auto; border: 1px solid #dcdfe6; border-radius: 4px; padding: 8px;' }, [
        h('div', { style: 'padding: 8px; border-bottom: 1px solid #f0f0f0; cursor: pointer;', onclick: () => {} }, [
          h('i', { class: 'el-icon-document', style: 'margin-right: 8px; color: #409eff;' }),
          h('span', 'SR20240115001_进水水质检测送检单.xlsx')
        ]),
        h('div', { style: 'padding: 8px; border-bottom: 1px solid #f0f0f0; cursor: pointer;', onclick: () => {} }, [
          h('i', { class: 'el-icon-document', style: 'margin-right: 8px; color: #409eff;' }),
          h('span', 'SR20240115002_出水水质检测送检单.xlsx')
        ]),
        h('div', { style: 'padding: 8px; border-bottom: 1px solid #f0f0f0; cursor: pointer;', onclick: () => {} }, [
          h('i', { class: 'el-icon-document', style: 'margin-right: 8px; color: #409eff;' }),
          h('span', 'SR20240116001_污泥检测送检单.xlsx')
        ]),
        h('div', { style: 'padding: 8px; cursor: pointer;', onclick: () => {} }, [
          h('i', { class: 'el-icon-document', style: 'margin-right: 8px; color: #409eff;' }),
          h('span', 'SR20240120001_重金属检测送检单.xlsx')
        ])
      ])
    ]),
    showCancelButton: true,
    confirmButtonText: '导入选中文件',
    cancelButtonText: '取消',
    dangerouslyUseHTMLString: true
  }).then(() => {
    ElMessage.success('送检单导入成功，已生成检验任务')
    fetchTableData('testTask')
  }).catch(() => {})
}

const handleImportFromTestRecord = () => {
  ElMessage.info('从检验单导入功能开发中...')
}

const handleEditData = (row: any) => {
  ElMessage.info(`编辑数据 ${row.dataCode} 功能开发中...`)
}

const handleDeleteData = (row: any) => {
  ElMessageBox.confirm(`确认删除数据 "${row.dataCode}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`数据 ${row.dataCode} 已删除`)
    fetchTableData('dataEntry')
  }).catch(() => {})
}

// 检验单记录相关方法
const handleCreateRecord = () => {
  ElMessage.info('新增记录功能开发中...')
}

const handleUploadFile = (row: any) => {
  ElMessage.info(`上传文件功能开发中...`)
}

const handlePreviewFile = (fileUrl: string) => {
  window.open(fileUrl, '_blank')
}

const handleRecordDetail = (row: any) => {
  ElMessage.info(`查看记录详情功能开发中...`)
}

// 获取状态类型和文本
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending': return 'info'
    case 'entered': return 'primary'
    case 'reviewed': return 'success'
    case 'rejected': return 'danger'
    case 'exception': return 'danger'
    case 'completed': return 'success'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待录入'
    case 'entered': return '已录入'
    case 'reviewed': return '已审核'
    case 'rejected': return '已驳回'
    case 'exception': return '异常'
    case 'completed': return '已完成'
    default: return '未知'
  }
}

// 获取表格数据
const fetchTableData = async (type: string) => {
  loading[type] = true
  try {
    // 模拟异步请求
    setTimeout(() => {
      if (type === 'dataEntry') {
        // 根据API文档更新的数据录入数据结构
        const baseData = [
          {
            id: 1,
            factoryId: 1,
            dataCode: 'TD20240115001',
            taskId: 1,
            indicatorId: 111,
            indicatorName: 'COD',
            testValue: 45.6,
            unit: 'mg/L',
            testDate: '2024-01-15',
            testerId: 10,
            testerName: '李四',
            method: '重铬酸钾法',
            instrument: 'COD分析仪',
            status: 'entered',
            remark: '检测过程正常',
            createTime: '2024-01-15 10:30:00',
            updateTime: '2024-01-15 15:30:00'
          },
          {
            id: 2,
            factoryId: 1,
            dataCode: 'TD20240115002',
            taskId: 2,
            indicatorId: 112,
            indicatorName: 'BOD5',
            testValue: null,
            unit: 'mg/L',
            testDate: '2024-01-15',
            testerId: 11,
            testerName: '王五',
            method: '稀释接种法',
            instrument: 'BOD测定仪',
            status: 'pending',
            remark: '',
            createTime: '2024-01-15 10:30:00',
            updateTime: '2024-01-15 10:30:00'
          },
          {
            id: 3,
            factoryId: 1,
            dataCode: 'TD20240115003',
            taskId: 3,
            indicatorId: 113,
            indicatorName: '氨氮',
            testValue: 3.2,
            unit: 'mg/L',
            testDate: '2024-01-15',
            testerId: 10,
            testerName: '李四',
            method: '纳氏试剂比色法',
            instrument: '分光光度计',
            status: 'reviewed',
            remark: '检测完成，数据正常',
            createTime: '2024-01-15 11:00:00',
            updateTime: '2024-01-15 16:00:00'
          },
          {
            id: 4,
            factoryId: 2,
            dataCode: 'TD20240116001',
            taskId: 4,
            indicatorId: 114,
            indicatorName: '总磷',
            testValue: 0.8,
            unit: 'mg/L',
            testDate: '2024-01-16',
            testerId: 12,
            testerName: '张三',
            method: '钼酸铵分光光度法',
            instrument: '分光光度计',
            status: 'entered',
            remark: '检测正常',
            createTime: '2024-01-16 09:00:00',
            updateTime: '2024-01-16 14:00:00'
          },
          {
            id: 5,
            factoryId: 2,
            dataCode: 'TD20240116002',
            taskId: 5,
            indicatorId: 115,
            indicatorName: '总氮',
            testValue: null,
            unit: 'mg/L',
            testDate: '2024-01-16',
            testerId: 11,
            testerName: '王五',
            method: '碱性过硫酸钾消解紫外分光光度法',
            instrument: '紫外分光光度计',
            status: 'pending',
            remark: '',
            createTime: '2024-01-16 09:00:00',
            updateTime: '2024-01-16 09:00:00'
          }
        ]

        tableData.dataEntry = baseData
        pagination.dataEntry.total = baseData.length
      } else if (type === 'testRecord') {
        // 检验单记录数据
        tableData.testRecord = [
          {
            id: 1,
            factoryId: 1,
            recordCode: 'TR20240115001',
            testId: 1,
            testDate: '2024-01-15',
            testPersonId: 10,
            testPersonName: '李四',
            testIndicator: 111,
            indicatorName: 'COD',
            testValue: 45.6,
            unit: 'mg/L',
            fileUrl: '/files/test-records/TR20240115001.pdf',
            createTime: '2024-01-15 16:00:00',
            updateTime: '2024-01-15 16:00:00'
          },
          {
            id: 2,
            factoryId: 1,
            recordCode: 'TR20240115002',
            testId: 3,
            testDate: '2024-01-15',
            testPersonId: 10,
            testPersonName: '李四',
            testIndicator: 113,
            indicatorName: '氨氮',
            testValue: 3.2,
            unit: 'mg/L',
            fileUrl: null,
            createTime: '2024-01-15 17:00:00',
            updateTime: '2024-01-15 17:00:00'
          }
        ]
        pagination.testRecord.total = tableData.testRecord.length
      } else if (type === 'testTask') {
        // 根据API文档更新的检验任务数据结构
        tableData.testTask = [
          {
            id: 1,
            taskCode: 'TT20240115001',
            factoryId: 1,
            sampleId: 1,
            testIndicator: {
              id: 111,
              name: 'COD',
              unit: 'mg/L',
              method: '重铬酸钾法',
              equipment: 'COD分析仪'
            },
            assignedToId: 10,
            assignedToName: '李四',
            priority: 'high',
            dueDate: '2024-01-18',
            status: 'completed',
            isRecheck: false,
            parentTaskId: null,
            recheckReason: null,
            acceptTime: '2024-01-15 09:00:00',
            startTime: '2024-01-15 09:30:00',
            completeTime: '2024-01-16 16:00:00',
            remark: '检验任务正常完成',
            createTime: '2024-01-15 08:30:00',
            updateTime: '2024-01-16 16:00:00'
          },
          {
            id: 2,
            taskCode: 'TT20240115002',
            factoryId: 1,
            sampleId: 2,
            testIndicator: {
              id: 112,
              name: 'BOD5',
              unit: 'mg/L',
              method: '稀释接种法',
              equipment: 'BOD测定仪'
            },
            assignedToId: 11,
            assignedToName: '王五',
            priority: 'high',
            dueDate: '2024-01-18',
            status: 'processing',
            isRecheck: false,
            parentTaskId: null,
            recheckReason: null,
            acceptTime: '2024-01-15 10:00:00',
            startTime: '2024-01-16 09:00:00',
            completeTime: null,
            remark: '任务进行中',
            createTime: '2024-01-15 09:00:00',
            updateTime: '2024-01-16 09:00:00'
          },
          {
            id: 3,
            taskCode: 'TT20240116001',
            factoryId: 2,
            sampleId: 3,
            testIndicator: {
              id: 113,
              name: '氨氮',
              unit: 'mg/L',
              method: '纳氏试剂比色法',
              equipment: '分光光度计'
            },
            assignedToId: 12,
            assignedToName: '张三',
            priority: 'normal',
            dueDate: '2024-01-19',
            status: 'pending',
            isRecheck: false,
            parentTaskId: null,
            recheckReason: null,
            acceptTime: null,
            startTime: null,
            completeTime: null,
            remark: '',
            createTime: '2024-01-16 08:00:00',
            updateTime: '2024-01-16 08:00:00'
          },

          {
            id: 5,
            taskCode: 'TT20240115004',
            factoryId: 1,
            sampleId: 1,
            testIndicator: {
              id: 111,
              name: 'COD',
              unit: 'mg/L',
              method: '重铬酸钾法',
              equipment: 'COD分析仪'
            },
            assignedToId: 10,
            assignedToName: '李四',
            priority: 'high',
            dueDate: '2024-01-20',
            status: 'completed',
            isRecheck: true,
            parentTaskId: 1,
            recheckReason: '数据异常，需要重新检测',
            acceptTime: '2024-01-17 09:00:00',
            startTime: '2024-01-17 10:00:00',
            completeTime: '2024-01-17 16:00:00',
            remark: '复检任务完成，数据正常',
            createTime: '2024-01-17 08:30:00',
            updateTime: '2024-01-17 16:00:00'
          }
        ]
        pagination.testTask.total = tableData.testTask.length

      }
      loading[type] = false
    }, 500)
  } catch (error) {
    console.error(`获取${type}数据失败:`, error)
    loading[type] = false
  }
}

// 加载来自采样模块的待检测数据
const loadPendingTestData = () => {
  try {
    const pendingData = localStorage.getItem('pendingTestData')
    if (pendingData) {
      const testDataRecords = JSON.parse(pendingData)
      // 清除已加载的数据，避免重复
      localStorage.removeItem('pendingTestData')
      return testDataRecords
    }
    return []
  } catch (error) {
    console.error('加载待检测数据失败:', error)
    return []
  }
}

// 更新样品状态到采样模块
const updateSampleStatus = (sampleCode: string, status: string) => {
  // 通过事件通知采样模块更新样品状态
  window.dispatchEvent(new CustomEvent('testStatusUpdated', {
    detail: {
      sampleCode: sampleCode,
      status: status,
      updateTime: new Date().toISOString()
    }
  }))
}





const showProcessFlow = () => {
  processFlowDialogRef.value?.open()
}



const handleBatchSubmit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要提交的数据')
    return
  }
  ElMessage.success(`已批量提交 ${selectedRows.value.length} 条数据`)
}

const handleBatchExport = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }
  ElMessage.success(`已批量导出 ${selectedRows.value.length} 条数据`)
}

// 刷新表格
const refreshTable = (type: string) => {
  fetchTableData(type)
}

// 数据录入
const handleDataEntry = (row: any) => {
  dataEntryDialogRef.value?.open(row)
}

// 提交审核
const handleSubmitForReview = (row: any) => {
  ElMessageBox.confirm(`确定要提交样品${row.sampleCode}的检测数据进行审核吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    setTimeout(() => {
      row.status = 'reviewing'
      row.updateTime = new Date().toISOString()

      // 同步状态到采样模块
      updateSampleStatus(row.sampleCode, 'testing')

      ElMessage.success('数据已提交审核')
      refreshTable('dataEntry')
    }, 500)
  }).catch(() => {})
}

// 重新提交
const handleResubmit = (row: any) => {
  dataEntryDialogRef.value?.open(row, true)
}

// 查看数据
const handleViewData = (row: any) => {
  dataDetailDialogRef.value?.open(row)
}

// 处理异常
const handleException = (row: any) => {
  exceptionDialogRef.value?.open(row)
}



// 从检验报告创建任务
const handleCreateTaskFromReport = () => {
  createTaskFromReportDialogRef.value?.open()
}

// 处理创建任务成功
const handleCreateTaskSuccess = (tasks: any[]) => {
  // 将新创建的任务添加到任务列表
  tableData.testTask.unshift(...tasks)
  pagination.testTask.total = tableData.testTask.length

  ElMessage.success(`成功创建${tasks.length}个检验任务`)
}



// 数据录入相关方法
const handleImportFromSubmissionReports = () => {
  // 从采样模块的送检单导入数据
  ElMessage.info('从送检单导入数据功能开发中')

  // 模拟从送检单导入数据
  const mockImportData = [
    {
      id: tableData.dataEntry.length + 1,
      sampleCode: 'SP20230720001',
      testIndicator: 'COD',
      indicatorId: 111,
      samplingPoint: '进水总口',
      samplingDate: '2023-07-20',
      status: 'pending',
      priority: 'normal',
      unit: 'mg/L',
      standardRange: '≤50mg/L',
      equipment: 'COD分析仪'
    },
    {
      id: tableData.dataEntry.length + 2,
      sampleCode: 'SP20230720001',
      testIndicator: '氨氮',
      indicatorId: 113,
      samplingPoint: '进水总口',
      samplingDate: '2023-07-20',
      status: 'pending',
      priority: 'normal',
      unit: 'mg/L',
      standardRange: '≤5mg/L',
      equipment: '分光光度计'
    }
  ]

  mockImportData.forEach(item => {
    tableData.dataEntry.unshift(item)
  })

  pagination.dataEntry.total = tableData.dataEntry.length
  ElMessage.success(`成功从送检单导入 ${mockImportData.length} 条数据`)
}



// ==================== 搜索和分页相关方法 ====================

// 搜索
const handleSearch = (type: string) => {
  pagination[type].current = 1
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: string) => {
  const formKey = type as keyof typeof searchForm
  Object.keys(searchForm[formKey]).forEach(key => {
    if (Array.isArray(searchForm[formKey][key])) {
      searchForm[formKey][key] = []
    } else {
      searchForm[formKey][key] = ''
    }
  })
  fetchTableData(type)
}

// 分页大小变化
const handleSizeChange = (type: string, size: number) => {
  pagination[type].pageSize = size
  pagination[type].current = 1
  fetchTableData(type)
}

// 当前页变化
const handleCurrentChange = (type: string, current: number) => {
  pagination[type].current = current
  fetchTableData(type)
}






</script>

<style scoped>
.search-form {
  margin-bottom: 1rem;
}

.status-summary {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.status-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.status-count {
  font-size: 14px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  min-width: 20px;
  text-align: center;
}

.status-count.pending {
  background: #e1f3ff;
  color: #409eff;
}

.status-count.entered {
  background: #f0f9ff;
  color: #67c23a;
}

.status-count.exception {
  background: #fef0f0;
  color: #f56c6c;
}

.status-count.imported {
  background: #f0f9ff;
  color: #67c23a;
}

.status-count.archived {
  background: #f5f7fa;
  color: #909399;
}

.upload-container {
  padding: 1rem;
}

.upload-area {
  width: 100%;
  margin-bottom: 1rem;
}

.import-form {
  margin: 1rem 0;
}

.import-history {
  margin-top: 1rem;
}

.import-history h3 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 500;
}
</style>

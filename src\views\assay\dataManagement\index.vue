<template>
  <ContentWrap title="检验执行管理">
    <el-tabs v-model="activeTab">
      <!-- 检验任务管理 -->
      <el-tab-pane label="检验任务管理" name="testTask">
        <el-card shadow="hover">
          <!-- 状态筛选器 -->
          <div class="mb-4">
            <el-tabs v-model="taskStatusFilter" type="card" @tab-change="handleStatusFilterChange">
              <el-tab-pane label="待下发" name="pending">
                <template #label>
                  <span>待下发 <el-badge :value="getTaskCountByStatus('pending')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="执行中" name="processing">
                <template #label>
                  <span>执行中 <el-badge :value="getTaskCountByStatus('processing')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
              <el-tab-pane label="已完成" name="completed">
                <template #label>
                  <span>已完成 <el-badge :value="getTaskCountByStatus('completed')" class="ml-1" /></span>
                </template>
              </el-tab-pane>
            </el-tabs>
          </div>

          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.testTask" class="search-form">

              <el-form-item label="任务编号">
                <el-input v-model="searchForm.testTask.taskCode" placeholder="请输入任务编号" clearable />
              </el-form-item>
              <el-form-item label="复检筛选">
                <el-select v-model="searchForm.testTask.isRecheck" placeholder="请选择" clearable style="min-width: 8rem;">
                  <el-option label="正常任务" :value="false" />
                  <el-option label="复检任务" :value="true" />
                </el-select>
              </el-form-item>
              <el-form-item label="检验员">
                <el-select v-model="searchForm.testTask.assignedToId" placeholder="请选择检验员" clearable style="min-width: 10rem;">
                  <el-option label="张三" :value="10" />
                  <el-option label="李四" :value="11" />
                  <el-option label="王五" :value="12" />
                </el-select>
              </el-form-item>
              <el-form-item label="优先级">
                <el-select v-model="searchForm.testTask.priority" placeholder="请选择优先级" clearable style="min-width: 8rem;">
                  <el-option label="低" value="low" />
                  <el-option label="正常" value="normal" />
                  <el-option label="高" value="high" />
                  <el-option label="紧急" value="urgent" />
                </el-select>
              </el-form-item>
              <el-form-item label="创建日期">
                <el-date-picker
                  v-model="searchForm.testTask.createTimeRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('testTask')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('testTask')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>


          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button type="primary" @click="handleImportFromSubmission">
              <el-icon><Import /></el-icon>从送检单导入任务
            </el-button>
            <el-button v-if="taskStatusFilter === 'pending'" type="success" @click="handleBatchDispatch" :disabled="selectedTasks.length === 0">
              <el-icon><Promotion /></el-icon>批量下发 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="['completed'].includes(taskStatusFilter)" type="info" @click="handleBatchGenerateOrder" :disabled="selectedTasks.length === 0">
              <el-icon><Printer /></el-icon>批量生成检验单 ({{ selectedTasks.length }})
            </el-button>
            <el-button v-if="taskStatusFilter === 'processing'" type="warning" @click="handleBatchComplete" :disabled="selectedTasks.length === 0">
              <el-icon><Check /></el-icon>批量完成 ({{ selectedTasks.length }})
            </el-button>
          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.testTask"
            :data="filteredTaskData"
            border
            style="width: 100%"
            @selection-change="handleTaskSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="taskCode" label="任务编号" min-width="12rem" />
            <el-table-column prop="testIndicator" label="检测指标" min-width="12rem">
              <template #default="{ row }">
                <div>
                  <div class="font-medium">{{ row.testIndicator?.name || '-' }}</div>
                  <div class="text-xs text-gray-500">{{ row.testIndicator?.unit || '-' }}</div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="assignedToName" label="检验员" min-width="10rem" />
            <el-table-column prop="priority" label="优先级" min-width="8rem">
              <template #default="{ row }">
                <el-tag :type="getPriorityColor(row.priority)">
                  {{ getPriorityText(row.priority) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="dueDate" label="截止日期" min-width="12rem" />
            <el-table-column prop="isRecheck" label="任务类型" min-width="10rem">
              <template #default="{ row }">
                <el-tag v-if="row.isRecheck" type="warning" size="small">
                  复检任务
                </el-tag>
                <el-tag v-else type="info" size="small">
                  正常任务
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="status" label="状态" min-width="10rem">
              <template #default="{ row }">
                <el-tag :type="getTaskStatusColor(row.status)">
                  {{ getTaskStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="25rem" fixed="right">
              <template #default="{ row }">
                <el-button v-if="row.status === 'pending'" link type="primary" @click="handleDispatchTask(row)">
                  下发
                </el-button>
                <el-button v-if="row.status === 'processing'" link type="success" @click="handleCompleteTask(row)">
                  完成
                </el-button>
                <el-button v-if="['completed'].includes(row.status)" link type="info" @click="handleGenerateOrder(row)">
                  生成检验单
                </el-button>
                <el-button v-if="['completed'].includes(row.status) && !row.isRecheck" link type="warning" @click="handleCreateRecheck(row)">
                  复检
                </el-button>
                <el-button link type="info" @click="handleTaskDetail(row)">
                  详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- 分页 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.testTask.current"
              v-model:page-size="pagination.testTask.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.testTask.total"
              @size-change="(size: number) => handleSizeChange('testTask', size)"
              @current-change="(current: number) => handleCurrentChange('testTask', current)"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 检验单记录管理 -->
      <el-tab-pane label="检验单记录管理" name="testRecord">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.testRecord" class="search-form">

              <el-form-item label="记录编号">
                <el-input v-model="searchForm.testRecord.recordCode" placeholder="请输入记录编号" clearable />
              </el-form-item>
              <el-form-item label="检测任务">
                <el-input v-model="searchForm.testRecord.testId" placeholder="请输入任务ID" clearable />
              </el-form-item>
              <el-form-item label="检验日期">
                <el-date-picker
                  v-model="searchForm.testRecord.testDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item>
                <el-button-group>
                  <el-button type="primary" @click="handleSearch('testRecord')">
                    <Icon icon="ep:search" />搜索
                  </el-button>
                  <el-button @click="resetSearch('testRecord')">
                    <Icon icon="ep:refresh" />重置
                  </el-button>
                </el-button-group>
              </el-form-item>
            </el-form>
          </div>

          <!-- 表格 -->
          <el-table
            v-loading="loading.testRecord"
            :data="tableData.testRecord"
            border
            style="width: 100%"
          >
            <el-table-column prop="recordCode" label="记录编号" min-width="12rem" />
            <el-table-column prop="testId" label="任务ID" min-width="8rem" />
            <el-table-column prop="testDate" label="检验日期" min-width="10rem" />
            <el-table-column prop="testPersonName" label="检验人员" min-width="10rem" />
            <el-table-column prop="indicatorName" label="检测指标" min-width="10rem" />
            <el-table-column prop="testValue" label="检测值" min-width="10rem">
              <template #default="{ row }">
                {{ row.testValue }} {{ row.unit || '' }}
              </template>
            </el-table-column>
            <el-table-column prop="fileUrl" label="检验单文件" min-width="12rem">
              <template #default="{ row }">
                <el-button v-if="row.fileUrl" link type="primary" @click="handlePreviewFile(row.fileUrl)">
                  <el-icon><Document /></el-icon>查看文件
                </el-button>
                <span v-else class="text-gray-400">暂无文件</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="10rem" fixed="right">
              <template #default="{ row }">
                <el-button link type="info" @click="handleRecordDetail(row)">
                  <el-icon><View /></el-icon>详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>

          <!-- 分页 -->
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.testRecord.current"
              v-model:page-size="pagination.testRecord.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.testRecord.total"
              @size-change="(size: number) => handleSizeChange('testRecord', size)"
              @current-change="(current: number) => handleCurrentChange('testRecord', current)"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 数据录入与管理 -->
      <el-tab-pane label="数据录入与管理" name="dataEntry">
        <el-card shadow="hover">
          <!-- 操作栏 -->
          <div class="mb-4 flex justify-between items-center">
            <div class="flex items-center space-x-2">
              <el-button type="info" @click="handleImportFromTestRecord">
                <el-icon><Import /></el-icon>从检验单导入
              </el-button>
              <el-button @click="handleBatchExport" :disabled="selectedRows.length === 0">
                <el-icon><Download /></el-icon>批量导出 ({{ selectedRows.length }})
              </el-button>
            </div>
            <div class="status-summary">
              <div class="status-item">
                <span class="status-label">待录入</span>
                <span class="status-count pending">{{ getStatusCount('pending') }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">已录入</span>
                <span class="status-count entered">{{ getStatusCount('entered') }}</span>
              </div>
              <div class="status-item">
                <span class="status-label">已审核</span>
                <span class="status-count reviewed">{{ getStatusCount('reviewed') }}</span>
              </div>
            </div>
          </div>

          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.dataEntry" class="search-form">

              <el-form-item label="数据编号">
                <el-input v-model="searchForm.dataEntry.dataCode" placeholder="请输入数据编号" clearable />
              </el-form-item>
              <el-form-item label="检测指标">
                <el-select style="min-width: 10rem;" v-model="searchForm.dataEntry.indicatorId" placeholder="请选择检测指标" clearable>
                  <el-option label="COD" :value="111" />
                  <el-option label="BOD5" :value="112" />
                  <el-option label="氨氮" :value="113" />
                  <el-option label="总磷" :value="114" />
                  <el-option label="总氮" :value="115" />
                </el-select>
              </el-form-item>
              <el-form-item label="状态">
                <el-select style="min-width: 8rem;" v-model="searchForm.dataEntry.status" placeholder="请选择状态" clearable>
                  <el-option label="待录入" value="pending" />
                  <el-option label="已录入" value="entered" />
                  <el-option label="已审核" value="reviewed" />
                </el-select>
              </el-form-item>
              <el-form-item label="检测日期">
                <el-date-picker
                  v-model="searchForm.dataEntry.testDateRange"
                  type="daterange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                />
              </el-form-item>
              <el-form-item label="检测人员">
                <el-select v-model="searchForm.dataEntry.testerId" placeholder="请选择检测人员" clearable style="min-width: 10rem;">
                  <el-option label="张三" :value="10" />
                  <el-option label="李四" :value="11" />
                  <el-option label="王五" :value="12" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('dataEntry')">
                  <Icon icon="ep:search" />搜索
                </el-button>
                <el-button @click="resetSearch('dataEntry')">
                  <Icon icon="ep:refresh" />重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格 -->
          <el-table
            v-loading="loading.dataEntry"
            :data="tableData.dataEntry"
            border
            style="width: 100%"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" />
            <el-table-column prop="dataCode" label="数据编号" min-width="12rem" />
            <el-table-column prop="indicatorName" label="检测指标" min-width="10rem" />
            <el-table-column prop="testValue" label="检测值" min-width="10rem">
              <template #default="{ row }">
                <span v-if="row.testValue !== null && row.testValue !== undefined">
                  {{ row.testValue }} {{ row.unit }}
                </span>
                <span v-else class="text-gray-400">待录入</span>
              </template>
            </el-table-column>
            <el-table-column prop="testDate" label="检测日期" min-width="10rem" />
            <el-table-column prop="testerName" label="检测人员" min-width="10rem" />
            <el-table-column prop="method" label="检测方法" min-width="12rem" show-overflow-tooltip />
            <el-table-column prop="instrument" label="检测仪器" min-width="12rem" show-overflow-tooltip />
            <el-table-column prop="status" label="状态" min-width="8rem">
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="10rem" fixed="right">
              <template #default="{ row }">
                <el-button v-if="row.status === 'pending'" link type="danger" @click="handleDeleteData(row)">
                  <el-icon><Delete /></el-icon>删除
                </el-button>
                <el-button link type="info" @click="handleViewData(row)">
                  <el-icon><View /></el-icon>详情
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.dataEntry.current"
              v-model:page-size="pagination.dataEntry.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.dataEntry.total"
              @size-change="(size: number) => handleSizeChange('dataEntry', size)"
              @current-change="(current: number) => handleCurrentChange('dataEntry', current)"
            />
          </div>
        </el-card>
      </el-tab-pane>


    </el-tabs>
  </ContentWrap>

  <!-- 数据录入对话框 -->


  <!-- 数据录入对话框 -->
  <DataEntryDialog
    ref="dataEntryDialogRef"
    @success="refreshTable('dataEntry')"
  />

  <!-- 数据详情对话框 -->
  <DataDetailDialog
    ref="dataDetailDialogRef"
  />

  <!-- 任务详情对话框 -->
  <TaskDetailDialog
    ref="taskDetailDialogRef"
  />

  <!-- 异常处理对话框 -->
  <ExceptionDialog
    ref="exceptionDialogRef"
    @success="refreshTable('dataEntry')"
  />

  <!-- 流程说明对话框 -->
  <ProcessFlowDialog
    ref="processFlowDialogRef"
  />

  <!-- 检验下发对话框 -->
  <TestDispatchDialog
    ref="testDispatchDialogRef"
  />

  <!-- 数据分析对话框 -->
  <DataAnalysisDialog
    ref="dataAnalysisDialogRef"
  />

  <!-- 数据对比对话框 -->
  <DataCompareDialog
    ref="dataCompareDialogRef"
  />

  <!-- 从检验报告创建任务对话框 -->
  <CreateTaskFromReportDialog
    ref="createTaskFromReportDialogRef"
    @success="handleCreateTaskSuccess"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed, h } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { exportTestForm } from '@/utils/csvExport'

// 导入API
import {
  AssayDataManagementApi,
  type AssayTestTaskVO,
  type AssayTestDataVO,
  type AssayTestRecordVO,
  type ImportTaskFromSubmissionReqVO,
  type TestTaskDispatchReqVO,
  type TestTaskCompleteReqVO,
  type BatchDispatchReqVO,
  type BatchCompleteReqVO,
  type TestDataCreateReqVO,
  type TestDataUpdateReqVO
} from '@/api/assay/dataManagement'

// 导入组件
import DataEntryDialog from './components/DataEntryDialog.vue'
import DataDetailDialog from './components/DataDetailDialog.vue'
import TaskDetailDialog from './components/TaskDetailDialog.vue'
import ExceptionDialog from './components/ExceptionDialog.vue'
import ProcessFlowDialog from './components/ProcessFlowDialog.vue'
import TestDispatchDialog from './components/TestDispatchDialog.vue'
import CreateTaskFromReportDialog from './components/CreateTaskFromReportDialog.vue'

defineOptions({ name: 'AssayDataManagement' })

// 当前激活的标签页
const activeTab = ref('testTask')

// 任务状态筛选器
const taskStatusFilter = ref('pending')

// 加载状态
const loading = reactive({
  testTask: false,
  dataEntry: false,
  testRecord: false
})

// 搜索表单
const searchForm = reactive({
  testTask: {
    taskCode: '',
    isRecheck: undefined as boolean | undefined,
    assignedToId: undefined as number | undefined,
    priority: '',
    createTimeRange: []
  },
  dataEntry: {
    dataCode: '',
    indicatorId: undefined as number | undefined,
    status: '',
    testDateRange: [],
    testerId: undefined as number | undefined
  },
  testRecord: {
    recordCode: '',
    testId: '',
    testDateRange: []
  }
})

// 选中的行
const selectedTasks = ref<any[]>([])
const selectedRows = ref<any[]>([])

// 分页配置
const pagination = reactive({
  testTask: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  dataEntry: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  testRecord: {
    current: 1,
    pageSize: 10,
    total: 0
  }
})

// 定义表格数据类型接口
// 使用API中定义的类型
type TestTask = AssayTestTaskVO
type DataEntry = AssayTestDataVO
type TestRecord = AssayTestRecordVO





// 表格数据
const tableData = reactive({
  testTask: [] as TestTask[],
  dataEntry: [] as DataEntry[],
  testRecord: [] as any[]
})

// 根据状态筛选的任务数据
const filteredTaskData = computed(() => {
  return tableData.testTask.filter(task => task.status === taskStatusFilter.value)
})

// 获取指定状态的任务数量
const getTaskCountByStatus = (status: string) => {
  return tableData.testTask.filter(task => task.status === status).length
}

// 对话框引用
const dataEntryDialogRef = ref()
const dataDetailDialogRef = ref()
const taskDetailDialogRef = ref()
const exceptionDialogRef = ref()
const processFlowDialogRef = ref()
const testDispatchDialogRef = ref()
const createTaskFromReportDialogRef = ref()

// 生命周期钩子
onMounted(() => {
  fetchTableData('testTask')
  fetchTableData('dataEntry')
  fetchTableData('testRecord')
  // 监听来自采样模块的样品送检事件
  window.addEventListener('sampleSentForTesting', handleSampleSentForTesting)
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener('sampleSentForTesting', handleSampleSentForTesting)
})

// 监听标签页切换
watch(activeTab, (newVal) => {
  if (['testTask', 'dataEntry', 'testRecord'].includes(newVal)) {
    fetchTableData(newVal)
  }
})

// 监听状态筛选器变化
watch(taskStatusFilter, () => {
  // 筛选数据已通过computed自动处理
})

// 获取状态统计
const getStatusCount = (status: string) => {
  return tableData.dataEntry.filter(item => item.status === status).length
}



// 处理任务表格选择变化
const handleTaskSelectionChange = (selection: any[]) => {
  selectedTasks.value = selection
}

// 处理数据录入表格选择变化
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 状态筛选器变化处理
const handleStatusFilterChange = (status: string) => {
  taskStatusFilter.value = status
}

// 新增的任务管理方法
const handleCreateTask = () => {
  ElMessage.info('新增任务功能开发中...')
}

const handleCreateRecheck = (row: any) => {
  ElMessageBox.prompt('请输入复检原因', '创建复检任务', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    inputPattern: /.+/,
    inputErrorMessage: '复检原因不能为空'
  }).then(({ value }) => {
    ElMessage.success(`已为任务 ${row.taskCode} 创建复检任务，原因：${value}`)
    fetchTableData('testTask')
  }).catch(() => {})
}

const handleBatchDispatch = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要下发的任务')
    return
  }

  try {
    const { value: assignedToId } = await ElMessageBox.prompt('请选择检验员', '批量下发任务', {
      confirmButtonText: '下发',
      cancelButtonText: '取消',
      inputPlaceholder: '请输入检验员ID'
    })

    if (!assignedToId) {
      ElMessage.warning('请选择检验员')
      return
    }

    const batchData: BatchDispatchReqVO = {
      taskIds: selectedTasks.value.map(task => task.id!),
      assignedToId: Number(assignedToId),
      specialRequirements: '批量下发任务'
    }

    const res = await AssayDataManagementApi.batchDispatchTasks(batchData)
    if (res.data) {
      ElMessage.success(`已批量下发 ${res.data.successCount || 0} 个任务`)
      fetchTableData('testTask')
      selectedTasks.value = []
    } else {
      ElMessage.error('批量下发失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量下发失败:', error)
      ElMessage.error('批量下发失败，请稍后重试')
    }
  }
}

const handleBatchGenerateOrder = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要生成检验单的任务')
    return
  }

  try {
    const taskIds = selectedTasks.value.map(task => task.id!)
    const res = await AssayDataManagementApi.batchGenerateTestRecord(taskIds)
    if (res.data) {
      ElMessage.success(`已批量生成 ${res.data.successCount || 0} 个检验单`)
      fetchTableData('testRecord')
      selectedTasks.value = []
    } else {
      ElMessage.error('批量生成检验单失败')
    }
  } catch (error) {
    console.error('批量生成检验单失败:', error)
    ElMessage.error('批量生成检验单失败，请稍后重试')
  }
}

const handleBatchComplete = async () => {
  if (selectedTasks.value.length === 0) {
    ElMessage.warning('请先选择要完成的任务')
    return
  }

  try {
    await ElMessageBox.confirm(`确定要批量完成 ${selectedTasks.value.length} 个任务吗？`, '批量完成任务', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 这里需要为每个任务提供检验结果，简化处理
    const batchData: BatchCompleteReqVO = {
      taskIds: selectedTasks.value.map(task => task.id!),
      testResults: selectedTasks.value.map(task => ({
        taskId: task.id!,
        testValue: 0, // 默认值，实际应该由用户输入
        testMethod: '批量完成',
        testInstrument: '批量操作',
        remark: '批量完成任务'
      }))
    }

    const res = await AssayDataManagementApi.batchCompleteTasks(batchData)
    if (res.data) {
      ElMessage.success(`已批量完成 ${res.data.successCount || 0} 个任务`)
      fetchTableData('testTask')
      selectedTasks.value = []
    } else {
      ElMessage.error('批量完成失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量完成失败:', error)
      ElMessage.error('批量完成失败，请稍后重试')
    }
  }
}

const handleDispatchTask = (row: any) => {
  // 创建弹窗表单
  ElMessageBox({
    title: '下发任务',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666;' }, `状态变更：待下发 → 执行中`),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '任务编号：'),
        h('span', row.taskCode)
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测指标：'),
        h('span', row.testIndicator?.name || '-')
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检验员：'),
        h('span', row.assignedToName || '-')
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '优先级：'),
        h('span', { style: `color: ${getPriorityColor(row.priority) === 'danger' ? '#f56c6c' : getPriorityColor(row.priority) === 'warning' ? '#e6a23c' : '#909399'}` },
          getPriorityText(row.priority))
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '截止日期：'),
        h('span', row.dueDate || '-')
      ]),
      h('p', { style: 'margin-top: 16px; color: #409eff;' }, '确认下发后，任务将分配给检验员开始执行')
    ]),
    showCancelButton: true,
    confirmButtonText: '确定下发',
    cancelButtonText: '取消',
    type: 'warning',
    dangerouslyUseHTMLString: true
  }).then(async () => {
    try {
      const dispatchData: TestTaskDispatchReqVO = {
        taskId: row.id!,
        assignedToId: row.assignedToId!,
        specialRequirements: '单个任务下发'
      }

      const res = await AssayDataManagementApi.dispatchTestTask(dispatchData)
      if (res.data) {
        ElMessage.success(`任务 ${row.taskCode} 已下发`)
        fetchTableData('testTask')
      } else {
        ElMessage.error('任务下发失败')
      }
    } catch (error) {
      console.error('任务下发失败:', error)
      ElMessage.error('任务下发失败，请稍后重试')
    }
  }).catch(() => {})
}

const handleCompleteTask = (row: any) => {
  // 创建检验结果输入弹窗
  let testValue = ''
  let testMethod = row.testIndicator?.method || ''
  let testInstrument = row.testIndicator?.equipment || ''
  let remark = ''

  ElMessageBox({
    title: '完成任务 - 录入检验结果',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666;' }, `状态变更：执行中 → 已完成`),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '任务编号：'),
        h('span', row.taskCode)
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测指标：'),
        h('span', `${row.testIndicator?.name || '-'} (${row.testIndicator?.unit || '-'})`)
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold; color: #f56c6c;' }, '*检测值：'),
        h('input', {
          type: 'number',
          placeholder: '请输入检测值',
          style: 'width: 200px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px;',
          oninput: (e: any) => { testValue = e.target.value }
        })
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测方法：'),
        h('input', {
          type: 'text',
          value: testMethod,
          placeholder: '请输入检测方法',
          style: 'width: 200px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px;',
          oninput: (e: any) => { testMethod = e.target.value }
        })
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '检测仪器：'),
        h('input', {
          type: 'text',
          value: testInstrument,
          placeholder: '请输入检测仪器',
          style: 'width: 200px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px;',
          oninput: (e: any) => { testInstrument = e.target.value }
        })
      ]),
      h('div', { style: 'margin-bottom: 12px;' }, [
        h('label', { style: 'display: inline-block; width: 80px; font-weight: bold;' }, '备注：'),
        h('textarea', {
          placeholder: '请输入备注信息',
          style: 'width: 200px; height: 60px; padding: 4px 8px; border: 1px solid #dcdfe6; border-radius: 4px; resize: vertical;',
          oninput: (e: any) => { remark = e.target.value }
        })
      ])
    ]),
    showCancelButton: true,
    confirmButtonText: '确定完成',
    cancelButtonText: '取消',
    type: 'success',
    dangerouslyUseHTMLString: true,
    beforeClose: (action, instance, done) => {
      if (action === 'confirm') {
        if (!testValue) {
          ElMessage.error('请输入检测值')
          return
        }
      }
      done()
    }
  }).then(() => {
    row.status = 'completed'
    row.completeTime = new Date().toISOString().split('T')[0]
    row.progress = 100
    // 保存检验结果
    row.testResult = {
      testValue: parseFloat(testValue),
      testMethod,
      testInstrument,
      remark
    }
    ElMessage.success(`任务 ${row.taskCode} 已完成，检验结果已保存`)
    fetchTableData('testTask')
  }).catch(() => {})
}

const handleGenerateOrder = (row: any) => {
  // 创建生成检验单弹窗，回显检验结果
  const testResult = row.testResult || {}

  ElMessageBox({
    title: '生成检验单',
    message: h('div', [
      h('p', { style: 'margin-bottom: 16px; color: #666; font-weight: bold;' }, '检验单详细信息：'),

      h('div', { style: 'background: #f5f7fa; padding: 16px; border-radius: 4px; margin-bottom: 16px;' }, [
        h('h4', { style: 'margin: 0 0 12px 0; color: #303133;' }, '基本信息'),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '任务编号：'),
          h('span', row.taskCode)
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测指标：'),
          h('span', `${row.testIndicator?.name || '-'} (${row.testIndicator?.unit || '-'})`)
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检验员：'),
          h('span', row.assignedToName || '-')
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '完成时间：'),
          h('span', row.completeTime || '-')
        ])
      ]),

      h('div', { style: 'background: #f0f9ff; padding: 16px; border-radius: 4px; margin-bottom: 16px;' }, [
        h('h4', { style: 'margin: 0 0 12px 0; color: #303133;' }, '检验结果'),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测值：'),
          h('span', { style: 'color: #409eff; font-weight: bold;' },
            testResult.testValue ? `${testResult.testValue} ${row.testIndicator?.unit || ''}` : '未录入')
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测方法：'),
          h('span', testResult.testMethod || row.testIndicator?.method || '-')
        ]),
        h('div', { style: 'margin-bottom: 8px;' }, [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '检测仪器：'),
          h('span', testResult.testInstrument || row.testIndicator?.equipment || '-')
        ]),
        h('div', [
          h('label', { style: 'display: inline-block; width: 100px; font-weight: bold;' }, '备注：'),
          h('span', testResult.remark || '-')
        ])
      ]),

      h('div', { style: 'background: #fef0f0; padding: 16px; border-radius: 4px;' }, [
        h('h4', { style: 'margin: 0 0 12px 0; color: #303133;' }, 'Excel文件将包含'),
        h('ul', { style: 'margin: 0; padding-left: 20px; color: #666;' }, [
          h('li', '任务基本信息（编号、指标、检验员等）'),
          h('li', '检测数据详情（检测值、单位、方法、仪器）'),
          h('li', '检验过程记录和备注信息'),
          h('li', '质量控制和审核信息')
        ])
      ])
    ]),
    showCancelButton: true,
    confirmButtonText: '确认生成并下载',
    cancelButtonText: '取消',
    type: 'info',
    dangerouslyUseHTMLString: true
  }).then(() => {
    ElMessage.success(`检验单生成成功，正在下载...`)
    // 这里后端会处理下载逻辑
  }).catch(() => {})
}

const handleTaskDetail = (row: any) => {
  taskDetailDialogRef.value?.open(row)
}

// 优先级相关方法
const getPriorityColor = (priority: string) => {
  const colorMap = {
    'low': 'info',
    'normal': 'primary',
    'high': 'warning',
    'urgent': 'danger'
  }
  return colorMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const textMap = {
    'low': '低',
    'normal': '正常',
    'high': '高',
    'urgent': '紧急'
  }
  return textMap[priority] || priority
}

// 任务状态相关方法
const getTaskStatusColor = (status: string) => {
  const colorMap = {
    'pending': 'info',
    'processing': 'primary',
    'completed': 'success',
    'cancelled': 'danger'
  }
  return colorMap[status] || 'info'
}

const getTaskStatusText = (status: string) => {
  const textMap = {
    'pending': '待下发',
    'processing': '执行中',
    'completed': '已完成',
    'cancelled': '已取消'
  }
  return textMap[status] || status
}



// 处理来自采样模块的样品送检事件
const handleSampleSentForTesting = (event: CustomEvent) => {
  const { sampleCode, sampleId, testIndicators } = event.detail

  // 为每个检测指标创建检测数据记录
  const newTestDataRecords = testIndicators.map((testIndicator: string, index: number) => ({
    id: Date.now() + index,
    sampleCode: sampleCode,
    sampleId: sampleId,
    testIndicator: testIndicator,
    samplingPoint: getSamplingPointBySampleCode(sampleCode),
    samplingDate: getSamplingDateBySampleCode(sampleCode),
    status: 'pending',
    priority: 'normal',
    // 从送检单继承的字段
    sampleNature: getSampleNatureBySampleCode(sampleCode),
    sampleQuantity: getSampleQuantityBySampleCode(sampleCode),
    submissionReportCode: generateSubmissionReportCode(sampleCode),
    testMethod: getTestMethodByItem(testIndicator),
    testInstrument: getTestInstrumentByItem(testIndicator),
    testConditions: getTestConditionsByItem(testIndicator),
    createTime: new Date().toISOString(),
    updateTime: new Date().toISOString()
  }))

  // 添加到数据录入表格
  tableData.dataEntry.unshift(...newTestDataRecords)
  pagination.dataEntry.total += newTestDataRecords.length

  ElMessage.success(`已接收样品 ${sampleCode} 的 ${testItems.length} 个检测项目`)

  // 刷新当前表格
  refreshTable('dataEntry')
}

// 根据样品编号获取采样点（模拟函数）
const getSamplingPointBySampleCode = (sampleCode: string): string => {
  // 实际项目中应该通过API查询
  const pointMap = {
    'SP20230715': '进水总口',
    'SP20230716': '出水总口',
    'SP20230717': '生化池',
    'SP20230718': '出水总口',
    'SP20230720': '进水总口'
  }
  const prefix = sampleCode.slice(0, 11)
  return pointMap[prefix] || '未知采样点'
}

// 根据样品编号获取采样日期（模拟函数）
const getSamplingDateBySampleCode = (sampleCode: string): string => {
  // 从样品编号中提取日期
  const dateStr = sampleCode.slice(2, 10) // SP20230715001 -> 20230715
  return `${dateStr.slice(0, 4)}-${dateStr.slice(4, 6)}-${dateStr.slice(6, 8)}`
}

// 根据样品编号获取样品性质（模拟函数）
const getSampleNatureBySampleCode = (sampleCode: string): string => {
  // 根据样品编号前缀判断样品性质
  if (sampleCode.includes('SP')) return '液体'
  if (sampleCode.includes('SS')) return '半固体'
  if (sampleCode.includes('SG')) return '气体'
  return '液体' // 默认
}

// 根据样品编号获取样品数量（模拟函数）
const getSampleQuantityBySampleCode = (sampleCode: string): number => {
  // 根据样品编号后缀判断数量
  const suffix = sampleCode.slice(-3)
  return parseInt(suffix) % 3 + 1 // 1-3个样品
}

// 生成送检单编号（模拟函数）
const generateSubmissionReportCode = (sampleCode: string): string => {
  return sampleCode.replace('SP', 'SR')
}

// 根据检测项目获取检测方法（模拟函数）
const getTestMethodByItem = (testItem: string): string => {
  const methodMap: Record<string, string> = {
    'COD': '重铬酸钾法',
    'BOD5': '稀释接种法',
    '氨氮': '纳氏试剂分光光度法',
    '总磷': '钼酸铵分光光度法',
    '总氮': '碱性过硫酸钾消解紫外分光光度法',
    'pH': '玻璃电极法',
    'SS': '重量法'
  }
  return methodMap[testItem] || '标准方法'
}

// 根据检测项目获取检测仪器（模拟函数）
const getTestInstrumentByItem = (testItem: string): string => {
  const instrumentMap: Record<string, string> = {
    'COD': 'COD测定仪',
    'BOD5': 'BOD培养箱',
    '氨氮': '分光光度计',
    '总磷': '分光光度计',
    '总氮': '紫外分光光度计',
    'pH': 'pH计',
    'SS': '分析天平'
  }
  return instrumentMap[testItem] || '通用仪器'
}

// 根据检测项目获取检测条件（模拟函数）
const getTestConditionsByItem = (testItem: string): string => {
  const conditionsMap: Record<string, string> = {
    'COD': '常温常压',
    'BOD5': '20℃±1℃，5天培养',
    '氨氮': '常温，避光',
    '总磷': '常温，消解后测定',
    '总氮': '高温高压消解',
    'pH': '常温',
    'SS': '105℃烘干'
  }
  return conditionsMap[testItem] || '标准条件'
}

// 数据录入相关方法
const handleCreateData = () => {
  ElMessage.info('录入数据功能开发中...')
}

const handleImportFromSubmission = async () => {
  try {
    // 弹窗显示当前服务器文件夹下的所有送检单文件
    const { value: filePath } = await ElMessageBox.prompt('请输入送检单文件路径', '从送检单导入任务', {
      confirmButtonText: '导入',
      cancelButtonText: '取消',
      inputPlaceholder: '例如: /uploads/submission_reports/report_20240115.xlsx'
    })

    if (!filePath) {
      ElMessage.warning('请输入文件路径')
      return
    }

    const importData: ImportTaskFromSubmissionReqVO = {
      factoryId: 1, // 会在API中自动获取
      filePath: filePath,
      importType: 'normal',
      remark: '从送检单导入检验任务'
    }

    const res = await AssayDataManagementApi.importTaskFromSubmission(importData)
    if (res.data) {
      ElMessage.success(`送检单导入成功，已生成${res.data.successCount || 0}个检验任务`)
      fetchTableData('testTask')
    } else {
      ElMessage.error('送检单导入失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导入送检单失败:', error)
      ElMessage.error('导入失败，请稍后重试')
    }
  }
}

const handleImportFromTestRecord = async () => {
  try {
    const { value: filePath } = await ElMessageBox.prompt('请输入检验单文件路径', '从检验单导入数据', {
      confirmButtonText: '导入',
      cancelButtonText: '取消',
      inputPlaceholder: '例如: /uploads/test_records/record_20240115.xlsx'
    })

    if (!filePath) {
      ElMessage.warning('请输入文件路径')
      return
    }

    const res = await AssayDataManagementApi.importDataFromTestRecord(filePath)
    if (res.data) {
      ElMessage.success(`检验单导入成功，已导入${res.data.successCount || 0}条数据`)
      fetchTableData('dataEntry')
    } else {
      ElMessage.error('检验单导入失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('导入检验单失败:', error)
      ElMessage.error('导入失败，请稍后重试')
    }
  }
}

const handleEditData = (row: any) => {
  ElMessage.info(`编辑数据 ${row.dataCode} 功能开发中...`)
}

const handleDeleteData = (row: any) => {
  ElMessageBox.confirm(`确认删除数据 "${row.dataCode}" 吗？`, '删除确认', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    ElMessage.success(`数据 ${row.dataCode} 已删除`)
    fetchTableData('dataEntry')
  }).catch(() => {})
}

// 检验单记录相关方法
const handleCreateRecord = () => {
  ElMessage.info('新增记录功能开发中...')
}

const handleUploadFile = (row: any) => {
  ElMessage.info(`上传文件功能开发中...`)
}

const handlePreviewFile = (fileUrl: string) => {
  window.open(fileUrl, '_blank')
}

const handleRecordDetail = (row: any) => {
  ElMessage.info(`查看记录详情功能开发中...`)
}

// 获取状态类型和文本
const getStatusType = (status: string) => {
  switch (status) {
    case 'pending': return 'info'
    case 'entered': return 'primary'
    case 'reviewed': return 'success'
    case 'rejected': return 'danger'
    case 'exception': return 'danger'
    case 'completed': return 'success'
    default: return 'info'
  }
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待录入'
    case 'entered': return '已录入'
    case 'reviewed': return '已审核'
    case 'rejected': return '已驳回'
    case 'exception': return '异常'
    case 'completed': return '已完成'
    default: return '未知'
  }
}

// 获取表格数据
const fetchTableData = async (type: string) => {
  loading[type] = true
  try {
    if (type === 'testTask') {
      // 调用检验任务API
      const params = {
        pageNo: pagination.testTask.current,
        pageSize: pagination.testTask.pageSize,
        status: taskStatusFilter.value,
        taskCode: searchForm.testTask.taskCode,
        isRecheck: searchForm.testTask.isRecheck,
        assignedToId: searchForm.testTask.assignedToId,
        priority: searchForm.testTask.priority,
        createTimeStart: searchForm.testTask.createTimeRange[0],
        createTimeEnd: searchForm.testTask.createTimeRange[1]
      }
      const res = await AssayDataManagementApi.getTestTaskPage(params)
      if (res.data) {
        tableData.testTask = res.data.list || []
        pagination.testTask.total = res.data.total || 0
      }
    } else if (type === 'dataEntry') {
      // 调用检验数据API
      const params = {
        pageNo: pagination.dataEntry.current,
        pageSize: pagination.dataEntry.pageSize,
        sampleCode: searchForm.dataEntry.sampleCode,
        testIndicatorId: searchForm.dataEntry.testIndicatorId,
        status: searchForm.dataEntry.status,
        testDateStart: searchForm.dataEntry.testDateRange[0],
        testDateEnd: searchForm.dataEntry.testDateRange[1]
      }
      const res = await AssayDataManagementApi.getTestDataPage(params)
      if (res.data) {
        tableData.dataEntry = res.data.list || []
        pagination.dataEntry.total = res.data.total || 0
      }
    } else if (type === 'testRecord') {
      // 调用检验单记录API
      const params = {
        pageNo: pagination.testRecord.current,
        pageSize: pagination.testRecord.pageSize
      }
      const res = await AssayDataManagementApi.getTestRecordPage(params)
      if (res.data) {
        tableData.testRecord = res.data.list || []
        pagination.testRecord.total = res.data.total || 0
      }
    }
  } catch (error) {
    console.error(`获取${type}数据失败:`, error)
    ElMessage.error(`获取数据失败，请稍后重试`)
  } finally {
    loading[type] = false
  }
}




  } catch (error) {
    console.error(`获取${type}数据失败:`, error)
    ElMessage.error(`获取数据失败，请稍后重试`)
  } finally {
    loading[type] = false
  }
}

// 加载来自采样模块的待检测数据
const loadPendingTestData = () => {
  try {
    const pendingData = localStorage.getItem('pendingTestData')
    if (pendingData) {
      const testDataRecords = JSON.parse(pendingData)
      // 清除已加载的数据，避免重复
      localStorage.removeItem('pendingTestData')
      return testDataRecords
    }
    return []
  } catch (error) {
    console.error('加载待检测数据失败:', error)
    return []
  }
}

// 更新样品状态到采样模块
const updateSampleStatus = (sampleCode: string, status: string) => {
  // 通过事件通知采样模块更新样品状态
  window.dispatchEvent(new CustomEvent('testStatusUpdated', {
    detail: {
      sampleCode: sampleCode,
      status: status,
      updateTime: new Date().toISOString()
    }
  }))
}





const showProcessFlow = () => {
  processFlowDialogRef.value?.open()
}



const handleBatchSubmit = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要提交的数据')
    return
  }
  ElMessage.success(`已批量提交 ${selectedRows.value.length} 条数据`)
}

const handleBatchExport = () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要导出的数据')
    return
  }
  ElMessage.success(`已批量导出 ${selectedRows.value.length} 条数据`)
}

// 刷新表格
const refreshTable = (type: string) => {
  fetchTableData(type)
}

// 数据录入
const handleDataEntry = (row: any) => {
  dataEntryDialogRef.value?.open(row)
}

// 提交审核
const handleSubmitForReview = (row: any) => {
  ElMessageBox.confirm(`确定要提交样品${row.sampleCode}的检测数据进行审核吗？`, '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 模拟API调用
    setTimeout(() => {
      row.status = 'reviewing'
      row.updateTime = new Date().toISOString()

      // 同步状态到采样模块
      updateSampleStatus(row.sampleCode, 'testing')

      ElMessage.success('数据已提交审核')
      refreshTable('dataEntry')
    }, 500)
  }).catch(() => {})
}

// 查看数据
const handleViewData = (row: any) => {
  dataDetailDialogRef.value?.open(row)
}

// 这些功能将在对应的组件中实现
// const handleResubmit = (row: any) => dataEntryDialogRef.value?.open(row, true)
// const handleException = (row: any) => exceptionDialogRef.value?.open(row)
// const handleCreateTaskFromReport = () => createTaskFromReportDialogRef.value?.open()

// 处理创建任务成功
const handleCreateTaskSuccess = (tasks: any[]) => {
  // 将新创建的任务添加到任务列表
  tableData.testTask.unshift(...tasks)
  pagination.testTask.total = tableData.testTask.length

  ElMessage.success(`成功创建${tasks.length}个检验任务`)
}



// 数据录入相关方法已整合到主要功能中



// ==================== 搜索和分页相关方法 ====================

// 搜索
const handleSearch = (type: string) => {
  pagination[type].current = 1
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: string) => {
  const formKey = type as keyof typeof searchForm
  Object.keys(searchForm[formKey]).forEach(key => {
    if (Array.isArray(searchForm[formKey][key])) {
      searchForm[formKey][key] = []
    } else {
      searchForm[formKey][key] = ''
    }
  })
  fetchTableData(type)
}

// 分页大小变化
const handleSizeChange = (type: string, size: number) => {
  pagination[type].pageSize = size
  pagination[type].current = 1
  fetchTableData(type)
}

// 当前页变化
const handleCurrentChange = (type: string, current: number) => {
  pagination[type].current = current
  fetchTableData(type)
}






</script>

<style scoped>
.search-form {
  margin-bottom: 1rem;
}

.status-summary {
  display: flex;
  gap: 16px;
  align-items: center;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #f5f7fa;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

.status-label {
  font-size: 12px;
  color: #606266;
  font-weight: 500;
}

.status-count {
  font-size: 14px;
  font-weight: 600;
  padding: 2px 8px;
  border-radius: 4px;
  min-width: 20px;
  text-align: center;
}

.status-count.pending {
  background: #e1f3ff;
  color: #409eff;
}

.status-count.entered {
  background: #f0f9ff;
  color: #67c23a;
}

.status-count.exception {
  background: #fef0f0;
  color: #f56c6c;
}

.status-count.imported {
  background: #f0f9ff;
  color: #67c23a;
}

.status-count.archived {
  background: #f5f7fa;
  color: #909399;
}

.upload-container {
  padding: 1rem;
}

.upload-area {
  width: 100%;
  margin-bottom: 1rem;
}

.import-form {
  margin: 1rem 0;
}

.import-history {
  margin-top: 1rem;
}

.import-history h3 {
  margin-bottom: 1rem;
  font-size: 1rem;
  font-weight: 500;
}
</style>

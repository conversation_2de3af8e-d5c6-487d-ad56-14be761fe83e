import request from '@/config/axios'

// ==================== 数据类型定义 ====================

/** 采样计划保存请求对象 */
export interface AssaySamplingPlanSaveReqVO {
  id?: number
  factoryId: number
  planCode: string
  name: string
  type: 'regular' | 'temporary'
  description?: string
  frequency?: string // 常规计划时必填
  startDate?: string // 常规计划时必填
  endDate?: string // 常规计划时必填
  planDatetime?: string // 临时计划时必填
  reason?: string // 临时计划时必填
  priority: string
  testItem: number
  samplingPoint: number
  samplerId: number
  testerId: number
  reviewerId: number
  expectedSampleQuantity?: number
  expectedSampleNature?: string
  expectedSampleAppearance?: string
  expectedSupernatant?: string
  samplingInstructions?: string
  isEnabled: boolean
  remark?: string
}

/** 采样计划响应对象 */
export interface AssaySamplingPlanRespVO {
  id: number
  factoryId: number
  planCode: string
  name: string
  type: string
  description?: string
  frequency?: string
  startDate?: string
  endDate?: string
  planDatetime?: string
  reason?: string
  priority: string
  testItem: number
  testItemName?: string
  samplingPoint: number
  samplingPointName?: string
  samplerId: number
  samplerName?: string
  testerId: number
  testerName?: string
  reviewerId: number
  reviewerName?: string
  expectedSampleQuantity?: number
  expectedSampleNature?: string
  expectedSampleAppearance?: string
  expectedSupernatant?: string
  samplingInstructions?: string
  isEnabled: boolean
  remark?: string
  createTime?: string
  updateTime?: string
}

/** 采样计划分页查询请求对象 */
export interface AssaySamplingPlanPageReqVO {
  pageNo: number
  pageSize: number
  factoryId: number
  name?: string
  type?: string
  frequency?: string
  priority?: string
  isEnabled?: boolean
  samplerId?: number
}

/** 采样任务响应对象 */
export interface AssaySamplingTaskRespVO {
  id: number
  factoryId: number
  taskCode: string
  planId: number
  planName: string
  taskDatetime: string
  testItem: number
  testItemName?: string
  samplingPoint: number
  samplingPointName?: string
  samplerId: number
  samplerName?: string
  testerId: number
  testerName?: string
  reviewerId: number
  reviewerName?: string
  priority: string
  expectedSampleQuantity?: number
  expectedSampleNature?: string
  expectedSampleAppearance?: string
  expectedSupernatant?: string
  samplingInstructions?: string
  status: string
  startTime?: string
  completeTime?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

/** 采样任务分页查询请求对象 */
export interface AssaySamplingTaskPageReqVO {
  pageNo: number
  pageSize: number
  factoryId: number
  planId?: number
  status?: string
  samplerId?: number
  startDate?: string
  endDate?: string
}

/** 任务生成请求对象 */
export interface AssayTaskGenerateReqVO {
  factoryId: number
  planId: number
  generateType?: string
  startDate?: string
  endDate?: string
}

/** 任务分配请求对象 */
export interface AssayTaskAssignReqVO {
  taskId: number
  samplerId?: number
  testerId?: number
  reviewerId?: number
  assignReason?: string
}

// ==================== API 接口 ====================

/**
 * 采样计划管理模块API
 */
export const AssayTestPlanApi = {
  // ==================== 采样计划管理 ====================
  
  /**
   * 创建采样计划
   */
  createSamplingPlan: async (data: AssaySamplingPlanSaveReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-plan/create',
      data
    })
  },

  /**
   * 更新采样计划
   */
  updateSamplingPlan: async (data: AssaySamplingPlanSaveReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-sampling-plan/update',
      data
    })
  },

  /**
   * 删除采样计划
   */
  deleteSamplingPlan: async (id: number, factoryId: number) => {
    return await request.deleteOriginal({
      url: '/assay/assay-sampling-plan/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 获取采样计划详情
   */
  getSamplingPlan: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-plan/get',
      params: { id, factoryId }
    })
  },

  /**
   * 获取采样计划分页列表
   */
  getSamplingPlanPage: async (params: AssaySamplingPlanPageReqVO) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-plan/page',
      params
    })
  },

  /**
   * 获取启用的采样计划列表
   */
  getEnabledSamplingPlanList: async (factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-plan/enabled-list',
      params: { factoryId }
    })
  },

  /**
   * 检查计划冲突
   */
  checkPlanConflicts: async (data: any) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-plan/check-conflicts',
      data
    })
  },

  /**
   * 获取计划日历视图
   */
  getPlanCalendar: async (factoryId: number, year: number, month: number, type?: string) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-plan/calendar',
      params: { factoryId, year, month, type }
    })
  },

  /**
   * 导出采样计划Excel
   */
  exportSamplingPlan: async (params: AssaySamplingPlanPageReqVO) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-plan/export-excel',
      params
    })
  },

  // ==================== 采样任务管理 ====================
  
  /**
   * 根据计划生成任务
   */
  generateTasksFromPlan: async (data: AssayTaskGenerateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-task/generate-from-plan',
      data
    })
  },

  /**
   * 分配任务
   */
  assignTask: async (data: AssayTaskAssignReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-sampling-task/assign',
      data
    })
  },

  /**
   * 获取采样任务详情
   */
  getSamplingTask: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-task/get',
      params: { id, factoryId }
    })
  },

  /**
   * 获取采样任务分页列表
   */
  getSamplingTaskPage: async (params: AssaySamplingTaskPageReqVO) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-task/page',
      params
    })
  },

  /**
   * 获取任务详情（包含历史记录）
   */
  getTaskDetail: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-task/detail',
      params: { id, factoryId }
    })
  },

  /**
   * 更新任务状态
   */
  updateTaskStatus: async (taskId: number, status: string, remark?: string) => {
    return await request.putOriginal({
      url: '/assay/assay-sampling-task/update-status',
      params: { taskId, status, remark }
    })
  },

  /**
   * 批量更新任务状态
   */
  batchUpdateTaskStatus: async (taskIds: number[], status: string, remark?: string) => {
    return await request.putOriginal({
      url: '/assay/assay-sampling-task/batch-update-status',
      params: { taskIds, status, remark }
    })
  },

  /**
   * 根据计划ID获取任务列表
   */
  getTasksByPlan: async (planId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-task/by-plan',
      params: { planId }
    })
  },

  /**
   * 根据状态获取任务列表
   */
  getTasksByStatus: async (factoryId: number, status: string) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-task/by-status',
      params: { factoryId, status }
    })
  },

  /**
   * 导出采样任务Excel
   */
  exportSamplingTask: async (params: AssaySamplingTaskPageReqVO) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-task/export-excel',
      params
    })
  }
}

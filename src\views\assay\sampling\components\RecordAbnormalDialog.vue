<template>
  <Dialog v-model="dialogVisible" title="记录异常 - 异常情况处理" width="800px">
    <div class="status-transition-info mb-4">
      <el-alert 
        title="状态变更提示" 
        type="warning" 
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="status-flow">
            <span class="status-item current">采样中</span>
            <el-icon class="arrow"><ArrowRight /></el-icon>
            <span class="status-item next">异常终止</span>
          </div>
          <p class="mt-2 text-sm">记录异常后，任务状态将变更为"异常终止"，请详细描述异常情况和处理措施</p>
        </template>
      </el-alert>
    </div>

    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="任务信息" class="task-info">
        <div class="info-card">
          <p><strong>任务名称：</strong>{{ taskInfo.name }}</p>
          <p><strong>采样点：</strong>{{ taskInfo.samplingPointName }}</p>
          <p><strong>检测项目：</strong>{{ taskInfo.testItemName }}</p>
        </div>
      </el-form-item>

      <el-divider content-position="left">异常信息记录</el-divider>

      <el-form-item label="异常原因" prop="abnormalReason">
        <el-input 
          v-model="formData.abnormalReason" 
          type="textarea" 
          :rows="4"
          placeholder="请详细描述异常原因，如设备故障、环境因素、样品问题等"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="处理措施" prop="handleMeasures">
        <el-input 
          v-model="formData.handleMeasures" 
          type="textarea" 
          :rows="4"
          placeholder="请描述已采取或计划采取的处理措施"
          maxlength="1000"
          show-word-limit
        />
      </el-form-item>

      <el-form-item label="异常类型" prop="abnormalType">
        <el-radio-group v-model="formData.abnormalType">
          <el-radio label="设备故障">设备故障</el-radio>
          <el-radio label="环境因素">环境因素</el-radio>
          <el-radio label="样品问题">样品问题</el-radio>
          <el-radio label="人为因素">人为因素</el-radio>
          <el-radio label="其他">其他</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="是否需要重新采样" prop="needResample">
        <el-radio-group v-model="formData.needResample">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item v-if="formData.needResample" label="重新采样计划" prop="resamplePlan">
        <el-input 
          v-model="formData.resamplePlan" 
          type="textarea" 
          :rows="2"
          placeholder="请描述重新采样的计划安排"
          maxlength="500"
          show-word-limit
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :loading="formLoading" type="danger" @click="submitForm">
        确认记录异常
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'

defineOptions({ name: 'RecordAbnormalDialog' })

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref<FormInstance>()

interface TaskInfo {
  id: number
  name: string
  samplingPointName: string
  testItemName: string
}

const taskInfo = ref<TaskInfo>({
  id: 0,
  name: '',
  samplingPointName: '',
  testItemName: ''
})

const formData = reactive({
  taskId: 0,
  abnormalReason: '',
  handleMeasures: '',
  abnormalType: '设备故障',
  needResample: false,
  resamplePlan: ''
})

const formRules: FormRules = {
  abnormalReason: [
    // 放宽必填限制，但保留长度限制
    { max: 2000, message: '长度不能超过 2000 个字符', trigger: 'blur' }
  ],
  handleMeasures: [
    // 放宽必填限制，但保留长度限制
    { max: 2000, message: '长度不能超过 2000 个字符', trigger: 'blur' }
  ],
  abnormalType: [
    // 放宽必填限制
  ],
  needResample: [
    // 放宽必填限制
  ],
  resamplePlan: [
    // 放宽必填限制，只保留长度限制
    { max: 2000, message: '长度不能超过 2000 个字符', trigger: 'blur' }
  ]
}

// 打开对话框
const open = (task: any) => {
  taskInfo.value = {
    id: task.id,
    name: task.name,
    samplingPointName: task.samplingPointName || '未指定',
    testItemName: task.testItemName || '未指定'
  }
  
  formData.taskId = task.id
  formData.abnormalReason = ''
  formData.handleMeasures = ''
  formData.abnormalType = '设备故障'
  formData.needResample = false
  formData.resamplePlan = ''
  
  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 调用记录异常API
    const requestData = {
      taskId: formData.taskId,
      abnormalReason: formData.abnormalReason,
      handleMeasures: formData.handleMeasures
    }
    
    // TODO: 替换为真实API调用
    // await recordAbnormalApi(requestData)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.warning('异常记录成功，任务状态已变更为"异常终止"')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('记录异常失败:', error)
    ElMessage.error('记录异常失败，请重试')
  } finally {
    formLoading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.status-transition-info {
  margin-bottom: 1rem;
}

.status-flow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-item {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-item.current {
  background-color: #fff3cd;
  color: #856404;
}

.status-item.next {
  background-color: #f8d7da;
  color: #721c24;
}

.arrow {
  color: #666;
}

.task-info .info-card {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #e6a23c;
}

.task-info .info-card p {
  margin: 0.25rem 0;
  color: #606266;
}
</style>

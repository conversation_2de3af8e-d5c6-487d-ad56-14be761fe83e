<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="任务名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入任务名称" />
      </el-form-item>
      <el-form-item label="关联计划" prop="planId">
        <el-select v-model="formData.planId" placeholder="请选择关联计划">
          <el-option v-for="item in planOptions" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="采样点" prop="samplingPointId">
        <el-select v-model="formData.samplingPointId" placeholder="请选择采样点">
          <el-option v-for="point in samplingPoints" :key="point.id" :label="point.name" :value="point.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="检测项目" prop="items">
        <el-select v-model="formData.items" multiple placeholder="请选择检测项目">
          <el-option v-for="item in testItems" :key="item.id" :label="item.name" :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="计划日期" prop="planDate">
        <el-date-picker v-model="formData.planDate" type="date" placeholder="请选择计划日期" />
      </el-form-item>
      <el-form-item label="执行人" prop="executor">
        <el-select v-model="formData.executor" placeholder="请选择执行人">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.name" />
        </el-select>
      </el-form-item>
      
      <!-- 采样详细信息区域 -->
      <el-divider content-position="left">采样详细信息</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采样地点" prop="samplingLocation">
            <el-input v-model="formData.samplingLocation" placeholder="请输入具体采样地点" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="样品性质" prop="sampleNature">
            <el-select v-model="formData.sampleNature" placeholder="请选择样品性质">
              <el-option label="液体" value="液体" />
              <el-option label="固体" value="固体" />
              <el-option label="半固体" value="半固体" />
              <el-option label="气体" value="气体" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品数量" prop="sampleQuantity">
            <el-input-number v-model="formData.sampleQuantity" :min="1" :max="10" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="是否检测" prop="needTest">
            <el-switch v-model="formData.needTest" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="现场采样情况" prop="samplingCondition">
        <el-input
          v-model="formData.samplingCondition"
          type="textarea"
          :rows="3"
          placeholder="请描述现场采样情况"
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品外观" prop="sampleAppearance">
            <el-input v-model="formData.sampleAppearance" placeholder="请描述样品外观" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="上清液" prop="supernatant">
            <el-input v-model="formData.supernatant" placeholder="请描述上清液状态" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品状态" prop="sampleStatus">
            <el-input v-model="formData.sampleStatus" placeholder="请输入样品状态" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采样人" prop="samplerId">
            <el-select v-model="formData.samplerId" placeholder="请选择采样人" filterable>
              <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.id" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="实际采样时间" prop="actualSamplingTime">
        <el-date-picker
          v-model="formData.actualSamplingTime"
          type="datetime"
          placeholder="选择实际采样时间"
          style="width: 100%"
        />
      </el-form-item>
      
      <!-- 根据采样条件显示不同的表单项 -->
      <template v-if="formData.samplingCondition === 'needConfirm' || formData.samplingCondition === 'abnormal'">
        <el-form-item label="条件确认" prop="conditionDetails">
          <el-checkbox-group v-model="formData.conditionChecklist">
            <el-checkbox label="设备正常" />
            <el-checkbox label="水质正常" />
            <el-checkbox label="天气适宜" />
            <el-checkbox label="采样点可达" />
            <el-checkbox label="安全措施完备" />
          </el-checkbox-group>
        </el-form-item>
        
        <el-form-item label="异常描述" prop="abnormalDescription" v-if="formData.samplingCondition === 'abnormal'">
          <el-input v-model="formData.abnormalDescription" type="textarea" placeholder="请描述异常情况" />
        </el-form-item>
        
        <el-form-item label="处理措施" prop="handleMeasures" v-if="formData.samplingCondition === 'abnormal'">
          <el-input v-model="formData.handleMeasures" type="textarea" placeholder="请输入处理措施" />
        </el-form-item>
      </template>
      
      <!-- 样品信息区域 -->
      <el-divider content-position="left">样品信息</el-divider>
      <el-form-item label="样品编号" prop="sampleCode" v-if="formData.status === 'processing' || formData.status === 'completed'">
        <el-input v-model="formData.sampleCode" placeholder="请输入样品编号" />
      </el-form-item>
      
      <el-form-item label="样品类型" prop="sampleType" v-if="formData.status === 'processing' || formData.status === 'completed'">
        <el-select v-model="formData.sampleType" placeholder="请选择样品类型">
          <el-option label="水样" value="water" />
          <el-option label="污泥" value="sludge" />
          <el-option label="气体" value="gas" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="保存方式" prop="preservationMethod" v-if="formData.status === 'processing' || formData.status === 'completed'">
        <el-select v-model="formData.preservationMethod" placeholder="请选择保存方式">
          <el-option label="常温" value="normal" />
          <el-option label="冷藏" value="refrigerated" />
          <el-option label="冷冻" value="frozen" />
          <el-option label="加酸" value="acid" />
          <el-option label="其他" value="other" />
        </el-select>
      </el-form-item>
      
      <el-form-item label="有效期至" prop="expiryDate" v-if="formData.status === 'processing' || formData.status === 'completed'">
        <el-date-picker v-model="formData.expiryDate" type="datetime" placeholder="请选择样品有效期" />
      </el-form-item>
      
      <el-form-item label="采样说明" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入采样说明" />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="'pending'">待执行</el-radio>
          <el-radio :label="'processing'">执行中</el-radio>
          <el-radio :label="'completed'">已完成</el-radio>
          <el-radio :label="'abnormal'">异常终止</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Dialog } from '../../../../components/Dialog'

defineOptions({ name: 'SamplingTaskDialog' })

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

interface PlanOption {
  id: number;
  name: string;
}

interface SamplingPoint {
  id: number;
  name: string;
}

interface TestItem {
  id: number;
  name: string;
}

interface UserOption {
  id: number;
  name: string;
}

interface FormData {
  id?: number;
  factoryId: number;   // ✅ v4.0新增：水厂ID
  name: string;
  planId?: number;
  samplingPointId?: number;
  items: number[];
  planDate: string | Date;
  executor: string;
  description: string;
  status: 'pending' | 'processing' | 'completed' | 'abnormal';
  remark: string;
  // previous.md 要求的字段
  samplingLocation: string; // 地点
  samplingCondition: string; // 现场采样情况
  sampleAppearance: string; // 样品外观
  supernatant: string; // 上清液
  sampleNature: string; // 性质
  sampleStatus: string; // 样品状态
  sampleQuantity: number; // 样品数量
  samplerId: number; // ✅ v4.0修改：采样人员ID
  samplerName: string; // 采样人员姓名（用于显示）
  actualSamplingTime: string | Date; // 采样时间
  needTest: boolean; // 是否检测
  // 原有的样品信息字段
  sampleCode: string;
  sampleType: 'water' | 'sludge' | 'gas' | 'other' | '';
  preservationMethod: 'normal' | 'refrigerated' | 'frozen' | 'acid' | 'other' | '';
  expiryDate: string | Date;
}

// 检测计划选项（模拟数据）
const planOptions: PlanOption[] = [
  { id: 1, name: '进水水质日常监测计划' },
  { id: 2, name: '出水水质监测计划' },
  { id: 3, name: '污泥浓度检测计划' },
  { id: 4, name: '重金属超标排查' },
  { id: 5, name: '药剂投加效果检测' }
]

// 采样点数据（模拟数据）
const samplingPoints: SamplingPoint[] = [
  { id: 1, name: '进水总口' },
  { id: 2, name: '生化池' },
  { id: 3, name: '二沉池出水' },
  { id: 4, name: '出水总口' },
  { id: 5, name: '污泥池' }
]

// 检测项目数据（模拟数据）
const testItems: TestItem[] = [
  { id: 1, name: 'COD' },
  { id: 2, name: 'BOD5' },
  { id: 3, name: '氨氮' },
  { id: 4, name: '总磷' },
  { id: 5, name: '总氮' },
  { id: 6, name: '重金属' },
  { id: 7, name: '挥发性有机物' }
]

// 用户选项（模拟数据）
const userOptions: UserOption[] = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' }
]

// 表单数据
const formData = ref<FormData>({
  factoryId: 1,     // ✅ v4.0新增：水厂ID
  name: '',
  planId: undefined,
  samplingPointId: undefined,
  items: [],
  planDate: '',
  executor: '',
  description: '',
  status: 'pending',
  remark: '',
  // previous.md 要求的字段初始值
  samplingLocation: '',
  samplingCondition: '',
  sampleAppearance: '',
  supernatant: '',
  sampleNature: '',
  sampleStatus: '',
  sampleQuantity: 1,
  samplerId: 0,     // ✅ v4.0修改：采样人员ID
  samplerName: '',  // 采样人员姓名（用于显示）
  actualSamplingTime: '',
  needTest: true,
  // 原有的样品信息字段初始值
  sampleCode: '',
  sampleType: '',
  preservationMethod: '',
  expiryDate: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '任务名称不能为空', trigger: 'blur' }],
  planId: [{ required: true, message: '关联计划不能为空', trigger: 'change' }],
  samplingPointId: [{ required: true, message: '采样点不能为空', trigger: 'change' }],
  items: [{ required: true, message: '检测项目不能为空', trigger: 'change' }],
  planDate: [{ required: true, message: '计划日期不能为空', trigger: 'blur' }],
  executor: [{ required: true, message: '执行人不能为空', trigger: 'change' }],
  status: [{ required: true, message: '状态不能为空', trigger: 'change' }],
  // 根据条件添加必填验证
  abnormalDescription: [{ required: true, message: '异常描述不能为空', trigger: 'blur' }],
  sampleCode: [{ required: true, message: '样品编号不能为空', trigger: 'blur' }],
  sampleType: [{ required: true, message: '样品类型不能为空', trigger: 'change' }],
  preservationMethod: [{ required: true, message: '保存方式不能为空', trigger: 'change' }]
})

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 监听状态变化，当状态为异常时，自动设置采样条件为异常
watch(() => formData.value.status, (newStatus) => {
  if (newStatus === 'abnormal') {
    formData.value.samplingCondition = 'abnormal';
  } else if (newStatus === 'pending' && formData.value.samplingCondition === 'abnormal') {
    formData.value.samplingCondition = '';
  }
})

// 打开对话框
const open = async (type: string, data?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增采样任务' : '编辑采样任务'
  formType.value = type
  resetForm()
  
  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = JSON.parse(JSON.stringify(data))
    // 映射一些显示字段到表单数据
    // 通常在实际应用中，这里会根据ID去查询关联数据
    // 这里简单模拟下
    formData.value.planId = planOptions.find(p => p.name === data.planName)?.id
    formData.value.samplingPointId = samplingPoints.find(p => p.name === data.samplingPoint)?.id
    // 如果数据中没有items，设置默认值
    if (!formData.value.items) formData.value.items = []
    // 如果没有采样条件相关字段，设置默认值
    if (!formData.value.samplingCondition) formData.value.samplingCondition = 'normal'
    if (!formData.value.conditionChecklist) formData.value.conditionChecklist = []
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    planId: undefined,
    samplingPointId: undefined,
    items: [],
    planDate: '',
    executor: '',
    description: '',
    status: 'pending',
    remark: '',
    // 重置新增字段
    samplingCondition: '',
    conditionChecklist: [],
    abnormalDescription: '',
    handleMeasures: '',
    sampleCode: '',
    sampleType: '',
    preservationMethod: '',
    expiryDate: ''
  }
  formRef.value?.resetFields()
}
</script> 
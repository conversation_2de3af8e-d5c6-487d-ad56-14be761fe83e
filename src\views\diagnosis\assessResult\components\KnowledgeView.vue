<template>
  <div class="knowledge-view">
    <!-- 顶部操作区域 -->
    <el-card class="mb-4">
      <div class="knowledge-header">
        <div class="search-area">
          <el-input v-model="searchKeyword" placeholder="搜索知识库内容..." style="width: 300px" clearable
            @input="handleSearch">
            <template #prefix>
              <el-icon>
                <Search />
              </el-icon>
            </template>
          </el-input>
          <el-tree-select v-model="selectedCategory" :data="categories" placeholder="选择分类"
            style="width: 200px; margin-left: 12px" clearable node-key="id"
            :props="{ label: 'label', children: 'children' }" check-strictly @change="handleCategoryChange" />
        </div>

        <div class="action-area">
          <el-button type="primary" @click="handleAdd">
            <el-icon>
              <Plus />
            </el-icon>
            新增配置
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 知识库列表 -->
    <el-card>
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">指标配置列表</span>
          <el-tag>共 {{ filteredConfigList.length }} 条记录</el-tag>
        </div>
      </template>

      <el-table :data="paginatedData" border stripe v-loading="loading">
        <el-table-column type="index" label="序号" width="60" align="center" />

        <el-table-column prop="indicatorName" label="指标名称" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            <el-link type="primary" @click="handleView(row)">{{ row.indicatorName }}</el-link>
          </template>
        </el-table-column>

        <el-table-column prop="category" label="指标分类" width="120" align="center">
          <template #default="{ row }">
            <el-tag :type="getCategoryTagType(row.category)">{{ getCategoryLabel(row.category) }}</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="rules" label="规则数量" width="100" align="center">
          <template #default="{ row }">
            <el-tag type="info">{{ row.rules?.length || 0 }} 条</el-tag>
          </template>
        </el-table-column>

        <el-table-column prop="createTime" label="创建时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.createTime) }}
          </template>
        </el-table-column>

        <el-table-column prop="updateTime" label="更新时间" width="160" align="center">
          <template #default="{ row }">
            {{ formatDate(row.updateTime) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="180" align="center" fixed="right">
          <template #default="{ row }">
            <el-button size="small" link @click="handleView(row)">查看</el-button>
            <el-button size="small" link type="primary" @click="handleEdit(row)">编辑</el-button>
            <el-button size="small" link type="danger" @click="handleDelete(row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="currentPage" v-model:page-size="pageSize" :page-sizes="[10, 20, 50, 100]"
          :total="filteredConfigList.length" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handleCurrentChange" />
      </div>
    </el-card>

    <!-- 指标配置弹窗 -->
    <el-dialog v-model="dialogVisible" :title="dialogTitle" width="80%" :before-close="handleDialogClose">
      <div class="indicator-config-container">
        <!-- 步骤1: 选择指标 -->
        <el-card class="mb-4">
          <template #header>
            <span class="font-bold">1. 选择指标</span>
          </template>
          <el-form :model="formData" label-width="100px">
            <el-form-item label="指标选择" prop="indicatorId">
              <el-select v-model="formData.indicatorId" placeholder="请选择指标" style="width: 100%" filterable
                @change="handleIndicatorChange">
                <el-option v-for="indicator in availableIndicators" :key="indicator.id"
                  :label="`${indicator.name} (${indicator.unit})`" :value="indicator.id">
                  <div class="indicator-option">
                    <span class="indicator-name">{{ indicator.name }}</span>
                    <span class="indicator-category">{{ getCategoryLabel(indicator.category) }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-form>
        </el-card>

        <!-- 步骤2: 指标基础信息 -->
        <el-card v-if="selectedIndicator" class="mb-4">
          <template #header>
            <span class="font-bold">2. 指标基础信息</span>
          </template>
          <div class="indicator-info">
            <el-descriptions :column="2" border>
              <el-descriptions-item label="指标名称">{{ selectedIndicator.name }}</el-descriptions-item>
              <el-descriptions-item label="指标分类">
                <el-tag :type="getCategoryTagType(selectedIndicator.category)">{{
                  getCategoryLabel(selectedIndicator.category) }}</el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="计量单位">{{ selectedIndicator.unit }}</el-descriptions-item>
              <el-descriptions-item label="参考阈值">
                <span class="threshold-text">{{ selectedIndicator.threshold || '请在下方配置具体区间规则' }}</span>
              </el-descriptions-item>
              <el-descriptions-item label="计算方式">{{ selectedIndicator.calculation }}</el-descriptions-item>
              <el-descriptions-item label="评估周期">{{ getEvaluationPeriodText(selectedIndicator.evaluationPeriod)
                }}</el-descriptions-item>
              <el-descriptions-item label="基础数据" :span="2">{{ selectedIndicator.baseData.join('、')
                }}</el-descriptions-item>
              <el-descriptions-item label="指标定义" :span="2">{{ selectedIndicator.definition }}</el-descriptions-item>
              <el-descriptions-item label="备注说明" :span="2">{{ selectedIndicator.remark }}</el-descriptions-item>
            </el-descriptions>
          </div>
        </el-card>

        <!-- 步骤3: 配置区间规则 -->
        <el-card v-if="selectedIndicator">
          <template #header>
            <div class="flex justify-between items-center">
              <span class="font-bold">3. 配置区间规则</span>
              <el-button type="primary" size="small" @click="handleAddRule">
                <el-icon>
                  <Plus />
                </el-icon>
                添加规则
              </el-button>
            </div>
          </template>

          <div class="rules-description mb-3">
            <el-alert title="配置说明" description="请根据上方参考阈值，配置具体的评价等级区间。系统将根据实际测量值自动匹配对应的评价等级和得分。" type="info" show-icon
              :closable="false" />
          </div>

          <div v-if="formData.rules && formData.rules.length > 0" class="rules-config">
            <el-table :data="formData.rules" border size="small">
              <el-table-column type="index" label="序号" width="60" align="center" />

              <el-table-column label="评价等级" width="120" align="center">
                <template #default="{ row }">
                  <el-select v-model="row.level" placeholder="选择等级" size="small">
                    <el-option label="优秀" value="优秀" />
                    <el-option label="良好" value="良好" />
                    <el-option label="合格" value="合格" />
                    <el-option label="待改进" value="待改进" />
                  </el-select>
                </template>
              </el-table-column>

              <el-table-column label="最小值" width="120" align="center">
                <template #default="{ row }">
                  <el-input-number v-model="row.minValue" :precision="2" size="small" style="width: 100%"
                    placeholder="最小值" />
                </template>
              </el-table-column>

              <el-table-column label="最大值" width="120" align="center">
                <template #default="{ row }">
                  <el-input-number v-model="row.maxValue" :precision="2" size="small" style="width: 100%"
                    placeholder="最大值" />
                </template>
              </el-table-column>

              <el-table-column label="得分" width="120" align="center">
                <template #default="{ row }">
                  <el-input-number v-model="row.score" :min="0" :max="100" size="small" style="width: 100%"
                    placeholder="得分" />
                </template>
              </el-table-column>

              <el-table-column label="建议与结论" min-width="200">
                <template #default="{ row }">
                  <el-input v-model="row.suggestion" size="small" placeholder="请输入建议与结论" />
                </template>
              </el-table-column>

              <el-table-column label="操作" width="100" align="center">
                <template #default="{ $index }">
                  <el-button size="small" link type="danger" @click="handleDeleteRule($index)">删除</el-button>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <div v-else class="no-rules">
            <el-empty description="暂未配置区间规则" :image-size="100">
              <el-button type="primary" @click="handleAddRule">立即配置</el-button>
            </el-empty>
          </div>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSubmit" :loading="submitting" :disabled="!selectedIndicator">
            {{ isEdit ? '更新配置' : '保存配置' }}
          </el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看详情弹窗 -->
    <el-dialog v-model="viewDialogVisible" :title="`${viewData?.indicatorName || ''} - 配置详情`" width="900px">
      <div v-if="viewData" class="config-detail">
        <!-- 指标详情 -->
        <el-card class="mb-4" shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">指标详情</span>
              <el-tag :type="getCategoryTagType(viewData.category)">{{ getCategoryLabel(viewData.category) }}</el-tag>
            </div>
          </template>

          <el-descriptions :column="2" border size="small">
            <el-descriptions-item label="指标名称">{{ viewData.indicatorName }}</el-descriptions-item>
            <el-descriptions-item label="指标分类">{{ getCategoryLabel(viewData.category) }}</el-descriptions-item>
            <el-descriptions-item label="创建时间">{{ formatDate(viewData.createTime) }}</el-descriptions-item>
            <el-descriptions-item label="更新时间">{{ formatDate(viewData.updateTime) }}</el-descriptions-item>
          </el-descriptions>
        </el-card>

        <!-- 区间规则配置 -->
        <el-card shadow="never">
          <template #header>
            <div class="card-header">
              <span class="card-title">区间规则配置</span>
              <el-tag type="info">共 {{ viewData.rules?.length || 0 }} 条规则</el-tag>
            </div>
          </template>

          <el-table :data="viewData.rules" border size="small" style="width: 100%">
            <el-table-column type="index" label="序号" width="60" align="center" />
            <el-table-column prop="level" label="评价等级" width="100" align="center">
              <template #default="{ row }">
                <el-tag :type="getLevelTagType(row.level)">{{ row.level }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="minValue" label="最小值" width="100" align="center" />
            <el-table-column prop="maxValue" label="最大值" width="100" align="center" />
            <el-table-column prop="score" label="得分" width="80" align="center">
              <template #default="{ row }">
                <el-tag type="success">{{ row.score }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="suggestion" label="建议与结论" min-width="250" show-overflow-tooltip />
          </el-table>
        </el-card>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="viewDialogVisible = false">关闭</el-button>
          <el-button type="primary" @click="handleEdit(viewData)">编辑</el-button>
        </div>
      </template>
    </el-dialog>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Plus } from '@element-plus/icons-vue'

// 接口定义
interface IndicatorItem {
  id: string
  name: string
  definition: string
  unit: string
  category: string
  threshold: string
  status: number
  evaluationPeriod: string
  baseData: string[]
  calculation: string
  remark: string
}

interface RuleItem {
  level: string
  minValue: number | null
  maxValue: number | null
  score: number | null
  suggestion: string
}

interface ConfigItem {
  id: string
  indicatorId: string
  indicatorName: string
  category: string
  rules: RuleItem[]
  createTime: string
  updateTime: string
}

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const selectedCategory = ref('')
const currentPage = ref(1)
const pageSize = ref(20)

// 弹窗控制
const dialogVisible = ref(false)
const viewDialogVisible = ref(false)
const isEdit = ref(false)
const submitting = ref(false)

// 表单数据
const formData = ref<Partial<ConfigItem>>({
  indicatorId: '',
  rules: []
})

const viewData = ref<ConfigItem | null>(null)
const selectedIndicator = ref<IndicatorItem | null>(null)

// 分类树数据（与指标管理保持一致）
const categoryTreeData = ref([
  {
    id: '1',
    label: '电力系统',
    children: [
      { id: '1-1', label: '电能质量' },
      { id: '1-2', label: '设备效率' }
    ]
  },
  {
    id: '2',
    label: '水处理系统',
    children: [
      { id: '2-1', label: '水质监测' },
      { id: '2-2', label: '处理效果' }
    ]
  },
  {
    id: '3',
    label: '环境监测',
    children: [
      { id: '3-1', label: '噪声监测' },
      { id: '3-2', label: '温度监测' }
    ]
  },
  {
    id: '4',
    label: '设备运行'
  },
  {
    id: '5',
    label: '安全管理'
  }
])

// 直接使用树结构作为分类数据
const categories = categoryTreeData

// 分类标签映射
const getCategoryLabel = (categoryId: string) => {
  const findLabel = (nodes: any[], id: string): string => {
    for (const node of nodes) {
      if (node.id === id) {
        return node.label
      }
      if (node.children) {
        const found = findLabel(node.children, id)
        if (found) return found
      }
    }
    return ''
  }

  const label = findLabel(categoryTreeData.value, categoryId)
  return label || categoryId
}

// 可用指标数据
const availableIndicators = ref<IndicatorItem[]>([
  {
    id: '1',
    name: '功率因数',
    definition: '有功功率与视在功率的比值，反映电力系统的效率',
    unit: '无',
    category: '1-1',
    threshold: '≥0.9',
    status: 1,
    evaluationPeriod: 'hour',
    baseData: ['有功功率', '视在功率'],
    calculation: '有功功率 / 视在功率',
    remark: '用于评估电力系统的效率，是重要的电能质量指标'
  },
  {
    id: '2',
    name: '水质达标率',
    definition: '水质指标达标次数与总检测次数的比值',
    unit: '%',
    category: '2-1',
    threshold: '≥95%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['达标次数', '总检测次数'],
    calculation: '达标次数 / 总检测次数 × 100',
    remark: '用于评估水处理系统的处理效果和环保达标情况'
  },
  {
    id: '3',
    name: '设备运行率',
    definition: '设备正常运行时间与总时间的比值',
    unit: '%',
    category: '4',
    threshold: '≥98%',
    status: 0,
    evaluationPeriod: 'month',
    baseData: ['运行时间', '总时间'],
    calculation: '运行时间 / 总时间 × 100',
    remark: '反映设备可靠性和维护水平的重要指标'
  },
  {
    id: '4',
    name: '环境噪声达标率',
    definition: '环境噪声监测达标次数与总监测次数的比值',
    unit: '%',
    category: '3-1',
    threshold: '≥90%',
    status: 1,
    evaluationPeriod: 'week',
    baseData: ['达标次数', '总监测次数'],
    calculation: '达标次数 / 总监测次数 × 100',
    remark: '反映环境噪声控制效果的重要指标'
  },
  {
    id: '5',
    name: '单位能耗',
    definition: '单位处理水量的能源消耗',
    unit: 'kWh/m³',
    category: '1-2',
    threshold: '≤0.5',
    status: 1,
    evaluationPeriod: 'month',
    baseData: ['总能耗', '处理水量'],
    calculation: '总能耗 / 处理水量',
    remark: '反映能源利用效率的关键指标'
  },
  {
    id: '6',
    name: 'COD去除率',
    definition: '化学需氧量去除率，反映有机物去除效果',
    unit: '%',
    category: '2-2',
    threshold: '≥85%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['进水COD', '出水COD'],
    calculation: '(进水COD-出水COD)/进水COD×100%',
    remark: '水质处理效果的核心指标'
  },
  {
    id: '7',
    name: '氨氮去除率',
    definition: '氨氮去除率，反映脱氮效果',
    unit: '%',
    category: '2-2',
    threshold: '≥80%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['进水NH3-N', '出水NH3-N'],
    calculation: '(进水NH3-N-出水NH3-N)/进水NH3-N×100%',
    remark: '脱氮工艺效果评价指标'
  },
  {
    id: '8',
    name: '污泥含水率',
    definition: '脱水污泥含水率，影响污泥处置成本',
    unit: '%',
    category: '2-3',
    threshold: '≤80%',
    status: 1,
    evaluationPeriod: 'day',
    baseData: ['污泥水分重量', '污泥总重量'],
    calculation: '污泥中水分重量/污泥总重量×100%',
    remark: '污泥脱水效果和处置成本的重要指标'
  }
])

// 配置数据
const configList = ref<ConfigItem[]>([
  {
    id: '1',
    indicatorId: '1',
    indicatorName: 'COD去除率',
    category: '2-2', // 处理效果
    rules: [
      { level: '优秀', minValue: 95, maxValue: 100, score: 95, suggestion: 'COD去除率达到优秀水平，继续保持当前工艺参数' },
      { level: '良好', minValue: 90, maxValue: 95, score: 85, suggestion: 'COD去除率良好，可适当优化工艺提升效果' },
      { level: '合格', minValue: 80, maxValue: 90, score: 75, suggestion: 'COD去除率合格，建议检查工艺参数并优化' },
      { level: '待改进', minValue: 0, maxValue: 80, score: 60, suggestion: 'COD去除率偏低，需要重点检查进水水质和工艺运行状况' }
    ],
    createTime: '2024-01-15 10:30:00',
    updateTime: '2024-02-20 14:20:00'
  },
  {
    id: '2',
    indicatorId: '5',
    indicatorName: '单位能耗',
    category: '1-2', // 设备效率
    rules: [
      { level: '优秀', minValue: 0, maxValue: 0.3, score: 95, suggestion: '能耗控制优秀，继续保持节能运行模式' },
      { level: '良好', minValue: 0.3, maxValue: 0.4, score: 85, suggestion: '能耗控制良好，可进一步优化设备运行策略' },
      { level: '合格', minValue: 0.4, maxValue: 0.5, score: 75, suggestion: '能耗控制合格，建议检查设备效率和运行参数' },
      { level: '待改进', minValue: 0.5, maxValue: 999, score: 60, suggestion: '能耗偏高，需要重点检查设备状态和工艺优化' }
    ],
    createTime: '2024-01-20 09:15:00',
    updateTime: '2024-02-25 16:45:00'
  },
  {
    id: '3',
    indicatorId: '1',
    indicatorName: '功率因数',
    category: '1-1', // 电能质量
    rules: [
      { level: '优秀', minValue: 0.95, maxValue: 1.0, score: 95, suggestion: '功率因数优秀，电能质量良好' },
      { level: '良好', minValue: 0.9, maxValue: 0.95, score: 85, suggestion: '功率因数良好，可适当优化' },
      { level: '合格', minValue: 0.85, maxValue: 0.9, score: 75, suggestion: '功率因数合格，建议检查电力系统' },
      { level: '待改进', minValue: 0, maxValue: 0.85, score: 60, suggestion: '功率因数偏低，需要改进电力系统效率' }
    ],
    createTime: '2024-01-25 14:20:00',
    updateTime: '2024-02-28 10:15:00'
  }
])

// 计算属性
const dialogTitle = computed(() => isEdit.value ? '编辑指标配置' : '新增指标配置')

const filteredConfigList = computed(() => {
  let result = configList.value

  // 分类筛选
  if (selectedCategory.value) {
    result = result.filter(item => item.category === selectedCategory.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(item =>
      item.indicatorName.toLowerCase().includes(keyword) ||
      getCategoryLabel(item.category).toLowerCase().includes(keyword)
    )
  }

  return result
})

const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredConfigList.value.slice(start, end)
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleCategoryChange = () => {
  currentPage.value = 1
}



const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    '1-1': 'primary',   // 电能质量
    '1-2': 'success',   // 设备效率
    '2-1': 'warning',   // 水质监测
    '2-2': 'info',      // 处理效果
    '2-3': 'danger',    // 污泥处理
    '3-1': 'primary',   // 噪声监测
    '3-2': 'success',   // 温度监测
    '4': 'warning',     // 设备运行
    '5': 'info'         // 安全管理
  }
  return typeMap[category] || ''
}

const getLevelTagType = (level: string) => {
  const typeMap: Record<string, string> = {
    '优秀': 'success',
    '良好': 'primary',
    '合格': 'warning',
    '待改进': 'danger'
  }
  return typeMap[level] || ''
}

const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleString()
}

const getEvaluationPeriodText = (period: string) => {
  const periodMap: Record<string, string> = {
    'hour': '小时',
    'day': '日',
    'week': '周',
    'month': '月',
    'quarter': '季度',
    'year': '年'
  }
  return periodMap[period] || period
}

// CRUD操作
const handleAdd = () => {
  isEdit.value = false
  formData.value = {
    indicatorId: '',
    rules: []
  }
  selectedIndicator.value = null
  dialogVisible.value = true
}

const handleEdit = (row: ConfigItem) => {
  isEdit.value = true
  formData.value = { ...row }

  // 设置选中的指标
  const indicator = availableIndicators.value.find(item => item.id === row.indicatorId)
  selectedIndicator.value = indicator || null

  dialogVisible.value = true
  viewDialogVisible.value = false
}

const handleView = (row: ConfigItem) => {
  viewData.value = row
  viewDialogVisible.value = true
}

const handleDelete = async (row: ConfigItem) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除指标"${row.indicatorName}"的配置吗？`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = configList.value.findIndex(item => item.id === row.id)
    if (index > -1) {
      configList.value.splice(index, 1)
      ElMessage.success('删除成功')
    }
  } catch {
    // 用户取消删除
  }
}



// 指标选择相关方法
const handleIndicatorChange = (indicatorId: string) => {
  const indicator = availableIndicators.value.find(item => item.id === indicatorId)
  selectedIndicator.value = indicator || null

  // 清空之前的规则
  formData.value.rules = []
}

// 规则配置相关方法
const handleAddRule = () => {
  if (!formData.value.rules) {
    formData.value.rules = []
  }

  formData.value.rules.push({
    level: '',
    minValue: null,
    maxValue: null,
    score: null,
    suggestion: ''
  })
}

const handleDeleteRule = (index: number) => {
  if (formData.value.rules) {
    formData.value.rules.splice(index, 1)
  }
}

const handleSubmit = async () => {
  try {
    // 验证必填项
    if (!formData.value.indicatorId) {
      ElMessage.warning('请选择指标')
      return
    }

    if (!formData.value.rules || formData.value.rules.length === 0) {
      ElMessage.warning('请配置区间规则')
      return
    }

    submitting.value = true

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    if (isEdit.value) {
      // 更新
      const index = configList.value.findIndex(item => item.id === formData.value.id)
      if (index > -1) {
        configList.value[index] = {
          ...formData.value,
          indicatorName: selectedIndicator.value?.name || '',
          category: selectedIndicator.value?.category || '',
          updateTime: new Date().toLocaleString()
        } as ConfigItem
      }
      ElMessage.success('更新成功')
    } else {
      // 新增
      const newItem: ConfigItem = {
        ...formData.value,
        id: Date.now().toString(),
        indicatorName: selectedIndicator.value?.name || '',
        category: selectedIndicator.value?.category || '',
        createTime: new Date().toLocaleString(),
        updateTime: new Date().toLocaleString()
      } as ConfigItem

      configList.value.unshift(newItem)
      ElMessage.success('创建成功')
    }

    dialogVisible.value = false
  } catch (error) {
    console.error('提交失败:', error)
  } finally {
    submitting.value = false
  }
}

const handleDialogClose = () => {
  dialogVisible.value = false
  selectedIndicator.value = null
}

onMounted(() => {
  // 初始化数据
})
</script>

<style scoped lang="scss">
.knowledge-view {
  .knowledge-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .search-area {
      display: flex;
      align-items: center;
    }

    .action-area {
      display: flex;
      gap: 12px;
    }
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: right;
  }

  // 知识详情样式
  .knowledge-detail {
    .detail-header {
      border-bottom: 1px solid #e5e7eb;
      padding-bottom: 16px;
      margin-bottom: 20px;

      h2 {
        margin: 0 0 12px 0;
        color: #1f2937;
        font-size: 20px;
        font-weight: 600;
      }

      .meta-info {
        display: flex;
        align-items: center;
        gap: 16px;
        margin-bottom: 12px;
        font-size: 14px;
        color: #6b7280;

        .author,
        .time {
          display: flex;
          align-items: center;
        }
      }

      .tags {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }
    }

    .detail-content {
      h3 {
        color: #374151;
        font-size: 16px;
        font-weight: 600;
        margin: 20px 0 12px 0;
        border-left: 4px solid #3b82f6;
        padding-left: 12px;
      }

      .summary {
        background: #f8fafc;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #10b981;
        margin-bottom: 20px;
        line-height: 1.6;
        color: #374151;
      }

      .content {
        line-height: 1.8;
        color: #4b5563;

        :deep(br) {
          margin-bottom: 8px;
        }
      }
    }
  }

  // 指标基础信息样式
  .indicator-info {
    .threshold-text {
      color: #059669;
      font-weight: 500;
      font-size: 13px;
      line-height: 1.4;
    }
  }

  // 规则配置样式
  .rules-description {
    margin-bottom: 16px;
  }

  // 详情弹窗样式
  .config-detail {
    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .card-title {
        font-size: 16px;
        font-weight: 600;
        color: #1f2937;
      }
    }

    .el-card {
      border: 1px solid #e5e7eb;

      &.mb-4 {
        margin-bottom: 16px;
      }
    }

    .el-descriptions {
      .el-descriptions__label {
        font-weight: 500;
        color: #374151;
      }

      .el-descriptions__content {
        color: #6b7280;
      }
    }

    .el-table {
      .el-tag {
        font-size: 12px;
      }
    }
  }

  // 表格样式优化
  .el-table {
    .el-link {
      font-weight: 500;
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .knowledge-header {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .search-area {
        flex-direction: column;
        gap: 12px;

        .el-input,
        .el-select {
          width: 100% !important;
        }
      }

      .action-area {
        justify-content: center;
      }
    }
  }
}
</style>

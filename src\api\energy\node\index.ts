import request from '@/config/axios'

// 能耗节点接口定义
export interface EnergyNodeVO {
  id?: number
  name: string
  nodeType: string // 节点类型：electric-电耗，drug-药耗等
  parentId?: number | null // 父节点ID（为空表示顶级节点）
  factoryId?: number // 所属厂站ID
  sort?: number // 排序值
  createTime?: Date
  updateTime?: Date
  children?: EnergyNodeVO[]
}

// 创建能耗节点请求参数
export interface EnergyNodeCreateReqVO {
  name: string
  nodeType: string // 节点类型：electric-电耗，drug-药耗等
  parentId?: number | null // 父节点ID（为空表示顶级节点）
  factoryId?: number // 所属厂站ID
}

// 更新能耗节点请求参数
export interface EnergyNodeUpdateReqVO {
  id: number
  name: string
  nodeType: string // 节点类型：electric-电耗，drug-药耗等
  parentId?: number | null // 父节点ID（为空表示顶级节点）
  factoryId?: number // 所属厂站ID
}

// 创建能耗节点
export const createEnergyNode = (data: EnergyNodeCreateReqVO) => {
  return request.post<number>({ url: '/energy/node/create', data })
}

// 更新能耗节点
export const updateEnergyNode = (data: EnergyNodeUpdateReqVO) => {
  return request.post<boolean>({ url: '/energy/node/update', data })
}

// 删除能耗节点
export const deleteEnergyNode = (id: number) => {
  return request.delete<boolean>({ url: `/energy/node/delete/${id}` })
}

// 获取能耗节点树
export const getEnergyNodesTree = (factoryId: number) => {
  return request.get<EnergyNodeVO[]>({ url: '/energy/node/get-nodes-tree', params: { factoryId } })
}

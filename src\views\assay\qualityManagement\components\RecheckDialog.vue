<template>
  <Dialog v-model="dialogVisible" title="安排复检">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
      <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
      <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
      <el-descriptions-item label="采样日期">{{ formData.samplingDate }}</el-descriptions-item>
      <el-descriptions-item label="检测值">
        <span class="text-danger">
          {{ formData.testValue }} {{ formData.unit }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="标准限值">
        {{ standardValue }} {{ formData.unit }}
      </el-descriptions-item>
      <el-descriptions-item label="检测日期">{{ formData.testDate }}</el-descriptions-item>
      <el-descriptions-item label="检测人">{{ formData.tester }}</el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">复检安排</el-divider>
    
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="recheckForm"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="复检类型" prop="type">
        <el-radio-group v-model="recheckForm.type">
          <el-radio label="resample">重新采样</el-radio>
          <el-radio label="retest">使用原样品重测</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="复检原因" prop="reason">
        <el-input v-model="recheckForm.reason" type="textarea" :rows="3" placeholder="请输入复检原因" />
      </el-form-item>
      <el-form-item label="复检人员" prop="tester">
        <el-select v-model="recheckForm.tester" placeholder="请选择复检人员">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="复检日期" prop="date">
        <el-date-picker v-model="recheckForm.date" type="date" placeholder="请选择复检日期" />
      </el-form-item>
      <el-form-item label="复检备注" prop="remark">
        <el-input v-model="recheckForm.remark" type="textarea" :rows="3" placeholder="请输入复检备注" />
      </el-form-item>
      <el-form-item label="通知相关人" prop="notify">
        <el-checkbox-group v-model="recheckForm.notify">
          <el-checkbox label="原检测人员" />
          <el-checkbox label="部门主管" />
          <el-checkbox label="质量管理员" />
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'

defineOptions({ name: 'RecheckDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

// 用户选项（模拟数据）
const userOptions = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' }
]

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  samplingDate: '',
  testValue: '',
  unit: '',
  testDate: '',
  tester: ''
})

// 复检表单
const recheckForm = ref({
  type: 'retest',
  reason: '',
  tester: '',
  date: '',
  remark: '',
  notify: [] as string[]
})

// 表单校验规则
const formRules = reactive<FormRules>({
  type: [{ required: true, message: '请选择复检类型', trigger: 'change' }],
  reason: [{ required: true, message: '请输入复检原因', trigger: 'blur' }],
  tester: [{ required: true, message: '请选择复检人员', trigger: 'change' }],
  date: [{ required: true, message: '请选择复检日期', trigger: 'change' }]
})

// 计算标准限值
const standardValue = computed(() => {
  switch (formData.value.testItem) {
    case 'COD':
      return '50'
    case 'BOD5':
      return '10'
    case '氨氮':
      return '5'
    case '总磷':
      return '0.5'
    case '总氮':
      return '15'
    default:
      return '/'
  }
})

const formRef = ref()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    samplingPoint: data.samplingPoint,
    samplingDate: data.samplingDate || data.testDate,
    testValue: data.testValue || '',
    unit: data.unit || 'mg/L',
    testDate: data.testDate || '',
    tester: data.tester || ''
  }
  
  // 重置复检表单
  recheckForm.value = {
    type: 'retest',
    reason: `${data.testItem}检测值超标，需要进行复检确认`,
    tester: '',
    date: '',
    remark: '',
    notify: ['原检测人员', '部门主管']
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('复检安排成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}
</style> 
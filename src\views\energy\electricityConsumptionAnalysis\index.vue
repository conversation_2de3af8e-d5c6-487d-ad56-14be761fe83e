<template>
  <div class="w-full" v-loading.fullscreen.lock="loading" element-loading-text="加载中...">
    <ContentWrap>
      <!-- 顶栏筛选区域 -->
      <div class="mb-4 flex items-center gap-4">
        <span class="text-gray-600 text-sm">配置选择:</span>
        <el-select v-model="selectedArea" placeholder="选择配置" @change="handleAreaChange" style="width: 200px;">
          <el-option
            v-for="node in areaOptions"
            :key="node.id"
            :label="node.name"
            :value="String(node.id)"
          />
        </el-select>
      </div>

      <!-- 电耗数据卡片 -->
      <el-card shadow="hover" class="mb-4">
        <div class="flex justify-between items-center mb-4">
          <div class="font-bold text-lg">{{ getIndicatorName }}概览</div>
        </div>
        
        <div class="grid grid-cols-4 gap-4">
          <div class="electricity-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">当月总{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.currentTotal.value }} <span
                class="text-sm">{{ cardData.currentTotal.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span :class="cardData.currentTotal.change >= 0 ? 'text-green-500' : 'text-red-500'"
                class="text-xs flex items-center">
                <el-icon class="mr-1">
                  <ArrowUp v-if="cardData.currentTotal.change >= 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(cardData.currentTotal.change) }}% vs 上月({{ cardData.currentTotal.prevValue }}{{
                  cardData.currentTotal.unit }})
              </span>
            </div>
          </div>

          <div class="electricity-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">同比去年同期{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.yearOnYear.value }} <span
                class="text-sm">{{ cardData.yearOnYear.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span :class="cardData.yearOnYear.change >= 0 ? 'text-green-500' : 'text-red-500'"
                class="text-xs flex items-center">
                <el-icon class="mr-1">
                  <ArrowUp v-if="cardData.yearOnYear.change >= 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(cardData.yearOnYear.change) }}% vs 去年({{ cardData.yearOnYear.prevValue }}{{
                  cardData.yearOnYear.unit }})
              </span>
            </div>
          </div>

          <div class="electricity-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">环比上月{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.monthOnMonth.value }} <span
                class="text-sm">{{ cardData.monthOnMonth.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span :class="cardData.monthOnMonth.change >= 0 ? 'text-green-500' : 'text-red-500'"
                class="text-xs flex items-center">
                <el-icon class="mr-1">
                  <ArrowUp v-if="cardData.monthOnMonth.change >= 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(cardData.monthOnMonth.change) }}% vs 上月({{ cardData.monthOnMonth.prevValue }}{{
                  cardData.monthOnMonth.unit }})
              </span>
            </div>
          </div>

          <div class="electricity-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">吨水{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.perTon.value }} <span class="text-sm">{{
              cardData.perTon.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span class="text-xs text-gray-400">
                <el-icon class="mr-1">
                  <InfoFilled />
                </el-icon>
                {{ cardData.perTon.date }} 数据
              </span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 电耗图表区域 -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <!-- 总电耗趋势图表 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold">总{{ getChartIndicatorName(chartSelectors.waterElectricity.indicator) }}趋势</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.waterElectricity.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="updateWaterElectricityChart"
              />
            </div>
          </div>
          <div ref="waterElectricityChart" class="w-full h-[19rem]"></div>
        </el-card>

        <!-- 同比电耗柱状图 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold text-base">{{ getChartIndicatorName(chartSelectors.monthlyComparison.indicator) }}同比对比</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.monthlyComparison.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="updateMonthlyComparisonChart"
              />
            </div>
          </div>
          <div class="flex gap-4 mb-2">
            <div class="flex items-center">
              <span class="w-3 h-3 inline-block bg-blue-500 rounded-sm mr-1"></span>
              <span class="text-sm">本期</span>
            </div>
            <div class="flex items-center">
              <span class="w-3 h-3 inline-block bg-green-400 rounded-sm mr-1"></span>
              <span class="text-sm">同期对比值</span>
            </div>
          </div>
          <div ref="monthlyComparisonChart" class="w-full h-[19rem]"></div>
        </el-card>
      </div>

      <!-- 电耗分析图表 -->
      <div class="grid grid-cols-2 gap-4">
        <!-- 吨水电耗图表 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold">吨水{{ getChartIndicatorName(chartSelectors.waterConsumption.indicator) }}趋势</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.waterConsumption.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="updateWaterConsumptionChart"
              />
            </div>
          </div>
          <div class="flex items-center mb-2">
            <span class="w-3 h-3 inline-block bg-blue-500 rounded-sm mr-1"></span>
            <span class="text-sm">吨水{{ getChartIndicatorName(chartSelectors.waterConsumption.indicator) }}</span>
          </div>
          <div ref="waterConsumptionChart" class="w-full h-[19rem]"></div>
        </el-card>

        <!-- 能耗对比分析环形图 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold text-base">电耗对比分析</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.energyComparison.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="generateEnergyComparisonData"
              />
            </div>
          </div>
          <div ref="energyComparisonChart" class="w-full h-[19rem]"></div>
        </el-card>
      </div>
    </ContentWrap>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ArrowUp, ArrowDown, InfoFilled } from '@element-plus/icons-vue'
import { getEnergyNodesTree, type EnergyNodeVO } from '@/api/energy/node'
// 暂时不使用后端API，使用模拟数据
// import {
//   getEnergyAnalysis,
//   getTotalEnergyTrend,
//   getEnergyYearOnYearCompare,
//   getEnergyPerTonTrend,
//   getEnergyCompare,
//   type EnergyAnalysisReqVO,
//   type EnergyAnalysisRespVO,
//   type EnergyTrendRespVO,
//   type EnergyCompareRespVO,
//   type EnergyAnalysisCommonReqVO
// } from '@/api/energy/analysis'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/store/modules/app'
import { ContentWrap } from '@/components/ContentWrap'

// 筛选条件
const selectedIndicator = ref('all') // 默认选择"全部"
const selectedArea = ref<string>('') // 默认未选择区域

// 区域选择相关
const areaOptions = ref<EnergyNodeVO[]>([])
const loading = ref(false)

// 获取应用store
const appStore = useAppStore()

// 获取默认日期范围（当前时间往前推12个月）
const getDefaultDateRange = (): [string, string] => {
  const end = new Date()
  const start = new Date()
  start.setMonth(start.getMonth() - 11) // 设置为11个月前（加上当月共12个月）
  
  // 格式化为 YYYY-MM 格式
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    return `${year}-${month}`
  }
  
  return [formatDate(start), formatDate(end)]
}

// 日期范围 - 添加顶栏日期选择器
const dateRange = ref<[string, string]>(getDefaultDateRange())

// 图表选择器
const chartSelectors = reactive({
  waterElectricity: {
    indicator: 'electricity',
    date: getDefaultDateRange()
  },
  monthlyComparison: {
    indicator: 'electricity',
    date: getDefaultDateRange()
  },
  waterConsumption: {
    indicator: 'electricity',
    date: getDefaultDateRange()
  },
  energyComparison: {
    date: getDefaultDateRange()
  }
})

// 数据状态
const cardData = reactive({
  currentTotal: { value: 49.89, unit: 'KWh', change: 5.83, prevValue: 47.14 },
  yearOnYear: { value: 46.48, unit: 'KWh', change: 6.88, prevValue: 43.47 },
  monthOnMonth: { value: 48.22, unit: 'KWh', change: 3.53, prevValue: 46.57, isPositive: true },
  perTon: { value: 0.65, unit: 'KWh/吨', date: '2023-04' }
})

// 计算获取当前指标名称
const getIndicatorName = computed(() => {
  switch (selectedIndicator.value) {
    case 'all':
      return '电耗'
    case 'electricity':
      return '电耗'
    case 'power':
      return '电耗'
    default:
      return '电耗'
  }
})

// 获取特定图表的指标名称
const getChartIndicatorName = (indicator) => {
  switch (indicator) {
    case 'all':
      return '电耗'
    case 'electricity':
      return '电耗'
    case 'power':
      return '电耗'
    default:
      return '电耗'
  }
}

// 筛选条件变化时，重新获取数据
watch([selectedIndicator], () => {
  fetchData()
}, { deep: true })

// 监听顶栏选择器变化
watch(() => selectedIndicator.value, () => {
  // 同步选择器值到所有图表
  syncIndicatorToAllCharts()
  // 更新所有图表
  updateAllCharts()
}, { deep: true })

// 同步指标到所有图表
const syncIndicatorToAllCharts = () => {
  chartSelectors.waterElectricity.indicator = selectedIndicator.value
  chartSelectors.monthlyComparison.indicator = selectedIndicator.value
  chartSelectors.waterConsumption.indicator = selectedIndicator.value
}

// 更新所有图表 - 添加新函数
const updateAllCharts = () => {
  updateWaterElectricityChart()
  updateMonthlyComparisonChart()
  updateWaterConsumptionChart()
}

// 获取数据 - 使用模拟数据
const fetchData = async () => {
  loading.value = true
  try {
    // 获取当前厂站信息
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      ElMessage.error('请先选择厂站')
      return
    }

    // 使用模拟数据更新卡片
    updateDefaultData()

    // 同步指标到所有图表
    syncIndicatorToAllCharts()
    // 更新所有图表
    updateAllCharts()
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')

    // 失败时使用默认数据
    updateDefaultData()
  } finally {
    loading.value = false
  }
}

// 获取总能耗趋势数据 - 使用模拟数据
const fetchTotalEnergyTrend = async (dateRange: [string, string]) => {
  try {
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return null
    }

    if (!selectedArea.value) {
      console.warn('未选择区域节点')
      return null
    }

    // 生成模拟数据
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const months: string[] = []

    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    // 生成模拟的电耗数据
    const values = months.map(() => +(Math.random() * 50 + 200).toFixed(2))

    return {
      dates: months,
      values: values,
      unit: 'kWh'
    }
  } catch (error) {
    console.error('获取总能耗趋势数据失败:', error)
    return null
  }
}

// 获取能耗同比对比数据 - 使用模拟数据
const fetchEnergyYearOnYearCompare = async (dateRange: [string, string]) => {
  try {
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return null
    }

    if (!selectedArea.value) {
      console.warn('未选择区域节点')
      return null
    }

    // 生成模拟数据
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const months: string[] = []

    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    // 生成今年和去年的模拟数据
    const currentValues = months.map(() => +(Math.random() * 50 + 200).toFixed(2))
    const comparisonValues = months.map(() => +(Math.random() * 50 + 180).toFixed(2))

    return {
      currentData: {
        dates: months,
        values: currentValues
      },
      comparisonData: {
        dates: months,
        values: comparisonValues
      },
      unit: 'kWh'
    }
  } catch (error) {
    console.error('获取能耗同比对比数据失败:', error)
    return null
  }
}

// 获取吨水能耗趋势数据 - 使用模拟数据
const fetchEnergyPerTonTrend = async (dateRange: [string, string]) => {
  try {
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return null
    }

    if (!selectedArea.value) {
      console.warn('未选择区域节点')
      return null
    }

    // 生成模拟数据
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const months: string[] = []

    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    // 生成模拟的吨水电耗数据
    const values = months.map(() => +(Math.random() * 0.5 + 1.5).toFixed(3))

    return {
      dates: months,
      values: values,
      unit: 'kWh/吨'
    }
  } catch (error) {
    console.error('获取吨水能耗趋势数据失败:', error)
    return null
  }
}

// 获取区域数据
const fetchAreaOptions = async () => {
  try {
    // 获取当前厂站ID
    const currentStation = appStore.getCurrentStation

    if (!currentStation || !currentStation.id) {
      ElMessage.error('请先选择厂站')
      return
    }

    const response = await getEnergyNodesTree(currentStation.id)

    // 过滤出电耗类型的父节点（parentId 为 null 或 undefined）
    const filterElectricParentNodes = (nodes: EnergyNodeVO[]): EnergyNodeVO[] => {
      const result: EnergyNodeVO[] = []

      const traverse = (nodeList: EnergyNodeVO[]) => {
        nodeList.forEach(node => {
          // 检查是否为电耗类型的父节点（根节点）
          if (node.nodeType === 'electric' &&
              (node.parentId === null || node.parentId === undefined)) {
            result.push(node)
          }

          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }

      traverse(nodes)
      return result
    }

    // 确保id是数字类型
    areaOptions.value = filterElectricParentNodes(response || [])

    // 自动选择ID最小的区域节点
    if (areaOptions.value.length > 0 && !selectedArea.value) {
      // 找出ID最小的节点
      const minIdNode = areaOptions.value.reduce((prev, current) => {
        // 如果当前节点的ID小于之前找到的最小ID节点，则更新
        const prevId = prev.id ? Number(prev.id) : undefined;
        const currentId = current.id ? Number(current.id) : undefined;
        if (currentId !== undefined && (prevId === undefined || currentId < prevId)) {
          return current;
        }
        return prev;
      }, areaOptions.value[0]);
      
      // 设置选中的区域为ID最小的节点
      selectedArea.value = minIdNode.id ? String(minIdNode.id) : '';
    }
  } catch (error) {
    console.error('获取区域数据失败:', error)
    ElMessage.error('获取区域数据失败')
  }
}

// 处理区域选择变化
const handleAreaChange = () => {
  fetchData()
  // 更新环形图数据
  generateEnergyComparisonData()
}

// 从API响应更新卡片数据 - 已移除，使用模拟数据

// 更新默认数据（API调用失败时使用）
const updateDefaultData = () => {
  // 根据选择的指标类型显示不同的数据
  if (selectedIndicator.value === 'all') {
    updateAllData()
  } else if (selectedIndicator.value === 'electricity') {
    updateElectricityData()
  } else if (selectedIndicator.value === 'power') {
    updatePowerData()
  }
}

// 更新所有数据
const updateAllData = () => {
  cardData.currentTotal = { value: 78650, unit: 'KWh', change: 4.92, prevValue: 74950 }
  cardData.yearOnYear = { value: 75830, unit: 'KWh', change: 5.76, prevValue: 71700 }
  cardData.monthOnMonth = { value: 76640, unit: 'KWh', change: 2.87, prevValue: 74500, isPositive: true }
  cardData.perTon = { value: 1.07, unit: 'KWh/吨', date: '2023-04' }
}

// 更新电耗数据
const updateElectricityData = () => {
  // 模拟不同数据
  cardData.currentTotal = { value: 49890, unit: 'KWh', change: 5.83, prevValue: 47140 }
  cardData.yearOnYear = { value: 46480, unit: 'KWh', change: 6.88, prevValue: 43470 }
  cardData.monthOnMonth = { value: 48220, unit: 'KWh', change: 3.53, prevValue: 46570, isPositive: true }
  cardData.perTon = { value: 65, unit: 'KWh/吨', date: '2023-04' }
}

// 更新电耗数据（二沉池区域）
const updatePowerData = () => {
  // 模拟不同数据
  cardData.currentTotal = { value: 28760, unit: 'KWh', change: 3.42, prevValue: 27810 }
  cardData.yearOnYear = { value: 29350, unit: 'KWh', change: 5.24, prevValue: 27890 }
  cardData.monthOnMonth = { value: 28420, unit: 'KWh', change: 2.19, prevValue: 27810, isPositive: true }
  cardData.perTon = { value: 42, unit: 'KWh/吨', date: '2023-04' }
}

// 更新图表数据
const updateChartData = (chartType: string) => {
  // 使用顶栏的日期范围
  console.log(`更新${chartType}图表数据，区域：${selectedIndicator.value}，时间范围：`, dateRange.value)

  // 重新初始化对应的图表，图表内部会使用新的时间范围
  switch (chartType) {
    case 'waterElectricity':
      updateWaterElectricityChart()
      break
    case 'monthlyComparison':
      updateMonthlyComparisonChart()
      break
    case 'waterConsumption':
      updateWaterConsumptionChart()
      break
  }
}

// 图表DOM引用
const monthlyComparisonChart = ref<HTMLElement | null>(null)
const waterElectricityChart = ref<HTMLElement | null>(null)
const waterConsumptionChart = ref<HTMLElement | null>(null)
const energyComparisonChart = ref<HTMLElement | null>(null)

// 图表实例
let monthlyChart: echarts.ECharts
let waterElecChart: echarts.ECharts
let waterConsumptionChartInstance: echarts.ECharts
let energyComparisonChartInstance: echarts.ECharts

onMounted(async () => {
  // 等待下一个tick，确保所有响应式数据都已初始化
  await nextTick()

  // 初始化基础图表（不依赖区域数据的图表）
  initWaterElectricityChart()
  initMonthlyComparisonChart()
  initWaterConsumptionChart()

  // 添加短暂延迟，等待厂站信息加载
  setTimeout(async () => {
    // 初始获取区域数据
    await fetchAreaOptions()

    // 初始获取数据
    fetchData()

    // 初始化依赖区域数据的图表
    initEnergyComparisonChart()
  }, 500)

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    monthlyChart?.resize()
    waterElecChart?.resize()
    waterConsumptionChartInstance?.resize()
    energyComparisonChartInstance?.resize()
  })
})

// 监听厂站变化
watch(() => appStore.getCurrentStation, async (newStation) => {
  if (newStation && newStation.id) {
    await fetchAreaOptions()
  }
}, { immediate: false })

// 初始化总电耗趋势图
const initWaterElectricityChart = () => {
  if (waterElectricityChart.value) {
    waterElecChart = echarts.init(waterElectricityChart.value)
    
    // 使用顶栏的日期范围和指标
    const xAxisData = generateTimeData('month', dateRange.value)
    const currentData = generateDataForIndicator(selectedIndicator.value, xAxisData, 'current') || []
    
    // 计算Y轴的最大值和最小值，确保图表显示完整
    const dataValues = currentData.map(v => typeof v === 'string' ? parseFloat(v) : v)
    const maxValue = dataValues.length > 0 ? Math.max(...dataValues) * 1.2 : 100
    const minValue = dataValues.length > 0 ? Math.min(...dataValues) * 0.8 : 0
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value',
        name: '电耗 (KWh)',
        min: Math.floor(minValue),
        max: Math.ceil(maxValue)
      },
      series: [
        {
          name: '总' + getChartIndicatorName(selectedIndicator.value),
          type: 'line',
          smooth: true,
          data: currentData,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0)'
                }
              ]
            }
          }
        }
      ]
    }
    waterElecChart.setOption(option)
  }
}

// 初始化月KWh同比对比图表
const initMonthlyComparisonChart = () => {
  if (monthlyComparisonChart.value) {
    monthlyChart = echarts.init(monthlyComparisonChart.value)

    // 使用顶栏的日期范围和指标
    const xAxisData = generateTimeData('month', dateRange.value)
    const currentData = generateDataForIndicator(selectedIndicator.value, xAxisData, 'current') || []
    const comparisonData = generateDataForIndicator(selectedIndicator.value, xAxisData, 'comparison') || []
    
    // 计算Y轴的最大值和最小值，确保图表显示完整
    const allDataValues = [
      ...currentData.map(v => typeof v === 'string' ? parseFloat(v) : v),
      ...comparisonData.map(v => typeof v === 'string' ? parseFloat(v) : v)
    ]
    const maxValue = allDataValues.length > 0 ? Math.max(...allDataValues) * 1.2 : 100
    const minValue = allDataValues.length > 0 ? Math.min(...allDataValues) * 0.8 : 0

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value',
        name: '电耗 (KWh)',
        min: Math.floor(minValue),
        max: Math.ceil(maxValue)
      },
      series: [
        {
          name: '本期',
          type: 'bar',
          data: currentData,
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '同期对比值',
          type: 'bar',
          data: comparisonData,
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    }
    monthlyChart.setOption(option)
  }
}

// 初始化吨水电耗趋势图
const initWaterConsumptionChart = () => {
  if (waterConsumptionChart.value) {
    waterConsumptionChartInstance = echarts.init(waterConsumptionChart.value)

    // 使用顶栏的日期范围和指标
    const xAxisData = generateTimeData('month', dateRange.value)
    const waterConsumptionData = generateDataForIndicator(selectedIndicator.value, xAxisData, 'waterConsumption') || []
    
    // 计算Y轴的最大值和最小值，确保图表显示完整
    const dataValues = waterConsumptionData.map(v => typeof v === 'string' ? parseFloat(v) : v)
    const maxValue = dataValues.length > 0 ? Math.max(...dataValues) * 1.2 : 1
    const minValue = dataValues.length > 0 ? Math.min(...dataValues) * 0.8 : 0

    const option = {
      tooltip: {
        trigger: 'axis'
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        data: xAxisData
      },
      yAxis: {
        type: 'value',
        name: '电耗 (KWh/吨)',
        min: Math.floor(minValue * 10) / 10,
        max: Math.ceil(maxValue * 10) / 10
      },
      series: [
        {
          name: '吨水' + getChartIndicatorName(selectedIndicator.value),
          type: 'line',
          smooth: true,
          data: waterConsumptionData,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#409EFF'
          }
        }
      ]
    }
    waterConsumptionChartInstance.setOption(option)
  }
}



// 生成时间数据
const generateTimeData = (timeType: string, dateRange?: string[]): string[] => {
  let xAxisData: string[] = []

  // 如果提供了日期范围，根据范围生成数据
  if (dateRange && dateRange.length === 2) {
    const startDate = dateRange[0] // 格式: YYYY-MM
    const endDate = dateRange[1]   // 格式: YYYY-MM

    const startYear = parseInt(startDate.split('-')[0])
    const startMonth = parseInt(startDate.split('-')[1])
    const endYear = parseInt(endDate.split('-')[0])
    const endMonth = parseInt(endDate.split('-')[1])

    // 生成月份范围数据
    for (let year = startYear; year <= endYear; year++) {
      const monthStart = year === startYear ? startMonth : 1
      const monthEnd = year === endYear ? endMonth : 12

      for (let month = monthStart; month <= monthEnd; month++) {
        xAxisData.push(`${year}-${String(month).padStart(2, '0')}`)
      }
    }

    return xAxisData
  }

  // 默认数据生成逻辑
  switch (timeType) {
    case 'day':
      xAxisData = Array.from({ length: 30 }, (_, i) => `2023-04-${String(i + 1).padStart(2, '0')}`)
      break
    case 'month':
      xAxisData = ['2023-01', '2023-02', '2023-03', '2023-04', '2023-05', '2023-06',
        '2023-07', '2023-08', '2023-09', '2023-10', '2023-11', '2023-12']
      break
    case 'quarter':
      xAxisData = ['2022-Q1', '2022-Q2', '2022-Q3', '2022-Q4', '2023-Q1', '2023-Q2', '2023-Q3', '2023-Q4']
      break
    case 'year':
      xAxisData = ['2018', '2019', '2020', '2021', '2022', '2023']
      break
  }

  return xAxisData
}

// 生成不同指标的数据
const generateDataForIndicator = (indicator: string, xAxisData: string[], dataType: string) => {
  if (indicator === 'all') {
    // 总电耗数据模拟 - 数值应该是各区域之和
    switch (dataType) {
      case 'current':
        return xAxisData.map(() => Math.floor(Math.random() * 60) + 90) // 更高的基础值和范围
      case 'comparison':
        return xAxisData.map(() => Math.floor(Math.random() * 50) + 85) // 更高的基础值和范围
      case 'waterConsumption':
        return xAxisData.map(() => (Math.random() * 0.4 + 0.7).toFixed(2)) // 更高的基础值
      case 'pollution':
        return xAxisData.map(() => Math.floor(Math.random() * 20) + 10) // 更高的基础值和范围
    }
  } else if (indicator === 'electricity') {
    // 污水处理区电耗数据模拟
    switch (dataType) {
      case 'current':
        return xAxisData.map(() => Math.floor(Math.random() * 50) + 70)
      case 'comparison':
        return xAxisData.map(() => Math.floor(Math.random() * 40) + 65)
      case 'waterConsumption':
        return xAxisData.map(() => (Math.random() * 0.3 + 0.3).toFixed(2))
      case 'pollution':
        return xAxisData.map(() => Math.floor(Math.random() * 15) + 8)
    }
  } else {
    // 二沉池区域电耗数据模拟
    switch (dataType) {
      case 'current':
        return xAxisData.map(() => Math.floor(Math.random() * 15) + 20)
      case 'comparison':
        return xAxisData.map(() => Math.floor(Math.random() * 12) + 18)
      case 'waterConsumption':
        return xAxisData.map(() => (Math.random() * 0.2 + 0.3).toFixed(2))
      case 'pollution':
        return xAxisData.map(() => Math.floor(Math.random() * 12) + 5)
    }
  }
}

// 更新总电耗趋势图
const updateWaterElectricityChart = async () => {
  if (waterElectricityChart.value && waterElecChart) {
    // 使用日期选择器的值作为日期范围
    const dateRange = chartSelectors.waterElectricity.date
    
    // 获取API数据
    const apiData = await fetchTotalEnergyTrend(dateRange)
    
    if (apiData) {
      // 使用API数据
      const xAxisData = apiData.dates.map(date => {
        const [, month] = date.split('-')
        return `${month}月`
      })
      
      const electricityData = apiData.values
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...electricityData) * 1.2
      const minValue = Math.min(...electricityData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: `电耗 (${apiData.unit})`,
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '总电耗',
            type: 'line',
            data: electricityData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      waterElecChart.setOption(option)
    } else {
      // 如果API调用失败，使用模拟数据
      const startDate = new Date(dateRange[0])
      const endDate = new Date(dateRange[1])
      const months: string[] = []
      
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }
      
      const xAxisData = months.map(m => {
        const [, month] = m.split('-')
        return `${month}月`
      })
      
      // 生成总电耗数据
      const electricityData = months.map(() => +(Math.random() * 2000 + 6000).toFixed(0))
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...electricityData) * 1.2
      const minValue = Math.min(...electricityData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '电耗 (KWh)',
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '总电耗',
            type: 'line',
            data: electricityData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      waterElecChart.setOption(option)
    }
  }
}

// 更新月KWh同比对比图表
const updateMonthlyComparisonChart = async () => {
  if (monthlyComparisonChart.value && monthlyChart) {
    // 使用日期选择器的值作为日期范围
    const dateRange = chartSelectors.monthlyComparison.date
    
    // 获取API数据
    const apiData = await fetchEnergyYearOnYearCompare(dateRange)
    
    if (apiData) {
      // 使用API数据
      const xAxisData = apiData.currentData.dates.map(date => {
        const [, month] = date.split('-')
        return `${month}月`
      })
      
      const currentData = apiData.currentData.values
      const comparisonData = apiData.comparisonData.values
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const allDataValues = [...currentData, ...comparisonData]
      const maxValue = Math.max(...allDataValues) * 1.2
      const minValue = Math.min(...allDataValues) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: `电耗 (${apiData.unit})`,
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '本期',
            type: 'bar',
            data: currentData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '同期对比值',
            type: 'bar',
            data: comparisonData,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      monthlyChart.setOption(option)
    } else {
      // 如果API调用失败，使用模拟数据
      const startDate = new Date(dateRange[0])
      const endDate = new Date(dateRange[1])
      const months: string[] = []
      
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }
      
      const xAxisData = months.map(m => {
        const [, month] = m.split('-')
        return `${month}月`
      })
      
      // 生成本期和同期对比数据
      const currentData = months.map(() => +(Math.random() * 1000 + 4000).toFixed(0))
      const comparisonData = months.map(() => +(Math.random() * 1000 + 3800).toFixed(0))
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const allDataValues = [...currentData, ...comparisonData]
      const maxValue = Math.max(...allDataValues) * 1.2
      const minValue = Math.min(...allDataValues) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '电耗 (KWh)',
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '本期',
            type: 'bar',
            data: currentData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '同期对比值',
            type: 'bar',
            data: comparisonData,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      monthlyChart.setOption(option)
    }
  }
}

// 更新吨水电耗趋势图
const updateWaterConsumptionChart = async () => {
  if (waterConsumptionChart.value && waterConsumptionChartInstance) {
    // 使用日期选择器的值作为日期范围
    const dateRange = chartSelectors.waterConsumption.date
    
    // 获取API数据
    const apiData = await fetchEnergyPerTonTrend(dateRange)
    
    if (apiData) {
      // 使用API数据
      const xAxisData = apiData.dates.map(date => {
        const [, month] = date.split('-')
        return `${month}月`
      })
      
      const waterConsumptionData = apiData.values
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...waterConsumptionData) * 1.2
      const minValue = Math.min(...waterConsumptionData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: `电耗 (${apiData.unit})`,
          min: Math.floor(minValue * 10) / 10,
          max: Math.ceil(maxValue * 10) / 10
        },
        series: [
          {
            name: '吨水电耗',
            type: 'line',
            data: waterConsumptionData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      
      waterConsumptionChartInstance.setOption(option)
    } else {
      // 如果API调用失败，使用模拟数据
      const startDate = new Date(dateRange[0])
      const endDate = new Date(dateRange[1])
      const months: string[] = []
      
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }
      
      const xAxisData = months.map(m => {
        const [, month] = m.split('-')
        return `${month}月`
      })
      
      // 生成吨水电耗数据
      const waterConsumptionData = months.map(() => +(Math.random() * 0.5 + 0.3).toFixed(2))
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...waterConsumptionData) * 1.2
      const minValue = Math.min(...waterConsumptionData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '电耗 (KWh/吨)',
          min: Math.floor(minValue * 10) / 10,
          max: Math.ceil(maxValue * 10) / 10
        },
        series: [
          {
            name: '吨水电耗',
            type: 'line',
            data: waterConsumptionData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      
      waterConsumptionChartInstance.setOption(option)
    }
  }
}

// 初始化能耗对比分析环形图
const initEnergyComparisonChart = async () => {
  if (energyComparisonChart.value) {
    energyComparisonChartInstance = echarts.init(energyComparisonChart.value)

    // 获取当前选中区域的节点数据
    await generateEnergyComparisonData()
  }
}

// 生成能耗对比分析数据
const generateEnergyComparisonData = async () => {
  try {
    // 获取当前厂站ID
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return
    }

    // 获取节点树数据
    const response = await getEnergyNodesTree(currentStation.id)

    // 找到当前选中区域的节点
    const findSelectedNode = (nodes: EnergyNodeVO[], targetId: number): EnergyNodeVO | null => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = findSelectedNode(node.children, targetId)
          if (found) return found
        }
      }
      return null
    }

    if (!selectedArea.value) {
      // 如果没有选中区域，显示空状态或默认提示
      const option = {
        tooltip: {
          show: false
        },
        legend: {
          show: false
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '请选择区域查看数据',
            fontSize: 16,
            fill: '#999'
          }
        },
        series: []
      }
      energyComparisonChartInstance.setOption(option)
      return
    }

    const selectedNode = findSelectedNode(response || [], Number(selectedArea.value))
    if (!selectedNode || !selectedNode.children) {
      // 如果选中的节点没有子节点，显示提示
      const option = {
        tooltip: {
          show: false
        },
        legend: {
          show: false
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '该区域暂无子节点数据',
            fontSize: 16,
            fill: '#999'
          }
        },
        series: []
      }
      energyComparisonChartInstance.setOption(option)
      return
    }

    // 分离二级和三级节点
    const secondLevelNodes: EnergyNodeVO[] = []
    const thirdLevelNodes: EnergyNodeVO[] = []

    selectedNode.children.forEach(child => {
      if (child.nodeType === 'electric') {
        secondLevelNodes.push(child)
        if (child.children && child.children.length > 0) {
          child.children.forEach(grandChild => {
            if (grandChild.nodeType === 'electric') {
              thirdLevelNodes.push(grandChild)
            }
          })
        }
      }
    })

    // 生成环形图数据
    const innerData = secondLevelNodes.map((node, index) => ({
      value: Math.floor(Math.random() * 50) + 30,
      name: node.name,
      itemStyle: {
        color: getNodeColor(index, 'inner')
      }
    }))

    const outerData = thirdLevelNodes.map((node, index) => ({
      value: Math.floor(Math.random() * 30) + 15,
      name: node.name,
      itemStyle: {
        color: getNodeColor(index, 'outer')
      }
    }))

    // 检查是否有数据
    if (innerData.length === 0 && outerData.length === 0) {
      const option = {
        tooltip: {
          show: false
        },
        legend: {
          show: false
        },
        graphic: {
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '该区域暂无电耗数据',
            fontSize: 16,
            fill: '#999'
          }
        },
        series: []
      }
      energyComparisonChartInstance.setOption(option)
      return
    }

    // 设置图表配置
    const option = {
      tooltip: {
        show: false
      },
      legend: {
        show: false
      },
      series: [
        {
          name: '二级节点',
          type: 'pie',
          radius: ['0%', '45%'],
          center: ['50%', '50%'],
          data: innerData,
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}\n{c}KWh',
            fontSize: 12,
            color: '#fff'
          },
          labelLine: {
            show: false
          }
        },
        {
          name: '三级节点',
          type: 'pie',
          radius: ['50%', '75%'],
          center: ['50%', '50%'],
          data: outerData,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {c}KWh',
            fontSize: 12,
            color: '#333'
          },
          labelLine: {
            show: true,
            length: 12,
            length2: 8
          }
        }
      ]
    }

    energyComparisonChartInstance.setOption(option)
  } catch (error) {
    console.error('生成能耗对比数据失败:', error)
  }
}

// 获取节点颜色
const getNodeColor = (index: number, type: 'inner' | 'outer'): string => {
  const innerColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const outerColors = ['#79BBFF', '#95D475', '#EEBE77', '#F89898', '#B3B3B3']

  if (type === 'inner') {
    return innerColors[index % innerColors.length]
  } else {
    return outerColors[index % outerColors.length]
  }
}
</script>

<style scoped lang="scss">
.electricity-card {
  transition: all 0.3s;
  border-radius: 0.5rem;
  overflow: hidden;

  &:hover {
    transform: translateY(-0.3rem);
    box-shadow: 0 0.625rem 0.9375rem -0.1875rem rgba(0, 0, 0, 0.1),
      0 0.25rem 0.375rem -0.125rem rgba(0, 0, 0, 0.05);
  }
}

.electricity-data-item {
  transition: all 0.3s;
  padding: 1rem;
  border-radius: 0.5rem;
  background-color: #f8f9fa;
  
  &:hover {
    transform: translateY(-0.3rem);
    box-shadow: 0 0.625rem 0.9375rem -0.1875rem rgba(0, 0, 0, 0.1),
      0 0.25rem 0.375rem -0.125rem rgba(0, 0, 0, 0.05);
  }
}

:deep(.el-card) {
  border-radius: 0.5rem;
  transition: all 0.3s;

  &.chart-card:hover {
    box-shadow: 0 0.5rem 0.75rem -0.125rem rgba(0, 0, 0, 0.1);
  }

  .el-card__body {
    padding: 1rem;
    height: 100%;
  }
}

:deep(.el-radio-button__inner) {
  padding: 0.5rem 1rem;
}

:deep(.el-radio-button.is-active .el-radio-button__inner) {
  background-color: #409EFF;
  border-color: #409EFF;
  color: white;
}

.el-icon-caret-top,
.el-icon-caret-bottom {
  margin-right: 0.25rem;
}

// 紧凑型日期选择器样式 - 使用更强的选择器
:deep(.el-date-editor.compact-date-picker) {
  width: 15rem !important;
  min-width: 15rem !important;
  max-width: 15rem !important;
  
  .el-input__wrapper,
  .el-range-editor {
    width: 15rem !important;
    min-width: 15rem !important;
    max-width: 15rem !important;
  }
  
  .el-range-input {
    width: 40% !important;
    font-size: 12px !important;
  }
  
  .el-range-separator {
    width: 10% !important;
    font-size: 12px !important;
    padding: 0 2px !important;
  }
  
  .el-range__close-icon,
  .el-range__icon {
    margin-left: 0 !important;
    font-size: 12px !important;
  }
}

// 直接强制覆盖 Element Plus 的内部样式
:deep(.el-date-editor--monthrange) {
  width: 15rem !important;
}

// 覆盖父元素容器宽度
.date-picker-container {
  width: 15rem !important;
  min-width: 15rem !important;
  max-width: 15rem !important;
}
</style>

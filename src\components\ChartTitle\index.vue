<template>
  <div class="chart-title">
    <h4 class="title">{{ title }}</h4>
    <slot name="extra"></slot>
  </div>
</template>

<script lang="ts" setup>
defineOptions({ name: 'ChartTitle' })

interface Props {
  title: string
}

defineProps<Props>()
</script>

<style lang="scss" scoped>
.chart-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  padding: 0 0.5rem;
  
  .title {
    font-size: 1rem;
    font-weight: bold;
    color: #303133;
    margin: 0;
  }
}
</style> 
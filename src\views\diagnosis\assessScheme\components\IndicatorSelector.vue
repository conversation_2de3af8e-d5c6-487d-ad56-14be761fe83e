<template>
  <el-dialog v-model="visible" title="选择指标" width="50rem" :close-on-click-modal="false" @close="handleClose">
    <div class="indicator-selector">
      <div class="mb-4">
        <el-input v-model="searchKeyword" placeholder="搜索指标名称" clearable @input="filterIndicators">
          <template #prefix>
            <el-icon>
              <Search />
            </el-icon>
          </template>
        </el-input>
      </div>

      <div class="indicator-list max-h-96 overflow-y-auto">
        <el-checkbox-group v-model="selectedIndicatorIds">
          <div v-for="indicator in filteredIndicators" :key="indicator.id" class="indicator-option">
            <div class="indicator-checkbox-wrapper">
              <el-checkbox :value="indicator.id" class="indicator-checkbox" />
              <div class="indicator-content">
                <div class="indicator-header">
                  <span class="indicator-name">{{ indicator.name }}</span>
                  <div class="indicator-tags">
                    <el-tag size="small" type="info">{{ indicator.unit }}</el-tag>
                    <el-tag size="small" type="primary">{{ indicator.cycle }}</el-tag>
                  </div>
                </div>
                <div class="indicator-details">
                  <div class="indicator-detail-item">
                    <span class="detail-label">公式:</span>
                    <span class="detail-value">{{ indicator.formula }}</span>
                  </div>
                  <div v-if="indicator.description" class="indicator-detail-item">
                    <span class="detail-label">说明:</span>
                    <span class="detail-value">{{ indicator.description }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-checkbox-group>
      </div>

      <div v-if="filteredIndicators.length === 0" class="text-center text-gray-400 py-8">
        {{ searchKeyword ? '未找到匹配的指标' : '暂无可用指标' }}
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">
          确定选择 ({{ selectedIndicatorIds.length }})
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { Search } from '@element-plus/icons-vue'

// 定义接口类型
interface Indicator {
  id: string
  name: string
  unit: string
  formula: string
  description?: string
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  weight?: number
}

// 定义属性
const props = defineProps<{
  modelValue: boolean
  indicatorLibrary: Indicator[]
  schemeCycle?: '时' | '天' | '周' | '月度' | '季度' | '年度'
}>()

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'confirm': [indicators: Indicator[]]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const searchKeyword = ref('')
const selectedIndicatorIds = ref<string[]>([])

// 过滤后的指标数据
const filteredIndicators = computed(() => {
  let indicators = props.indicatorLibrary

  // 根据方案评估周期筛选指标
  if (props.schemeCycle) {
    indicators = indicators.filter(indicator => indicator.cycle === props.schemeCycle)
  }

  // 根据搜索关键词筛选
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    indicators = indicators.filter(indicator =>
      indicator.name.toLowerCase().includes(keyword) ||
      indicator.description?.toLowerCase().includes(keyword) ||
      indicator.formula.toLowerCase().includes(keyword)
    )
  }

  return indicators
})

// 监听弹窗打开，重置选择状态
watch(visible, (newVisible) => {
  if (newVisible) {
    selectedIndicatorIds.value = []
    searchKeyword.value = ''
  }
})

// 方法
const filterIndicators = () => {
  // 过滤逻辑已在 computed 中实现
}

const handleConfirm = () => {
  // 获取选中的指标
  const selectedIndicators = props.indicatorLibrary.filter(indicator =>
    selectedIndicatorIds.value.includes(indicator.id)
  )

  emit('confirm', selectedIndicators)
  emit('update:modelValue', false)
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

const handleClose = () => {
  emit('update:modelValue', false)
}
</script>

<style scoped lang="scss">
// 指标选择器样式
.indicator-selector {
  .indicator-list {
    border: 0.0625rem solid #e4e7ed;
    border-radius: 0.375rem;
    padding: 0.5rem;

    .indicator-option {
      border: 0.0625rem solid #e4e7ed;
      border-radius: 0.375rem;
      padding: 1rem;
      transition: all 0.2s ease;
      background-color: #fff;
      margin-bottom: 0.75rem;

      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }

      &:last-child {
        margin-bottom: 0;
      }

      .indicator-checkbox-wrapper {
        display: flex;
        align-items: flex-start;
        gap: 12px;
        width: 100%;
      }

      .indicator-checkbox {
        flex-shrink: 0;
        margin-top: 2px;

        :deep(.el-checkbox__label) {
          display: none;
        }
      }

      .indicator-content {
        flex: 1;
        min-width: 0;

        .indicator-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 8px;
          gap: 8px;

          .indicator-name {
            font-weight: 600;
            color: #303133;
            font-size: 14px;
            line-height: 1.4;
            flex: 1;
            min-width: 0;
          }

          .el-tag {
            flex-shrink: 0;
          }

          .indicator-tags {
            display: flex;
            gap: 4px;
            flex-shrink: 0;
          }
        }

        .indicator-details {
          .indicator-detail-item {
            margin-bottom: 4px;
            font-size: 12px;
            line-height: 1.4;

            .detail-label {
              color: #909399;
              font-weight: 500;
              margin-right: 4px;
            }

            .detail-value {
              color: #606266;
              word-break: break-word;
            }
          }
        }
      }
    }
  }
}

// 对话框底部样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

// 标签样式优化
.el-tag {
  &.el-tag--small {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
  }
}
</style>

<template>
  <Dialog v-model="dialogVisible" title="检测数据录入">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="样品编号">
        <el-input v-model="formData.sampleCode" disabled />
      </el-form-item>
      <el-form-item label="检测项目">
        <el-input v-model="formData.testItem" disabled />
      </el-form-item>
      <el-form-item label="采样点">
        <el-input v-model="formData.samplingPoint" disabled />
      </el-form-item>
      <el-form-item label="采样日期">
        <el-input v-model="formData.samplingDate" disabled />
      </el-form-item>
      <el-divider content-position="center">检测数据</el-divider>
      <el-form-item label="检测值" prop="testValue">
        <div class="test-value-input">
          <el-input
            v-model="formData.testValue"
            placeholder="请输入检测值"
            @input="validateTestValue"
            @focus="showInputGuide = true"
            @blur="showInputGuide = false"
            clearable
          >
            <template #append>{{ formData.unit }}</template>
            <template #prefix>
              <el-icon><TrendCharts /></el-icon>
            </template>
          </el-input>

          <!-- 输入引导提示 -->
          <div v-if="showInputGuide" class="input-guide">
            <div class="guide-item">
              <span class="guide-label">标准值:</span>
              <span class="guide-value">{{ getStandardValueText() }}</span>
            </div>
            <div class="guide-item">
              <span class="guide-label">报警阈值:</span>
              <span class="guide-value warning">{{ getAlarmThresholdText() }}</span>
            </div>
            <div class="guide-item">
              <span class="guide-label">精度要求:</span>
              <span class="guide-value">{{ getPrecisionText() }}</span>
            </div>
            <div class="guide-actions">
              <el-button size="small" type="text" @click="showTrend = !showTrend">
                <el-icon><TrendCharts /></el-icon>查看趋势
              </el-button>
              <el-button size="small" type="text" @click="showHistory = !showHistory">
                <el-icon><Clock /></el-icon>历史数据
              </el-button>
            </div>
          </div>
        </div>
      </el-form-item>

      <!-- 智能验证反馈 -->
      <div v-if="showValueFeedback" class="value-feedback mt-2">
        <el-alert
          :title="valueFeedbackTitle"
          :type="valueFeedbackType"
          :description="valueFeedbackDescription"
          show-icon
          :closable="false"
        >
          <template #default>
            <div class="feedback-content">
              <p>{{ valueFeedbackDescription }}</p>
              <!-- 智能建议 -->
              <div v-if="getSmartSuggestions().length > 0" class="smart-suggestions">
                <p class="suggestions-title">建议操作:</p>
                <div class="suggestions-actions">
                  <el-button
                    v-for="suggestion in getSmartSuggestions()"
                    :key="suggestion.action"
                    size="small"
                    :type="suggestion.type"
                    @click="handleSuggestion(suggestion)"
                  >
                    <el-icon v-if="suggestion.icon">
                      <component :is="suggestion.icon" />
                    </el-icon>
                    {{ suggestion.text }}
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 历史数据趋势 -->
      <div v-if="showTrend && historicalData.length > 0" class="trend-chart mt-2">
        <div class="trend-header">
          <span class="trend-title">
            <el-icon><TrendCharts /></el-icon>
            近期趋势 (最近10次)
          </span>
          <el-button type="text" size="small" @click="showTrend = false">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <div class="trend-container">
          <div class="trend-line">
            <div
              v-for="(point, index) in historicalData"
              :key="index"
              class="trend-point"
              :class="getTrendPointClass(point.value)"
              :style="{ left: `${(index / (historicalData.length - 1)) * 100}%` }"
              :title="`${point.date}: ${point.value}${formData.unit}`"
            ></div>
            <!-- 当前输入值 -->
            <div v-if="formData.testValue" class="current-point" :style="{ left: '100%' }">
              <span class="current-value">{{ formData.testValue }}</span>
            </div>
          </div>
          <div class="trend-labels">
            <span class="label-start">{{ historicalData[0]?.date }}</span>
            <span class="label-end">{{ historicalData[historicalData.length - 1]?.date }}</span>
          </div>
        </div>
      </div>
      
      <el-form-item label="检测日期" prop="testDate">
        <el-date-picker v-model="formData.testDate" type="date" placeholder="请选择检测日期" />
      </el-form-item>
      <el-form-item label="检测人" prop="tester">
        <el-select v-model="formData.tester" placeholder="请选择检测人">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="检测方法" prop="method">
        <el-select v-model="formData.method" placeholder="请选择检测方法">
          <el-option v-for="method in methodOptions" :key="method.id" :label="method.name" :value="method.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="检测仪器" prop="instrument">
        <el-select v-model="formData.instrument" placeholder="请选择检测仪器">
          <el-option v-for="instrument in instrumentOptions" :key="instrument.id" :label="instrument.name" :value="instrument.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="平行双样" prop="parallelSample">
        <el-switch v-model="formData.parallelSample" />
      </el-form-item>
      <div v-if="formData.parallelSample" class="parallel-sample mt-2">
        <el-form-item label="平行样值" prop="parallelValue">
          <el-input v-model="formData.parallelValue" placeholder="请输入平行样检测值" @input="validateParallelValue">
            <template #append>{{ formData.unit }}</template>
          </el-input>
        </el-form-item>
        
        <div v-if="showParallelFeedback" class="parallel-feedback mt-2">
          <el-alert
            :title="parallelFeedbackTitle"
            :type="parallelFeedbackType"
            :description="parallelFeedbackDescription"
            show-icon
            :closable="false"
          />
        </div>
      </div>
      <el-form-item label="质控样" prop="qualityControl">
        <el-switch v-model="formData.qualityControl" />
      </el-form-item>
      <div v-if="formData.qualityControl" class="quality-control mt-2">
        <el-form-item label="质控样类型" prop="qualityControlType">
          <el-select v-model="formData.qualityControlType" placeholder="请选择质控样类型">
            <el-option label="加标回收" value="spiked" />
            <el-option label="标准样品" value="standard" />
            <el-option label="空白样" value="blank" />
          </el-select>
        </el-form-item>
        <el-form-item label="质控样值" prop="qualityControlValue">
          <el-input v-model="formData.qualityControlValue" placeholder="请输入质控样值">
            <template #append>{{ formData.unit }}</template>
          </el-input>
        </el-form-item>
        <el-form-item v-if="formData.qualityControlType === 'spiked'" label="回收率" prop="recoveryRate">
          <el-input v-model="formData.recoveryRate" placeholder="请输入回收率">
            <template #append>%</template>
          </el-input>
        </el-form-item>
      </div>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Warning, Check, Close, TrendCharts, Clock, RefreshRight, Document } from '@element-plus/icons-vue'

defineOptions({ name: 'DataEntryDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)
const isResubmit = ref(false)
const showInputGuide = ref(false)
const showTrend = ref(false)
const showHistory = ref(false)

interface UserOption {
  id: number;
  name: string;
}

interface MethodOption {
  id: number;
  name: string;
}

interface InstrumentOption {
  id: number;
  name: string;
}

interface FormData {
  id?: number;
  sampleCode: string;
  testItem: string;
  samplingPoint: string;
  samplingDate: string;
  testValue: string;
  unit: string;
  testDate: string | Date;
  tester: string;
  method: string;
  instrument: string;
  parallelSample: boolean;
  parallelValue: string;
  qualityControl: boolean;
  qualityControlType: string;
  qualityControlValue: string;
  recoveryRate: string;
  remark: string;
  standard?: { min: number; max: number; unit: string; warn: number } | null;
}

// 用户选项（模拟数据）
const userOptions: UserOption[] = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' }
]

// 检测方法选项（模拟数据）
const methodOptions: MethodOption[] = [
  { id: 1, name: '重铬酸钾法' },
  { id: 2, name: '稀释接种法' },
  { id: 3, name: '纳氏试剂比色法' },
  { id: 4, name: '钼酸铵分光光度法' },
  { id: 5, name: '碱性过硫酸钾消解紫外分光光度法' }
]

// 检测仪器选项（模拟数据）
const instrumentOptions: InstrumentOption[] = [
  { id: 1, name: '分光光度计A' },
  { id: 2, name: '分光光度计B' },
  { id: 3, name: 'BOD测定仪' },
  { id: 4, name: '紫外分光光度计' },
  { id: 5, name: '原子吸收光谱仪' }
]

// 表单数据
const formData = ref<FormData>({
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  samplingDate: '',
  testValue: '',
  unit: 'mg/L',
  testDate: '',
  tester: '',
  method: '',
  instrument: '',
  parallelSample: false,
  parallelValue: '',
  qualityControl: false,
  qualityControlType: 'standard',
  qualityControlValue: '',
  recoveryRate: '',
  remark: ''
})

// 历史数据（模拟）
const historicalData = ref([
  { date: '07-01', value: 8.5 },
  { date: '07-02', value: 9.2 },
  { date: '07-03', value: 7.8 },
  { date: '07-04', value: 8.9 },
  { date: '07-05', value: 9.5 },
  { date: '07-06', value: 8.1 },
  { date: '07-07', value: 8.7 },
  { date: '07-08', value: 9.1 },
  { date: '07-09', value: 8.3 },
  { date: '07-10', value: 8.8 }
])

// 获取检测项目的标准范围
const getTestItemStandard = (testItem: string) => {
  // 模拟从配置或API获取标准值
  switch (testItem) {
    case 'COD':
      return { min: 0, max: 50, unit: 'mg/L', warn: 40 }
    case 'BOD5':
      return { min: 0, max: 10, unit: 'mg/L', warn: 8 }
    case '氨氮':
    case 'NH3-N':
      return { min: 0, max: 5, unit: 'mg/L', warn: 4 }
    case '总磷':
    case 'TP':
      return { min: 0, max: 0.5, unit: 'mg/L', warn: 0.4 }
    case '总氮':
    case 'TN':
      return { min: 0, max: 15, unit: 'mg/L', warn: 12 }
    default:
      return null
  }
}

// 检测值反馈
const showValueFeedback = computed(() => {
  return formData.value.testValue !== '' && formData.value.standard !== null
})

const isValueExceeded = computed(() => {
  if (!formData.value.standard || !formData.value.testValue) return false
  const value = parseFloat(formData.value.testValue)
  return value > formData.value.standard.max
})

const isValueWarning = computed(() => {
  if (!formData.value.standard || !formData.value.testValue) return false
  const value = parseFloat(formData.value.testValue)
  return value > formData.value.standard.warn && value <= formData.value.standard.max
})

const valueFeedbackType = computed(() => {
  if (isValueExceeded.value) return 'error'
  if (isValueWarning.value) return 'warning'
  return 'success'
})

const valueFeedbackTitle = computed(() => {
  if (isValueExceeded.value) return '检测值超标！'
  if (isValueWarning.value) return '检测值接近标准限值，请注意！'
  return '检测值正常'
})

const valueFeedbackDescription = computed(() => {
  if (!formData.value.standard) return ''
  const { min, max, unit } = formData.value.standard
  return `标准范围：${min} - ${max} ${unit}`
})

// 平行样反馈
const showParallelFeedback = computed(() => {
  return formData.value.parallelSample && 
         formData.value.testValue !== '' && 
         formData.value.parallelValue !== ''
})

const parallelDifference = computed(() => {
  if (!formData.value.testValue || !formData.value.parallelValue) return 0
  const value1 = parseFloat(formData.value.testValue)
  const value2 = parseFloat(formData.value.parallelValue)
  const avg = (value1 + value2) / 2
  if (avg === 0) return 0
  return Math.abs(value1 - value2) / avg * 100
})

const isParallelValid = computed(() => {
  // 通常平行样相对偏差不应超过5-10%
  return parallelDifference.value <= 10
})

const parallelFeedbackType = computed(() => {
  if (!isParallelValid.value) return 'error'
  return 'success'
})

const parallelFeedbackTitle = computed(() => {
  if (!isParallelValid.value) return '平行样偏差过大！'
  return '平行样符合要求'
})

const parallelFeedbackDescription = computed(() => {
  return `相对偏差: ${parallelDifference.value.toFixed(2)}%，允许范围: ≤ 10%`
})

// 表单校验规则
const formRules = reactive<FormRules>({
  testValue: [
    { required: true, message: '请输入检测值', trigger: 'blur' },
    { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '检测值必须为数字', trigger: 'blur' }
  ],
  testDate: [{ required: true, message: '请选择检测日期', trigger: 'change' }],
  tester: [{ required: true, message: '请选择检测人', trigger: 'change' }],
  method: [{ required: true, message: '请选择检测方法', trigger: 'change' }],
  instrument: [{ required: true, message: '请选择检测仪器', trigger: 'change' }],
  parallelValue: [
    { required: false, message: '请输入平行样值', trigger: 'blur' },
    { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '平行样值必须为数字', trigger: 'blur' }
  ],
  qualityControlType: [
    { required: false, message: '请选择质控样类型', trigger: 'change' }
  ],
  qualityControlValue: [
    { required: false, message: '请输入质控样值', trigger: 'blur' },
    { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '质控样值必须为数字', trigger: 'blur' }
  ],
  recoveryRate: [
    { required: false, message: '请输入回收率', trigger: 'blur' },
    { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '回收率必须为数字', trigger: 'blur' },
    { validator: (rule, value, callback) => {
        if (value && (parseFloat(value) < 70 || parseFloat(value) > 130)) {
          callback(new Error('回收率应在70%-130%范围内'))
        } else {
          callback()
        }
      }, 
      trigger: 'blur' 
    }
  ]
})

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any, resubmit = false) => {
  if (!data) return
  
  dialogVisible.value = true
  isResubmit.value = resubmit
  
  // 获取当前项目的标准信息
  const standard = getTestItemStandard(data.testItem)
  
  formData.value.id = data.id
  formData.value.sampleCode = data.sampleCode
  formData.value.testItem = data.testItem
  formData.value.samplingPoint = data.samplingPoint
  formData.value.samplingDate = data.samplingDate
  formData.value.standard = standard
  
  // 根据检测项目设置单位
  switch (data.testItem) {
    case 'COD':
    case 'BOD5':
    case '氨氮':
    case '总磷':
    case '总氮':
      formData.value.unit = 'mg/L'
      break
    case 'pH':
      formData.value.unit = ''
      break
    case '浊度':
      formData.value.unit = 'NTU'
      break
    default:
      formData.value.unit = 'mg/L'
  }
  
  if (isResubmit.value && data.testValue) {
    // 如果是重新提交，保留原有的检测数据
    formData.value.testValue = data.testValue
    formData.value.testDate = data.testDate || ''
    formData.value.tester = data.tester || ''
    formData.value.method = data.method || ''
    formData.value.instrument = data.instrument || ''
    formData.value.remark = data.remark || ''
  } else {
    // 否则重置数据
    formData.value.testValue = ''
    formData.value.testDate = new Date()
    formData.value.tester = ''
    formData.value.method = ''
    formData.value.instrument = ''
    formData.value.remark = ''
  }
  
  // 重置平行样和质控样数据
  formData.value.parallelSample = false
  formData.value.parallelValue = ''
  formData.value.qualityControl = false
  formData.value.qualityControlType = 'standard'
  formData.value.qualityControlValue = ''
  formData.value.recoveryRate = ''
}
defineExpose({ open })

// 获取标准值文本
const getStandardValueText = () => {
  if (!formData.value.standard) return '-'
  const { min, max, unit } = formData.value.standard
  return `${min}-${max} ${unit}`
}

// 获取报警阈值文本
const getAlarmThresholdText = () => {
  if (!formData.value.standard) return '-'
  const { warn, unit } = formData.value.standard
  return `≤${warn} ${unit}`
}

// 获取精度要求文本
const getPrecisionText = () => {
  // 根据检测项目返回精度要求
  switch (formData.value.testItem) {
    case 'COD':
    case 'BOD5':
      return '±0.1 mg/L'
    case '氨氮':
    case 'NH3-N':
      return '±0.01 mg/L'
    case '总磷':
    case 'TP':
      return '±0.001 mg/L'
    default:
      return '±0.1'
  }
}

// 获取智能建议
const getSmartSuggestions = () => {
  const suggestions = []

  if (isValueExceeded.value) {
    suggestions.push({
      action: 'recheck',
      text: '安排复检',
      type: 'warning',
      icon: RefreshRight
    })
    suggestions.push({
      action: 'report',
      text: '生成异常报告',
      type: 'danger',
      icon: Document
    })
  } else if (isValueWarning.value) {
    suggestions.push({
      action: 'verify',
      text: '验证数据',
      type: 'warning',
      icon: Check
    })
  }

  return suggestions
}

// 处理建议操作
const handleSuggestion = (suggestion: any) => {
  switch (suggestion.action) {
    case 'recheck':
      ElMessageBox.confirm('是否安排复检？', '确认操作', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        ElMessage.success('已安排复检')
        // 这里可以调用复检相关的API
      })
      break
    case 'report':
      ElMessage.info('正在生成异常报告...')
      // 这里可以调用生成报告的API
      break
    case 'verify':
      ElMessage.info('请仔细核对检测数据')
      break
  }
}

// 获取趋势点样式类
const getTrendPointClass = (value: number) => {
  if (!formData.value.standard) return 'normal'

  const { max, warn } = formData.value.standard
  if (value > max) return 'exceeded'
  if (value > warn) return 'warning'
  return 'normal'
}

// 验证检测值
const validateTestValue = () => {
  // 实时验证检测值是否超标
  if (!formData.value.testValue) return

  // 检查数值格式
  if (!/^[0-9]+(\.[0-9]+)?$/.test(formData.value.testValue)) {
    return
  }
}

// 验证平行样值
const validateParallelValue = () => {
  // 实时验证平行样偏差
  if (!formData.value.parallelValue) return
  
  // 检查数值格式
  if (!/^[0-9]+(\.[0-9]+)?$/.test(formData.value.parallelValue)) {
    return
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  // 手动验证条件性必填字段
  if (formData.value.parallelSample && !formData.value.parallelValue) {
    ElMessage.error('请输入平行样值')
    return
  }
  
  if (formData.value.qualityControl) {
    if (!formData.value.qualityControlType) {
      ElMessage.error('请选择质控样类型')
      return
    }
    
    if (!formData.value.qualityControlValue) {
      ElMessage.error('请输入质控样值')
      return
    }
    
    if (formData.value.qualityControlType === 'spiked' && !formData.value.recoveryRate) {
      ElMessage.error('请输入回收率')
      return
    }
  }
  
  // 平行样校验
  if (formData.value.parallelSample && !isParallelValid.value) {
    try {
      await ElMessageBox.confirm('平行样偏差超过允许范围，是否确认提交？', '提示', {
        confirmButtonText: '确认提交',
        cancelButtonText: '返回修改',
        type: 'warning'
      })
    } catch {
      return // 用户选择返回修改
    }
  }
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('数据录入成功')
    dialogVisible.value = false
    emit('success')
  } catch (error: any) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.value-feedback,
.parallel-feedback {
  margin-bottom: 1rem;
}

.parallel-sample,
.quality-control {
  margin-left: 2rem;
}

/* 检测值输入增强样式 */
.test-value-input {
  position: relative;
}

.input-guide {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  padding: 12px;
  margin-top: 4px;
}

.guide-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 13px;
}

.guide-item:last-child {
  margin-bottom: 0;
}

.guide-label {
  color: #909399;
  font-weight: 500;
}

.guide-value {
  color: #303133;
  font-weight: 600;
}

.guide-value.warning {
  color: #e6a23c;
}

.guide-actions {
  margin-top: 12px;
  padding-top: 8px;
  border-top: 1px solid #ebeef5;
  display: flex;
  gap: 8px;
}

/* 智能建议样式 */
.feedback-content {
  margin-top: 8px;
}

.smart-suggestions {
  margin-top: 12px;
}

.suggestions-title {
  margin: 0 0 8px 0;
  font-size: 13px;
  font-weight: 600;
  color: #606266;
}

.suggestions-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

/* 趋势图样式 */
.trend-chart {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
  background-color: #fafafa;
}

.trend-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.trend-title {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  font-weight: 600;
  color: #606266;
}

.trend-container {
  position: relative;
}

.trend-line {
  position: relative;
  height: 40px;
  background: linear-gradient(to right, #f0f9ff, #e0f2fe);
  border-radius: 4px;
  margin-bottom: 8px;
}

.trend-point {
  position: absolute;
  top: 50%;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  transform: translateY(-50%);
  cursor: pointer;
  transition: all 0.3s;
}

.trend-point.normal {
  background-color: #67c23a;
}

.trend-point.warning {
  background-color: #e6a23c;
}

.trend-point.exceeded {
  background-color: #f56c6c;
}

.trend-point:hover {
  transform: translateY(-50%) scale(1.5);
}

.current-point {
  position: absolute;
  top: -20px;
  transform: translateX(-50%);
}

.current-value {
  background-color: #409eff;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  white-space: nowrap;
}

.trend-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .input-guide {
    position: fixed;
    left: 10px;
    right: 10px;
    top: auto;
    bottom: 10px;
  }

  .suggestions-actions {
    flex-direction: column;
  }

  .trend-chart {
    padding: 8px;
  }
}
</style>
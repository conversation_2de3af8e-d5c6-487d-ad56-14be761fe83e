<template>
  <div class="assess-execute-container">

    <!-- 任务列表 -->
    <!-- 筛选区域 -->
    <el-card class="mb-4">
      <el-form :model="filterForm" inline>
        <el-form-item label="任务名称">
          <el-input v-model="filterForm.taskName" placeholder="请输入任务名称" clearable style="width: 200px" />
        </el-form-item>
        <el-form-item label="周期">
          <el-select v-model="filterForm.period" placeholder="请选择周期" clearable style="width: 150px">
            <el-option label="时" value="时" />
            <el-option label="天" value="天" />
            <el-option label="周" value="周" />
            <el-option label="月度" value="月度" />
            <el-option label="季度" value="季度" />
            <el-option label="年度" value="年度" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleFilter">筛选</el-button>
          <el-button @click="handleResetFilter">重置</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 任务表格 -->
    <el-card>
      <el-table :data="filteredTasks" border stripe>
        <el-table-column prop="name" label="任务名称" min-width="200" show-overflow-tooltip />
        <el-table-column prop="schemeName" label="来自方案" width="150" show-overflow-tooltip />
        <el-table-column label="任务描述" min-width="200" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.description || `${row.schemeName}的执行任务` }}
          </template>
        </el-table-column>
        <el-table-column label="项目数量" width="100" align="center">
          <template #default="{ row }">
            {{ row.schemeData?.projects?.length || 0 }}
          </template>
        </el-table-column>
        <el-table-column prop="period" label="周期" width="100" align="center" />
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="row.status === '已完成' ? 'success' : 'warning'">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="dueDate" label="截止时间" width="120" align="center" />
        <el-table-column label="操作" width="200" align="center" fixed="right">
          <template #default="{ row }">
            <!-- 编辑按钮 - 所有任务都可编辑 -->
            <el-button type="primary" link @click="handleUploadData(row)">
              编辑
            </el-button>

            <!-- 查看按钮 - 所有任务都可查看 -->
            <el-button type="info" link @click="handleViewTask(row)">
              查看
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页组件 -->
      <div class="pagination-container">
        <el-pagination v-model:current-page="pagination.currentPage" v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]" :total="paginationTotal" layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange" @current-change="handlePageChange" />
      </div>
    </el-card>
  </div>

  <!-- 数据上传弹窗 -->
  <DataUploadDialog v-model="uploadDialogVisible" :task-data="selectedTask" :global-data="globalIndicatorData"
    @submit="handleDataSubmit" />



  <!-- 任务详情查看弹窗 -->
  <el-dialog v-model="viewDialogVisible" title="任务详情" width="80%" :close-on-click-modal="false">
    <div v-if="selectedTask" class="task-detail-container">
      <!-- 任务基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span class="font-bold">任务信息</span>
        </template>
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="任务名称">{{ selectedTask.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag
              :type="selectedTask.status === '已提交' ? 'success' : selectedTask.status === '异常' ? 'danger' : 'warning'">{{
                selectedTask.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="项目数量">{{ selectedTask.schemeData?.projects?.length || 0
          }}个</el-descriptions-item>
          <el-descriptions-item label="周期">{{ selectedTask.period }}</el-descriptions-item>
          <el-descriptions-item label="启用时间">{{ selectedTask.enableTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="失效时间">{{ selectedTask.expireTime || selectedTask.dueDate
          }}</el-descriptions-item>
          <el-descriptions-item label="来自方案">{{ selectedTask.schemeName || selectedTask.schemeData?.name || '-'
          }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ selectedTask.description ||
            selectedTask.schemeData?.description
            ||
            '-'
          }}</el-descriptions-item>
        </el-descriptions>
      </el-card>





      <!-- 评估项目和指标数据 -->
      <el-card v-if="selectedTask.schemeData">
        <template #header>
          <span class="font-bold">评估项目和指标数据</span>
        </template>
        <el-collapse>
          <el-collapse-item v-for="project in selectedTask.schemeData.projects" :key="project.id"
            :title="`${project.name} (权重: ${project.weight}%)`">
            <el-table :data="project.indicators" border size="small">
              <el-table-column prop="name" label="指标名称" width="200" />
              <el-table-column prop="unit" label="单位" width="100" align="center" />
              <el-table-column prop="formula" label="计算公式" min-width="200" show-overflow-tooltip />
              <el-table-column prop="threshold" label="阈值" width="120" align="center" />
              <el-table-column label="指标分值" width="100" align="center">
                <template #default="{ row }">
                  {{ getIndicatorScore(project, row) }}
                </template>
              </el-table-column>
              <el-table-column label="数值填报" width="150" align="center">
                <template #default="{ row }">
                  {{ getMockIndicatorValue(project.id, row.id) || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="得分" width="100" align="center">
                <template #default="{ row }">
                  {{ getMockIndicatorScore(project.id, row.id) || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="最后修改时间" width="150" align="center">
                <template #default="{ row }">
                  {{ getMockLastModifyTime(project.id, row.id) || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="最后修改人" width="120" align="center">
                <template #default="{ row }">
                  {{ getMockLastModifyUser(project.id, row.id) || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="viewDialogVisible = false">关闭</el-button>

        <!-- 编辑按钮 - 所有任务都可编辑 -->
        <el-button v-if="selectedTask" type="primary" @click="handleUploadFromView">
          编辑
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import DataUploadDialog from './components/DataUploadDialog.vue'

// 接口定义 - 引用上游模块的数据结构
interface Indicator {
  id: string
  name: string
  unit: string
  formula: string
  applicableObject: string
  description?: string
  weight?: number
  threshold?: string // 来自指标管理模块的阈值
  expectedRange?: string // 期望区间，基于threshold计算
}

interface Project {
  id: string
  name: string
  weight: number
  scoreType: '加权平均' | '区间打分' | '减分制'
  fullScore: number
  indicators: Indicator[]
}

interface SchemeItem {
  id: string
  name: string
  status: '草稿' | '审批中' | '启用中' | '已失效' | '审批退回'
  targetType: '水厂' | '泵站' | '管网'
  cycle: '月度' | '季度' | '年度'
  startDate: string
  endDate: string
  description: string
  projects: Project[]
  createTime: string
  creator: string
}

interface TaskItem {
  id: string
  name: string
  schemeId: string // 引用方案ID
  schemeName: string
  objectName: string
  description?: string // 任务描述
  period: string
  status: '待提交' | '已完成'
  dueDate: string
  schemeData?: SchemeItem // 关联的方案数据
  indicators?: TaskIndicatorData[] // 任务中的指标采集项
}

interface TaskIndicatorData {
  id: string // 指标ID，引用指标管理模块
  projectId: string // 所属项目ID
  projectName: string // 所属项目名称
  name: string
  unit: string
  formula: string
  threshold: string // 来自指标管理模块
  expectedRange: string // 基于threshold计算的期望区间
  weight: number // 在项目中的权重
  value?: number // 用户录入的值
  status?: 'normal' | 'warning' | 'error' // 校验状态
  errorMessage?: string // 错误信息
  applicableObject: string
  description?: string
}

// 响应式数据
const uploadDialogVisible = ref(false)
const viewDialogVisible = ref(false)
const selectedTask = ref<TaskItem | null>(null)

// 全局数据存储 - 用于在详情和编辑弹窗之间共享数据
const globalIndicatorData = ref<Record<string, Record<string, {
  value?: number
  score?: number
  lastModifyTime?: string
  lastModifyUser?: string
}>>>({})

// 筛选表单
const filterForm = reactive({
  taskName: '',
  period: ''
})

// 分页相关
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 0
})

// Mock 启用的方案数据（来自方案管理模块）
const mockActiveSchemes = ref<SchemeItem[]>([
  {
    id: 'scheme_001',
    name: '泵站月度评估方案',
    status: '启用中',
    targetType: '泵站',
    cycle: '月度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '用于考核泵站运营质量和效率的综合评估方案',
    projects: [
      {
        id: 'proj_001',
        name: '能耗指标',
        weight: 50,
        scoreType: '加权平均',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_001',
            name: '单位能耗',
            weight: 70,
            formula: '总能耗 / 产出量',
            unit: 'kWh/m³',
            applicableObject: '泵站',
            description: '单位处理水量的能源消耗',
            threshold: '0.3-0.5',
            expectedRange: '0.3-0.5'
          },
          {
            id: 'ind_002',
            name: '功率因数',
            weight: 30,
            formula: '有功功率 / 视在功率',
            unit: '无',
            applicableObject: '泵站',
            description: '电力系统效率指标',
            threshold: '≥0.85',
            expectedRange: '0.85-0.95'
          }
        ]
      },
      {
        id: 'proj_002',
        name: '运行效率',
        weight: 30,
        scoreType: '区间打分',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_003',
            name: '设备运行率',
            formula: '运行时间 / 总时间 × 100',
            unit: '%',
            applicableObject: '泵站',
            description: '设备正常运行时间占比',
            threshold: '≥85%',
            expectedRange: '85-95'
          }
        ]
      }
    ],
    createTime: '2024-01-01 10:00:00',
    creator: '张三'
  },
  {
    id: 'scheme_003',
    name: '管网年度评估方案',
    status: '启用中',
    targetType: '管网',
    cycle: '年度',
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    description: '管网系统年度评估方案',
    projects: [
      {
        id: 'proj_004',
        name: '管网完整性',
        weight: 60,
        scoreType: '加权平均',
        fullScore: 100,
        indicators: [
          {
            id: 'ind_005',
            name: '漏损率',
            weight: 100,
            formula: '(进水流量 - 出水流量) / 进水流量 × 100',
            unit: '%',
            applicableObject: '管网',
            description: '管网漏损水量占比',
            threshold: '≤5%',
            expectedRange: '0-5'
          }
        ]
      }
    ],
    createTime: '2024-02-20 09:15:00',
    creator: '王五'
  }
])

// Mock 任务数据（基于启用方案生成）
const mockTasks = ref<TaskItem[]>([
  {
    id: 'task_001',
    name: '泵站月度评估-2025年1月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城东泵站',
    description: '对城东泵站进行月度运行效率和能耗评估',
    period: '月度',
    status: '待提交',
    dueDate: '2025-01-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_002',
    name: '泵站月度评估-2025年1月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城西泵站',
    description: '对城西泵站进行月度运行效率和能耗评估',
    period: '月度',
    status: '待提交',
    dueDate: '2025-01-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_003',
    name: '管网年度评估-2025年',
    schemeId: 'scheme_003',
    schemeName: '管网年度评估方案',
    objectName: '主干管网A区',
    description: '对主干管网A区进行年度综合性能评估',
    period: '年度',
    status: '已完成',
    dueDate: '2025-12-31',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_004',
    name: '泵站月度评估-2024年12月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城东泵站',
    description: '对城东泵站进行2024年12月运行评估',
    period: '月度',
    status: '已完成',
    dueDate: '2024-12-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_005',
    name: '泵站月度评估-2024年11月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城西泵站',
    description: '对城西泵站进行2024年11月运行评估',
    period: '月度',
    status: '待提交',
    dueDate: '2024-11-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_006',
    name: '泵站日度评估-2024年10月',
    schemeId: 'scheme_001',
    schemeName: '泵站日度评估方案',
    objectName: '城东泵站',
    description: '对城东泵站进行日度运行监控评估',
    period: '天',
    status: '已完成',
    dueDate: '2024-10-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_007',
    name: '水厂季度评估-2024年Q4',
    schemeId: 'scheme_002',
    schemeName: '水厂季度评估方案',
    objectName: '第一水厂',
    period: '季度',
    status: '已完成',
    dueDate: '2024-12-31',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_008',
    name: '泵站月度评估-2024年9月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城南泵站',
    period: '月度',
    status: '已完成',
    dueDate: '2024-09-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_009',
    name: '管网年度评估-2024年',
    schemeId: 'scheme_003',
    schemeName: '管网年度评估方案',
    objectName: '主干管网B区',
    period: '年度',
    status: '已完成',
    dueDate: '2024-12-31',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_010',
    name: '水厂季度评估-2024年Q3',
    schemeId: 'scheme_002',
    schemeName: '水厂季度评估方案',
    objectName: '第二水厂',
    period: '季度',
    status: '已完成',
    dueDate: '2024-09-30',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_011',
    name: '泵站月度评估-2024年8月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城北泵站',
    period: '月度',
    status: '已完成',
    dueDate: '2024-08-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_012',
    name: '管网年度评估-2023年',
    schemeId: 'scheme_003',
    schemeName: '管网年度评估方案',
    objectName: '主干管网C区',
    period: '年度',
    status: '已完成',
    dueDate: '2023-12-31',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_013',
    name: '水厂季度评估-2024年Q2',
    schemeId: 'scheme_002',
    schemeName: '水厂季度评估方案',
    objectName: '第三水厂',
    period: '季度',
    status: '已完成',
    dueDate: '2024-06-30',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_014',
    name: '泵站月度评估-2024年7月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城东泵站',
    period: '月度',
    status: '已完成',
    dueDate: '2024-07-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_015',
    name: '管网年度评估-2022年',
    schemeId: 'scheme_003',
    schemeName: '管网年度评估方案',
    objectName: '主干管网D区',
    period: '年度',
    status: '已完成',
    dueDate: '2022-12-31',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_016',
    name: '泵站周度评估-2025年第8周',
    schemeId: 'scheme_001',
    schemeName: '泵站周度评估方案',
    objectName: '城西泵站',
    period: '周',
    status: '待提交',
    dueDate: '2025-02-20',
    schemeData: mockActiveSchemes.value[0]
  },
  {
    id: 'task_017',
    name: '水厂小时评估-2025年1月',
    schemeId: 'scheme_002',
    schemeName: '水厂小时评估方案',
    objectName: '第一水厂',
    period: '时',
    status: '待提交',
    dueDate: '2025-03-31',
    schemeData: mockActiveSchemes.value[1]
  },
  {
    id: 'task_018',
    name: '泵站月度评估-2024年12月',
    schemeId: 'scheme_001',
    schemeName: '泵站月度评估方案',
    objectName: '城南泵站',
    period: '月度',
    status: '已完成',
    dueDate: '2024-12-20',
    schemeData: mockActiveSchemes.value[0]
  }
])

// 计算属性
// 筛选后的任务列表（不分页）
const filteredTasksAll = computed(() => {
  let tasks = mockTasks.value

  if (filterForm.taskName) {
    tasks = tasks.filter(task => task.name.toLowerCase().includes(filterForm.taskName.toLowerCase()))
  }

  if (filterForm.period) {
    tasks = tasks.filter(task => task.period === filterForm.period)
  }

  return tasks
})

// 分页后的任务列表
const filteredTasks = computed(() => {
  const tasks = filteredTasksAll.value
  const startIndex = (pagination.currentPage - 1) * pagination.pageSize
  const endIndex = startIndex + pagination.pageSize
  return tasks.slice(startIndex, endIndex)
})

// 分页总数
const paginationTotal = computed(() => {
  return filteredTasksAll.value.length
})

// 分页变化处理
const handlePageChange = (page: number) => {
  pagination.currentPage = page
}

// 每页条数变化处理
const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  pagination.currentPage = 1
}







// Mock数据：模拟已有的指标数据（与DataUploadDialog保持一致）
const mockIndicatorData = ref<Record<string, Record<string, {
  value?: number
  score?: number
  lastModifyTime?: string
  lastModifyUser?: string
}>>>({
  // 基本指标项目的mock数据
  'project_001': {
    'indicator_001': {
      value: 0.35,
      score: 68.5,
      lastModifyTime: '2025-01-15 14:30:25',
      lastModifyUser: '张三'
    },
    'indicator_002': {
      value: 0.88,
      score: 26.4,
      lastModifyTime: '2025-01-15 14:32:18',
      lastModifyUser: '李四'
    }
  },
  // 运行效率项目的mock数据
  'project_002': {
    'indicator_003': {
      value: 85.2,
      score: 42.6,
      lastModifyTime: '2025-01-15 15:15:42',
      lastModifyUser: '王五'
    },
    'indicator_004': {
      value: 92.8,
      score: 37.1,
      lastModifyTime: '2025-01-15 15:18:33',
      lastModifyUser: '赵六'
    },
    'indicator_005': {
      value: 78.5,
      score: 15.7,
      lastModifyTime: '2025-01-15 15:22:15',
      lastModifyUser: '钱七'
    }
  },
  // 能耗分析项目的mock数据
  'project_003': {
    'indicator_006': {
      value: 1.25,
      score: 18.8,
      lastModifyTime: '2025-01-15 16:05:28',
      lastModifyUser: '孙八'
    },
    'indicator_007': {
      value: 0.95,
      score: 14.3,
      lastModifyTime: '2025-01-15 16:08:45',
      lastModifyUser: '周九'
    }
  }
})

// 获取指标分值（同一项目内所有指标分值相同）
const getIndicatorScore = (project: any, indicator: any): string => {
  // 同一项目内所有指标分值相同 = 项目总分 / 指标数量
  if (project.fullScore && project.indicators && project.indicators.length > 0) {
    const scorePerIndicator = (project.fullScore / project.indicators.length).toFixed(1)
    return scorePerIndicator
  }

  return '-'
}

// 获取指标数值（优先从全局数据，然后是Mock数据）
const getMockIndicatorValue = (projectId: string, indicatorId: string): number | undefined => {
  // 优先从全局数据存储中获取
  const globalValue = globalIndicatorData.value[projectId]?.[indicatorId]?.value
  if (globalValue !== undefined) {
    return globalValue
  }

  // 如果全局数据中没有，则从Mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.value
}

// 获取指标得分（优先从全局数据，然后是Mock数据）
const getMockIndicatorScore = (projectId: string, indicatorId: string): number | undefined => {
  // 优先从全局数据存储中获取
  const globalScore = globalIndicatorData.value[projectId]?.[indicatorId]?.score
  if (globalScore !== undefined) {
    return globalScore
  }

  // 如果全局数据中没有，则从Mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.score
}

// 获取最后修改时间（优先从全局数据，然后是Mock数据）
const getMockLastModifyTime = (projectId: string, indicatorId: string): string | undefined => {
  // 优先从全局数据存储中获取
  const globalTime = globalIndicatorData.value[projectId]?.[indicatorId]?.lastModifyTime
  if (globalTime) {
    return globalTime
  }

  // 如果全局数据中没有，则从Mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.lastModifyTime
}

// 获取最后修改人（优先从全局数据，然后是Mock数据）
const getMockLastModifyUser = (projectId: string, indicatorId: string): string | undefined => {
  // 优先从全局数据存储中获取
  const globalUser = globalIndicatorData.value[projectId]?.[indicatorId]?.lastModifyUser
  if (globalUser) {
    return globalUser
  }

  // 如果全局数据中没有，则从Mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.lastModifyUser
}

// 事件处理方法
const handleFilter = () => {
  ElMessage.success('筛选完成')
}

const handleResetFilter = () => {
  filterForm.taskName = ''
  filterForm.period = ''
  ElMessage.info('筛选条件已重置')
}

const handleUploadData = (task: TaskItem) => {
  // 确保任务包含完整的方案数据
  if (!task.schemeData) {
    const scheme = mockActiveSchemes.value.find(s => s.id === task.schemeId)
    if (scheme) {
      task.schemeData = scheme
    }
  }

  selectedTask.value = task
  uploadDialogVisible.value = true
}

const handleViewTask = (task: TaskItem) => {
  // 确保任务包含完整的方案数据
  if (!task.schemeData) {
    const scheme = mockActiveSchemes.value.find(s => s.id === task.schemeId)
    if (scheme) {
      task.schemeData = scheme
    }
  }

  selectedTask.value = task
  viewDialogVisible.value = true
}



const handleUploadFromView = () => {
  viewDialogVisible.value = false
  uploadDialogVisible.value = true
}











const handleDataSubmit = (data: any) => {
  console.log('提交的数据:', data)

  // 将提交的数据保存到全局数据存储中
  if (data.projects && Array.isArray(data.projects)) {
    data.projects.forEach((project: any) => {
      if (project.indicators && Array.isArray(project.indicators)) {
        project.indicators.forEach((indicator: any) => {
          // 确保全局数据存储中有对应的项目
          if (!globalIndicatorData.value[project.id]) {
            globalIndicatorData.value[project.id] = {}
          }

          // 保存指标数据
          globalIndicatorData.value[project.id][indicator.id] = {
            value: indicator.value,
            score: indicator.score,
            lastModifyTime: indicator.lastModifyTime,
            lastModifyUser: indicator.lastModifyUser
          }
        })
      }
    })
  }

  ElMessage.success('数据提交成功，任务已完成')
  uploadDialogVisible.value = false

  // 更新任务状态为已完成
  if (selectedTask.value) {
    const task = mockTasks.value.find(t => t.id === selectedTask.value!.id)
    if (task) {
      task.status = '已完成'
    }
  }
}

// 初始化全局数据存储
const initializeGlobalData = () => {
  // 将Mock数据复制到全局数据存储中
  Object.keys(mockIndicatorData.value).forEach(projectId => {
    if (!globalIndicatorData.value[projectId]) {
      globalIndicatorData.value[projectId] = {}
    }
    Object.keys(mockIndicatorData.value[projectId]).forEach(indicatorId => {
      globalIndicatorData.value[projectId][indicatorId] = {
        ...mockIndicatorData.value[projectId][indicatorId]
      }
    })
  })
}

// 生命周期
onMounted(() => {
  console.log('评估执行管理页面已加载')
  // 初始化全局数据
  initializeGlobalData()
})
</script>

<style scoped lang="scss">
.assess-execute-container {
  padding: 20px;

  .mb-4 {
    margin-bottom: 16px;
  }

  // 统计卡片样式
  .stat-card {
    .stat-item {
      text-align: center;
      padding: 20px 0;

      .stat-value {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 8px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }

  // 异常任务项样式
  .exception-item {
    padding: 8px 0;
    border-bottom: 1px solid #f0f0f0;

    &:last-child {
      border-bottom: none;
    }
  }

  // 工具类
  .text-center {
    text-align: center;
  }

  .text-gray-500 {
    color: #9ca3af;
  }

  .text-gray-600 {
    color: #6b7280;
  }

  .text-green-600 {
    color: #059669;
  }

  .text-orange-600 {
    color: #ea580c;
  }

  .text-red-600 {
    color: #dc2626;
  }

  .py-4 {
    padding-top: 16px;
    padding-bottom: 16px;
  }

  .py-8 {
    padding-top: 32px;
    padding-bottom: 32px;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .mb-2 {
    margin-bottom: 8px;
  }

  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  // 分页样式
  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 20px;
    padding: 20px 0;
  }

  // 任务详情弹窗样式
  .task-detail-container {
    .font-bold {
      font-weight: bold;
    }

    .ml-2 {
      margin-left: 8px;
    }
  }
}
</style>
<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800px">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="企业名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入企业名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="注册资本" prop="registeredCapital">
            <el-input-number
              v-model="formData.registeredCapital"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="万元"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="所在省份" prop="province">
            <el-input v-model="formData.province" placeholder="请输入省份" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所在城市" prop="city">
            <el-input v-model="formData.city" placeholder="请输入城市" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="服务人口" prop="servicePopulation">
            <el-input-number
              v-model="formData.servicePopulation"
              :min="0"
              :precision="1"
              controls-position="right"
              placeholder="万人"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="服务面积" prop="serviceArea">
            <el-input-number
              v-model="formData.serviceArea"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="km²"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="职工人数" prop="employeeCount">
            <el-input-number
              v-model="formData.employeeCount"
              :min="0"
              controls-position="right"
              placeholder="人"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="厂站数量" prop="plantCount">
            <el-input-number
              v-model="formData.plantCount"
              :min="0"
              controls-position="right"
              placeholder="个"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">污水处理能力</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设计处理量" prop="sewageTreatmentDesign">
            <el-input-number
              v-model="formData.sewageTreatmentDesign"
              :min="0"
              :precision="0"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投产处理量" prop="sewageTreatmentProduction">
            <el-input-number
              v-model="formData.sewageTreatmentProduction"
              :min="0"
              :precision="0"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">再生水能力</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设计处理量" prop="recycledWaterDesign">
            <el-input-number
              v-model="formData.recycledWaterDesign"
              :min="0"
              :precision="0"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投产处理量" prop="recycledWaterProduction">
            <el-input-number
              v-model="formData.recycledWaterProduction"
              :min="0"
              :precision="0"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">供水能力</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="设计供水量" prop="waterSupplyDesign">
            <el-input-number
              v-model="formData.waterSupplyDesign"
              :min="0"
              :precision="0"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投产供水量" prop="waterSupplyProduction">
            <el-input-number
              v-model="formData.waterSupplyProduction"
              :min="0"
              :precision="0"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-divider content-position="left">其他指标</el-divider>
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="泥饼产量" prop="sludgeOutput">
            <el-input-number
              v-model="formData.sludgeOutput"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="吨/日"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="新能源发电量" prop="renewableEnergyGeneration">
            <el-input-number
              v-model="formData.renewableEnergyGeneration"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="kWh/年"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="配套管网长度" prop="pipelineLength">
            <el-input-number
              v-model="formData.pipelineLength"
              :min="0"
              :precision="2"
              controls-position="right"
              placeholder="km"
              class="!w-full"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="风险评估" prop="riskAssessment">
            <el-select v-model="formData.riskAssessment" placeholder="请选择风险等级" class="!w-full">
              <el-option label="低风险" value="低风险" />
              <el-option label="中风险" value="中风险" />
              <el-option label="高风险" value="高风险" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="备注" prop="remark">
        <el-input
          v-model="formData.remark"
          type="textarea"
          :rows="3"
          placeholder="请输入备注信息"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  name: '',
  province: '',
  city: '',
  registeredCapital: undefined,
  servicePopulation: undefined,
  serviceArea: undefined,
  employeeCount: undefined,
  plantCount: undefined,
  sewageTreatmentDesign: undefined,
  sewageTreatmentProduction: undefined,
  recycledWaterDesign: undefined,
  recycledWaterProduction: undefined,
  waterSupplyDesign: undefined,
  waterSupplyProduction: undefined,
  sludgeOutput: undefined,
  renewableEnergyGeneration: undefined,
  pipelineLength: undefined,
  riskAssessment: '',
  remark: ''
})

const formRules = reactive({
  name: [{ required: true, message: '企业名称不能为空', trigger: 'blur' }],
  province: [{ required: true, message: '所在省份不能为空', trigger: 'blur' }],
  city: [{ required: true, message: '所在城市不能为空', trigger: 'blur' }],
  registeredCapital: [{ required: true, message: '注册资本不能为空', trigger: 'blur' }]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('action.' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      // TODO: 对接后端API
      // formData.value = await EnterpriseApi.getEnterprise(id)
      // 模拟数据
      const mockData = {
        1: {
          id: 1,
          name: '河南环保科技有限公司',
          province: '河南省',
          city: '洛阳市',
          registeredCapital: 5000,
          servicePopulation: 120,
          serviceArea: 850,
          employeeCount: 280,
          plantCount: 12,
          sewageTreatmentDesign: 50000,
          sewageTreatmentProduction: 45000,
          recycledWaterDesign: 20000,
          recycledWaterProduction: 18000,
          waterSupplyDesign: 80000,
          waterSupplyProduction: 75000,
          sludgeOutput: 150,
          renewableEnergyGeneration: 2500,
          pipelineLength: 1200,
          riskAssessment: '低风险',
          remark: '主要服务洛阳市区及周边地区'
        },
        2: {
          id: 2,
          name: '华北水务集团',
          province: '河北省',
          city: '石家庄市',
          registeredCapital: 15000,
          servicePopulation: 300,
          serviceArea: 2100,
          employeeCount: 650,
          plantCount: 25,
          sewageTreatmentDesign: 120000,
          sewageTreatmentProduction: 110000,
          recycledWaterDesign: 50000,
          recycledWaterProduction: 45000,
          waterSupplyDesign: 200000,
          waterSupplyProduction: 185000,
          sludgeOutput: 380,
          renewableEnergyGeneration: 6800,
          pipelineLength: 3200,
          riskAssessment: '中风险',
          remark: '华北地区重要的水务服务提供商'
        }
      }

      if (mockData[id]) {
        formData.value = mockData[id]
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value
    if (formType.value === 'create') {
      // TODO: 对接后端API
      // await EnterpriseApi.createEnterprise(data)
      message.success(t('common.createSuccess'))
    } else {
      // TODO: 对接后端API
      // await EnterpriseApi.updateEnterprise(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    province: '',
    city: '',
    registeredCapital: undefined,
    servicePopulation: undefined,
    serviceArea: undefined,
    employeeCount: undefined,
    plantCount: undefined,
    sewageTreatmentDesign: undefined,
    sewageTreatmentProduction: undefined,
    recycledWaterDesign: undefined,
    recycledWaterProduction: undefined,
    waterSupplyDesign: undefined,
    waterSupplyProduction: undefined,
    sludgeOutput: undefined,
    renewableEnergyGeneration: undefined,
    pipelineLength: undefined,
    riskAssessment: '',
    remark: ''
  }
  formRef.value?.resetFields()
}
</script>

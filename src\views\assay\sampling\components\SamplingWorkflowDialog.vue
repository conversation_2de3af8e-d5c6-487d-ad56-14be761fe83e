<template>
  <Dialog v-model="dialogVisible" :title="dialogConfig.title || '工作流步骤'" width="600px">
    <div v-loading="formLoading">
      <!-- 基本信息部分 -->
      <div class="mb-4">
        <div class="font-bold text-lg mb-2">{{ dialogConfig.taskName }}</div>
        <div class="text-gray-600">{{ dialogConfig.description }}</div>
      </div>

      <!-- 位置选择器 -->
      <template v-if="dialogConfig.showLocationPicker">
        <el-divider content-position="left">现场位置</el-divider>
        <div class="mb-4">
          <el-form :model="formData" label-width="100px">
            <el-form-item label="当前位置">
              <el-input v-model="formData.location" placeholder="系统自动定位中..." disabled>
                <template #append>
                  <el-button @click="handleRefreshLocation">
                    <el-icon><Refresh /></el-icon>
                  </el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="位置说明">
              <el-input v-model="formData.locationDescription" placeholder="请输入位置补充说明" />
            </el-form-item>
          </el-form>
        </div>
      </template>

      <!-- 条件检查表单 -->
      <template v-if="dialogConfig.showConditionCheck">
        <el-divider content-position="left">采样条件检查</el-divider>
        <el-form :model="formData" label-width="0">
          <el-form-item>
            <el-checkbox-group v-model="formData.conditionChecklist">
              <div class="flex flex-col gap-2">
                <el-checkbox label="equipment">设备正常</el-checkbox>
                <el-checkbox label="waterQuality">水质正常</el-checkbox>
                <el-checkbox label="weather">天气适宜</el-checkbox>
                <el-checkbox label="accessible">采样点可达</el-checkbox>
                <el-checkbox label="safety">安全措施完备</el-checkbox>
              </div>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="条件备注">
            <el-input v-model="formData.conditionRemark" type="textarea" placeholder="请输入条件说明" />
          </el-form-item>
        </el-form>
      </template>

      <!-- 采样信息表单 -->
      <template v-if="dialogConfig.showSamplingInfo">
        <el-divider content-position="left">采样信息</el-divider>
        <el-form :model="formData" label-width="100px">
          <el-form-item label="采样方法">
            <el-select v-model="formData.samplingMethod" placeholder="请选择采样方法">
              <el-option label="瞬时采样" value="instant" />
              <el-option label="等时间间隔采样" value="equalTime" />
              <el-option label="等流量间隔采样" value="equalFlow" />
              <el-option label="流量比例采样" value="flowProportion" />
            </el-select>
          </el-form-item>
          <el-form-item label="样品容器">
            <el-select v-model="formData.containerType" placeholder="请选择样品容器">
              <el-option label="聚乙烯瓶" value="polyethylene" />
              <el-option label="棕色玻璃瓶" value="brownGlass" />
              <el-option label="密封袋" value="sealedBag" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="预计完成时间">
            <el-time-select v-model="formData.estimatedEndTime" placeholder="请选择预计完成时间" />
          </el-form-item>
          <el-form-item label="采样说明">
            <el-input v-model="formData.samplingDescription" type="textarea" placeholder="请输入采样说明" />
          </el-form-item>
        </el-form>
      </template>

      <!-- 异常表单 -->
      <template v-if="dialogConfig.showAbnormalForm">
        <el-divider content-position="left">异常情况</el-divider>
        <el-form :model="formData" label-width="100px">
          <el-form-item label="异常类型">
            <el-select v-model="formData.abnormalType" placeholder="请选择异常类型">
              <el-option label="设备故障" value="equipment" />
              <el-option label="水质异常" value="waterQuality" />
              <el-option label="天气原因" value="weather" />
              <el-option label="安全隐患" value="safety" />
              <el-option label="人员原因" value="personnel" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="异常描述" required>
            <el-input v-model="formData.abnormalDescription" type="textarea" placeholder="请详细描述异常情况" />
          </el-form-item>
          <el-form-item label="处理措施" required>
            <el-input v-model="formData.handleMeasures" type="textarea" placeholder="请输入处理措施" />
          </el-form-item>
          <el-form-item label="上报主管">
            <el-select v-model="formData.reportToManager" placeholder="请选择上报主管">
              <el-option label="张主管" value="manager1" />
              <el-option label="王主管" value="manager2" />
              <el-option label="李主管" value="manager3" />
            </el-select>
          </el-form-item>
        </el-form>
      </template>

      <!-- 样品结果表单 -->
      <template v-if="dialogConfig.showSampleResult">
        <el-divider content-position="left">样品信息</el-divider>
        <el-form :model="formData" label-width="100px">
          <el-form-item label="样品数量">
            <el-input-number v-model="formData.sampleCount" :min="1" :max="10" />
          </el-form-item>
          <el-form-item label="样品类型">
            <el-select v-model="formData.sampleType" placeholder="请选择样品类型">
              <el-option label="水样" value="water" />
              <el-option label="污泥" value="sludge" />
              <el-option label="气体" value="gas" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="保存方式">
            <el-select v-model="formData.preservationMethod" placeholder="请选择保存方式">
              <el-option label="常温" value="normal" />
              <el-option label="冷藏" value="refrigerated" />
              <el-option label="冷冻" value="frozen" />
              <el-option label="加酸" value="acid" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
          <el-form-item label="有效期至">
            <el-date-picker v-model="formData.expiryDate" type="date" placeholder="请选择样品有效期" />
          </el-form-item>
          <el-form-item>
            <el-checkbox v-model="formData.generateSample">生成样品记录</el-checkbox>
          </el-form-item>
          <el-form-item label="备注">
            <el-input v-model="formData.sampleRemark" type="textarea" placeholder="请输入备注" />
          </el-form-item>
        </el-form>
      </template>
    </div>

    <template #footer>
      <el-button @click="handleCancel">
        {{ dialogConfig.cancelText || '取消' }}
      </el-button>
      <el-button type="primary" :disabled="formLoading" @click="handleConfirm">
        {{ dialogConfig.confirmText || '确定' }}
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Dialog } from '../../../../components/Dialog'

defineOptions({ name: 'SamplingWorkflowDialog' })

// 定义结果类型接口
interface WorkflowResult {
  taskId: number;
  type: string;
  success: boolean;
  conditionMet?: boolean;
  abnormalReason?: string;
  handleMeasures?: string;
  generateSample?: boolean;
  sampleType?: string;
  preservationMethod?: string;
}

const dialogVisible = ref(false)
const formLoading = ref(false)

// 对话框配置
const dialogConfig = ref({
  title: '',
  type: '',
  taskId: 0,
  taskName: '',
  description: '',
  confirmText: '确定',
  cancelText: '取消',
  showLocationPicker: false,
  showConditionCheck: false,
  showSamplingInfo: false,
  showAbnormalForm: false,
  showSampleResult: false
})

// 表单数据
const formData = reactive({
  // 位置信息
  location: '正在获取位置...',
  locationDescription: '',
  
  // 条件检查
  conditionChecklist: [] as string[],
  conditionRemark: '',
  
  // 采样信息
  samplingMethod: '',
  containerType: '',
  estimatedEndTime: '',
  samplingDescription: '',
  
  // 异常信息
  abnormalType: '',
  abnormalDescription: '',
  handleMeasures: '',
  reportToManager: '',
  
  // 样品结果
  sampleCount: 1,
  sampleType: 'water',
  preservationMethod: 'refrigerated',
  expiryDate: '',
  generateSample: true,
  sampleRemark: ''
})

const emit = defineEmits(['success'])

// 打开对话框
const open = (config) => {
  dialogVisible.value = true
  // 合并配置
  dialogConfig.value = { ...dialogConfig.value, ...config }
  
  // 重置表单
  resetForm()
  
  // 如果需要获取位置信息
  if (config.showLocationPicker) {
    mockGetLocation()
  }
}

// 模拟获取位置
const mockGetLocation = () => {
  formData.location = '正在获取位置...'
  setTimeout(() => {
    formData.location = '北纬30°26′47″ 东经114°20′33″'
  }, 1500)
}

// 刷新位置信息
const handleRefreshLocation = () => {
  mockGetLocation()
  ElMessage.success('位置已更新')
}

// 确认操作
const handleConfirm = async () => {
  // 表单验证
  if (dialogConfig.value.showConditionCheck) {
    // 检查条件是否满足（需要全部勾选）
    if (formData.conditionChecklist.length < 5 && !dialogConfig.value.cancelText.includes('不满足')) {
      ElMessage.warning('请确认所有采样条件')
      return
    }
  }
  
  if (dialogConfig.value.showAbnormalForm) {
    if (!formData.abnormalDescription || !formData.handleMeasures) {
      ElMessage.warning('请填写异常描述和处理措施')
      return
    }
  }
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 构造返回结果
    const result: WorkflowResult = {
      taskId: dialogConfig.value.taskId,
      type: dialogConfig.value.type,
      success: true
    }
    
    // 根据不同类型返回不同结果
    switch (dialogConfig.value.type) {
      case 'confirmCondition':
        result.conditionMet = true
        break
      case 'recordAbnormal':
        result.abnormalReason = formData.abnormalDescription
        result.handleMeasures = formData.handleMeasures
        break
      case 'finishSampling':
        result.generateSample = formData.generateSample
        result.sampleType = formData.sampleType
        result.preservationMethod = formData.preservationMethod
        break
    }
    
    ElMessage.success('操作成功')
    dialogVisible.value = false
    emit('success', result)
  } catch (error) {
    console.error('操作失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  // 如果是确认条件步骤，点击"条件不满足"按钮
  if (dialogConfig.value.type === 'confirmCondition' && dialogConfig.value.cancelText.includes('不满足')) {
    // 打开异常记录表单
    dialogVisible.value = false
    
    // 模拟打开异常记录表单
    setTimeout(() => {
      open({
        title: '记录异常情况',
        type: 'recordAbnormal',
        taskId: dialogConfig.value.taskId,
        taskName: dialogConfig.value.taskName,
        description: `请记录采样异常情况`,
        confirmText: '提交异常',
        showAbnormalForm: true
      })
    }, 100)
    
    return
  }
  
  dialogVisible.value = false
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    location: '正在获取位置...',
    locationDescription: '',
    conditionChecklist: [],
    conditionRemark: '',
    samplingMethod: '',
    containerType: '',
    estimatedEndTime: '',
    samplingDescription: '',
    abnormalType: '',
    abnormalDescription: '',
    handleMeasures: '',
    reportToManager: '',
    sampleCount: 1,
    sampleType: 'water',
    preservationMethod: 'refrigerated',
    expiryDate: '',
    generateSample: true,
    sampleRemark: ''
  })
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
// 美化样式
.mb-4 {
  margin-bottom: 1rem;
}

.font-bold {
  font-weight: 600;
}

.text-lg {
  font-size: 1.125rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.text-gray-600 {
  color: #718096;
}

// 表单样式优化
.el-form {
  margin-top: 0.75rem;
  
  .el-form-item {
    margin-bottom: 1.25rem;
  }
}

// 分割线样式
.el-divider {
  margin: 1.5rem 0 1rem;
  
  &__text {
    font-weight: 600;
    color: var(--el-color-primary);
    font-size: 1rem;
  }
}

// 复选框组样式
.el-checkbox-group {
  .flex-col {
    display: flex;
    flex-direction: column;
  }
  
  .gap-2 {
    gap: 0.5rem;
  }
  
  .el-checkbox {
    margin-right: 0;
    padding: 0.25rem 0;
  }
}

// 按钮样式优化
.el-button + .el-button {
  margin-left: 0.75rem;
}
</style> 
<template>
  <div class="group-view">
    <!-- 1️⃣ 顶部筛选区域 -->
    <el-card class="mb-4">
      <div class="filter-header">
        <el-form :model="filters" inline>
          <el-form-item label="评估方案">
            <el-select v-model="filters.schemeId" placeholder="请选择评估方案" style="width: 280px"
              @change="handleSchemeChange">
              <el-option v-for="scheme in schemes" :key="scheme.id" :label="scheme.name" :value="scheme.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="评估周期">
            <el-select v-model="filters.period" placeholder="请选择周期" style="width: 180px" @change="handlePeriodChange">
              <el-option v-for="period in availablePeriods" :key="period" :label="period" :value="period" />
            </el-select>
          </el-form-item>
        </el-form>
      </div>
    </el-card>

    <!-- 2️⃣ 综合得分排行榜与异常汇总 -->
    <div class="score-anomaly-container mb-4">
      <div class="left-panel">
        <el-card>
          <template #header>
            <span class="font-bold">综合得分排行榜</span>
          </template>
          <div class="table-container">
            <el-table :data="stationScores" border stripe height="400" :flexible="true" style="width: 100%">
              <el-table-column type="index" label="序号" width="60" align="center" />
              <el-table-column prop="name" label="厂站名称" min-width="120" show-overflow-tooltip />
              <el-table-column prop="score" label="综合得分" width="120" align="center" sortable>
                <template #default="{ row }">
                  <el-tag :type="getScoreType(row.score)">{{ row.score }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="momChange" label="环比变化" width="100" align="center">
                <template #default="{ row }">
                  <span :class="getChangeClass(row.momChange)">{{ row.momChange }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="anomalyRate" label="异常率" width="100" align="center">
                <template #default="{ row }">
                  <span :class="getAnomalyRateClass(row.anomalyRate)">{{ row.anomalyRate }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="rank" label="排名" width="80" align="center" />
            </el-table>
          </div>
        </el-card>
      </div>

      <div class="right-panel">
        <el-card>
          <template #header>
            <span class="font-bold">异常汇总</span>
          </template>
          <div class="anomaly-container">
            <div class="anomaly-summary">
              <div v-for="anomaly in anomalySummary" :key="anomaly.stationId" class="anomaly-item">
                <el-alert :title="`${anomaly.stationName} - 异常率: ${anomaly.anomalyRate}`" type="warning"
                  :closable="false">
                  <div>主要异常指标: {{ anomaly.indicators.join(', ') }}</div>
                  <div class="mt-2">
                    <strong>改进建议:</strong> {{ anomaly.suggestion }}
                  </div>
                </el-alert>
              </div>
              <div v-if="anomalySummary.length === 0" class="no-anomaly">
                <el-empty description="暂无异常数据" :image-size="80" />
              </div>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 3️⃣ 指标热力图表格 -->
    <el-card class="mb-4">
      <template #header>
        <span class="font-bold">指标热力图</span>
      </template>
      <div class="heatmap-container">
        <table class="heatmap-table">
          <thead>
            <tr>
              <th class="indicator-header">指标</th>
              <th v-for="station in currentStations" :key="station.id" class="station-header">
                {{ station.name }}
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(indicator, index) in currentIndicators" :key="indicator">
              <td class="indicator-cell">{{ indicator }}</td>
              <td v-for="(station, stationIndex) in currentStations" :key="station.id" class="score-cell"
                :class="getHeatmapCellClass(heatmapData.scores[index][stationIndex])"
                @mouseenter="showTooltip($event, indicator, station, heatmapData.scores[index][stationIndex])"
                @mouseleave="hideTooltip">
                {{ heatmapData.scores[index][stationIndex] }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </el-card>

    <!-- 4️⃣ 指标分析图表 -->
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">{{ selectedIndicator ? `${selectedIndicator} 指标分析` : '指标分析图表' }}</span>
          <el-select v-model="selectedIndicator" placeholder="选择指标" style="width: 200px"
            @change="handleIndicatorChange">
            <el-option v-for="indicator in currentIndicators" :key="indicator" :label="indicator" :value="indicator" />
          </el-select>
        </div>
      </template>
      <div class="indicator-analysis">
        <div v-if="!selectedIndicator" class="chart-placeholder">
          <div class="placeholder-content">
            <el-icon size="48" color="#909399">
              <TrendCharts />
            </el-icon>
            <p>请选择指标查看分析图表</p>
          </div>
        </div>
        <div v-else class="chart-container">
          <!-- 图表类型切换 -->
          <div class="chart-type-selector mb-4">
            <el-radio-group v-model="chartType" @change="handleChartTypeChange">
              <el-radio-button label="radar">雷达图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
              <el-radio-button label="comparison">对比分析</el-radio-button>
            </el-radio-group>
          </div>
          <!-- 雷达图 -->
          <div v-if="chartType === 'radar'" class="radar-chart">
            <svg class="radar-svg" :width="chartWidth" :height="chartHeight" viewBox="0 0 600 400">
              <!-- 雷达图背景网格 -->
              <g class="radar-grid" transform="translate(300, 200)">
                <!-- 同心圆 -->
                <g v-for="level in 5" :key="level">
                  <circle :r="level * 30" fill="none" stroke="#e0e0e0" stroke-width="1" />
                  <text :x="level * 30 + 5" y="-5" font-size="10" fill="#999">{{ level * 20 }}</text>
                </g>

                <!-- 雷达轴线 -->
                <g v-for="(station, index) in currentStations" :key="station.id">
                  <line x1="0" y1="0" :x2="getRadarX(index, 150)" :y2="getRadarY(index, 150)" stroke="#d0d0d0"
                    stroke-width="1" />
                  <text :x="getRadarX(index, 170)" :y="getRadarY(index, 170)" text-anchor="middle" font-size="12"
                    fill="#666">
                    {{ station.name }}
                  </text>
                </g>

                <!-- 雷达数据区域 -->
                <polygon :points="getRadarPolygonPoints()" fill="rgba(64, 158, 255, 0.2)" stroke="#409EFF"
                  stroke-width="2" />

                <!-- 雷达数据点 -->
                <g v-for="(station, index) in currentStations" :key="station.id">
                  <circle :cx="getRadarX(index, getRadarRadius(getIndicatorScore(station)))"
                    :cy="getRadarY(index, getRadarRadius(getIndicatorScore(station)))" r="4"
                    :fill="getStationColor(index)" stroke="white" stroke-width="2" class="radar-point"
                    @mouseover="showTooltip($event, station.name, selectedIndicator, getIndicatorScore(station))"
                    @mouseout="hideTooltip" />
                </g>
              </g>
            </svg>
          </div>

          <!-- 柱状图 -->
          <div v-if="chartType === 'bar'" class="bar-chart">
            <svg class="bar-svg" :width="chartWidth" :height="chartHeight" viewBox="0 0 800 400">
              <!-- 坐标轴 -->
              <g class="axes">
                <!-- Y轴 -->
                <line x1="60" y1="50" x2="60" y2="350" stroke="#d0d0d0" stroke-width="2" />
                <g v-for="tick in [0, 20, 40, 60, 80, 100]" :key="tick">
                  <line :x1="55" :x2="65" :y1="getBarYPosition(tick)" :y2="getBarYPosition(tick)" stroke="#d0d0d0"
                    stroke-width="1" />
                  <text :x="45" :y="getBarYPosition(tick) + 5" text-anchor="end" font-size="12" fill="#666">{{ tick
                    }}</text>
                </g>

                <!-- X轴 -->
                <line x1="60" y1="350" x2="740" y2="350" stroke="#d0d0d0" stroke-width="2" />
              </g>

              <!-- 柱状图 -->
              <g class="bars">
                <g v-for="(station, index) in currentStations" :key="station.id">
                  <rect :x="getBarXPosition(index)" :y="getBarYPosition(getIndicatorScore(station))" :width="barWidth"
                    :height="350 - getBarYPosition(getIndicatorScore(station))" :fill="getStationColor(index)"
                    stroke="white" stroke-width="1" class="bar-rect"
                    @mouseover="showTooltip($event, station.name, selectedIndicator, getIndicatorScore(station))"
                    @mouseout="hideTooltip" />
                  <text :x="getBarXPosition(index) + barWidth / 2" y="370" text-anchor="middle" font-size="11"
                    fill="#666">
                    {{ station.name }}
                  </text>
                  <text :x="getBarXPosition(index) + barWidth / 2" :y="getBarYPosition(getIndicatorScore(station)) - 5"
                    text-anchor="middle" font-size="12" font-weight="bold" fill="#333">
                    {{ getIndicatorScore(station) }}
                  </text>
                </g>
              </g>
            </svg>
          </div>

          <!-- 对比分析表格 -->
          <div v-if="chartType === 'comparison'" class="comparison-table">
            <el-table :data="comparisonTableData" size="small" border>
              <el-table-column prop="station" label="厂站名称" width="150" />
              <el-table-column prop="score" label="指标得分" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getScoreTagType(row.score)">{{ row.score }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="rank" label="排名" width="80" align="center">
                <template #default="{ row }">
                  <el-tag :type="getRankTagType(row.rank)" effect="plain">第{{ row.rank }}名</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="deviation" label="偏离平均值" width="120" align="center">
                <template #default="{ row }">
                  <span :class="getDeviationClass(row.deviation)">{{ row.deviation > 0 ? '+' : '' }}{{ row.deviation
                    }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="level" label="评价等级" width="100" align="center">
                <template #default="{ row }">
                  <el-tag :type="getLevelTagType(row.level)">{{ row.level }}</el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="suggestion" label="改进建议" min-width="200" show-overflow-tooltip />
            </el-table>
          </div>
        </div>
      </div>
    </el-card>




  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { TrendCharts } from '@element-plus/icons-vue'

// 接口定义
interface Scheme {
  id: string
  name: string
  periodType: '月度' | '季度' | '年度'
  stations: Station[]
  indicators: Indicator[]
}

interface Station {
  id: string
  name: string
}

interface Indicator {
  id: string
  name: string
  unit: string
}

interface StationScore {
  stationId: string
  name: string
  score: number
  momChange: string
  anomalyRate: string
  rank: number
}

interface HeatmapData {
  indicators: string[]
  stations: string[]
  scores: number[][]
}

interface TrendData {
  periods: string[]
  stations: string[]
  scores: Record<string, number[]>
}

interface AnomalySummary {
  stationId: string
  stationName: string
  anomalyRate: string
  indicators: string[]
  suggestion: string
}

// 响应式数据
const filters = reactive({
  schemeId: 'scheme_001',
  period: '2025年3月'
})

const selectedIndicator = ref('')
const chartType = ref('radar') // 图表类型：radar, bar, comparison

// 方案数据 - 与厂站视角保持一致，基于真实净水厂业务场景，丰富完善
const schemes = ref<Scheme[]>([
  {
    id: 'scheme_001',
    name: '净水厂月度运营评估方案',
    periodType: '月度',
    stations: [
      { id: 'plant_001', name: '经开净水厂' },
      { id: 'plant_002', name: '望塘净水厂' },
      { id: 'plant_003', name: '塘西河净水厂' },
      { id: 'plant_004', name: '北涝圩净水厂' },
      { id: 'plant_005', name: '长岗净水厂' },
      { id: 'plant_006', name: '滨湖净水厂' },
      { id: 'plant_007', name: '新站净水厂' }
    ],
    indicators: [
      { id: 'ind_001', name: '出水浊度', unit: 'NTU' },
      { id: 'ind_002', name: '余氯含量', unit: 'mg/L' },
      { id: 'ind_003', name: '出水水质达标率', unit: '%' },
      { id: 'ind_004', name: 'pH值稳定性', unit: '无' },
      { id: 'ind_005', name: '细菌总数合格率', unit: '%' },
      { id: 'ind_006', name: '单位产水能耗', unit: 'kWh/m³' },
      { id: 'ind_007', name: '产水率', unit: '%' },
      { id: 'ind_008', name: '药剂单耗', unit: 'g/m³' },
      { id: 'ind_009', name: '处理负荷率', unit: '%' },
      { id: 'ind_010', name: '原水利用率', unit: '%' },
      { id: 'ind_011', name: '设备完好率', unit: '%' },
      { id: 'ind_012', name: '供水保证率', unit: '%' },
      { id: 'ind_013', name: '故障响应时间', unit: '分钟' },
      { id: 'ind_014', name: '预防性维护完成率', unit: '%' },
      { id: 'ind_015', name: '安全事故发生率', unit: '次/年' },
      { id: 'ind_016', name: '环保达标率', unit: '%' },
      { id: 'ind_017', name: '应急演练完成率', unit: '%' },
      { id: 'ind_018', name: '员工安全培训覆盖率', unit: '%' }
    ]
  },
  {
    id: 'scheme_002',
    name: '净水站季度评估方案',
    periodType: '季度',
    stations: [
      { id: 'station_001', name: '长临河镇净水站' },
      { id: 'station_002', name: '元疃镇净水站' },
      { id: 'station_003', name: '梁园镇净水站' },
      { id: 'station_004', name: '石塘镇净水站' },
      { id: 'station_005', name: '旺兴塘净水站' }
    ],
    indicators: [
      { id: 'ind_009', name: '出水浊度', unit: 'NTU' },
      { id: 'ind_010', name: '余氯含量', unit: 'mg/L' },
      { id: 'ind_011', name: '出水水质达标率', unit: '%' },
      { id: 'ind_012', name: '单位产水能耗', unit: 'kWh/m³' },
      { id: 'ind_013', name: '设备完好率', unit: '%' }
    ]
  },
  {
    id: 'scheme_003',
    name: '区域净水中心年度评估方案',
    periodType: '年度',
    stations: [
      { id: 'center_001', name: '巢湖净水中心' },
      { id: 'center_002', name: '肥东净水中心' },
      { id: 'center_003', name: '西部组团净水一厂' },
      { id: 'center_004', name: '西部组团净水二厂' }
    ],
    indicators: [
      { id: 'ind_014', name: '出水浊度', unit: 'NTU' },
      { id: 'ind_015', name: '余氯含量', unit: 'mg/L' },
      { id: 'ind_016', name: '出水水质达标率', unit: '%' },
      { id: 'ind_017', name: '单位产水能耗', unit: 'kWh/m³' },
      { id: 'ind_018', name: '产水率', unit: '%' },
      { id: 'ind_019', name: '药剂单耗', unit: 'g/m³' }
    ]
  },
  {
    id: 'scheme05',
    name: '2025年集团年度综合绩效评估方案',
    periodType: '年度',
    stations: [
      { id: 'region01', name: '华东区域' },
      { id: 'region02', name: '华南区域' },
      { id: 'region03', name: '华北区域' },
      { id: 'region04', name: '西南区域' },
      { id: 'region05', name: '东北区域' }
    ],
    indicators: [
      { id: 'ind23', name: '供水安全保障率', unit: '%' },
      { id: 'ind24', name: '客户满意度', unit: '%' },
      { id: 'ind25', name: '经营效益', unit: '万元' },
      { id: 'ind26', name: '环保合规率', unit: '%' },
      { id: 'ind27', name: '安全生产率', unit: '%' },
      { id: 'ind28', name: '技术创新指数', unit: '分' }
    ]
  }
])

// 初始化为空，将根据选择的方案动态生成
const stationScores = ref<StationScore[]>([])

// 初始化为空，将根据选择的方案动态生成
const heatmapData = ref<HeatmapData>({
  indicators: [],
  stations: [],
  scores: []
})

// 初始化为空，将根据选择的方案动态生成
const trendData = ref<TrendData>({
  periods: [],
  stations: [],
  scores: {}
})

// 初始化为空，将根据选择的方案动态生成
const anomalySummary = ref<AnomalySummary[]>([])

// 计算属性
const selectedScheme = computed(() => {
  return schemes.value.find(s => s.id === filters.schemeId)
})

const currentStations = computed(() => {
  return selectedScheme.value?.stations || []
})

const currentIndicators = computed(() => {
  return selectedScheme.value?.indicators.map(ind => ind.name) || []
})

const availablePeriods = computed(() => {
  if (!selectedScheme.value) return []

  const periodType = selectedScheme.value.periodType
  const periods: string[] = []

  if (periodType === '月度') {
    for (let i = 1; i <= 12; i++) {
      periods.push(`2025年${i}月`)
    }
  } else if (periodType === '季度') {
    periods.push('2025年Q1', '2025年Q2', '2025年Q3', '2025年Q4')
  } else if (periodType === '年度') {
    periods.push('2023年', '2024年', '2025年')
  }

  return periods
})

// 图表相关计算属性
const chartWidth = computed(() => 800)
const chartHeight = computed(() => 400)

const yAxisTicks = computed(() => {
  return [100, 90, 80, 70, 60, 50]
})

// 柱状图相关计算属性
const barWidth = computed(() => {
  const availableWidth = 680 // 总宽度减去边距
  const stationCount = currentStations.value.length
  const spacing = 20 // 柱子间距
  return Math.max(30, (availableWidth - spacing * (stationCount - 1)) / stationCount)
})

// 对比分析表格数据
const comparisonTableData = computed(() => {
  if (!selectedIndicator.value) return []

  const stationsWithScores = currentStations.value.map(station => ({
    station: station.name,
    score: getIndicatorScore(station)
  }))

  // 按得分排序
  stationsWithScores.sort((a, b) => b.score - a.score)

  // 计算平均分
  const averageScore = stationsWithScores.reduce((sum, item) => sum + item.score, 0) / stationsWithScores.length

  return stationsWithScores.map((item, index) => {
    const deviation = Math.round((item.score - averageScore) * 10) / 10
    const level = item.score >= 90 ? '优秀' : item.score >= 80 ? '良好' : item.score >= 70 ? '合格' : '待改进'
    const suggestion = getSuggestionByScore(item.score, selectedIndicator.value)

    return {
      station: item.station,
      score: item.score,
      rank: index + 1,
      deviation,
      level,
      suggestion
    }
  })
})

const trendTableData = computed(() => {
  if (!selectedIndicator.value) return []

  return currentStations.value.map(station => {
    const scores = trendData.value.periods.map((_, index) => getTrendScore(station.name, index))
    const average = Math.round(scores.reduce((sum, score) => sum + score, 0) / scores.length * 10) / 10
    const trend = scores.length > 1 ? scores[scores.length - 1] - scores[0] : 0
    const trendText = trend > 2 ? '↗️ 上升' : trend < -2 ? '↘️ 下降' : '➡️ 平稳'

    return {
      station: station.name,
      scores,
      average,
      trend,
      trendText
    }
  })
})

// 工具方法
const getScoreType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'warning'
  if (score >= 70) return ''
  return 'danger'
}

const getChangeClass = (change: string) => {
  if (change.startsWith('+')) return 'text-green-500'
  if (change.startsWith('-')) return 'text-red-500'
  return 'text-gray-500'
}

const getAnomalyRateClass = (rate: string) => {
  const numRate = parseFloat(rate)
  if (numRate <= 3) return 'text-green-500'
  if (numRate <= 6) return 'text-orange-500'
  return 'text-red-500'
}

const getHeatmapCellClass = (score: number) => {
  if (score < 60) return 'score-red'
  if (score < 85) return 'score-yellow'
  return 'score-green'
}

const getTrendScore = (stationName: string, periodIndex: number) => {
  return trendData.value.scores[stationName]?.[periodIndex] || 0
}

// 图表绘制方法
const getXPosition = (periodIndex: number) => {
  const chartStart = 80
  const chartWidth = 640
  const stepWidth = chartWidth / Math.max(1, trendData.value.periods.length - 1)
  return chartStart + periodIndex * stepWidth
}

const getYPosition = (score: number) => {
  const chartTop = 60
  const chartHeight = 280
  const minScore = 40
  const maxScore = 100
  const normalizedScore = Math.max(minScore, Math.min(maxScore, score))
  return chartTop + chartHeight - ((normalizedScore - minScore) / (maxScore - minScore)) * chartHeight
}

const getTrendLinePoints = (stationName: string) => {
  const points = trendData.value.periods.map((_, index) => {
    const x = getXPosition(index)
    const y = getYPosition(getTrendScore(stationName, index))
    return `${x},${y}`
  })
  return points.join(' ')
}

const getStationColor = (index: number) => {
  const colors = [
    '#409EFF', // 蓝色
    '#67C23A', // 绿色
    '#E6A23C', // 橙色
    '#F56C6C', // 红色
    '#909399', // 灰色
    '#9C27B0', // 紫色
    '#FF9800', // 深橙色
    '#4CAF50'  // 深绿色
  ]
  return colors[index % colors.length]
}

const getScoreClass = (score: number) => {
  if (score >= 90) return 'text-green-500'
  if (score >= 80) return 'text-blue-500'
  if (score >= 70) return 'text-orange-500'
  return 'text-red-500'
}

const getScoreTagType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return ''
  if (score >= 70) return 'warning'
  return 'danger'
}

const getTrendClass = (trend: number) => {
  if (trend > 2) return 'text-green-500'
  if (trend < -2) return 'text-red-500'
  return 'text-gray-500'
}

const showTooltip = (event: MouseEvent, param1: string | Station, param2: string, param3: number) => {
  // 简单的tooltip实现，支持两种调用方式
  const tooltip = document.createElement('div')
  tooltip.className = 'chart-tooltip'

  let content = ''
  if (typeof param1 === 'string') {
    // 趋势图调用：showTooltip(event, stationName, period, score)
    content = `
      <div><strong>${param1}</strong></div>
      <div>${param2}: ${param3}分</div>
    `
  } else {
    // 热力图调用：showTooltip(event, indicator, station, score)
    const indicatorData = selectedScheme.value?.indicators.find(ind => ind.name === param2)
    content = `
      <div><strong>${param1.name}</strong></div>
      <div>${param2}: ${param3}分</div>
      ${indicatorData?.unit ? `<div>单位: ${indicatorData.unit}</div>` : ''}
    `
  }

  tooltip.innerHTML = content
  tooltip.style.cssText = `
    position: absolute;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    left: ${event.pageX + 10}px;
    top: ${event.pageY - 10}px;
  `
  document.body.appendChild(tooltip)
}

const hideTooltip = () => {
  const tooltips = document.querySelectorAll('.chart-tooltip')
  tooltips.forEach(tooltip => tooltip.remove())
}

// 新增图表相关方法
const getIndicatorScore = (station: Station) => {
  // 从热力图数据中获取指标得分
  if (!selectedIndicator.value) return 0

  const indicatorIndex = currentIndicators.value.indexOf(selectedIndicator.value)
  const stationIndex = currentStations.value.findIndex(s => s.id === station.id)

  if (indicatorIndex >= 0 && stationIndex >= 0) {
    return heatmapData.value.scores[indicatorIndex]?.[stationIndex] || 0
  }

  return 0
}

// 雷达图相关方法
const getRadarX = (index: number, radius: number) => {
  const angle = (index * 2 * Math.PI) / currentStations.value.length - Math.PI / 2
  return Math.cos(angle) * radius
}

const getRadarY = (index: number, radius: number) => {
  const angle = (index * 2 * Math.PI) / currentStations.value.length - Math.PI / 2
  return Math.sin(angle) * radius
}

const getRadarRadius = (score: number) => {
  // 将得分(0-100)转换为雷达图半径(0-150)
  return (score / 100) * 150
}

const getRadarPolygonPoints = () => {
  const points = currentStations.value.map((station, index) => {
    const score = getIndicatorScore(station)
    const radius = getRadarRadius(score)
    const x = getRadarX(index, radius)
    const y = getRadarY(index, radius)
    return `${x},${y}`
  })
  return points.join(' ')
}

// 柱状图相关方法
const getBarXPosition = (index: number) => {
  const startX = 80
  const spacing = (680 - barWidth.value * currentStations.value.length) / (currentStations.value.length - 1)
  return startX + index * (barWidth.value + spacing)
}

const getBarYPosition = (score: number) => {
  const chartTop = 50
  const chartHeight = 300
  return chartTop + chartHeight - (score / 100) * chartHeight
}

// 对比分析相关方法
const getSuggestionByScore = (score: number, indicator: string) => {
  if (score >= 90) {
    return '表现优秀，继续保持当前水平'
  } else if (score >= 80) {
    return '表现良好，可进一步优化提升'
  } else if (score >= 70) {
    return '达到合格标准，建议重点关注薄弱环节'
  } else {
    return `${indicator}表现待改进，建议制定专项提升计划`
  }
}

const getRankTagType = (rank: number) => {
  if (rank === 1) return 'success'
  if (rank <= 3) return ''
  return 'info'
}

const getDeviationClass = (deviation: number) => {
  if (deviation > 5) return 'text-green-500'
  if (deviation < -5) return 'text-red-500'
  return 'text-gray-500'
}

const getLevelTagType = (level: string) => {
  switch (level) {
    case '优秀': return 'success'
    case '良好': return ''
    case '合格': return 'warning'
    case '待改进': return 'danger'
    default: return 'info'
  }
}

// 图表类型切换处理
const handleChartTypeChange = (type: string) => {
  console.log('图表类型切换到:', type)
}

// 事件处理方法
const handleSchemeChange = (schemeId: string) => {
  filters.period = ''
  selectedIndicator.value = ''

  // 更新所有相关数据
  updateStationScores()
  updateHeatmapData()
  updateTrendData()
  updateAnomalySummary()

  ElMessage.success('方案已切换，数据已更新')
}

const handlePeriodChange = (period: string) => {
  // 更新相关数据
  updateStationScores()
  updateHeatmapData()
  updateTrendData()
  updateAnomalySummary()

  ElMessage.info(`周期已切换到: ${period}`)
}

const handleIndicatorChange = (indicator: string) => {
  ElMessage.info(`已选择指标: ${indicator}`)
}

const handleViewDetail = (row: StationScore) => {
  ElMessage.success(`查看 ${row.name} 详情`)
}



// 数据更新方法
const updateStationScores = () => {
  // 根据方案生成更真实的厂站得分数据
  const scheme = selectedScheme.value
  if (!scheme) return

  // 根据不同方案类型生成不同的得分分布
  const getScoreRange = (schemeId: string) => {
    switch (schemeId) {
      case 'scheme_001': // 净水厂月度评估
        return { min: 85, max: 98, avgAnomalyRate: 2.8 }
      case 'scheme_002': // 净水站季度评估
        return { min: 82, max: 95, avgAnomalyRate: 3.5 }
      case 'scheme_003': // 区域净水中心年度评估
        return { min: 88, max: 97, avgAnomalyRate: 2.2 }
      default:
        return { min: 85, max: 96, avgAnomalyRate: 3.0 }
    }
  }

  const scoreRange = getScoreRange(scheme.id)
  const stations = scheme.stations.map((station, index) => {
    // 生成基础得分
    const baseScore = scoreRange.min + Math.random() * (scoreRange.max - scoreRange.min)

    // 添加一些变化，让排名更有层次
    const variation = (Math.random() - 0.5) * 10
    const finalScore = Math.max(scoreRange.min, Math.min(scoreRange.max, baseScore + variation))

    // 生成环比变化
    const changeValue = (Math.random() - 0.5) * 6
    const momChange = changeValue >= 0 ? `+${changeValue.toFixed(1)}` : `${changeValue.toFixed(1)}`

    // 生成异常率
    const anomalyBase = scoreRange.avgAnomalyRate
    const anomalyVariation = (Math.random() - 0.5) * 4
    const anomalyRate = Math.max(0.1, anomalyBase + anomalyVariation)

    return {
      stationId: station.id,
      name: station.name,
      score: Math.round(finalScore * 10) / 10,
      momChange,
      anomalyRate: `${anomalyRate.toFixed(1)}%`,
      rank: 0 // 将在排序后设置
    }
  })

  // 按得分排序并设置排名
  stations.sort((a, b) => b.score - a.score)
  stations.forEach((station, index) => {
    station.rank = index + 1
  })

  stationScores.value = stations
}

const updateHeatmapData = () => {
  // 根据方案生成更真实的热力图数据
  const scheme = selectedScheme.value
  if (!scheme) return

  // 根据不同方案类型生成不同的得分分布
  const getIndicatorScoreRange = (schemeId: string, indicatorIndex: number) => {
    switch (schemeId) {
      case 'scheme01': // 水厂运营效率
        const waterFactoryRanges = [
          { min: 85, max: 98 }, // 出水水质达标率
          { min: 70, max: 90 }, // 单位产水能耗
          { min: 80, max: 95 }, // 处理效率
          { min: 75, max: 92 }, // 设备完好率
          { min: 78, max: 88 }, // 药剂消耗
          { min: 82, max: 96 }  // 污泥处置率
        ]
        return waterFactoryRanges[indicatorIndex] || { min: 75, max: 95 }

      case 'scheme02': // 泵站运行监控
        const pumpStationRanges = [
          { min: 88, max: 99 }, // 流量稳定性
          { min: 85, max: 98 }, // 设备运行率
          { min: 72, max: 89 }, // 能耗效率
          { min: 92, max: 99 }, // 故障率（低故障率=高分）
          { min: 80, max: 95 }  // 维护及时性
        ]
        return pumpStationRanges[indicatorIndex] || { min: 80, max: 98 }

      case 'scheme03': // 管网运维质量
        const networkRanges = [
          { min: 75, max: 92 }, // 管网漏损率
          { min: 85, max: 97 }, // 水压合格率
          { min: 78, max: 94 }, // 抢修及时率
          { min: 88, max: 99 }, // 水质二次污染率
          { min: 70, max: 88 }  // 管网完好率
        ]
        return networkRanges[indicatorIndex] || { min: 70, max: 90 }

      case 'scheme04': // 污水处理厂
        const wwtpRanges = [
          { min: 90, max: 99 }, // COD去除率
          { min: 88, max: 98 }, // BOD去除率
          { min: 85, max: 96 }, // 氨氮去除率
          { min: 82, max: 94 }, // 总磷去除率
          { min: 78, max: 92 }, // 污泥处理率
          { min: 75, max: 89 }  // 能耗控制
        ]
        return wwtpRanges[indicatorIndex] || { min: 78, max: 94 }

      case 'scheme05': // 集团年度综合
        const groupRanges = [
          { min: 92, max: 99 }, // 供水安全保障率
          { min: 85, max: 96 }, // 客户满意度
          { min: 78, max: 94 }, // 经营效益
          { min: 88, max: 98 }, // 环保合规率
          { min: 90, max: 99 }, // 安全生产率
          { min: 75, max: 92 }  // 技术创新指数
        ]
        return groupRanges[indicatorIndex] || { min: 82, max: 96 }

      default:
        return { min: 75, max: 95 }
    }
  }

  heatmapData.value = {
    indicators: scheme.indicators.map(ind => ind.name),
    stations: scheme.stations.map(sta => sta.name),
    scores: scheme.indicators.map((indicator, indicatorIndex) => {
      const range = getIndicatorScoreRange(scheme.id, indicatorIndex)
      return scheme.stations.map(() => {
        // 生成符合指标特性的得分
        const baseScore = range.min + Math.random() * (range.max - range.min)
        const variation = (Math.random() - 0.5) * 8 // 添加一些随机变化
        const finalScore = Math.max(range.min, Math.min(range.max, baseScore + variation))
        return Math.floor(finalScore)
      })
    })
  }
}

const updateTrendData = () => {
  // 根据方案生成趋势数据
  const scheme = selectedScheme.value
  if (!scheme) return

  // 根据周期类型生成时间序列
  const generatePeriods = (periodType: string) => {
    switch (periodType) {
      case '月度':
        return ['2024年10月', '2024年11月', '2024年12月', '2025年1月', '2025年2月', '2025年3月']
      case '季度':
        return ['2024年Q3', '2024年Q4', '2025年Q1', '2025年Q2']
      case '年度':
        return ['2022年', '2023年', '2024年', '2025年']
      default:
        return ['2024年Q4', '2025年Q1', '2025年Q2']
    }
  }

  const periods = generatePeriods(scheme.periodType)
  const stations = scheme.stations.map(sta => sta.name)
  const scores: Record<string, number[]> = {}

  // 为每个厂站生成趋势数据
  stations.forEach(stationName => {
    const stationScores: number[] = []
    let baseScore = 75 + Math.random() * 20 // 基础得分

    periods.forEach((period, index) => {
      // 模拟趋势变化
      const trendFactor = Math.sin(index * 0.5) * 3 // 周期性变化
      const randomFactor = (Math.random() - 0.5) * 4 // 随机变化
      const improvementFactor = index * 0.8 // 整体改善趋势

      baseScore = Math.max(60, Math.min(100, baseScore + trendFactor + randomFactor + improvementFactor))
      stationScores.push(Math.round(baseScore * 10) / 10)
    })

    scores[stationName] = stationScores
  })

  trendData.value = {
    periods,
    stations,
    scores
  }
}

const updateAnomalySummary = () => {
  // 根据方案生成异常汇总数据
  const scheme = selectedScheme.value
  if (!scheme) return

  const anomalies: AnomalySummary[] = []

  // 从得分较低的厂站中选择一些作为异常案例
  const lowScoreStations = stationScores.value
    .filter(station => station.score < 85)
    .sort((a, b) => a.score - b.score)
    .slice(0, Math.min(3, Math.ceil(stationScores.value.length * 0.3)))

  lowScoreStations.forEach(station => {
    // 随机选择一些指标作为异常指标
    const indicatorCount = Math.min(3, Math.max(1, Math.floor(Math.random() * scheme.indicators.length)))
    const selectedIndicators = scheme.indicators
      .sort(() => Math.random() - 0.5)
      .slice(0, indicatorCount)
      .map(ind => ind.name)

    // 生成建议
    const getSuggestion = (schemeId: string, indicators: string[]) => {
      const suggestions = {
        'scheme01': [
          '建议加强水质监控，优化处理工艺',
          '建议检查设备运行状态，降低能耗',
          '建议完善药剂投加控制系统',
          '建议加强污泥处理设施维护'
        ],
        'scheme02': [
          '建议检查泵站设备，确保流量稳定',
          '建议加强设备维护，提高运行率',
          '建议优化运行参数，降低能耗',
          '建议建立预防性维护计划'
        ],
        'scheme03': [
          '建议加强管网巡检，及时发现漏损点',
          '建议优化供水调度，保证水压稳定',
          '建议完善应急抢修机制',
          '建议加强管网水质监测'
        ],
        'scheme04': [
          '建议优化生化处理工艺参数',
          '建议加强进水水质监控',
          '建议完善污泥处理处置流程',
          '建议提升自动化控制水平'
        ],
        'scheme05': [
          '建议完善供水安全保障体系',
          '建议提升客户服务质量',
          '建议优化经营管理模式',
          '建议加强环保合规管理'
        ]
      }

      const schemeSuggestions = suggestions[schemeId as keyof typeof suggestions] || suggestions['scheme01']
      return schemeSuggestions[Math.floor(Math.random() * schemeSuggestions.length)]
    }

    anomalies.push({
      stationId: station.stationId,
      stationName: station.name,
      anomalyRate: station.anomalyRate,
      indicators: selectedIndicators,
      suggestion: getSuggestion(scheme.id, selectedIndicators)
    })
  })

  anomalySummary.value = anomalies
}

// 初始化
const init = () => {
  if (schemes.value.length > 0) {
    filters.schemeId = schemes.value[0].id
    handleSchemeChange(filters.schemeId)
  }
}

// 组件挂载时初始化
init()
</script>

<style scoped lang="scss">
.group-view {
  .mb-4 {
    margin-bottom: 16px;
  }

  .font-bold {
    font-weight: bold;
  }

  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  // 文本颜色类
  .text-green-500 {
    color: #10b981;
  }

  .text-red-500 {
    color: #ef4444;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  .text-orange-500 {
    color: #f59e0b;
  }

  // 热力图样式
  .heatmap-container {
    overflow-x: auto;

    .heatmap-table {
      width: 100%;
      border-collapse: collapse;

      th,
      td {
        border: 1px solid #e5e7eb;
        padding: 12px 8px;
        text-align: center;
      }

      .indicator-header,
      .station-header {
        background-color: #f9fafb;
        font-weight: bold;
      }

      .indicator-cell {
        background-color: #f9fafb;
        font-weight: 500;
        text-align: left;
        min-width: 120px;
      }

      .score-cell {
        cursor: pointer;
        transition: all 0.2s;
        font-weight: bold;

        &:hover {
          transform: scale(1.05);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        &.score-green {
          background-color: #dcfce7;
          color: #166534;
        }

        &.score-yellow {
          background-color: #fef3c7;
          color: #92400e;
        }

        &.score-red {
          background-color: #fecaca;
          color: #991b1b;
        }
      }
    }
  }

  // 指标分析样式
  .indicator-analysis {
    .chart-placeholder {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background-color: #f9fafb;

      .placeholder-content {
        text-align: center;
        color: #909399;

        p {
          margin-top: 16px;
          font-size: 16px;
        }
      }
    }

    .chart-container {
      .chart-type-selector {
        text-align: center;
        margin-bottom: 20px;
      }

      // 雷达图样式
      .radar-chart {
        .radar-svg {
          width: 100%;
          height: 400px;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          background-color: #fafafa;

          .radar-point {
            cursor: pointer;
            transition: r 0.2s ease;

            &:hover {
              r: 6;
            }
          }
        }
      }

      // 柱状图样式
      .bar-chart {
        .bar-svg {
          width: 100%;
          height: 400px;
          border: 1px solid #e5e7eb;
          border-radius: 6px;
          background-color: #fafafa;

          .bar-rect {
            cursor: pointer;
            transition: opacity 0.2s ease;

            &:hover {
              opacity: 0.8;
            }
          }
        }
      }

      // 对比分析表格样式
      .comparison-table {
        .el-table {
          font-size: 13px;
        }
      }
    }
  }

  // 新增颜色类
  .text-blue-500 {
    color: #3b82f6;
  }

  // 左右分栏样式
  .score-anomaly-container {
    display: flex;
    gap: 20px;
    height: 500px; // 设置固定高度

    .left-panel {
      flex: 1.2; // 左侧稍宽一些，给表格更多空间

      .el-card {
        height: 100%;
        display: flex;
        flex-direction: column;

        .el-card__body {
          flex: 1;
          padding: 0;
          overflow: hidden;
        }
      }

      .table-container {
        height: 100%;
        width: 100%;

        .el-table {
          height: 100% !important;
          width: 100% !important;

          // 确保表格完全填充容器
          .el-table__inner-wrapper {
            height: 100% !important;
          }

          // 表格头部样式
          .el-table__header-wrapper {
            width: 100% !important;
          }

          // 表格主体样式
          .el-table__body-wrapper {
            width: 100% !important;
            flex: 1;
          }
        }
      }
    }

    .right-panel {
      flex: 0.8; // 右侧稍窄一些

      .el-card {
        height: 100%;
        display: flex;
        flex-direction: column;

        .el-card__body {
          flex: 1;
          padding: 20px;
          overflow: hidden;
        }
      }

      .anomaly-container {
        height: 100%;
        overflow-y: auto;
        padding-right: 8px; // 为滚动条留出空间

        // 自定义滚动条样式
        &::-webkit-scrollbar {
          width: 6px;
        }

        &::-webkit-scrollbar-track {
          background: #f1f1f1;
          border-radius: 3px;
        }

        &::-webkit-scrollbar-thumb {
          background: #c1c1c1;
          border-radius: 3px;

          &:hover {
            background: #a8a8a8;
          }
        }
      }
    }

    // 响应式设计
    @media (max-width: 1200px) {
      flex-direction: column;
      height: auto;

      .left-panel,
      .right-panel {
        flex: 1;

        .el-card {
          height: 400px; // 移动端固定高度
        }
      }
    }
  }

  // 异常汇总样式
  .anomaly-summary {
    .anomaly-item {
      margin-bottom: 12px;

      &:last-child {
        margin-bottom: 0;
      }

      .suggestion {
        margin-top: 8px;
        font-style: italic;
        color: #6b7280;
      }
    }
  }

  // 筛选头部样式
  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .el-form {
      margin: 0;
    }

    .filter-actions {
      .el-button {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .filter-actions {
        display: flex;
        justify-content: center;
      }
    }
  }
}
</style>
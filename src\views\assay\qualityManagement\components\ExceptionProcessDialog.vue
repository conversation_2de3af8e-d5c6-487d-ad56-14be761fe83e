<template>
  <el-dialog
    v-model="dialogVisible"
    title="异常处理"
    width="900px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="exception-process-container">
      <!-- 样品信息 -->
      <el-card class="sample-info-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><Warning /></el-icon>
            <span>异常样品信息</span>
          </div>
        </template>
        
        <el-descriptions :column="3" border>
          <el-descriptions-item label="样品编号">{{ sampleData.sampleCode }}</el-descriptions-item>
          <el-descriptions-item label="检测项目">{{ sampleData.testItem }}</el-descriptions-item>
          <el-descriptions-item label="检测值">
            <span class="exception-value">{{ sampleData.testValue }} {{ sampleData.unit }}</span>
          </el-descriptions-item>
          <el-descriptions-item label="标准值">{{ sampleData.standardValue }} {{ sampleData.unit }}</el-descriptions-item>
          <el-descriptions-item label="超标倍数">
            <el-tag type="danger">{{ getExceedRatio() }}倍</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="检测日期">{{ sampleData.testDate }}</el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 异常处理选项 -->
      <el-card class="process-options-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>处理方式</span>
          </div>
        </template>
        
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
          <el-form-item label="处理类型" prop="processType">
            <el-radio-group v-model="formData.processType" @change="handleProcessTypeChange">
              <el-radio label="recheck">安排复检</el-radio>
              <el-radio label="alarm">发起预警</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 复检安排 -->
          <div v-if="formData.processType === 'recheck'" class="process-section">
            <el-form-item label="复检人员" prop="recheckPerson">
              <el-select v-model="formData.recheckPerson" placeholder="请选择复检人员" style="width: 200px">
                <el-option label="张三" value="张三" />
                <el-option label="李四" value="李四" />
                <el-option label="王五" value="王五" />
              </el-select>
            </el-form-item>
            <el-form-item label="复检时间" prop="recheckTime">
              <el-date-picker
                v-model="formData.recheckTime"
                type="datetime"
                placeholder="请选择复检时间"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item label="复检要求" prop="recheckRequirement">
              <el-input
                v-model="formData.recheckRequirement"
                type="textarea"
                :rows="3"
                placeholder="请输入复检要求和注意事项"
                style="width: 400px"
              />
            </el-form-item>
          </div>

          <!-- 预警设置 -->
          <div v-if="formData.processType === 'alarm'" class="process-section">
            <el-form-item label="预警级别" prop="alarmLevel">
              <el-select v-model="formData.alarmLevel" placeholder="请选择预警级别" style="width: 200px">
                <el-option label="一般预警" value="normal" />
                <el-option label="重要预警" value="important" />
                <el-option label="紧急预警" value="urgent" />
              </el-select>
            </el-form-item>
            <el-form-item label="通知人员" prop="notifyPersons">
              <el-select
                v-model="formData.notifyPersons"
                multiple
                placeholder="请选择通知人员"
                style="width: 300px"
              >
                <el-option label="质量主管" value="quality_manager" />
                <el-option label="技术负责人" value="tech_leader" />
                <el-option label="厂长" value="plant_manager" />
                <el-option label="环保部门" value="env_dept" />
              </el-select>
            </el-form-item>
            <el-form-item label="预警内容" prop="alarmContent">
              <el-input
                v-model="formData.alarmContent"
                type="textarea"
                :rows="4"
                placeholder="请输入预警内容"
                style="width: 400px"
              />
            </el-form-item>
          </div>



          <el-form-item label="处理备注" prop="remark">
            <el-input
              v-model="formData.remark"
              type="textarea"
              :rows="2"
              placeholder="请输入其他备注信息"
              style="width: 400px"
            />
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 历史处理记录 -->
      <el-card class="history-card">
        <template #header>
          <div class="card-header">
            <el-icon><Clock /></el-icon>
            <span>历史处理记录</span>
          </div>
        </template>
        
        <el-timeline>
          <el-timeline-item
            v-for="record in historyRecords"
            :key="record.id"
            :timestamp="record.processTime"
            :type="getTimelineType(record.processType)"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ getProcessTypeText(record.processType) }}</div>
              <div class="timeline-desc">{{ record.description }}</div>
              <div class="timeline-operator">处理人：{{ record.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认处理
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'ExceptionProcessDialog' })

const emit = defineEmits(['success'])

// 对话框状态
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 样品数据
const sampleData = ref({
  sampleCode: '',
  testItem: '',
  testValue: '',
  standardValue: '',
  unit: '',
  testDate: ''
})

// 表单数据
const formData = reactive({
  processType: 'recheck',
  // 复检相关
  recheckPerson: '',
  recheckTime: '',
  recheckRequirement: '',
  // 预警相关
  alarmLevel: '',
  notifyPersons: [],
  alarmContent: '',
  // 异常记录相关
  exceptionType: '',
  exceptionReason: '',
  processMeasure: '',
  // 紧急处理相关
  emergencyLevel: '',
  emergencyMeasure: '',
  responsiblePerson: '',
  // 通用
  remark: ''
})

// 历史处理记录
const historyRecords = ref([
  {
    id: 1,
    processType: 'record',
    description: '检测值超标，已记录异常情况',
    operator: '张三',
    processTime: '2023-07-15 14:30'
  }
])

// 表单验证规则
const formRules: FormRules = {
  processType: [{ required: true, message: '请选择处理类型', trigger: 'change' }],
  recheckPerson: [{ required: true, message: '请选择复检人员', trigger: 'change' }],
  recheckTime: [{ required: true, message: '请选择复检时间', trigger: 'change' }],
  alarmLevel: [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  notifyPersons: [{ required: true, message: '请选择通知人员', trigger: 'change' }],
  exceptionType: [{ required: true, message: '请选择异常类型', trigger: 'change' }],
  emergencyLevel: [{ required: true, message: '请选择紧急级别', trigger: 'change' }],
  responsiblePerson: [{ required: true, message: '请选择责任人', trigger: 'change' }]
}

// 打开对话框
const open = (data: any) => {
  dialogVisible.value = true
  sampleData.value = { ...data }
  resetForm()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    processType: 'recheck',
    recheckPerson: '',
    recheckTime: '',
    recheckRequirement: '',
    alarmLevel: '',
    notifyPersons: [],
    alarmContent: '',
    exceptionType: '',
    exceptionReason: '',
    processMeasure: '',
    emergencyLevel: '',
    emergencyMeasure: '',
    responsiblePerson: '',
    remark: ''
  })
}

// 处理类型变更
const handleProcessTypeChange = () => {
  // 清空其他类型的字段
  formRef.value?.clearValidate()
}

// 获取超标倍数
const getExceedRatio = () => {
  const testValue = parseFloat(sampleData.value.testValue)
  const standardValue = parseFloat(sampleData.value.standardValue)
  if (standardValue > 0) {
    return (testValue / standardValue).toFixed(1)
  }
  return '0'
}

// 获取时间线类型
const getTimelineType = (processType: string) => {
  const typeMap: Record<string, string> = {
    recheck: 'primary',
    alarm: 'warning',
    record: 'info',
    emergency: 'danger'
  }
  return typeMap[processType] || 'info'
}

// 获取处理类型文本
const getProcessTypeText = (processType: string) => {
  const textMap: Record<string, string> = {
    recheck: '安排复检',
    alarm: '发起预警'
  }
  return textMap[processType] || processType
}

// 确认处理
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟处理
    setTimeout(() => {
      loading.value = false
      
      // 添加到历史记录
      const newRecord = {
        id: Date.now(),
        processType: formData.processType,
        description: getProcessDescription(),
        operator: '当前用户',
        processTime: new Date().toLocaleString()
      }
      historyRecords.value.unshift(newRecord)
      
      emit('success', {
        sampleCode: sampleData.value.sampleCode,
        processType: formData.processType,
        processData: { ...formData }
      })
      
      ElMessage.success('异常处理完成')
      handleClose()
    }, 1000)
    
  } catch (error) {
    loading.value = false
    console.error('表单验证失败:', error)
  }
}

// 获取处理描述
const getProcessDescription = () => {
  switch (formData.processType) {
    case 'recheck':
      return `安排${formData.recheckPerson}进行复检`
    case 'alarm':
      return `发起${formData.alarmLevel}预警`
    case 'record':
      return `记录${formData.exceptionType}异常`
    case 'emergency':
      return `启动${formData.emergencyLevel}应急处理`
    default:
      return '异常处理'
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.exception-process-container {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .exception-value {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .process-section {
    background: #f8f9fa;
    padding: 16px;
    border-radius: 6px;
    margin: 16px 0;
  }
  
  .timeline-content {
    .timeline-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }
    
    .timeline-desc {
      color: #606266;
      font-size: 14px;
      margin-bottom: 4px;
    }
    
    .timeline-operator {
      color: #9ca3af;
      font-size: 12px;
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800px">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="指标名称" prop="name">
            <el-input v-model="formData.name" placeholder="请输入指标名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指标代码" prop="code">
            <el-input v-model="formData.code" placeholder="请输入指标代码" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检测方法" prop="method">
            <el-input v-model="formData.method" placeholder="请输入检测方法" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="数据单位" prop="unit">
            <el-input v-model="formData.unit" placeholder="请输入数据单位" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="标准值下限" prop="standardMin">
            <el-input-number v-model="formData.standardMin" placeholder="请输入下限值" :precision="6" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="标准值上限" prop="standardMax">
            <el-input-number v-model="formData.standardMax" placeholder="请输入上限值" :precision="6" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检测仪器" prop="equipment">
            <el-input v-model="formData.equipment" placeholder="请输入检测仪器" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="精度要求" prop="precisionLimit">
            <el-input v-model="formData.precisionLimit" placeholder="如：5%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品量(mL)" prop="sampleVolume">
            <el-input-number v-model="formData.sampleVolume" :min="1" placeholder="请输入样品量" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测耗时(分钟)" prop="detectionTimeMinutes">
            <el-input-number v-model="formData.detectionTimeMinutes" :min="1" placeholder="请输入检测耗时" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="状态" prop="isEnabled">
            <el-radio-group v-model="formData.isEnabled">
              <el-radio :label="true">启用</el-radio>
              <el-radio :label="false">停用</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { AssayBaseInfoApi } from '@/api/assay/baseInfo'
import { useAppStore } from '@/store/modules/app'

defineOptions({ name: 'TestIndicatorDialog' })

const appStore = useAppStore()

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

// 表单数据
const formData = ref({
  id: undefined,
  factoryId: undefined as number | undefined,
  name: '',
  code: '',
  projectId: undefined as number | undefined,
  method: '',
  unit: '',
  standardMin: undefined as number | undefined,
  standardMax: undefined as number | undefined,
  equipment: '',
  precisionLimit: '',
  sampleVolume: 100,
  detectionTimeMinutes: 60,
  isEnabled: true
})

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '指标名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '指标代码不能为空', trigger: 'blur' }],
  method: [{ required: true, message: '检测方法不能为空', trigger: 'blur' }],
  unit: [{ required: true, message: '数据单位不能为空', trigger: 'blur' }],
  equipment: [{ required: true, message: '检测仪器不能为空', trigger: 'blur' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (type: string, data?: any, projectId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增检测指标' : type === 'update' ? '编辑检测指标' : '查看检测指标'
  formType.value = type
  resetForm()
  
  // 如果是新增模式，设置项目ID
  if (type === 'create' && projectId) {
    formData.value.projectId = projectId
  }
  
  // 如果是编辑或查看模式，设置表单数据
  if ((type === 'update' || type === 'view') && data) {
    formData.value = JSON.parse(JSON.stringify(data))
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  if (formType.value === 'view') {
    dialogVisible.value = false
    return
  }

  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法提交')
    return
  }

  formLoading.value = true
  try {
    // 构建请求数据，确保数字类型正确，过滤undefined值
    const data: any = {
      factoryId,
      code: formData.value.code,
      name: formData.value.name,
      projectId: formData.value.projectId,
      method: formData.value.method || '',
      unit: formData.value.unit || '',
      equipment: formData.value.equipment || '',
      precisionLimit: formData.value.precisionLimit || '',
      sampleVolume: Number(formData.value.sampleVolume) || 100,
      detectionTimeMinutes: Number(formData.value.detectionTimeMinutes) || 60,
      isEnabled: formData.value.isEnabled
    }

    // 处理可选的数字字段
    if (formData.value.standardMin !== undefined && formData.value.standardMin !== null) {
      data.standardMin = Number(formData.value.standardMin)
    }
    if (formData.value.standardMax !== undefined && formData.value.standardMax !== null) {
      data.standardMax = Number(formData.value.standardMax)
    }

    // 编辑时添加id
    if (formType.value === 'update' && formData.value.id) {
      data.id = formData.value.id
    }

    console.log('提交的检测指标数据:', data)

    if (formType.value === 'create') {
      await AssayBaseInfoApi.createTestIndicator(data)
    } else {
      await AssayBaseInfoApi.updateTestIndicator(data)
    }

    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    factoryId: undefined,
    name: '',
    code: '',
    projectId: undefined,
    method: '',
    unit: '',
    standardMin: undefined,
    standardMax: undefined,
    equipment: '',
    precisionLimit: '',
    sampleVolume: 100,
    detectionTimeMinutes: 60,
    isEnabled: true
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>

// augment.config.js

export default {
  rules: {
    styling: {
      units: {
        preferred: 'rem',
        avoid: ['px']
      },
      layout: {
        preferred: ['flex', 'grid'],
        avoid: ['fixed', 'px']
      }
    },
    components: {
      preferred: ['el-button', 'el-input', 'el-table'],
      overrides: {
        styleUnit: 'rem'
      },
      table: {
        useMinWidth: true,
        widthUnit: 'rem'
      }
    },
    responsive: {
      targetResolutions: ['1920px', '2560px', '3840px', '4096px'],
      avoid: ['transform-scale', 'media-query'],
      preferred: ['rem-units', 'flexible-layout']
    },
    structure: {
      styleScope: 'scoped',
      scriptSetup: true,
      typescript: true
    }
  }
}

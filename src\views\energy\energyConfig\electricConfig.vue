<template>
  <div class="electric-config-container">
    <div class="config-layout">
      <!-- 左侧节点列表 -->
      <div class="left-panel">
        <div class="panel-header">
          <h3 class="panel-title">节点架构</h3>
          <el-button type="primary" size="small" @click="handleAddProcess">
            <el-icon class="mr-1"><Plus /></el-icon>新增根节点
          </el-button>
        </div>
        <div class="panel-content">
          <div class="panel-tip">
            <el-alert
              title="根节点可配置总测量点，用于统计整体消耗情况。"
              type="info"
              :closable="false"
              show-icon
            />
          </div>
          <el-tree
            ref="treeRef"
            class="custom-tree"
            :data="filteredProcessTreeData"
            :props="{
              label: 'name',
              children: 'children'
            }"
            node-key="id"
            :default-expanded-keys="expandedKeys"
            :expand-on-click-node="false"
            highlight-current
            @node-click="handleTreeNodeClick"
          >
            <template #default="{ data }">
              <div class="tree-node">
                <div class="node-content">
                  <span class="node-icon">
                    <el-icon class="text-blue-500"><Collection /></el-icon>
                  </span>
                  <span class="node-label font-medium">{{ data.name }}</span>
                </div>
                <div class="node-actions" @click.stop>
                  <!-- 只有前两级节点可以添加子节点，第三级节点不能再添加 -->
                  <el-tooltip content="添加" placement="top" v-if="getNodeLevel(data) < 3">
                    <el-icon class="action-icon add-icon" @click="handleAddSubGroup(data)">
                      <Plus />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip content="编辑" placement="top">
                    <el-icon class="action-icon edit-icon" @click="handleEditProcess(data)">
                      <Edit />
                    </el-icon>
                  </el-tooltip>
                  <el-tooltip content="删除" placement="top">
                    <el-icon class="action-icon delete-icon" @click="handleDeleteProcess(data)">
                      <Delete />
                    </el-icon>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-tree>
        </div>
      </div>

      <!-- 右侧内容 -->
      <div class="right-panel">
        <div class="panel-header">
          <h3 class="panel-title">{{ contentTitle }}</h3>
          <el-button v-if="currentProcess" type="primary" size="small" @click="handleAddDevice">
            <el-icon class="mr-1"><Plus /></el-icon>添加测量点
          </el-button>
        </div>
        <div class="panel-content">
          <el-table v-if="currentProcess" :data="tableData" border  style="width: 100%" class="no-resize">
            <el-table-column prop="name" label="测量名称" width="180" />
            <el-table-column prop="code" label="测量点编码(物联中台指标编码)" width="320" />
            <el-table-column prop="unit" label="单位" width="100" />
            <el-table-column prop="timeGranularity" label="时间粒度" width="120">
              <template #default="{ row }">
                <el-tag
                  :type="getTimeGranularityTagType(row.timeGranularity || 'day')"
                  size="small"
                >
                  {{ getTimeGranularityText(row.timeGranularity || 'day') }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="150">
              <template #default="{ row }">
                <el-button size="small" type="primary" plain @click="handleEditDevice(row)">
                  编辑
                </el-button>
                <el-button size="small" type="danger" plain @click="handleDeleteDevice(row)">
                  删除
                </el-button>
              </template>
            </el-table-column>
          </el-table>
          <div v-else class="empty-state">
            <el-empty description="请先选择左侧节点" />
          </div>
        </div>
      </div>
    </div>

    <!-- 节点新增/编辑对话框 -->
    <el-dialog v-model="processDialogVisible" :title="getProcessDialogTitle()" width="30rem" destroy-on-close>
      <el-form ref="processFormRef" :model="processForm" :rules="processRules" label-width="6rem" label-position="left">
        <el-form-item label="节点名称" prop="name">
          <el-input v-model="processForm.name" placeholder="请输入节点名称" />
        </el-form-item>
        <el-form-item label="节点类型" prop="groupType">
          <el-input v-model="processForm.groupType" disabled />
        </el-form-item>
        <!-- 显示父级信息（如果有） -->
        <el-form-item label="所属节点" v-if="processForm.parentName">
          <el-input v-model="processForm.parentName" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="processDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitProcessForm">确定</el-button>
        </span>
      </template>
    </el-dialog>



    <!-- 测量点新增/编辑对话框 -->
    <el-dialog v-model="deviceDialogVisible" :title="deviceDialogType === 'add' ? '新增测量点' : '编辑测量点'" width="30rem" destroy-on-close>
      <el-form ref="deviceFormRef" :model="deviceForm" :rules="deviceRules" label-width="6rem" label-position="left">
        <el-form-item label="测量名称" prop="name">
          <el-input v-model="deviceForm.name" disabled />
        </el-form-item>
        <el-form-item label="测量点编码" prop="code">
          <el-select v-model="deviceForm.code" placeholder="请选择测量点编码" style="width: 100%">
            <el-option-group
              v-for="device in codeOptions"
              :key="device.deviceId"
              :label="device.deviceName"
            >
              <el-option
                v-for="point in device.points"
                :key="point.indicatorId"
                :label="`${point.indicatorId} - ${point.pointName}`"
                :value="point.indicatorId"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item label="单位" prop="unit">
          <el-input v-model="deviceForm.unit" placeholder="请输入单位" />
        </el-form-item>
        <el-form-item label="时间粒度" prop="timeGranularity">
          <el-select v-model="deviceForm.timeGranularity" placeholder="请选择时间粒度" style="width: 100%">
            <el-option label="小时" value="hour" />
            <el-option label="天" value="day" />
            <el-option label="月" value="month" />
          </el-select>
        </el-form-item>
        <el-form-item label="所属" prop="processName">
          <el-input v-model="deviceForm.processName" disabled />
        </el-form-item>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="deviceDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="submitDeviceForm">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick, watch } from 'vue'
import type { FormInstance, FormRules, ElTree } from 'element-plus'
import { Collection, Delete, Edit, Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { useAppStore } from '@/store/modules/app'
import {
  getEnergyNodesTree,
  createEnergyNode,
  updateEnergyNode,
  deleteEnergyNode,
  type EnergyNodeVO,
  type EnergyNodeCreateReqVO,
  type EnergyNodeUpdateReqVO
} from '@/api/energy/node'
import {
  getEnergyIndicatorsByNodeId,
  createEnergyIndicator,
  updateEnergyIndicator,
  deleteEnergyIndicator,
  getIndicatorCodeOptions,
  type EnergyIndicatorCreateReqVO,
  type EnergyIndicatorUpdateReqVO,
  type IndicatorCodeOption
} from '@/api/energy/indicator'

// 定义类型接口 - 兼容原有逻辑的节点类型
interface ProcessGroup {
  id: string | number;
  name: string;
  parentId?: string | number | null; // 父级ID，顶级节点没有父级
  groupType?: string; // 节点类型：电耗、药耗等
  children?: ProcessGroup[];
}

// 设备数据接口 - 兼容原有逻辑的指标类型
interface Device {
  id: number;
  name: string;
  code: string;
  unit: string;
  energyType: string;
  processId: string | number; // 关联到设备ID
  nodeId?: number; // API 节点ID
  timeGranularity?: 'hour' | 'day' | 'month'; // 时间粒度：小时、天、月
}

// 树形组件引用
const treeRef = ref<InstanceType<typeof ElTree>>()

// 展开的节点keys
const expandedKeys = ref<(string | number)[]>([])

// 平铺的节点和设备列表
const processList = ref<ProcessGroup[]>([])

// 配置设备数据
const configDeviceData = ref<Device[]>([])

// 加载状态
const loading = ref(false)

// 全局状态管理
const appStore = useAppStore()

// 当前水厂ID
const currentFactoryId = computed(() => appStore.getCurrentStation?.id)

// 指标编码选项（按设备节点）
const codeOptions = ref<IndicatorCodeOption[]>([])

// 加载指标编码选项
const loadIndicatorCodeOptions = async () => {
  if (!currentFactoryId.value) return

  try {
    const response = await getIndicatorCodeOptions(currentFactoryId.value)
    if (response && response.data && Array.isArray(response.data)) {
      // 直接使用节点数据
      codeOptions.value = response.data
    }
  } catch (error) {
    console.error('加载指标编码选项失败:', error)
    // 使用默认选项作为备选
    codeOptions.value = [
      {
        deviceId: 'default',
        deviceName: '默认设备',
        points: [
          { pointId: 'E001', pointCode: 'E001', pointName: '总电表', indicatorId: 'JK4Q_2CC8_E001', pointType: 'electric', unit: 'kWh' },
          { pointId: 'E002', pointCode: 'E002', pointName: '照明用电', indicatorId: 'JK4Q_2CC8_E002', pointType: 'electric', unit: 'kWh' },
          { pointId: 'E003', pointCode: 'E003', pointName: '空调用电', indicatorId: 'JK4Q_2CC8_E003', pointType: 'electric', unit: 'kWh' },
        ]
      }
    ]
  }
}

// 数据加载函数
const loadEnergyNodesTree = async () => {
  try {
    loading.value = true

    // 获取当前厂站ID
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      ElMessage.error('请先选择厂站')
      loading.value = false
      return
    }

    const treeData = await getEnergyNodesTree(currentStation.id)

    // 转换 API 数据为组件需要的格式
    const convertToProcessList = (): ProcessGroup[] => {
      const result: ProcessGroup[] = []

      const traverse = (nodeList: EnergyNodeVO[]) => {
        nodeList.forEach(node => {
          const processGroup: ProcessGroup = {
            id: node.id!,
            name: node.name,
            parentId: node.parentId, // 使用 API 返回的 parentId
            groupType: node.nodeType === 'electric' ? '电耗' : node.nodeType === 'drug' ? '药耗' : '其他'
          }
          result.push(processGroup)

          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }

      traverse(treeData)
      return result
    }

    processList.value = convertToProcessList()
  } catch (error) {
    console.error('加载能耗节点树失败:', error)
    ElMessage.error('加载数据失败')
  } finally {
    loading.value = false
  }
}

// 加载指定节点的能耗指标
const loadEnergyIndicators = async (nodeId: number) => {
  try {
    const indicators = await getEnergyIndicatorsByNodeId(nodeId)

    // 转换为组件需要的格式
    const convertedIndicators: Device[] = indicators.map(indicator => ({
      id: indicator.id!,
      name: indicator.indicatorName,
      code: indicator.indicatorCode,
      unit: indicator.unit,
      energyType: '电力', // 默认为电力类型
      processId: indicator.nodeId,
      nodeId: indicator.nodeId,
      timeGranularity: indicator.timeGranularity || 'day' // 从指标数据获取时间粒度
    }))

    // 更新当前节点的指标数据
    configDeviceData.value = configDeviceData.value.filter(item => item.processId !== nodeId)
    configDeviceData.value.push(...convertedIndicators)
  } catch (error) {
    console.error('加载测量名称失败:', error)
    ElMessage.error('加载测量点数据失败')
  }
}

// 对话框显示控制
const processDialogVisible = ref(false)
const deviceDialogVisible = ref(false)
const deviceDialogType = ref<'add' | 'edit'>('add')
const dialogType = ref<'add' | 'edit'>('add')

// 当前选中的节点或设备
const currentProcess = ref<ProcessGroup | null>(null)

// 表格数据计算属性
const tableData = computed(() => {
  // 如果选中了具体设备或节点，只显示该设备或节点的指标
  if (currentProcess.value) {
    return configDeviceData.value.filter(device => 
      device.processId === currentProcess.value!.id && 
      device.energyType === '电力'
    );
  }
  
  return [];
});

// 节点表单
const processFormRef = ref<FormInstance>()
const processForm = reactive({
  id: '' as string | number,
  name: '',
  parentId: '' as string | number | null,
  parentName: '', // 所属节点名称
  groupType: '电耗' // 默认节点类型
})

// 节点表单验证规则
const processRules = reactive<FormRules>({
  name: [{ required: true, message: '请输入节点名称', trigger: 'blur' }],
  groupType: [{ required: true, message: '请选择节点类型', trigger: 'change' }]
})

// 设备表单
const deviceFormRef = ref<FormInstance>()
const deviceForm = reactive({
  id: 0,
  name: '',
  code: '',
  processId: '' as string | number,
  processName: '', // 添加节点名称字段
  unit: '',
  energyType: '电力',
  timeGranularity: 'day' as 'hour' | 'day' | 'month' // 时间粒度
})

// 设备表单验证规则
const deviceRules = reactive<FormRules>({
  name: [{ required: true, message: '请输入测量名称', trigger: 'blur' }],
  code: [{ required: true, message: '请选择指标编码', trigger: 'change' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  timeGranularity: [{ required: true, message: '请选择时间粒度', trigger: 'change' }]
})



// 根据当前标签页过滤的树形数据
const filteredProcessTreeData = computed(() => {
  // 查找电耗类型的节点
  const topGroups = processList.value.filter(item => !item.parentId && item.groupType === '电耗');
  
  // 递归构建树
  const buildTree = (groups: ProcessGroup[]) => {
    return groups.map(group => {
      // 查找当前节点的子节点/设备
      const children = processList.value.filter(item => item.parentId === group.id);
      
      return {
        ...group,
        children: children.length > 0 ? buildTree(children) : []
      };
    });
  };
  
  return buildTree(topGroups);
});

// 树节点点击处理
const handleTreeNodeClick = async (data: ProcessGroup) => {
  // 设置当前选中的节点/设备
  currentProcess.value = data;

  // 如果选中的是节点（节点或设备），加载其能耗指标
  if (data.id && typeof data.id === 'number') {
    await loadEnergyIndicators(data.id)
  }
};

// 监听水厂切换，自动刷新数据（包含初始加载）
watch(() => currentFactoryId.value, async (newFactoryId) => {
  if (newFactoryId) {
    // 清空当前选中状态
    currentProcess.value = null
    // 重新加载数据
    await Promise.all([
      loadEnergyNodesTree(),
      loadIndicatorCodeOptions()
    ])
  }
}, { immediate: true })

// 新增顶级节点
const handleAddProcess = () => {
  dialogType.value = 'add'
  nextTick(() => {
    processForm.id = ''
    processForm.name = ''
    processForm.parentId = null // 节点的 parentId 为 null
    processForm.parentName = ''
    processForm.groupType = '电耗' // 设置默认节点类型
    processDialogVisible.value = true
  })
}

// 新增中间级分组节点
const handleAddSubGroup = (parentProcess: ProcessGroup) => {
  dialogType.value = 'add'
  nextTick(() => {
    processForm.id = ''
    processForm.name = ''
    processForm.parentId = parentProcess.id // 设置父节点ID
    processForm.parentName = parentProcess.name // 设置父节点名称
    processForm.groupType = '电耗' // 设置默认节点类型
    processDialogVisible.value = true
  })
}

// 修改节点
const handleEditProcess = (process: ProcessGroup) => {
  // 编辑节点（所有级别的节点都可以编辑）
  dialogType.value = 'edit'
  nextTick(() => {
    processForm.id = process.id
    processForm.name = process.name
    processForm.parentId = process.parentId || null // 保持原有的父级关系
    // 查找父级节点名称
    const parentGroup = process.parentId ? processList.value.find(p => p.id === process.parentId) : null
    processForm.parentName = parentGroup ? parentGroup.name : ''
    processForm.groupType = process.groupType || '电耗' // 获取节点类型，默认为电耗
    processDialogVisible.value = true
  })
}

// 删除节点
const handleDeleteProcess = (process: ProcessGroup) => {
  ElMessageBox.confirm(
    `确定要删除节点"${process.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用删除 API
        if (typeof process.id === 'number') {
          await deleteEnergyNode(process.id)
          ElMessage({
            type: 'success',
            message: '删除成功',
          })

          // 如果删除的是当前选中的节点，清空选中状态
          if (currentProcess.value && currentProcess.value.id === process.id) {
            currentProcess.value = null
          }

          // 重新加载数据
          await loadEnergyNodesTree()
        } else {
          ElMessage.error('无效的节点ID')
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 提交节点表单
const submitProcessForm = async () => {
  if (!processFormRef.value) return
  await processFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (dialogType.value === 'add') {
          // 新增分组节点（根节点或中间级节点）
          const createData: EnergyNodeCreateReqVO = {
            name: processForm.name,
            nodeType: processForm.groupType === '电耗' ? 'electric' : 'drug', // 转换为后端的节点类型
            parentId: processForm.parentId ? Number(processForm.parentId) : null, // 根据是否有父级设置parentId
            factoryId: currentFactoryId.value // 从全局状态获取水厂ID
          }

          await createEnergyNode(createData)
          ElMessage({
            type: 'success',
            message: '添加成功',
          })

          // 展开父节点（如果有父节点的话）
          if (processForm.parentId) {
            // 添加父节点到展开列表
            if (!expandedKeys.value.includes(processForm.parentId)) {
              expandedKeys.value.push(processForm.parentId)
            }
          }

          // 重新加载数据
          await loadEnergyNodesTree()
        } else {
          // 修改分组节点（根节点或中间级节点）
          const updateData: EnergyNodeUpdateReqVO = {
            id: Number(processForm.id),
            name: processForm.name,
            nodeType: processForm.groupType === '电耗' ? 'electric' : 'drug', // 转换为后端的节点类型
            parentId: processForm.parentId ? Number(processForm.parentId) : null, // 保持原有的父级关系
            factoryId: currentFactoryId.value // 从全局状态获取水厂ID
          }

          await updateEnergyNode(updateData)
          ElMessage({
            type: 'success',
            message: '修改成功',
          })

          // 重新加载数据
          await loadEnergyNodesTree()
        }
        processDialogVisible.value = false
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}




// 新增设备
const handleAddDevice = () => {
  if (!currentProcess.value) return
  deviceDialogType.value = 'add'
  nextTick(() => {
    deviceForm.id = 0 // 新增设备时ID设为0
    deviceForm.code = ''
    
    if (currentProcess.value) {
      deviceForm.processId = currentProcess.value.id
      deviceForm.processName = currentProcess.value.name
      
      // 电耗固定为累计电量
      deviceForm.name = '累计电量'
      deviceForm.unit = 'KWh'
      deviceForm.energyType = '电力'
      deviceForm.timeGranularity = 'day' // 默认为天
    } else {
      deviceForm.processId = ''
      deviceForm.processName = ''
      deviceForm.name = '累计电量'
      deviceForm.unit = 'KWh'
      deviceForm.energyType = '电力'
      deviceForm.timeGranularity = 'day' // 默认为天
    }
    
    deviceDialogVisible.value = true
  })
}



// 修改设备
const handleEditDevice = (device: Device) => {
  deviceDialogType.value = 'edit'
  
  // 查找设备所属的节点/设备名称
  const processGroup = processList.value.find(p => p.id === device.processId);
  const processName = processGroup ? processGroup.name : '';
  
  // 临时设置当前节点，以便获取正确的单位和编码选项
  if (processGroup) {
    currentProcess.value = processGroup;
  }
  
  nextTick(() => {
    deviceForm.id = device.id
    deviceForm.name = device.name
    deviceForm.code = device.code
    deviceForm.processId = device.processId
    deviceForm.processName = processName
    deviceForm.unit = device.unit
    deviceForm.energyType = device.energyType
    deviceForm.timeGranularity = device.timeGranularity || 'day' // 设置时间粒度，默认为天
    deviceDialogVisible.value = true
  })
}

// 删除设备
const handleDeleteDevice = (device: Device) => {
  ElMessageBox.confirm(
    `确定要删除测量点"${device.name}"吗？`,
    '删除确认',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning',
    }
  )
    .then(async () => {
      try {
        // 调用删除 API
        await deleteEnergyIndicator(device.id)
        ElMessage({
          type: 'success',
          message: '删除成功',
        })

        // 重新加载当前节点的指标数据
        if (currentProcess.value && typeof currentProcess.value.id === 'number') {
          await loadEnergyIndicators(currentProcess.value.id)
        }
      } catch (error) {
        console.error('删除失败:', error)
        ElMessage.error('删除失败')
      }
    })
    .catch(() => {
      // 用户取消删除
    })
}

// 右侧内容标题
const contentTitle = computed(() => {
  if (currentProcess.value) {
    return `${currentProcess.value.name}的测量点列表`;
  } else {
    return '请选择节点';
  }
});

// 获取节点对话框标题
const getProcessDialogTitle = () => {
  const typeName = '电耗';

  if (dialogType.value === 'add') {
    // 新增模式
    if (!processForm.parentId) {
      return `新增${typeName}根节点`;
    } else {
      return `新增${typeName}节点`;
    }
  } else {
    // 编辑模式
    if (!processForm.parentId) {
      return `编辑${typeName}根节点`;
    } else {
      return `编辑${typeName}节点`;
    }
  }
};

// 提交设备表单
const submitDeviceForm = async () => {
  if (!deviceFormRef.value) return
  await deviceFormRef.value.validate(async (valid) => {
    if (valid) {
      try {
        if (deviceDialogType.value === 'add') {
          // 新增能耗指标
          const createData: EnergyIndicatorCreateReqVO = {
            indicatorName: deviceForm.name,
            indicatorCode: deviceForm.code,
            unit: deviceForm.unit,
            nodeId: Number(deviceForm.processId),
            factoryId: currentFactoryId.value,
            timeGranularity: deviceForm.timeGranularity // 包含时间粒度
          }

          await createEnergyIndicator(createData)
          ElMessage({
            type: 'success',
            message: '添加成功',
          })
        } else {
          // 修改能耗指标
          const updateData: EnergyIndicatorUpdateReqVO = {
            id: Number(deviceForm.id),
            indicatorName: deviceForm.name,
            indicatorCode: deviceForm.code,
            unit: deviceForm.unit,
            nodeId: Number(deviceForm.processId),
            factoryId: currentFactoryId.value,
            timeGranularity: deviceForm.timeGranularity // 包含时间粒度
          }

          await updateEnergyIndicator(updateData)
          ElMessage({
            type: 'success',
            message: '修改成功',
          })
        }

        // 重新加载当前节点的指标数据
        if (currentProcess.value && typeof currentProcess.value.id === 'number') {
          await loadEnergyIndicators(currentProcess.value.id)
        }

        deviceDialogVisible.value = false
      } catch (error) {
        console.error('操作失败:', error)
        ElMessage.error('操作失败')
      }
    }
  })
}

// 计算节点层级深度
const getNodeLevel = (node: ProcessGroup): number => {
  let level = 1 // 根节点为第1级
  let currentNode = node

  // 通过 parentId 向上查找，计算层级
  while (currentNode.parentId) {
    level++
    // 在 processList 中查找父节点
    const parentNode = processList.value.find(p => p.id === currentNode.parentId)
    if (parentNode) {
      currentNode = parentNode
    } else {
      break // 如果找不到父节点，跳出循环
    }
  }

  return level
}

// 获取时间粒度显示文本
const getTimeGranularityText = (timeGranularity: string) => {
  switch (timeGranularity) {
    case 'hour':
      return '小时'
    case 'day':
      return '天'
    case 'month':
      return '月'
    default:
      return '未知'
  }
}

// 获取时间粒度标签类型
const getTimeGranularityTagType = (timeGranularity: string) => {
  switch (timeGranularity) {
    case 'hour':
      return 'primary'
    case 'day':
      return 'success'
    case 'month':
      return 'warning'
    default:
      return 'info'
  }
}
</script>

<style scoped lang="scss">
.electric-config-container {
  width: 100%;
  height: 100%;
}

.config-layout {
  display: flex;
  height: 100%;
  gap: 1rem;
}

.left-panel, .right-panel {
  background-color: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.left-panel {
  width: 26rem;
  min-width: 26rem;
}

.right-panel {
  flex: 1;
}

.panel-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #f0f0f0;
  background-color: #fafbfc;
}

.panel-title {
  font-size: 1rem;
  font-weight: 600;
  color: #303133;
  margin: 0;
}

.panel-content {
  flex: 1;
  overflow: hidden;
  padding: 0;
  display: flex;
  flex-direction: column;
}

// 树形组件样式
.custom-tree {
  flex: 1;
  overflow: auto;
  padding: 0 1rem 1rem;

  :deep(.el-tree-node__content) {
    height: auto;
    padding: 0;
    border-radius: 6px;
    transition: all 0.2s;
    margin-bottom: 2px;

    &:hover {
      background-color: #f0f7ff;
    }
  }

  // 放大展开按钮
  :deep(.el-tree-node__expand-icon) {
    font-size: 16px !important;
    width: 24px !important;
    height: 24px !important;
    line-height: 24px !important;
    border-radius: 4px;
    transition: all 0.2s;

    &:hover {
      background-color: #e6f7ff;
      color: #1890ff;
    }

    &.expanded {
      transform: rotate(90deg);
    }
  }

  // 禁用表格列宽拖拽
  .no-resize {
    :deep(.el-table__header-wrapper) {
      .el-table__header {
        th {
          .cell {
            position: relative;

            &::after {
              display: none !important;
            }
          }

          &:hover .cell::after {
            display: none !important;
          }
        }
      }
    }

    :deep(.el-table__border-left-patch) {
      display: none;
    }

    :deep(.el-table__border-right-patch) {
      display: none;
    }

    :deep(.el-table th.gutter) {
      display: none;
    }

    // 禁用列边界的拖拽光标
    :deep(.el-table th) {
      cursor: default !important;

      &::before {
        display: none !important;
      }
    }

    // 禁用列分割线的拖拽功能
    :deep(.el-table__header-wrapper .el-table__header th) {
      border-right: 1px solid #ebeef5;

      &:hover {
        cursor: default !important;
      }
    }
  }

  :deep(.el-tree-node.is-current > .el-tree-node__content) {
    background-color: #e6f2ff;
    color: #409EFF;
  }
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 0.5rem 0.75rem;
}

.node-content {
  display: flex;
  align-items: center;
  flex: 1;
}

.node-icon {
  margin-right: 0.5rem;
  display: flex;
  align-items: center;
}

.node-label {
  color: #606266;
  font-size: 0.875rem;
}

.node-code {
  color: #909399;
  font-size: 0.75rem;
  margin-left: 0.5rem;
}

.node-actions {
  display: flex;
  gap: 0.5rem;
  opacity: 0.8;
  transition: opacity 0.2s;

  &:hover {
    opacity: 1;
  }
}

.action-icon {
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 6px;
  transition: all 0.2s;
  font-size: 1rem;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;

  &.add-icon {
    color: #67C23A;
    &:hover {
      background-color: #f0f9ff;
      color: #529b2e;
      transform: scale(1.1);
    }
  }

  &.edit-icon {
    color: #409EFF;
    &:hover {
      background-color: #ecf5ff;
      color: #337ecc;
      transform: scale(1.1);
    }
  }

  &.delete-icon {
    color: #F56C6C;
    &:hover {
      background-color: #fef0f0;
      color: #c45656;
      transform: scale(1.1);
    }
  }
}

// 表格样式
.custom-table {
  height: 100%;

  :deep(.el-table__header-wrapper) {
    .el-table__header th {
      background-color: #f8f9fa;
      color: #606266;
      font-weight: 600;
      padding: 0.75rem 0;
      border-bottom: 2px solid #e4e7ed;
    }
  }

  :deep(.el-table__row) {
    transition: background-color 0.2s;

    &:hover {
      background-color: #f5f7ff;
    }

    td {
      padding: 0.75rem 0.75rem;
      border-bottom: 1px solid #f0f0f0;
    }
  }
}

.table-actions {
  display: flex;
  justify-content: center;
  gap: 0.5rem;

  .el-button {
    min-width: 60px;
    padding: 0.25rem 0.75rem;
  }
}

.empty-state {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #909399;
}

.panel-tip {
  padding: 1rem 1rem 0.75rem;

  :deep(.el-alert) {
    border-radius: 6px;
    border: 1px solid #d9ecff;
    background-color: #ecf5ff;

    .el-alert__title {
      font-size: 0.875rem;
      color: #409eff;
      line-height: 1.4;
    }

    .el-alert__icon {
      color: #409eff;
      font-size: 16px;
    }
  }
}



.table-cell-content {
  white-space: normal;
  word-break: break-word;
  line-height: 1.5;
  padding: 0.25rem;
}

.cell-content {
  text-align: center;
  width: 100%;
  display: inline-block;
}

/* 表格单元格内容居中 */
:deep(.el-table) {
  /* 表格标题居中 */
  th.el-table__cell .cell {
    text-align: center !important;
  }

  /* 表格内容居中 */
  td.el-table__cell .cell {
    text-align: center !important;
  }

  /* 所有单元格垂直居中 */
  .el-table__cell {
    vertical-align: middle;
  }
}

/* 操作按钮样式优化 */
:deep(.el-table .el-button) {
  margin: 0 0.25rem;
}

/* 确保表单标签文字对齐 */
:deep(.el-form) {
  .el-form-item__label {
    display: inline-flex;
    justify-content: flex-end;

    &::before {
      margin-right: 4px;
      display: inline-block;
    }
  }
}
</style>
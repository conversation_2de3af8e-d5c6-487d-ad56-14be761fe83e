<template>
  <el-dialog v-model="visible" title="方案详情" width="50rem" :close-on-click-modal="false" @close="handleClose">
    <div v-if="schemeData" class="scheme-detail">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="方案名称">{{ schemeData.name }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="getStatusType(schemeData.status)">{{ schemeData.status }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="评估周期">{{ schemeData.cycle }}</el-descriptions-item>
        <el-descriptions-item label="启用时间">{{ schemeData.startDate }}</el-descriptions-item>
        <el-descriptions-item label="失效时间">{{ schemeData.endDate }}</el-descriptions-item>
        <el-descriptions-item label="方案说明" :span="2">{{ schemeData.description || '无' }}</el-descriptions-item>
      </el-descriptions>



      <!-- 项目配置详情 -->
      <div class="mt-4">
        <el-divider content-position="left">项目配置</el-divider>
        <div v-if="schemeData.projects && schemeData.projects.length > 0">
          <el-card v-for="project in schemeData.projects" :key="project.id" class="mb-3" shadow="hover">
            <template #header>
              <div class="flex justify-between items-center">
                <span class="font-medium">{{ project.name }}</span>
                <div class="flex gap-2">
                  <el-tag size="small">权重: {{ project.weight }}%</el-tag>
                  <el-tag size="small" type="info">{{ project.scoreType }}</el-tag>
                </div>
              </div>
            </template>

            <div v-if="project.indicators && project.indicators.length > 0">
              <div class="text-sm font-medium mb-2">绑定指标 ({{ project.indicators.length }})</div>
              <div class="space-y-2">
                <div v-for="indicator in project.indicators" :key="indicator.id"
                  class="flex items-center justify-between p-2 bg-gray-50 rounded">
                  <div class="flex-1">
                    <div class="flex items-center">
                      <span class="font-medium">{{ indicator.name }}</span>
                      <el-tag size="small" type="info" class="ml-2">{{ indicator.unit }}</el-tag>
                    </div>
                    <div class="text-xs text-gray-500 mt-1" v-if="indicator.description">
                      {{ indicator.description }}
                    </div>
                  </div>
                  <div class="flex items-center gap-2 text-sm">
                    <el-tag size="small" type="warning">指标分值: {{ getIndicatorScore(project, indicator) }}分</el-tag>
                    <div v-if="project.scoreType === '加权平均'" class="text-gray-600">
                      权重: {{ indicator.weight }}%
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div v-else class="text-center text-gray-400 py-4">
              暂无绑定指标
            </div>
          </el-card>
        </div>
        <div v-else class="text-center text-gray-400 py-4">
          暂无项目配置
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>

        <!-- 编辑按钮 -->
        <el-button v-if="schemeData" type="primary" @click="handleEdit">
          编辑方案
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'

// 定义接口类型
interface Indicator {
  id: string
  name: string
  unit: string
  formula: string
  applicableObject: string
  description?: string
  weight?: number
}

interface Project {
  id: string
  name: string
  weight: number
  scoreType: '加权平均' | '区间打分' | '减分制'
  fullScore: number
  indicators: Indicator[]
}

interface SchemeItem {
  id: string
  name: string
  status: '启用中' | '已失效'
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  startDate: string
  endDate: string
  description: string
  projects: Project[]
  createTime: string
  creator: string
}

// 定义属性
const props = defineProps<{
  modelValue: boolean
  schemeData: SchemeItem | null
}>()

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'edit': [scheme: SchemeItem]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 工具方法
const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
    '启用中': 'success',
    '已失效': 'danger'
  }
  return typeMap[status] || 'info'
}

// 计算指标分值
const getIndicatorScore = (project: Project, indicator: Indicator): number => {
  // 同一个项目中的指标分值一致，等于项目的满分值
  return project.fullScore || 100
}

// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
}

const handleEdit = () => {
  if (props.schemeData) {
    emit('edit', props.schemeData)
  }
}


</script>

<style scoped lang="scss">
// 方案详情样式
.scheme-detail {
  :deep(.el-descriptions) {
    .el-descriptions__label {
      font-weight: 500;
    }
  }

  :deep(.el-steps) {
    margin: 1rem 0;
  }
}

// 对话框底部样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

// 项目卡片样式
.el-card {
  &:hover {
    box-shadow: 0 0.25rem 0.5rem rgba(0, 0, 0, 0.15);
  }
}

// 标签样式优化
.el-tag {
  &.el-tag--small {
    font-size: 0.75rem;
    padding: 0.125rem 0.375rem;
  }
}

// 分割线样式
.el-divider {
  margin: 1rem 0;

  :deep(.el-divider__text) {
    font-weight: 500;
    color: #606266;
  }
}

// 指标项样式
.space-y-2>*+* {
  margin-top: 0.5rem;
}
</style>

<template>
  <Dialog v-model="dialogVisible" title="异常数据处理">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="样品编号">
        <el-input v-model="formData.sampleCode" disabled />
      </el-form-item>
      <el-form-item label="检测项目">
        <el-input v-model="formData.testItem" disabled />
      </el-form-item>
      <el-form-item label="采样点">
        <el-input v-model="formData.samplingPoint" disabled />
      </el-form-item>
      <el-form-item label="采样日期">
        <el-input v-model="formData.samplingDate" disabled />
      </el-form-item>
      
      <el-divider content-position="center">异常处理</el-divider>
      
      <el-form-item label="异常类型" prop="exceptionType">
        <el-select v-model="formData.exceptionType" placeholder="请选择异常类型">
          <el-option label="仪器故障" value="instrument" />
          <el-option label="样品异常" value="sample" />
          <el-option label="操作失误" value="operation" />
          <el-option label="超标异常" value="exceed" />
          <el-option label="其他异常" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理方式" prop="handleMethod">
        <el-select v-model="formData.handleMethod" placeholder="请选择处理方式">
          <el-option label="重新检测" value="retest" />
          <el-option label="重新采样" value="resample" />
          <el-option label="修正数据" value="correct" />
          <el-option label="标记为无效" value="invalid" />
          <el-option label="其他处理" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item v-if="formData.handleMethod === 'correct'" label="修正值" prop="correctedValue">
        <el-input v-model="formData.correctedValue" placeholder="请输入修正后的检测值">
          <template #append>{{ formData.unit }}</template>
        </el-input>
      </el-form-item>
      <el-form-item label="处理人" prop="handler">
        <el-select v-model="formData.handler" placeholder="请选择处理人">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="处理日期" prop="handleDate">
        <el-date-picker v-model="formData.handleDate" type="date" placeholder="请选择处理日期" />
      </el-form-item>
      <el-form-item label="异常原因" prop="reason">
        <el-input v-model="formData.reason" type="textarea" placeholder="请输入异常原因" />
      </el-form-item>
      <el-form-item label="处理说明" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入处理说明" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'

defineOptions({ name: 'ExceptionDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

// 用户选项（模拟数据）
const userOptions = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' }
]

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  samplingDate: '',
  unit: 'mg/L',
  exceptionType: '',
  handleMethod: '',
  correctedValue: '',
  handler: '',
  handleDate: '',
  reason: '',
  description: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  exceptionType: [{ required: true, message: '请选择异常类型', trigger: 'change' }],
  handleMethod: [{ required: true, message: '请选择处理方式', trigger: 'change' }],
  correctedValue: [
    { required: true, message: '请输入修正值', trigger: 'blur' },
    { pattern: /^[0-9]+(\.[0-9]+)?$/, message: '修正值必须为数字', trigger: 'blur' }
  ],
  handler: [{ required: true, message: '请选择处理人', trigger: 'change' }],
  handleDate: [{ required: true, message: '请选择处理日期', trigger: 'change' }],
  reason: [{ required: true, message: '请输入异常原因', trigger: 'blur' }],
  description: [{ required: true, message: '请输入处理说明', trigger: 'blur' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value.id = data.id
  formData.value.sampleCode = data.sampleCode
  formData.value.testItem = data.testItem
  formData.value.samplingPoint = data.samplingPoint
  formData.value.samplingDate = data.samplingDate
  
  // 根据检测项目设置单位
  switch (data.testItem) {
    case 'COD':
    case 'BOD5':
    case '氨氮':
    case '总磷':
    case '总氮':
      formData.value.unit = 'mg/L'
      break
    case 'pH':
      formData.value.unit = ''
      break
    case '浊度':
      formData.value.unit = 'NTU'
      break
    default:
      formData.value.unit = 'mg/L'
  }
  
  // 重置其他字段
  formData.value.exceptionType = ''
  formData.value.handleMethod = ''
  formData.value.correctedValue = ''
  formData.value.handler = ''
  formData.value.handleDate = ''
  formData.value.reason = ''
  formData.value.description = ''
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  
  // 条件校验
  if (formData.value.handleMethod === 'correct' && !formData.value.correctedValue) {
    ElMessage.warning('请输入修正值')
    return
  }
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success('异常处理成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script> 
<template>
  <ContentWrap title="检验质量管理">
    <!-- 添加Tab页面 -->
    <el-tabs v-model="activeTab" type="border-card" class="demo-tabs">
      <!-- 数据审核管理 -->
      <el-tab-pane label="数据审核管理" name="dataAudit">
        <el-card shadow="hover">
          <!-- 状态筛选和统计信息 -->
          <div class="header-section">
            <div class="status-filter">
              <el-radio-group v-model="auditStatusFilter" @change="handleAuditStatusChange" size="large">
                <el-radio-button label="pending">
                  <el-icon><Clock /></el-icon>
                  待审核数据
                  <el-badge :value="statusCounts.pending" class="ml-2" />
                </el-radio-button>
                <el-radio-button label="history">
                  <el-icon><FolderOpened /></el-icon>
                  审核历史
                  <el-badge :value="statusCounts.history" class="ml-2" />
                </el-radio-button>
              </el-radio-group>
            </div>

            <!-- 快速统计卡片 -->
            <div class="stats-cards" v-if="auditStatusFilter === 'pending'">
              <div class="stat-card danger">
                <div class="stat-number">{{ statusCounts.exceeded }}</div>
                <div class="stat-label">超标数据</div>
              </div>
              <div class="stat-card warning">
                <div class="stat-number">{{ statusCounts.warning }}</div>
                <div class="stat-label">预警数据</div>
              </div>
              <div class="stat-card success">
                <div class="stat-number">{{ statusCounts.normal }}</div>
                <div class="stat-label">正常数据</div>
              </div>
            </div>
          </div>

          <!-- 搜索表单 -->
          <div class="search-section">
            <el-form :inline="true" :model="searchForm.dataAudit" class="search-form">

              <el-form-item label="数据编号">
                <el-input v-model="searchForm.dataAudit.dataId" placeholder="请输入数据ID" clearable style="width: 150px" />
              </el-form-item>
              <el-form-item label="检测指标">
                <el-select v-model="searchForm.dataAudit.indicatorId" placeholder="请选择检测指标" clearable style="width: 150px">
                  <el-option label="COD" :value="111" />
                  <el-option label="BOD5" :value="112" />
                  <el-option label="氨氮" :value="113" />
                  <el-option label="总磷" :value="114" />
                  <el-option label="总氮" :value="115" />
                </el-select>
              </el-form-item>
              <el-form-item v-if="auditStatusFilter === 'history'" label="审核结果">
                <el-select v-model="searchForm.dataAudit.auditResult" placeholder="请选择审核结果" clearable style="width: 120px">
                  <el-option label="已通过" value="passed" />
                  <el-option label="已退回" value="rejected" />
                </el-select>
              </el-form-item>
              <el-form-item label="检测日期">
                <el-date-picker 
                  v-model="searchForm.dataAudit.testDateRange" 
                  type="daterange" 
                  range-separator="至"
                  start-placeholder="开始日期" 
                  end-placeholder="结束日期" 
                  style="width: 240px" 
                />
              </el-form-item>
              <el-form-item>
                <div class="search-buttons">
                  <el-button type="primary" @click="handleSearch('dataAudit')">
                    <el-icon><Search /></el-icon>搜索
                  </el-button>
                  <el-button @click="resetSearch('dataAudit')">
                    <el-icon><Refresh /></el-icon>重置
                  </el-button>
                </div>
              </el-form-item>
            </el-form>
          </div>

          <!-- 数据表格 -->
          <div class="table-section">
            <!-- 待审核数据表格 -->
            <el-table 
              v-if="auditStatusFilter === 'pending'" 
              v-loading="loading.dataAudit" 
              :data="tableData.dataAudit"
              border 
              stripe 
              style="width: 100%" 
              @row-click="handleRowClick"
            >
              <el-table-column prop="dataId" label="数据ID" width="80" />
              <el-table-column prop="indicatorName" label="检测指标" width="100" />
              <el-table-column prop="testValue" label="检测值" width="120">
                <template #default="{ row }">
                  <div class="test-value-cell">
                    <span :class="getValueClass(row)">
                      {{ row.testValue }} {{ row.unit }}
                    </span>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="testDate" label="检测日期" width="110" />
              <el-table-column prop="testerName" label="检测人员" width="100" />
              <el-table-column prop="method" label="检测方法" min-width="120" show-overflow-tooltip />
              <el-table-column prop="instrument" label="检测仪器" min-width="120" show-overflow-tooltip />
              <el-table-column prop="status" label="状态" width="100">
                <template #default="{ row }">
                  <el-tag :type="getStatusTagType(row.status)" size="small">
                    {{ getStatusLabel(row.status) }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" fixed="right">
                <template #default="{ row }">
                  <div class="action-buttons">
                    <el-button size="small" type="primary" @click.stop="handleAudit(row)">
                      <el-icon><Check /></el-icon>审核
                    </el-button>
                    <el-button size="small" type="info" @click.stop="handleViewDetail(row)">
                      <el-icon><View /></el-icon>详情
                    </el-button>
                  </div>
                </template>
              </el-table-column>
            </el-table>

            <!-- 审核历史表格 -->
            <el-table 
              v-else 
              v-loading="loading.dataAudit" 
              :data="tableData.dataAudit"
              border 
              stripe 
              style="width: 100%" 
              @row-click="handleRowClick"
            >
              <el-table-column prop="id" label="ID" width="80" />
              <el-table-column prop="sampleCode" label="样品编号" min-width="140" show-overflow-tooltip />
              <el-table-column prop="testIndicator" label="检测指标" width="100" />
              <el-table-column prop="testValue" label="检测值" width="120">
                <template #default="{ row }">
                  <div class="test-value-cell">
                    <span :class="getValueClass(row)">
                      {{ row.testValue }} {{ row.unit }}
                    </span>
                    <el-icon v-if="row.isExceeded" class="status-icon danger"><Warning /></el-icon>
                    <el-icon v-else-if="row.isWarning" class="status-icon warning"><InfoFilled /></el-icon>
                  </div>
                </template>
              </el-table-column>
              <el-table-column prop="auditResult" label="审核结果" width="100">
                <template #default="{ row }">
                  <el-tag :type="row.auditResult === 'passed' ? 'success' : 'danger'" size="small">
                    {{ row.auditResult === 'passed' ? '已通过' : '已退回' }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column prop="auditComment" label="审核意见" min-width="180" show-overflow-tooltip />
              <el-table-column prop="auditor" label="审核人" width="100" />
              <el-table-column prop="auditDate" label="审核日期" width="110" />
              <el-table-column prop="storageStatus" label="入库状态" width="100">
                <template #default="{ row }">
                  <el-tag 
                    v-if="row.auditResult === 'passed'"
                    :type="getStorageStatusTagType(row.storageStatus || 'pending')" 
                    size="small"
                  >
                    {{ getStorageStatusLabel(row.storageStatus || 'pending') }}
                  </el-tag>
                  <span v-else class="text-muted">-</span>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="180" fixed="right">
                <template #default="{ row }">
                  <div class="action-buttons">
                    <el-button size="small" type="primary" @click.stop="handleViewAuditDetail(row)">
                      <el-icon><View /></el-icon>详情
                    </el-button>
                    <el-dropdown @command="(command) => handleHistoryAction(command, row)" @click.stop>
                      <el-button size="small" type="primary">
                        更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
                      </el-button>
                      <template #dropdown>
                        <el-dropdown-menu>
                          <el-dropdown-item command="track" v-if="row.auditResult === 'rejected'">
                            <el-icon><Position /></el-icon>追踪修正
                          </el-dropdown-item>
                          <el-dropdown-item 
                            command="storage"
                            v-if="row.auditResult === 'passed' && (!row.storageStatus || row.storageStatus === 'pending' || row.storageStatus === 'failed')"
                          >
                            <el-icon><Upload /></el-icon>{{ !row.storageStatus || row.storageStatus === 'pending' ? '入库' : '重试入库' }}
                          </el-dropdown-item>
                          <el-dropdown-item command="recheck" v-if="row.auditResult === 'rejected'" divided>
                            <el-icon><RefreshRight /></el-icon>安排复检
                          </el-dropdown-item>
                          <el-dropdown-item command="viewReport" v-if="row.auditResult === 'passed'">
                            <el-icon><Document /></el-icon>查看报告
                          </el-dropdown-item>
                          <el-dropdown-item command="downloadReport" v-if="row.auditResult === 'passed'">
                            <el-icon><Download /></el-icon>下载报告
                          </el-dropdown-item>
                        </el-dropdown-menu>
                      </template>
                    </el-dropdown>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 分页组件 -->
          <div class="pagination-section">
            <el-pagination
              v-model:current-page="pagination.dataAudit.current"
              v-model:page-size="pagination.dataAudit.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.dataAudit.total"
              @size-change="handleSizeChange('audit')"
              @current-change="handleCurrentChange('audit')" 
            />
          </div>
        </el-card>
      </el-tab-pane>
    </el-tabs>
  </ContentWrap>

  <!-- 数据审核对话框 -->
  <AuditDialog ref="auditDialogRef" @success="refreshTable('audit')" />

  <!-- 复检管理对话框 -->
  <RecheckDialog ref="recheckDialogRef" @success="refreshTable('exception')" />

  <!-- 数据详情对话框 -->
  <DataDetailDialog ref="dataDetailDialogRef" />

  <!-- 数据入库对话框 -->
  <StorageDialog ref="storageDialogRef" @success="refreshTable('audit')" />

  <!-- 超标预警对话框 -->
  <AlarmDialog ref="alarmDialogRef" @success="refreshTable('exception')" />

  <!-- 标准对比对话框 -->
  <StandardCompareDialog ref="standardCompareDialogRef" />

  <!-- 处理结果查看对话框 -->
  <HandleResultDialog ref="handleResultDialogRef" />

  <!-- 修正追踪对话框 -->
  <RevisionTrackDialog ref="revisionTrackDialogRef" @success="refreshTable('audit')" />

  <!-- 异常处理对话框 -->
  <ExceptionProcessDialog ref="exceptionProcessDialogRef" @success="handleExceptionProcessSuccess" />

  <!-- 生成报告对话框 -->
  <GenerateReportDialog ref="generateReportDialogRef" @success="handleGenerateReportSuccess" />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted } from 'vue'
// 图标已在模板中使用
import {
  Search, Refresh, Clock, Check, FolderOpened, Warning, InfoFilled,
  ArrowDown, View, TrendCharts, Upload, RefreshRight, Document,
  Position, Download
} from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'
// 导入对话框组件
import AuditDialog from './components/AuditDialog.vue'
import RecheckDialog from './components/RecheckDialog.vue'
import DataDetailDialog from '../dataManagement/components/DataDetailDialog.vue'
import StorageDialog from './components/StorageDialog.vue'
import AlarmDialog from './components/AlarmDialog.vue'
import StandardCompareDialog from './components/StandardCompareDialog.vue'
import HandleResultDialog from './components/HandleResultDialog.vue'
import RevisionTrackDialog from './components/RevisionTrackDialog.vue'
import ExceptionProcessDialog from './components/ExceptionProcessDialog.vue'
import GenerateReportDialog from './components/GenerateReportDialog.vue'

defineOptions({ name: 'AssayQualityManagement' })

// 当前激活的标签页
const activeTab = ref('dataAudit')

// 筛选状态
const auditStatusFilter = ref('pending')

// 状态统计数据
const statusCounts = reactive({
  pending: 0,
  history: 0,
  exceeded: 0,
  warning: 0,
  normal: 0
})

// 加载状态
const loading = reactive({
  dataAudit: false,
  dataAlarm: false,
  testReport: false
})

// 搜索表单
const searchForm = reactive({
  dataAudit: {
    dataId: '',
    indicatorId: undefined as number | undefined,
    auditResult: '',
    testDateRange: []
  },
  dataAlarm: {
    alarmType: '',
    alarmLevel: '',
    isHandled: undefined as boolean | undefined,
    alarmDateRange: []
  },
  testReport: {
    reportCode: '',
    reportType: '',
    generateDateRange: []
  }
})

// 分页配置
const pagination = reactive({
  dataAudit: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  dataAlarm: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  testReport: {
    current: 1,
    pageSize: 10,
    total: 0
  }
})

// 定义表格数据类型接口
interface AuditData {
  id: number;
  sampleCode: string;
  testIndicator: string; // 改为检测指标
  indicatorId?: number; // 检测指标ID
  samplingPoint: string;
  testValue: string;
  unit: string;
  testDate: string;
  tester: string;
  status: string;
  isExceeded: boolean;
  isWarning: boolean;
  dataQuality: string; // 添加数据质量字段
  auditResult?: string;
  auditComment?: string;
  auditor?: string;
  auditDate?: string;
  storageStatus?: string;
  storageDate?: string;
  storageBy?: string;
  // 检测指标相关字段
  standardRange?: string; // 标准范围
  equipment?: string; // 检测设备
  testMethod?: string; // 检测方法
}



// 表格数据
const tableData = reactive({
  dataAudit: [] as any[],
  dataAlarm: [] as any[],
  testReport: [] as any[]
})

// 对话框引用
const auditDialogRef = ref()
const recheckDialogRef = ref()
const dataDetailDialogRef = ref()
const storageDialogRef = ref()
const alarmDialogRef = ref()
const standardCompareDialogRef = ref()
const handleResultDialogRef = ref()
const revisionTrackDialogRef = ref()
const exceptionProcessDialogRef = ref()
const generateReportDialogRef = ref()

// 生命周期钩子
onMounted(() => {
  fetchTableData('dataAudit')
  fetchTableData('dataAlarm')
  fetchTableData('testReport')
  updateStatusCounts()
})

// 筛选状态变化
const handleAuditStatusChange = () => {
  searchForm.dataAudit.dataId = ''
  searchForm.dataAudit.indicatorId = undefined
  searchForm.dataAudit.auditResult = ''
  searchForm.dataAudit.testDateRange = []
  fetchTableData('dataAudit')
}

// 更新状态统计
const updateStatusCounts = () => {
  // 模拟统计数据
  statusCounts.pending = 15
  statusCounts.history = 128
  statusCounts.exceeded = 3
  statusCounts.warning = 5
  statusCounts.normal = 7
}

// 获取检测值样式类
const getValueClass = (row) => {
  if (row.isExceeded) return 'text-danger'
  if (row.isWarning) return 'text-warning'
  return 'text-normal'
}

// 行点击事件
const handleRowClick = (row) => {
  handleViewAuditDetail(row)
}

// 新增的方法
const handleAudit = (row) => {
  auditDialogRef.value?.open(row)
}

const handleViewDetail = (row) => {
  dataDetailDialogRef.value?.open(row)
}

const handleViewAuditDetail = (row) => {
  dataDetailDialogRef.value?.open(row)
}

// 告警管理相关方法
const handleAlarm = (row) => {
  alarmDialogRef.value?.open(row)
}

const handleViewAlarmDetail = (row) => {
  ElMessage.info(`查看告警详情功能开发中...`)
}

const getAlarmLevelType = (level) => {
  const typeMap = {
    'low': 'info',
    'medium': 'warning',
    'high': 'danger'
  }
  return typeMap[level] || 'info'
}

const getAlarmLevelText = (level) => {
  const textMap = {
    'low': '低',
    'medium': '中',
    'high': '高'
  }
  return textMap[level] || level
}

// 报告管理相关方法
const handleGenerateReport = () => {
  generateReportDialogRef.value?.open()
}

const handlePreviewReport = (fileUrl) => {
  window.open(fileUrl, '_blank')
}

const handleDownloadReport = (row) => {
  ElMessage.success(`正在下载报告 ${row.reportCode}...`)
}

const handleViewReportDetail = (row) => {
  ElMessage.info(`查看报告详情功能开发中...`)
}

const getStatusTagType = (status) => {
  const typeMap = {
    'pending': 'info',
    'entered': 'primary',
    'reviewed': 'success'
  }
  return typeMap[status] || 'info'
}

const getStatusLabel = (status) => {
  const labelMap = {
    'pending': '待录入',
    'entered': '已录入',
    'reviewed': '已审核'
  }
  return labelMap[status] || status
}

// 更多操作处理
const handleMoreAction = (command, row) => {
  switch (command) {
    case 'detail':
      handleViewDetail(row)
      break
    case 'compare':
      handleCompareStandard(row)
      break
    case 'storage':
      handleDataStorage(row)
      break
    case 'recheck':
      handleRecheck(row)
      break
    case 'exception':
      handleExceptionProcess(row)
      break
    case 'report':
      handleGenerateReport(row)
      break
  }
}

// 历史操作处理
const handleHistoryAction = (command, row) => {
  switch (command) {
    case 'track':
      handleTrackRevision(row)
      break
    case 'storage':
      handleDataStorage(row)
      break
    case 'recheck':
      handleRecheck(row)
      break
    case 'viewReport':
      handleViewReport(row)
      break
    case 'downloadReport':
      handleDownloadReport(row)
      break
  }
}

// 获取表格数据
const fetchTableData = async (type) => {
  loading[type] = true
  try {
    // 模拟异步请求
    setTimeout(() => {
      if (type === 'dataAudit') {
        // 根据API文档更新的数据审核数据结构
        if (auditStatusFilter.value === 'pending') {
          // 待审核数据
          tableData.dataAudit = [
            {
              id: 1,
              factoryId: 1,
              dataId: 1,
              indicatorName: 'COD',
              testValue: 45.2,
              unit: 'mg/L',
              testDate: '2024-01-15',
              testerName: '李四',
              method: '重铬酸钾法',
              instrument: 'COD分析仪',
              status: 'pending',
              isExceeded: false,
              isWarning: false,
              exceedRatio: null,
              qualityLevel: 'good',
              auditResult: 'pending',
              auditComment: null,
              auditorId: null,
              auditorName: null,
              auditTime: null,
              createTime: '2024-01-15 10:30:00',
              updateTime: '2024-01-15 10:30:00'
            },
            {
              id: 2,
              sampleCode: 'SP20240115001',  // 来源：检测数据模块ID=8（同一样品）
              testIndicator: 'pH值',
              indicatorId: 114,
              samplingPoint: '进水总口',
              testValue: '7.2',  // 符合标准的数值
              unit: '无量纲',
              testDate: '2024-01-15',
              tester: '李四',
              status: 'pending',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              standardValue: '6-9',
              testMethod: '玻璃电极法'
            },
            {
              id: 3,
              sampleCode: 'SP20240115002',  // 来源：检测数据模块ID=3
              testItem: '氨氮',
              samplingPoint: '出水总口',
              testValue: '4.8',  // 接近标准限值
              unit: 'mg/L',
              testDate: '2024-01-15',
              tester: '孙八',
              status: 'pending',
              isExceeded: false,
              isWarning: true,  // 警告状态
              dataQuality: 'warning',
              standardValue: '≤5',
              testMethod: '纳氏试剂比色法'
            },
            {
              id: 4,
              sampleCode: 'SP20240115002',  // 来源：检测数据模块ID=10
              testItem: '总磷',
              samplingPoint: '出水总口',
              testValue: '0.6',  // 超标数值
              unit: 'mg/L',
              testDate: '2024-01-15',
              tester: '孙八',
              status: 'pending',
              isExceeded: true,  // 超标
              isWarning: false,
              dataQuality: 'danger',
              standardValue: '≤0.5',
              testMethod: '钼酸铵分光光度法'
            },
            {
              id: 5,
              sampleCode: 'SP20240120001',  // 来源：检测数据模块ID=6
              testItem: '铜',
              samplingPoint: '进水总口',
              testValue: '0.8',  // 超标数值（异常检测）
              unit: 'mg/L',
              testDate: '2024-01-20',
              tester: '孙八',
              status: 'pending',
              isExceeded: true,  // 超标
              isWarning: false,
              dataQuality: 'danger',
              standardValue: '≤0.5',
              testMethod: '原子吸收分光光度法'
            },
            {
              id: 6,
              sampleCode: 'SP20240116001',  // 来源：检测数据模块ID=4
              testItem: '污泥浓度',
              samplingPoint: '污泥浓缩池',
              testValue: '3200',  // 正常范围内
              unit: 'mg/L',
              testDate: '2024-01-16',
              tester: '周九',
              status: 'pending',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              standardValue: '2000-4000',
              testMethod: '重量法'
            },
            {
              id: 7,
              sampleCode: 'SP20240116001',  // 来源：检测数据模块ID=5
              testItem: '污泥沉降比',
              samplingPoint: '污泥浓缩池',
              testValue: '25',  // 正常范围内
              unit: '%',
              testDate: '2024-01-16',
              tester: '周九',
              status: 'pending',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              standardValue: '15-30',
              testMethod: '静置沉降法'
            }
          ]
        } else {
          // 审核历史数据
          tableData.dataAudit = [
            {
              id: 1,
              sampleCode: 'SP20240114001',  // 历史已审核数据
              testItem: 'COD',
              samplingPoint: '进水总口',
              testValue: '42.5',  // 符合标准
              unit: 'mg/L',
              testDate: '2024-01-14',
              tester: '李四',
              status: 'reviewed',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              auditResult: 'passed',
              auditComment: '数据质量良好，符合标准要求。',
              auditor: '赵六',  // 来源：计划模块.reviewer
              auditDate: '2024-01-14',
              storageStatus: 'completed',
              storageDate: '2024-01-14',
              storageBy: '系统自动',
              standardValue: '≤50',
              testMethod: '重铬酸钾法'
            },
            {
              id: 2,
              sampleCode: 'SP20240113001',  // 历史已审核数据
              testItem: 'BOD5',
              samplingPoint: '出水总口',
              testValue: '8.5',  // 符合标准
              unit: 'mg/L',
              testDate: '2024-01-13',
              tester: '李四',
              status: 'reviewed',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              auditResult: 'passed',
              auditComment: '数据质量良好，符合标准要求。',
              auditor: '赵六',
              auditDate: '2024-01-13',
              storageStatus: 'completed',
              storageDate: '2024-01-13',
              storageBy: '系统自动',
              standardValue: '≤10',
              testMethod: '稀释接种法'
            },
            {
              id: 3,
              sampleCode: 'SP20240112001',  // 历史已审核数据
              testItem: '氨氮',
              samplingPoint: '出水总口',
              testValue: '5.2',  // 超标数据
              unit: 'mg/L',
              testDate: '2024-01-12',
              tester: '孙八',
              status: 'reviewed',
              isExceeded: true,
              isWarning: false,
              dataQuality: 'danger',
              auditResult: 'rejected',
              auditComment: '检测数据超出标准限值，需要重新检测确认。',
              auditor: '赵六',
              auditDate: '2024-01-12',
              storageStatus: 'rejected',
              standardValue: '≤5',
              testMethod: '纳氏试剂比色法'
            },
            {
              id: 4,
              sampleCode: 'SP20240111001',  // 历史已审核数据
              testItem: '总磷',
              samplingPoint: '出水总口',
              testValue: '0.45',  // 符合标准
              unit: 'mg/L',
              testDate: '2024-01-11',
              tester: '孙八',
              status: 'reviewed',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              auditResult: 'passed',
              auditComment: '数据质量良好，符合标准要求。',
              auditor: '周九',
              auditDate: '2024-01-11',
              storageStatus: 'completed',
              storageDate: '2024-01-11',
              storageBy: '系统自动',
              standardValue: '≤0.5',
              testMethod: '钼酸铵分光光度法'
            },
            {
              id: 5,
              sampleCode: 'SP20240110001',  // 历史已审核数据
              testItem: 'pH值',
              samplingPoint: '生化池进口',
              testValue: '7.8',  // 符合标准
              unit: '无量纲',
              testDate: '2024-01-10',
              tester: '李四',
              status: 'reviewed',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              auditResult: 'passed',
              auditComment: '数据质量良好，符合标准要求。',
              auditor: '王五',
              auditDate: '2024-01-10',
              storageStatus: 'completed',
              storageDate: '2024-01-10',
              storageBy: '系统自动',
              standardValue: '6-9',
              testMethod: '玻璃电极法'
            },
            {
              id: 6,
              sampleCode: 'SP20240109001',  // 历史已审核数据
              testItem: '污泥浓度',
              samplingPoint: '污泥浓缩池',
              testValue: '3800',  // 符合标准
              unit: 'mg/L',
              testDate: '2024-01-09',
              tester: '周九',
              status: 'reviewed',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              auditResult: 'passed',
              auditComment: '污泥浓度正常，符合工艺要求。',
              auditor: '王五',
              auditDate: '2024-01-09',
              storageStatus: 'completed',
              storageDate: '2024-01-09',
              storageBy: '系统自动',
              standardValue: '2000-4000',
              testMethod: '重量法'
            },
            {
              id: 7,
              sampleCode: 'SP20240108001',  // 历史已审核数据
              testItem: '悬浮物',
              samplingPoint: '出水总口',
              testValue: '12',  // 超标数据
              unit: 'mg/L',
              testDate: '2024-01-08',
              tester: '孙八',
              status: 'reviewed',
              isExceeded: true,
              isWarning: false,
              dataQuality: 'danger',
              auditResult: 'rejected',
              auditComment: '悬浮物超标，需要检查处理工艺。',
              auditor: '赵六',
              auditDate: '2024-01-08',
              storageStatus: 'rejected',
              standardValue: '≤10',
              testMethod: '重量法'
            },
            {
              id: 8,
              sampleCode: 'SP20240107001',  // 历史已审核数据
              testItem: '浊度',
              samplingPoint: '出水总口',
              testValue: '3.2',  // 符合标准
              unit: 'NTU',
              testDate: '2024-01-07',
              tester: '孙八',
              status: 'reviewed',
              isExceeded: false,
              isWarning: false,
              dataQuality: 'good',
              auditResult: 'passed',
              auditComment: '浊度正常，出水清澈。',
              auditor: '周九',
              auditDate: '2024-01-07',
              storageStatus: 'completed',
              storageDate: '2024-01-07',
              storageBy: '系统自动',
              standardValue: '≤5',
              testMethod: '散射法'
            }
          ]
        }
        pagination.dataAudit.total = auditStatusFilter.value === 'pending' ? 7 : 8

      }
      loading[type] = false
    }, 500)
  } catch (error) {
    console.error(`获取${type}数据失败:`, error)
    loading[type] = false
  }
}

// 搜索
const handleSearch = (type) => {
  pagination[type].current = 1
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: string) => {
  if (type === 'dataAudit') {
    searchForm.dataAudit.dataId = ''
    searchForm.dataAudit.indicatorId = undefined
    searchForm.dataAudit.auditResult = ''
    searchForm.dataAudit.testDateRange = []
  } else if (type === 'dataAlarm') {
    searchForm.dataAlarm.alarmType = ''
    searchForm.dataAlarm.alarmLevel = ''
    searchForm.dataAlarm.isHandled = undefined
    searchForm.dataAlarm.alarmDateRange = []
  } else if (type === 'testReport') {
    searchForm.testReport.reportCode = ''
    searchForm.testReport.reportType = ''
    searchForm.testReport.generateDateRange = []
  }
  handleSearch(type)
}

// 刷新表格
const refreshTable = (type) => {
  fetchTableData(type)
}

// 数据审核 - 已在上面定义，删除重复定义

// 安排复检
const handleRecheck = (row) => {
  recheckDialogRef.value?.open(row)
}

// 追踪修正
const handleTrackRevision = (row) => {
  revisionTrackDialogRef.value?.open(row)
}

// 数据入库
const handleDataStorage = (row) => {
  storageDialogRef.value?.open(row)
}

// 重试入库
const handleRetryStorage = (row) => {
  // 模拟重试入库逻辑，因为缺少组件暂时使用消息提示
  ElMessage.success(`已安排重试入库：${row.sampleCode} - ${row.testItem}`)
  refreshTable('audit')
  // storageDialogRef.value?.open(row)
}



// 查看入库详情
const handleViewStorageDetail = (row) => {
  dataDetailDialogRef.value?.open(row)
}





// 标准对比
const handleCompareStandard = (row) => {
  standardCompareDialogRef.value?.open(row)
}

// 查看报告
const handleViewReport = (row) => {
  ElMessage.info(`查看样品 ${row.sampleCode} 的检验报告`)
  // 这里应该打开报告查看对话框或新窗口
}

// 异常处理
const handleExceptionProcess = (row) => {
  exceptionProcessDialogRef.value?.open(row)
}

// 异常处理成功回调
const handleExceptionProcessSuccess = (result) => {
  ElMessage.success(`样品 ${result.sampleCode} 异常处理完成`)
  refreshTable('audit')
}

// 生成报告成功回调
const handleGenerateReportSuccess = (reportInfo) => {
  ElMessage.success(`检验报告已生成：${reportInfo.reportCode}`)
  // 可以在这里添加下载逻辑或其他后续处理
}



// 分页大小变化
const handleSizeChange = (type: string) => {
  fetchTableData(type)
}

// 页码变化
const handleCurrentChange = (type: string) => {
  fetchTableData(type)
}

// 获取数据质量标签类型
const getDataQualityTagType = (dataQuality: string) => {
  if (dataQuality === 'good') return 'success'
  if (dataQuality === 'warning') return 'warning'
  if (dataQuality === 'danger') return 'danger'
  return 'info'
}

// 获取数据质量标签文本
const getDataQualityLabel = (dataQuality: string) => {
  if (dataQuality === 'good') return '数据质量良好'
  if (dataQuality === 'warning') return '数据质量警告'
  if (dataQuality === 'danger') return '数据质量异常'
  return '数据质量未知'
}

// 获取入库状态标签类型
const getStorageStatusTagType = (status: string) => {
  if (status === 'pending') return 'info'
  if (status === 'completed') return 'success'
  if (status === 'failed') return 'danger'
  return 'info'
}

// 获取入库状态标签文本
const getStorageStatusLabel = (status: string) => {
  if (status === 'pending') return '待入库'
  if (status === 'completed') return '已入库'
  if (status === 'failed') return '入库失败'
  return '未知状态'
}




</script>

<style scoped>
/* 页面布局样式 */
.header-section {
  margin-bottom: 24px;
}

.status-filter {
  margin-bottom: 16px;
}

.status-filter .el-radio-button {
  margin-right: 8px;
}

.stats-cards {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.stat-card {
  flex: 1;
  padding: 16px;
  border-radius: 8px;
  text-align: center;
  color: white;
  min-width: 120px;
}

.stat-card.danger {
  background: linear-gradient(135deg, #f56c6c, #f78989);
}

.stat-card.warning {
  background: linear-gradient(135deg, #e6a23c, #eebe77);
}

.stat-card.success {
  background: linear-gradient(135deg, #67c23a, #85ce61);
}

.stat-number {
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  opacity: 0.9;
}

/* 搜索区域样式 */
.search-section {
  margin-bottom: 20px;
  padding: 16px;
  background-color: #f8f9fa;
  border-radius: 8px;
}

.search-form {
  margin: 0;
}

.search-buttons {
  display: flex;
  gap: 8px;
}

/* 表格区域样式 */
.table-section {
  margin-bottom: 20px;
}

.test-value-cell {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-icon {
  font-size: 16px;
}

.status-icon.danger {
  color: #f56c6c;
}

.status-icon.warning {
  color: #e6a23c;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

/* 分页区域样式 */
.pagination-section {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

/* 文本样式 */
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.text-normal {
  color: #606266;
}

.text-muted {
  color: #909399;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-cards {
    flex-wrap: wrap;
  }

  .stat-card {
    min-width: 100px;
  }
}

@media (max-width: 768px) {
  .search-form {
    flex-direction: column;
  }

  .search-form .el-form-item {
    margin-right: 0;
    margin-bottom: 12px;
  }

  .stats-cards {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
  }
}

/* 表格行悬停效果 */
.el-table__row:hover {
  cursor: pointer;
}

/* 下拉菜单样式优化 */
.el-dropdown-menu__item {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 徽章样式 */
.ml-2 {
  margin-left: 8px;
}
</style>

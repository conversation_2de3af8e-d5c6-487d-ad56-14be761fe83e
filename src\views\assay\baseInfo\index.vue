<template>
  <ContentWrap title="基础信息管理">
    <el-tabs v-model="activeTab">
      <el-tab-pane label="检测项目管理" name="testItem">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.testItem" class="search-form">
              <el-form-item label="项目类型">
                <el-select
                  v-model="searchForm.testItem.categoryId"
                  placeholder="请选择项目类型"
                  clearable
                  style="min-width: 10rem;"
                  @change="handleCategoryChange"
                >
                  <el-option
                    v-for="category in categoryOptions"
                    :key="category.id"
                    :label="category.name"
                    :value="category.id"
                  />
                </el-select>
              </el-form-item>
              <el-form-item label="项目类型名称">
                <el-input v-model="searchForm.testItem.name" placeholder="请输入项目类型名称" clearable />
              </el-form-item>
              <el-form-item label="项目类型状态" >
                <el-select style="min-width: 8rem;" v-model="searchForm.testItem.isEnabled" placeholder="请选择项目类型状态" clearable>
                  <el-option label="启用" :value="true" />
                  <el-option label="停用" :value="false" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('testItem')">
                  <el-icon><Search /></el-icon>搜索
                </el-button>
                <el-button @click="resetSearch('testItem')">
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button type="primary" @click="handleAddCategory">
              <el-icon><Plus /></el-icon>新增项目类型
            </el-button>

          </div>
          <!-- 表格 -->
          <el-table
            ref="tableRef"
            v-loading="loading.testItem"
            :data="tableData.testItem"
            border
            style="width: 100%"
            row-key="id"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
            :expand-row-keys="Array.from(treeState.expandedRows)"
            :row-class-name="getRowClassName"
            @expand-change="handleExpandChange"
            lazy
            :load="loadTreeNode"
          >
            <el-table-column prop="name" label="项目名称" min-width="20rem">
              <template #default="{ row }">
                <div class="flex items-center tree-node" :class="`tree-level-${getNodeLevel(row)}`">
                  <!-- 层级缩进 -->
                  <div :style="{ width: `${getNodeLevel(row) * 20}px` }" class="tree-indent"></div>

                  <!-- 图标和标签 -->
                  <div class="flex items-center">
                    <!-- 一级：项目类型 -->
                    <template v-if="row.type === 'category'">
                      <el-icon class="mr-2 text-blue-600" size="18">
                        <FolderOpened />
                      </el-icon>
                      <el-tag type="primary" size="small" class="mr-2">类型</el-tag>
                      <span class="font-bold text-blue-800 text-base">{{ row.name }}</span>
                    </template>

                    <!-- 二级：检测项目 -->
                    <template v-else-if="row.type === 'project'">
                      <el-icon class="mr-2 text-green-600" size="16">
                        <Document />
                      </el-icon>
                      <el-tag type="success" size="small" class="mr-2">项目</el-tag>
                      <span class="font-semibold text-green-700">{{ row.name }}</span>
                    </template>

                    <!-- 三级：检测指标 -->
                    <template v-else>
                      <el-icon class="mr-2 text-orange-600" size="14">
                        <DataLine />
                      </el-icon>
                      <el-tag type="warning" size="small" class="mr-2">指标</el-tag>
                      <span class="text-orange-700">{{ row.name }}</span>
                      <el-tag v-if="row.code" size="small" class="ml-2" type="info">{{ row.code }}</el-tag>
                    </template>
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="code" label="编码" min-width="10rem">
              <template #default="{ row }">
                <span v-if="row.code" class="font-mono text-sm">{{ row.code }}</span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="检测方法" min-width="12rem" show-overflow-tooltip>
              <template #default="{ row }">
                <template v-if="row.type === 'indicator'">
                  <div class="text-sm">
                    <div class="font-medium">{{ row.method }}</div>
                    <div class="text-gray-500 text-xs mt-1" v-if="row.methodPrinciple">
                      {{ row.methodPrinciple.substring(0, 30) }}...
                    </div>
                  </div>
                </template>
                <span v-else-if="row.type === 'project'" class="text-gray-500 text-sm">
                  {{ row.indicatorNum || row.indicatorCount || 0 }} 个指标
                </span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="单位/标准值" min-width="12rem">
              <template #default="{ row }">
                <template v-if="row.type === 'indicator'">
                  <div class="text-sm">
                    <el-tag size="small" type="info" class="mb-1">{{ row.unit }}</el-tag>
                    <div class="text-gray-600">
                      <span v-if="row.standardMin !== undefined && row.standardMax !== undefined">
                        {{ row.standardMin }} - {{ row.standardMax }}
                      </span>
                      <span v-else-if="row.standardMin !== undefined">
                        ≥ {{ row.standardMin }}
                      </span>
                      <span v-else-if="row.standardMax !== undefined">
                        ≤ {{ row.standardMax }}
                      </span>
                      <span v-else>-</span>
                    </div>
                  </div>
                </template>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column label="检测仪器/样品信息" min-width="15rem" show-overflow-tooltip>
              <template #default="{ row }">
                <template v-if="row.type === 'indicator'">
                  <div class="text-sm">
                    <div class="font-medium text-blue-600">{{ row.equipment }}</div>
                    <div class="text-gray-500 text-xs mt-1">
                      <span v-if="row.sampleVolume">样品量: {{ row.sampleVolume }}mL</span>
                      <span v-if="row.detectionTimeMinutes" class="ml-2">耗时: {{ row.detectionTimeMinutes }}min</span>
                    </div>
                    <div class="text-gray-500 text-xs mt-1" v-if="row.precisionLimit">
                      精度: {{ row.precisionLimit }}
                    </div>
                  </div>
                </template>
                <template v-else-if="row.type === 'project'">
                  <span class="text-gray-400">-</span>
                </template>
                <template v-else>
                  <span class="text-gray-400">-</span>
                </template>
              </template>
            </el-table-column>
            <el-table-column prop="isEnabled" label="状态" min-width="8rem">
              <template #default="{ row }">
                <el-tag v-if="row.isEnabled !== undefined" :type="row.isEnabled ? 'success' : 'danger'">
                  {{ row.isEnabled ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="20rem" fixed="right">
              <template #default="{ row }">
                <template v-if="row.type === 'category'">
                  <el-button link type="primary" @click="handleAddProject(row)">
                    <el-icon><Plus /></el-icon>添加项目
                  </el-button>
                  <el-button link type="warning" @click="handleEditCategory(row)">编辑</el-button>
                  <el-button link type="danger" @click="handleDeleteCategory(row)">删除</el-button>
                </template>
                <template v-else-if="row.type === 'project'">
                  <el-button link type="primary" @click="handleAddIndicator(row)">
                    <el-icon><Plus /></el-icon>添加指标
                  </el-button>
                  <el-button link type="warning" @click="handleEditProject(row)">编辑</el-button>
                  <el-button link type="danger" @click="handleDeleteProject(row)">删除</el-button>
                </template>
                <template v-else>
                  <el-button link type="primary" @click="handleViewIndicator(row)">查看详情</el-button>
                  <el-button link type="warning" @click="handleEditIndicator(row)">编辑</el-button>
                  <el-button link type="danger" @click="handleDeleteIndicator(row)">删除</el-button>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.testItem.current"
              v-model:page-size="pagination.testItem.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.testItem.total"
              @size-change="handleSizeChange('testItem')"
              @current-change="handleCurrentChange('testItem')"
            />
          </div>
        </el-card>
      </el-tab-pane>

      <el-tab-pane label="采样点管理" name="samplingPoint">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.samplingPoint" class="search-form">
              <el-form-item label="采样点名称">
                <el-input v-model="searchForm.samplingPoint.name" placeholder="请输入采样点名称" clearable />
              </el-form-item>
              <el-form-item label="采样点类型">
                <el-select
                  style="min-width: 8rem;"
                  v-model="searchForm.samplingPoint.type"
                  placeholder="请选择类型"
                  clearable
                  @change="handleSamplingPointTypeChange"
                >
                  <el-option label="进水口" value="inlet" />
                  <el-option label="出水口" value="outlet" />
                </el-select>
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('samplingPoint')">
                  <el-icon><Search /></el-icon>搜索
                </el-button>
                <el-button @click="resetSearch('samplingPoint')">
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          <!-- 表格操作栏 -->
          <div class="mb-4">
            <el-button type="primary" @click="handleAdd('samplingPoint')">
              <el-icon><Plus /></el-icon>新增采样点
            </el-button>
          </div>
          <!-- 表格 -->
          <el-table v-loading="loading.samplingPoint" :data="tableData.samplingPoint" border style="width: 100%">
            <el-table-column prop="id" label="采样点编号" min-width="8rem" />
            <el-table-column prop="name" label="采样点名称" min-width="12rem" />
            <el-table-column prop="code" label="采样点代码" min-width="8rem" />
            <el-table-column prop="type" label="采样点类型" min-width="8rem">
              <template #default="{ row }">
                <el-tag :type="row.type === 'inlet' ? 'primary' : row.type === 'outlet' ? 'success' : 'warning'">
                  {{ row.type === 'inlet' ? '进水口' : row.type === 'outlet' ? '出水口' : '处理单元' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="location" label="位置描述" min-width="12rem" show-overflow-tooltip />
            <el-table-column label="管理人" min-width="8rem">
              <template #default="{ row }">
                <span v-if="row.managerId">{{ getManagerName(row.managerId) }}</span>
                <span v-else class="text-gray-400">-</span>
              </template>
            </el-table-column>
            <el-table-column prop="isEnabled" label="状态" min-width="8rem">
              <template #default="{ row }">
                <el-tag v-if="row.isEnabled !== undefined" :type="row.isEnabled ? 'success' : 'danger'">
                  {{ row.isEnabled ? '启用' : '停用' }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="15rem" fixed="right">
              <template #default="{ row }">
                <el-button link type="primary" @click="handleEdit('samplingPoint', row)">编辑</el-button>
                <el-button link type="danger" @click="handleDelete('samplingPoint', row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="mt-4 flex justify-end">
            <el-pagination
              v-model:current-page="pagination.samplingPoint.current"
              v-model:page-size="pagination.samplingPoint.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :background="true"
              layout="total, sizes, prev, pager, next, jumper"
              :total="pagination.samplingPoint.total"
              @size-change="handleSizeChange('samplingPoint')"
              @current-change="handleCurrentChange('samplingPoint')"
            />
          </div>
        </el-card>
      </el-tab-pane>


    </el-tabs>
  </ContentWrap>

  <!-- 项目类型对话框 -->
  <TestCategoryDialog
    ref="testCategoryDialogRef"
    @success="handleCategoryDialogSuccess"
  />

  <!-- 检测项目对话框 -->
  <TestProjectDialog
    ref="testProjectDialogRef"
    @success="handleProjectDialogSuccess"
  />

  <!-- 检测指标对话框 -->
  <TestIndicatorDialog
    ref="testIndicatorDialogRef"
    @success="handleIndicatorDialogSuccess"
  />

  <!-- 检测项目对话框（旧版） -->
  <TestItemDialog
    ref="testItemDialogRef"
    @success="refreshTable('testItem')"
  />

  <!-- 采样点对话框 -->
  <SamplingDialog
    ref="samplingDialogRef"
    @success="refreshTable('samplingPoint')"
  />
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, watch, nextTick } from 'vue'
import { Search, Refresh, Plus, Document, DataLine, FolderOpened } from '@element-plus/icons-vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'

import { AssayBaseInfoApi, type TestTreeNodeVO, type AssaySamplingPointVO } from '@/api/assay/baseInfo'
import { useAppStore } from '@/store/modules/app'
import TestCategoryDialog from './components/TestCategoryDialog.vue'
import TestProjectDialog from './components/TestProjectDialog.vue'
import TestIndicatorDialog from './components/TestIndicatorDialog.vue'
import TestItemDialog from './components/TestItemDialog.vue'
import SamplingDialog from './components/SamplingDialog.vue'

defineOptions({ name: 'AssayBaseInfo' })

// 当前激活的标签页
const activeTab = ref('testItem')

// 获取全局状态管理
const appStore = useAppStore()



// 加载状态
const loading = reactive({
  testItem: false,
  samplingPoint: false
})

// 搜索表单
const searchForm = reactive({
  testItem: {
    categoryId: '',
    name: '',
    isEnabled: ''
  },
  samplingPoint: {
    name: '',
    type: '',
    isEnabled: ''
  }
})

// 分页配置
const pagination = reactive({
  testItem: {
    current: 1,
    pageSize: 10,
    total: 0
  },
  samplingPoint: {
    current: 1,
    pageSize: 10,
    total: 0
  }
})

// 下拉框选项数据
const categoryOptions = ref([]) // 检测项目类型选项
const categoryOptionsLoaded = ref(false) // 缓存标记，避免重复请求

// 表格数据
const tableData = reactive({
  testItem: [] as TestTreeNodeVO[],
  samplingPoint: [] as AssaySamplingPointVO[]
})

// 树形结构完整管理系统 - 新增：解决缓存和刷新问题
const treeState = reactive({
  expandedRows: new Set<number>(), // 展开的行ID集合
  loadedNodes: new Map<string, any[]>(), // 已加载的节点数据缓存 key: "category_123" | "project_456"
  loadingNodes: new Set<string>(), // 正在加载的节点
  nodeChildren: new Map<number, number[]>(), // 父子关系映射
  tableRef: null as any // 表格引用，用于强制刷新
})

// 对话框引用
const testCategoryDialogRef = ref()
const testProjectDialogRef = ref()
const testIndicatorDialogRef = ref()
const testItemDialogRef = ref()
const samplingDialogRef = ref()
const tableRef = ref() // 表格引用


// 树形数据管理核心方法 - 新增：完整的缓存管理
const TreeManager = {
  // 生成节点缓存key
  getCacheKey(type: 'category' | 'project', parentId: number): string {
    return `${type}_${parentId}`
  },

  // 获取缓存的子节点数据
  getCachedChildren(type: 'category' | 'project', parentId: number): any[] | null {
    const key = this.getCacheKey(type, parentId)
    return treeState.loadedNodes.get(key) || null
  },

  // 缓存子节点数据
  setCachedChildren(type: 'category' | 'project', parentId: number, children: any[]): void {
    const key = this.getCacheKey(type, parentId)
    treeState.loadedNodes.set(key, children)

    // 更新父子关系映射
    const childIds = children.map(child => child.id)
    treeState.nodeChildren.set(parentId, childIds)
  },

  // 清除指定节点的缓存
  clearNodeCache(type: 'category' | 'project', parentId: number): void {
    const key = this.getCacheKey(type, parentId)
    treeState.loadedNodes.delete(key)
    treeState.nodeChildren.delete(parentId)
  },

  // 清除所有缓存
  clearAllCache(): void {
    treeState.loadedNodes.clear()
    treeState.nodeChildren.clear()
    treeState.expandedRows.clear()
  },

  // 移除指定子节点
  removeChildNode(parentId: number, childId: number): void {
    const children = treeState.nodeChildren.get(parentId)
    if (children) {
      const index = children.indexOf(childId)
      if (index > -1) {
        children.splice(index, 1)
        // 同步更新缓存数据
        const cacheKeys = Array.from(treeState.loadedNodes.keys())
        for (const key of cacheKeys) {
          if (key.endsWith(`_${parentId}`)) {
            const cachedData = treeState.loadedNodes.get(key)
            if (cachedData) {
              const filteredData = cachedData.filter(item => item.id !== childId)
              treeState.loadedNodes.set(key, filteredData)
            }
          }
        }
      }
    }
  },

  // 强制刷新表格
  forceRefreshTable(): void {
    if (tableRef.value) {
      // 清除Element Plus内部缓存
      tableRef.value.store.states.lazyTreeNodeMap.value = {}
      tableRef.value.store.states.treeData.value = {}

      // 重新加载根节点数据
      fetchTableData('testItem')
    }
  }
}

// 获取管理人姓名
const getManagerName = (managerId: number) => {
  const managers = {
    1001: '张三',
    1002: '李四',
    1003: '王五',
    1004: '赵六'
  }
  return managers[managerId] || '未知'
}

// 加载检测项目类型选项 - 修复：添加缓存机制
const loadCategoryOptions = async (forceReload = false) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    console.warn('⚠️ 未获取到当前水厂ID')
    return
  }

  // 如果已经加载过且不是强制重新加载，则直接返回
  if (categoryOptionsLoaded.value && !forceReload) {
    console.log('✅ 检测项目类型选项已缓存，跳过请求')
    return
  }

  try {
    console.log('🔄 正在获取检测项目类型选项...', factoryId)
    const res = await AssayBaseInfoApi.getTestCategorySimpleList(factoryId)

    if (res && res.data) {
      categoryOptions.value = res.data
      categoryOptionsLoaded.value = true // 标记已加载
      console.log('✅ 检测项目类型选项获取成功:', res.data)
    } else {
      console.warn('⚠️ 检测项目类型选项数据格式异常:', res)
      categoryOptions.value = []
      categoryOptionsLoaded.value = false
    }
  } catch (error) {
    console.error('❌ 获取检测项目类型选项失败:', error)
    categoryOptions.value = []
    categoryOptionsLoaded.value = false
  }
}

// 获取表格数据
const fetchTableData = async (type: string) => {
  // 获取当前选中水厂ID
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    console.warn('⚠️ 水厂ID未获取，无法加载数据')
    return
  }

  loading[type] = true
  try {
    if (type === 'testItem') {
      // 修改：调用API获取检测项目类型分页数据，不再使用tree接口
      const params = {
        factoryId: factoryId,
        pageNo: pagination.testItem.current,
        pageSize: pagination.testItem.pageSize,
        name: searchForm.testItem.name || undefined,
        isEnabled: searchForm.testItem.isEnabled !== '' ? Boolean(searchForm.testItem.isEnabled) : undefined
      }

      console.log('🔄 正在获取检测项目类型数据...', params)
      const res = await AssayBaseInfoApi.getTestCategoryPage(params)

      if (res && res.data && res.data.list) {
        // 处理项目类型数据，添加类型标识和懒加载属性
        tableData.testItem = res.data.list.map(category => ({
          ...category,
          type: 'category',
          hasChildren: true // 项目类型下有检测项目
        }))
        pagination.testItem.total = res.data.total || 0
        console.log('✅ 检测项目类型数据获取成功:', res.data)
      } else {
        console.warn('⚠️ 检测项目类型数据格式异常:', res)
        tableData.testItem = []
        pagination.testItem.total = 0
      }
    } else if (type === 'samplingPoint') {
      // 调用API获取采样点分页数据
      const params = {
        factoryId: factoryId,
        pageNo: pagination.samplingPoint.current,
        pageSize: pagination.samplingPoint.pageSize,
        name: searchForm.samplingPoint.name || undefined,
        type: searchForm.samplingPoint.type || undefined,
        isEnabled: searchForm.samplingPoint.isEnabled !== '' ? Boolean(searchForm.samplingPoint.isEnabled) : undefined
      }

      console.log('🔄 正在获取采样点数据...', params)
      const res = await AssayBaseInfoApi.getSamplingPointPage(params)

      if (res && res.data && res.data.list) {
        tableData.samplingPoint = res.data.list
        pagination.samplingPoint.total = res.data.total || 0
        console.log('✅ 采样点数据获取成功:', res.data)
      } else {
        console.warn('⚠️ 采样点数据格式异常:', res)
        tableData.samplingPoint = []
        pagination.samplingPoint.total = 0
      }
    }
  } catch (error) {
    console.error(`❌ 获取${type}数据失败:`, error)
    ElMessage.error(`获取${type === 'testItem' ? '检测项目' : '采样点'}数据失败`)
    tableData[type] = []
    pagination[type].total = 0
  } finally {
    loading[type] = false
  }
}

// 搜索
const handleSearch = (type: string) => {
  pagination[type].current = 1
  fetchTableData(type)
}

// 重置搜索
const resetSearch = (type: string) => {
  if (type === 'testItem') {
    searchForm.testItem.categoryId = ''
    searchForm.testItem.name = ''
    searchForm.testItem.isEnabled = ''
  } else if (type === 'samplingPoint') {
    searchForm.samplingPoint.name = ''
    searchForm.samplingPoint.type = ''
    searchForm.samplingPoint.isEnabled = ''
  }
  handleSearch(type)
}

// 刷新表格
const refreshTable = (type: string) => {
  fetchTableData(type)
}

// 项目类型对话框成功回调 - 重构：精确的缓存管理
const handleCategoryDialogSuccess = () => {
  console.log('🎉 项目类型操作成功，刷新相关数据')

  // 清除所有缓存，因为项目类型的变化可能影响整个树结构
  TreeManager.clearAllCache()

  // 刷新根节点数据
  refreshTable('testItem')

  // 强制刷新项目类型下拉框选项
  categoryOptionsLoaded.value = false
  loadCategoryOptions(true)
}

// 检测项目对话框成功回调 - 重构：精确的缓存管理
const handleProjectDialogSuccess = (projectData?: any) => {
  console.log('🎉 检测项目操作成功，刷新相关数据')

  if (projectData && projectData.categoryId) {
    // 精确清除对应项目类型的缓存
    TreeManager.clearNodeCache('category', projectData.categoryId)
    console.log('🗑️ 清除项目类型缓存:', projectData.categoryId)
  } else {
    // 如果无法确定具体类型，清除所有项目缓存
    const categoryKeys = Array.from(treeState.loadedNodes.keys()).filter(key => key.startsWith('category_'))
    categoryKeys.forEach(key => {
      const categoryId = parseInt(key.split('_')[1])
      TreeManager.clearNodeCache('category', categoryId)
    })
  }

  // 强制刷新表格以显示最新数据
  TreeManager.forceRefreshTable()
}

// 检测指标对话框成功回调 - 重构：精确的缓存管理
const handleIndicatorDialogSuccess = (indicatorData?: any) => {
  console.log('🎉 检测指标操作成功，刷新相关数据')

  if (indicatorData && indicatorData.projectId) {
    // 精确清除对应检测项目的缓存
    TreeManager.clearNodeCache('project', indicatorData.projectId)
    console.log('🗑️ 清除检测项目缓存:', indicatorData.projectId)

    // 同时需要更新父级项目类型的指标数量显示
    // 这里需要重新加载对应的项目类型数据
    const projectKeys = Array.from(treeState.loadedNodes.keys()).filter(key => key.startsWith('category_'))
    projectKeys.forEach(key => {
      const cachedProjects = treeState.loadedNodes.get(key)
      if (cachedProjects) {
        const targetProject = cachedProjects.find(p => p.id === indicatorData.projectId)
        if (targetProject) {
          const categoryId = parseInt(key.split('_')[1])
          TreeManager.clearNodeCache('category', categoryId)
          console.log('🗑️ 清除父级项目类型缓存:', categoryId)
        }
      }
    })
  } else {
    // 如果无法确定具体项目，清除所有指标缓存
    const projectKeys = Array.from(treeState.loadedNodes.keys()).filter(key => key.startsWith('project_'))
    projectKeys.forEach(key => {
      const projectId = parseInt(key.split('_')[1])
      TreeManager.clearNodeCache('project', projectId)
    })
  }

  // 强制刷新表格以显示最新数据
  TreeManager.forceRefreshTable()
}

// 添加
const handleAdd = (type: string) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法添加')
    return
  }

  if (type === 'testItem') {
    testItemDialogRef.value.open('create', null, factoryId)
  } else if (type === 'samplingPoint') {
    samplingDialogRef.value.open('create', null, factoryId)
  }
}

// 编辑
const handleEdit = (type: string, row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法编辑')
    return
  }

  if (type === 'testItem') {
    testItemDialogRef.value.open('update', row, factoryId)
  } else if (type === 'samplingPoint') {
    samplingDialogRef.value.open('update', row, factoryId)
  }
}

// 删除
const handleDelete = async (type: string, row: any) => {
  // 获取当前选中水厂ID
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除该${type === 'testItem' ? '检测项目' : '采样点'}吗?`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    // 调用对应的删除API
    if (type === 'testItem') {
      if (row.type === 'category') {
        await AssayBaseInfoApi.deleteTestCategory(row.id, factoryId)
      } else if (row.type === 'project') {
        await AssayBaseInfoApi.deleteTestProject(row.id, factoryId)
      } else if (row.type === 'indicator') {
        await AssayBaseInfoApi.deleteTestIndicator(row.id, factoryId)
      }
    } else if (type === 'samplingPoint') {
      await AssayBaseInfoApi.deleteSamplingPoint(row.id, factoryId)
    }

    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    fetchTableData(type)
  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 分页大小变化
const handleSizeChange = (type: string) => {
  fetchTableData(type)
}

// 页码变化
const handleCurrentChange = (type: string) => {
  fetchTableData(type)
}

// 懒加载树节点数据 - 重构：使用完整的缓存管理系统
const loadTreeNode = async (row: any, treeNode: any, resolve: Function) => {
  console.log('🌲 懒加载树节点:', row.type, row.id, row.name)

  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    console.warn('⚠️ 未获取到当前水厂ID')
    resolve([])
    return
  }

  try {
    if (row.type === 'category') {
      // 检查缓存
      const cached = TreeManager.getCachedChildren('category', row.id)
      if (cached) {
        console.log('📦 使用缓存的检测项目数据:', cached.length, '条')
        resolve(cached)
        return
      }

      // 防止重复加载
      const loadingKey = TreeManager.getCacheKey('category', row.id)
      if (treeState.loadingNodes.has(loadingKey)) {
        console.log('⏳ 检测项目正在加载中，等待...')
        resolve([])
        return
      }

      treeState.loadingNodes.add(loadingKey)

      console.log('🔄 正在加载检测项目...', row.id, factoryId)
      const res = await AssayBaseInfoApi.getTestProjectListByCategory(row.id, factoryId)

      if (res && res.data) {
        const projects = res.data.map(project => ({
          ...project,
          type: 'project',
          hasChildren: true,
          indicatorCount: project.indicatorNum || 0
        }))

        // 缓存数据
        TreeManager.setCachedChildren('category', row.id, projects)
        console.log('✅ 检测项目加载并缓存成功:', projects.length, '条')
        resolve(projects)
      } else {
        console.warn('⚠️ 检测项目数据格式异常:', res)
        resolve([])
      }

      treeState.loadingNodes.delete(loadingKey)

    } else if (row.type === 'project') {
      // 检查缓存
      const cached = TreeManager.getCachedChildren('project', row.id)
      if (cached) {
        console.log('📦 使用缓存的检测指标数据:', cached.length, '条')
        resolve(cached)
        return
      }

      // 防止重复加载
      const loadingKey = TreeManager.getCacheKey('project', row.id)
      if (treeState.loadingNodes.has(loadingKey)) {
        console.log('⏳ 检测指标正在加载中，等待...')
        resolve([])
        return
      }

      treeState.loadingNodes.add(loadingKey)

      console.log('🔄 正在加载检测指标...', row.id, factoryId)
      const res = await AssayBaseInfoApi.getTestIndicatorListByProject(row.id, factoryId)

      if (res && res.data) {
        const indicators = res.data.map(indicator => ({
          ...indicator,
          type: 'indicator',
          hasChildren: false
        }))

        // 缓存数据
        TreeManager.setCachedChildren('project', row.id, indicators)
        console.log('✅ 检测指标加载并缓存成功:', indicators.length, '条')
        resolve(indicators)
      } else {
        console.warn('⚠️ 检测指标数据格式异常:', res)
        resolve([])
      }

      treeState.loadingNodes.delete(loadingKey)

    } else {
      resolve([])
    }
  } catch (error) {
    console.error('❌ 懒加载树节点数据失败:', error)
    ElMessage.error('加载子节点数据失败')
    resolve([])

    // 清理加载状态
    const loadingKey = row.type === 'category'
      ? TreeManager.getCacheKey('category', row.id)
      : TreeManager.getCacheKey('project', row.id)
    treeState.loadingNodes.delete(loadingKey)
  }
}

// 展开/收起行 - 重构：使用新的状态管理
const handleExpandChange = (row: any, expandedRowsParam: any) => {
  console.log('🔄 展开状态变化:', row.type, row.id, row.name, expandedRowsParam)

  // 更新展开状态
  if (Array.isArray(expandedRowsParam)) {
    // 批量更新展开状态
    treeState.expandedRows.clear()
    expandedRowsParam.forEach(r => treeState.expandedRows.add(r.id))
  } else {
    // 单行展开/收起
    if (expandedRowsParam) {
      treeState.expandedRows.add(row.id)
      console.log('📂 展开节点:', row.name)
    } else {
      treeState.expandedRows.delete(row.id)
      console.log('📁 收起节点:', row.name)
    }
  }
}

// 获取节点层级
const getNodeLevel = (row: any) => {
  if (row.type === 'category') return 0      // 一级：项目类型
  if (row.type === 'project') return 1       // 二级：检测项目
  if (row.type === 'indicator') return 2     // 三级：检测指标
  return 0
}

// 获取表格行的CSS类名
const getRowClassName = ({ row }: { row: any }) => {
  return `tree-level-${getNodeLevel(row)}`
}

// 新增项目类型
const handleAddCategory = () => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法添加')
    return
  }
  testCategoryDialogRef.value.open('create', null, factoryId)
}

// 编辑项目类型
const handleEditCategory = (row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法编辑')
    return
  }
  testCategoryDialogRef.value.open('update', row, factoryId)
}

// 删除项目类型 - 重构：精确的缓存清理
const handleDeleteCategory = async (row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除项目类型"${row.name}"吗？删除后该类型下的所有项目和指标都将被删除！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await AssayBaseInfoApi.deleteTestCategory(row.id, factoryId)

    // 精确清除相关缓存
    TreeManager.clearNodeCache('category', row.id)

    // 清除所有相关的项目缓存（因为项目类型被删除了）
    const childrenIds = treeState.nodeChildren.get(row.id) || []
    childrenIds.forEach(projectId => {
      TreeManager.clearNodeCache('project', projectId)
    })

    console.log('🗑️ 删除项目类型及相关缓存:', row.name, childrenIds.length, '个子项目')

    ElMessage.success('删除成功')

    // 刷新根节点数据和下拉框
    refreshTable('testItem')
    categoryOptionsLoaded.value = false
    loadCategoryOptions(true)

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除项目类型失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 添加检测项目
const handleAddProject = (category: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法添加')
    return
  }
  // 传递类型ID作为第三个参数
  testProjectDialogRef.value.open('create', null, category.id)
}

// 编辑检测项目
const handleEditProject = (row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法编辑')
    return
  }
  testProjectDialogRef.value.open('update', row, factoryId)
}

// 删除检测项目 - 重构：精确的缓存清理
const handleDeleteProject = async (row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除检测项目"${row.name}"吗？删除后该项目下的所有指标都将被删除！`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await AssayBaseInfoApi.deleteTestProject(row.id, factoryId)

    // 精确清除相关缓存
    TreeManager.clearNodeCache('project', row.id)

    // 从父级项目类型的缓存中移除此项目
    if (row.categoryId) {
      TreeManager.removeChildNode(row.categoryId, row.id)
      console.log('🗑️ 从项目类型缓存中移除项目:', row.categoryId, '->', row.id)
    }

    console.log('🗑️ 删除检测项目及相关缓存:', row.name)

    ElMessage.success('删除成功')

    // 强制刷新表格以显示最新数据
    TreeManager.forceRefreshTable()

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除检测项目失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 添加检测指标
const handleAddIndicator = (project: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法添加')
    return
  }
  testIndicatorDialogRef.value.open('create', null, project.id)
}

// 查看指标详情
const handleViewIndicator = (row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法查看')
    return
  }
  testIndicatorDialogRef.value.open('view', row, factoryId)
}

// 编辑检测指标
const handleEditIndicator = (row: any) => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法编辑')
    return
  }
  testIndicatorDialogRef.value.open('update', row, factoryId)
}

// 删除检测指标
const handleDeleteIndicator = async (row: any) => {
  // 获取当前选中水厂ID
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法删除')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除检测指标"${row.name}"吗？`, '警告', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await AssayBaseInfoApi.deleteTestIndicator(row.id, factoryId)

    // 精确清除相关缓存
    if (row.projectId) {
      TreeManager.removeChildNode(row.projectId, row.id)
      console.log('🗑️ 从检测项目缓存中移除指标:', row.projectId, '->', row.id)

      // 同时需要更新父级项目的指标数量显示，清除项目类型缓存
      const categoryKeys = Array.from(treeState.loadedNodes.keys()).filter(key => key.startsWith('category_'))
      categoryKeys.forEach(key => {
        const cachedProjects = treeState.loadedNodes.get(key)
        if (cachedProjects) {
          const targetProject = cachedProjects.find(p => p.id === row.projectId)
          if (targetProject) {
            const categoryId = parseInt(key.split('_')[1])
            TreeManager.clearNodeCache('category', categoryId)
            console.log('🗑️ 清除父级项目类型缓存以更新指标数量:', categoryId)
          }
        }
      })
    }

    console.log('🗑️ 删除检测指标:', row.name)

    ElMessage.success('删除成功')

    // 强制刷新表格以显示最新数据
    TreeManager.forceRefreshTable()

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('删除检测指标失败:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 下拉框变化处理方法
const handleCategoryChange = () => {
  // 当项目类型下拉框变化时，重新加载数据
  fetchTableData('testItem')
}

const handleSamplingPointTypeChange = () => {
  // 当采样点类型下拉框变化时，重新加载数据
  fetchTableData('samplingPoint')
}

// 监听标签页切换
watch(activeTab, (newVal) => {
  fetchTableData(newVal)
  // 切换到检测项目管理时，加载类型选项（使用缓存）
  if (newVal === 'testItem') {
    loadCategoryOptions(false) // 不强制重新加载，使用缓存
  }
})

// 监听水厂变化，重新加载数据
watch(() => appStore.currentStation, (newStation, oldStation) => {
  if (newStation && newStation.id) {
    console.log('🔄 水厂切换，重新加载数据:', newStation)

    // 如果水厂ID发生变化，清除缓存并重新加载
    const factoryIdChanged = oldStation?.id !== newStation.id
    if (factoryIdChanged) {
      categoryOptionsLoaded.value = false // 清除缓存标记
      expandedRows.value = [] // 清除展开状态
    }

    fetchTableData(activeTab.value)
    // 重新加载检测项目类型选项（强制重新加载）
    loadCategoryOptions(true)
  }
}, { deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  // 初始化水厂信息
  appStore.initCurrentStation()

  // 初始化表格引用
  nextTick(() => {
    treeState.tableRef = tableRef.value
  })

  // 如果有水厂信息，则加载数据
  if (appStore.currentStation?.id) {
    fetchTableData(activeTab.value)
    // 加载检测项目类型选项（首次加载）
    loadCategoryOptions(false)
  } else {
    console.warn('⚠️ 组件挂载时未获取到水厂信息')
  }

  console.log('🚀 基础信息管理页面初始化完成，树形缓存管理系统已启动')
})
</script>

<style scoped>
.search-form {
  margin-bottom: 1rem;
}

/* 树形结构样式 */
.tree-node {
  position: relative;
}

.tree-indent {
  flex-shrink: 0;
}

/* 不同层级的背景色 */
:deep(.el-table__row) {
  &.tree-level-0 {
    background-color: #f8faff;
  }

  &.tree-level-1 {
    background-color: #f0f9f0;
  }

  &.tree-level-2 {
    background-color: #fff7e6;
  }
}

/* 悬停效果 */
:deep(.el-table__row:hover) {
  &.tree-level-0 {
    background-color: #e6f0ff !important;
  }

  &.tree-level-1 {
    background-color: #e6f7e6 !important;
  }

  &.tree-level-2 {
    background-color: #fff2d9 !important;
  }
}

/* 树形连接线 */
.tree-node::before {
  content: '';
  position: absolute;
  left: 10px;
  top: 0;
  bottom: 50%;
  width: 1px;
  background-color: #dcdfe6;
}

.tree-level-0 .tree-node::before {
  display: none;
}

.tree-level-1 .tree-node::before {
  left: 20px;
}

.tree-level-2 .tree-node::before {
  left: 40px;
}

/* 水平连接线 */
.tree-node::after {
  content: '';
  position: absolute;
  left: 10px;
  top: 50%;
  width: 10px;
  height: 1px;
  background-color: #dcdfe6;
}

.tree-level-0 .tree-node::after {
  display: none;
}

.tree-level-1 .tree-node::after {
  left: 20px;
}

.tree-level-2 .tree-node::after {
  left: 40px;
}

/* 标签样式优化 */
.el-tag {
  font-weight: 500;
  border-radius: 4px;
}

/* 字体大小层级 */
.tree-level-0 span {
  font-size: 16px;
}

.tree-level-1 span {
  font-size: 14px;
}

.tree-level-2 span {
  font-size: 13px;
}
</style>
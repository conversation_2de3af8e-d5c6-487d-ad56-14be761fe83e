import request from '@/config/axios'
import { useAppStore } from '@/store/modules/app'

// ==================== 类型定义 ====================

// 检验任务相关类型
export interface AssayTestTaskVO {
  id?: number
  taskCode?: string
  factoryId: number
  sampleId: number
  testIndicator: {
    id: number
    name: string
    unit: string
    method: string
    equipment: string
  }
  assignedToId?: number
  assignedToName?: string
  priority: 'low' | 'normal' | 'high' | 'urgent'
  dueDate: string
  status: 'pending' | 'processing' | 'completed'
  isRecheck: boolean
  parentTaskId?: number
  recheckReason?: string
  acceptTime?: string
  startTime?: string
  completeTime?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 检验数据相关类型
export interface AssayTestDataVO {
  id?: number
  factoryId: number
  taskId: number
  sampleCode: string
  testIndicatorId: number
  testIndicatorName: string
  testValue: number
  testUnit: string
  testMethod?: string
  testInstrument?: string
  testDate: string
  testPersonId: number
  testPersonName?: string
  status: 'entered' | 'reviewed' | 'exception'
  isRecheck: boolean
  parentDataId?: number
  recheckReason?: string
  standardValue?: number
  standardRange?: string
  isQualified?: boolean
  remark?: string
  createTime?: string
  updateTime?: string
}

// 检验单记录相关类型
export interface AssayTestRecordVO {
  id?: number
  factoryId: number
  recordCode?: string
  testId: number
  testDate: string
  testPersonId: number
  testIndicatorId: number
  testValue: number
  fileUrl?: string
  remark?: string
  createTime?: string
  updateTime?: string
}

// 请求参数类型
export interface ImportTaskFromSubmissionReqVO {
  factoryId: number
  filePath: string
  importType: 'normal' | 'urgent'
  remark?: string
}

export interface TestTaskCreateReqVO {
  factoryId: number
  sampleId: number
  testIndicator: {
    id: number
    name: string
    unit: string
    method: string
    equipment: string
  }
  assignedToId?: number
  priority: 'low' | 'normal' | 'high' | 'urgent'
  dueDate: string
  remark?: string
}

export interface TestTaskDispatchReqVO {
  taskId: number
  assignedToId: number
  specialRequirements?: string
}

export interface TestTaskCompleteReqVO {
  taskId: number
  testResult: {
    testValue: number
    testMethod?: string
    testInstrument?: string
    remark?: string
  }
}

export interface BatchDispatchReqVO {
  taskIds: number[]
  assignedToId: number
  specialRequirements?: string
}

export interface BatchCompleteReqVO {
  taskIds: number[]
  testResults: {
    taskId: number
    testValue: number
    testMethod?: string
    testInstrument?: string
    remark?: string
  }[]
}

export interface TestDataCreateReqVO {
  factoryId: number
  taskId: number
  sampleCode: string
  testIndicatorId: number
  testValue: number
  testUnit: string
  testMethod?: string
  testInstrument?: string
  testDate: string
  testPersonId: number
  remark?: string
}

export interface TestDataUpdateReqVO {
  id: number
  factoryId: number
  taskId: number
  sampleCode: string
  testIndicatorId: number
  testValue: number
  testUnit: string
  testMethod?: string
  testInstrument?: string
  testDate: string
  testPersonId: number
  status: 'entered' | 'reviewed' | 'exception'
  isRecheck: boolean
  parentDataId?: number
  recheckReason?: string
  standardValue?: number
  standardRange?: string
  isQualified?: boolean
  remark?: string
}

export interface TestRecordCreateReqVO {
  factoryId: number
  recordCode?: string
  testId: number
  testDate: string
  testPersonId: number
  testIndicatorId: number
  testValue: number
  fileUrl?: string
  remark?: string
}

export interface TestRecordUpdateReqVO {
  id: number
  factoryId: number
  recordCode?: string
  testId: number
  testDate: string
  testPersonId: number
  testIndicatorId: number
  testValue: number
  fileUrl?: string
  remark?: string
}

// ==================== API 接口 ====================

/**
 * 获取全局水厂ID
 */
const getFactoryId = (): number => {
  const appStore = useAppStore()
  return appStore.currentStation?.id || 1
}

/**
 * 检验执行管理模块API
 */
export const AssayDataManagementApi = {
  // ==================== 检验任务管理 ====================
  
  /**
   * 从送检单导入任务
   */
  importTaskFromSubmission: async (data: ImportTaskFromSubmissionReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-task/import-from-submission',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取检验任务分页列表
   */
  getTestTaskPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/test-task/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 创建检验任务
   */
  createTestTask: async (data: TestTaskCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-task/create',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 下发检验任务
   */
  dispatchTestTask: async (data: TestTaskDispatchReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-task/dispatch',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 完成检验任务
   */
  completeTestTask: async (data: TestTaskCompleteReqVO) => {
    return await request.putOriginal({
      url: '/assay/test-task/complete',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 批量下发任务
   */
  batchDispatchTasks: async (data: BatchDispatchReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-task/batch-dispatch',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 批量完成任务
   */
  batchCompleteTasks: async (data: BatchCompleteReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-task/batch-complete',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取检验任务详情
   */
  getTestTask: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/test-task/get',
      params: { 
        id, 
        factoryId: getFactoryId() 
      }
    })
  },

  /**
   * 删除检验任务
   */
  deleteTestTask: async (id: number) => {
    return await request.deleteOriginal({
      url: '/assay/test-task/delete',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 检验数据管理 ====================

  /**
   * 从检验单导入数据
   */
  importDataFromTestRecord: async (filePath: string) => {
    return await request.postOriginal({
      url: '/assay/test-data/import-from-record',
      data: {
        filePath,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取检验数据分页列表
   */
  getTestDataPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/test-data/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 创建检验数据
   */
  createTestData: async (data: TestDataCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-data/create',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 更新检验数据
   */
  updateTestData: async (data: TestDataUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/test-data/update',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 删除检验数据
   */
  deleteTestData: async (id: number) => {
    return await request.deleteOriginal({
      url: '/assay/test-data/delete',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取检验数据详情
   */
  getTestData: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/test-data/get',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 检验单记录管理 ====================

  /**
   * 获取检验单记录分页列表
   */
  getTestRecordPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/test-record/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 创建检验单记录
   */
  createTestRecord: async (data: TestRecordCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-record/create',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 更新检验单记录
   */
  updateTestRecord: async (data: TestRecordUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/test-record/update',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 删除检验单记录
   */
  deleteTestRecord: async (id: number) => {
    return await request.deleteOriginal({
      url: '/assay/test-record/delete',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取检验单记录详情
   */
  getTestRecord: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/test-record/get',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 批量生成检验单
   */
  batchGenerateTestRecord: async (taskIds: number[]) => {
    return await request.postOriginal({
      url: '/assay/test-record/batch-generate',
      data: {
        taskIds,
        factoryId: getFactoryId()
      }
    })
  }
}

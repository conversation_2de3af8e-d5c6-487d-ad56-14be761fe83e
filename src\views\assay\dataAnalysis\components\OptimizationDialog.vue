<template>
  <Dialog v-model="dialogVisible" title="执行优化建议" width="800px">
    <div class="optimization-content">
      <!-- 基本信息 -->
      <el-descriptions :column="2" border>
        <el-descriptions-item label="计划名称">{{ formData.planName }}</el-descriptions-item>
        <el-descriptions-item label="负责人">{{ formData.assignee }}</el-descriptions-item>
        <el-descriptions-item label="水厂">{{ formData.plant }}</el-descriptions-item>
        <el-descriptions-item label="当前效率">
          <el-rate v-model="formData.efficiency" :max="5" disabled show-score />
        </el-descriptions-item>
        <el-descriptions-item label="完成率">
          <el-progress :percentage="formData.completionRate" :color="getProgressColor(formData.completionRate)" />
        </el-descriptions-item>
        <el-descriptions-item label="执行状态">
          <el-tag :type="getStatusType(formData.status)">
            {{ getStatusLabel(formData.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">问题分析</el-divider>

      <!-- 问题分析 -->
      <div class="analysis-section">
        <el-alert
          v-for="issue in identifiedIssues"
          :key="issue.type"
          :title="issue.title"
          :description="issue.description"
          :type="issue.severity"
          show-icon
          :closable="false"
          class="issue-alert"
        />
      </div>

      <el-divider content-position="center">优化建议</el-divider>

      <!-- 优化建议 -->
      <div class="suggestions-section">
        <el-timeline>
          <el-timeline-item
            v-for="(suggestion, index) in optimizationSuggestions"
            :key="index"
            :timestamp="suggestion.priority"
            :type="getSuggestionType(suggestion.priority)"
          >
            <el-card>
              <div class="suggestion-header">
                <h4>{{ suggestion.title }}</h4>
                <el-tag :type="getSuggestionTagType(suggestion.impact)">
                  {{ suggestion.impact }}
                </el-tag>
              </div>
              <p>{{ suggestion.description }}</p>
              <div class="suggestion-details">
                <div class="detail-item">
                  <span class="label">预期效果：</span>
                  <span class="value">{{ suggestion.expectedResult }}</span>
                </div>
                <div class="detail-item">
                  <span class="label">实施难度：</span>
                  <el-rate v-model="suggestion.difficulty" :max="5" disabled size="small" />
                </div>
                <div class="detail-item">
                  <span class="label">预计时间：</span>
                  <span class="value">{{ suggestion.estimatedTime }}</span>
                </div>
              </div>
              <div class="suggestion-actions">
                <el-button type="primary" size="small" @click="handleApplySuggestion(suggestion)">
                  采纳建议
                </el-button>
                <el-button size="small" @click="handleViewDetail(suggestion)">
                  查看详情
                </el-button>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </div>

      <el-divider content-position="center">执行计划</el-divider>

      <!-- 执行计划 -->
      <div class="execution-plan">
        <el-form :model="planForm" label-width="100px">
          <el-form-item label="优化目标">
            <el-input
              v-model="planForm.target"
              type="textarea"
              :rows="2"
              placeholder="请输入优化目标"
            />
          </el-form-item>
          <el-form-item label="执行时间">
            <el-date-picker
              v-model="planForm.executionDate"
              type="datetimerange"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
            />
          </el-form-item>
          <el-form-item label="负责人">
            <el-select v-model="planForm.assignee" placeholder="选择负责人">
              <el-option label="张三" value="zhangsan" />
              <el-option label="李四" value="lisi" />
              <el-option label="王五" value="wangwu" />
            </el-select>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="planForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>
    </div>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="handleCreatePlan">制定优化计划</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { Dialog } from '@/components/Dialog'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'OptimizationDialog' })

// 对话框显示状态
const dialogVisible = ref(false)

// 表单数据
const formData = reactive({
  planName: '',
  assignee: '',
  plant: '',
  efficiency: 0,
  completionRate: 0,
  status: ''
})

// 识别的问题
const identifiedIssues = ref([
  {
    type: 'time',
    title: '时间管理问题',
    description: '任务执行时间经常延期，建议优化时间安排',
    severity: 'warning'
  },
  {
    type: 'resource',
    title: '资源配置不足',
    description: '人员配置可能不够充足，影响执行效率',
    severity: 'error'
  },
  {
    type: 'process',
    title: '流程优化空间',
    description: '当前流程存在优化空间，可以提升整体效率',
    severity: 'info'
  }
])

// 优化建议
const optimizationSuggestions = ref([
  {
    title: '增加人员配置',
    description: '建议在高峰期增加1-2名检测人员，提升采样效率',
    priority: '高优先级',
    impact: '高影响',
    expectedResult: '提升30%执行效率',
    difficulty: 2,
    estimatedTime: '1周内实施'
  },
  {
    title: '优化采样路线',
    description: '重新规划采样路线，减少往返时间，提升工作效率',
    priority: '中优先级',
    impact: '中影响',
    expectedResult: '节省20%时间成本',
    difficulty: 3,
    estimatedTime: '2周内实施'
  },
  {
    title: '引入移动设备',
    description: '使用移动设备进行现场数据录入，减少后续录入工作',
    priority: '低优先级',
    impact: '中影响',
    expectedResult: '提升15%数据准确性',
    difficulty: 4,
    estimatedTime: '1个月内实施'
  }
])

// 计划表单
const planForm = reactive({
  target: '',
  executionDate: [],
  assignee: '',
  remark: ''
})

// 打开对话框
const open = (data: any) => {
  Object.assign(formData, data)
  
  // 根据数据生成个性化建议
  generatePersonalizedSuggestions(data)
  
  dialogVisible.value = true
}

// 生成个性化建议
const generatePersonalizedSuggestions = (data: any) => {
  // 根据完成率和效率生成不同的建议
  if (data.completionRate < 70) {
    identifiedIssues.value.unshift({
      type: 'completion',
      title: '完成率偏低',
      description: `当前完成率仅为${data.completionRate}%，需要重点关注`,
      severity: 'error'
    })
  }
  
  if (data.efficiency < 3) {
    identifiedIssues.value.unshift({
      type: 'efficiency',
      title: '效率评级较低',
      description: `当前效率评级为${data.efficiency}星，有较大提升空间`,
      severity: 'warning'
    })
  }
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67C23A'
  if (percentage >= 70) return '#E6A23C'
  return '#F56C6C'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap = {
    'completed': 'success',
    'delayed': 'warning',
    'ongoing': 'info',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labelMap = {
    'completed': '按时完成',
    'delayed': '延期完成',
    'ongoing': '进行中',
    'pending': '未开始'
  }
  return labelMap[status] || '未知'
}

// 获取建议类型
const getSuggestionType = (priority: string) => {
  const typeMap = {
    '高优先级': 'danger',
    '中优先级': 'warning',
    '低优先级': 'info'
  }
  return typeMap[priority] || 'info'
}

// 获取建议标签类型
const getSuggestionTagType = (impact: string) => {
  const typeMap = {
    '高影响': 'danger',
    '中影响': 'warning',
    '低影响': 'info'
  }
  return typeMap[impact] || 'info'
}

// 采纳建议
const handleApplySuggestion = (suggestion: any) => {
  ElMessage.success(`已采纳建议：${suggestion.title}`)
  emit('suggestion-applied', suggestion)
}

// 查看详情
const handleViewDetail = (suggestion: any) => {
  ElMessage.info(`查看建议详情：${suggestion.title}`)
}

// 制定优化计划
const handleCreatePlan = () => {
  if (!planForm.target) {
    ElMessage.warning('请输入优化目标')
    return
  }
  
  ElMessage.success('优化计划已制定')
  emit('plan-created', planForm)
  dialogVisible.value = false
}

// 定义事件
const emit = defineEmits(['suggestion-applied', 'plan-created'])

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.optimization-content {
  .analysis-section {
    .issue-alert {
      margin-bottom: 0.5rem;
    }
  }

  .suggestions-section {
    .suggestion-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 0.5rem;

      h4 {
        margin: 0;
        color: #303133;
      }
    }

    .suggestion-details {
      margin: 1rem 0;

      .detail-item {
        display: flex;
        align-items: center;
        margin-bottom: 0.5rem;

        .label {
          font-weight: bold;
          color: #606266;
          min-width: 80px;
        }

        .value {
          color: #303133;
        }
      }
    }

    .suggestion-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .execution-plan {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 0.25rem;
  }
}
</style>

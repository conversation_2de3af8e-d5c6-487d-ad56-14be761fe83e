import request from '@/config/axios'

// 统计数据接口响应类型
export interface StatisticsVO {
  factoryCount: number  // 厂站个数
  deviceCount: number   // 设备个数
  pointCount: number    // 点位个数
}

// 厂站/分组接口响应类型
export interface GroupVO {
  id: number
  groupCode: string
  groupName: string
}

// 设备分页查询请求参数
export interface DevicePageReqVO {
  currentPage: number
  pageSize: number
  factoryId?: number
  deviceCode?: string
  deviceName?: string
}

// 设备信息类型
export interface DeviceVO {
  id: number
  deviceCode: string
  deviceName: string
  driverType: string
  factoryId: string
  factoryName: string
  remark: string
}

// 设备分页响应类型
export interface DevicePageRespVO {
  records: DeviceVO[]
  total: number
  size: number
  current: number
  pages: number
}

// 点位分页查询请求参数
export interface PointPageReqVO {
  currentPage: number
  pageSize: number
  deviceId?: number
  pointCode?: string
  pointName?: string
}

// 点位信息类型
export interface PointVO {
  id: number
  pointCode: string
  pointName: string
  pointUnit: string
  enableFlag: string
  latestValue: string
}

// 点位分页响应类型
export interface PointPageRespVO {
  records: PointVO[]
  total: number
  size: number
  current: number
  pages: number
}

/**
 * 物联网设备管理 API
 */
export const IotDeviceApi = {
  /**
   * 获取系统统计数据
   */
  getStatistics: async (): Promise<StatisticsVO> => {
    return await request.post({ url: '/acq-conf/iot-device/statistics' })
  },

  /**
   * 获取厂站/分组列表
   */
  getGroupList: async (): Promise<GroupVO[]> => {
    return await request.get({ url: '/acq-conf/iot-group/list' })
  },

  /**
   * 设备分页查询
   */
  getDevicePage: async (params: DevicePageReqVO): Promise<DevicePageRespVO> => {
    return await request.post({ url: '/acq-conf/iot-device/device-page', data: params })
  },

  /**
   * 点位分页查询
   */
  getPointPage: async (params: PointPageReqVO): Promise<PointPageRespVO> => {
    return await request.post({ url: '/acq-conf/iot-point/point-page', data: params })
  }
}

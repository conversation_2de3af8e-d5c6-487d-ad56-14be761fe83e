import request from '@/config/axios'

// 通用能耗分析请求参数
export interface EnergyAnalysisCommonReqVO {
  factoryId: number // 厂站ID（必填）
  nodeId: number // 节点ID（必填）
  startTime: string // 开始时间（必填，格式：YYYY-MM-DD HH:mm:ss）
  endTime: string // 结束时间（必填，格式：YYYY-MM-DD HH:mm:ss）
}

// 能耗分析请求参数（兼容旧接口）
export interface EnergyAnalysisReqVO {
  nodeId?: number // 节点ID（可选，不传则查询所有）
  indicatorId?: number // 指标ID（可选，不传则查询该节点下所有指标）
  factoryId: number // 厂站ID
  startTime?: string // 开始时间（可选）
  endTime?: string // 结束时间（可选）

}

// 能耗分析响应数据
export interface EnergyAnalysisRespVO {
  currentTotal: number // 当期总能耗
  lastYearTotal: number // 去年同期总能耗
  lastPeriodTotal: number // 上期总能耗
  yearOnYearGrowthRate: number // 同比增长率
  periodOnPeriodGrowthRate: number // 环比增长率
  energyPerTon: number // 当期吨水能耗
  lastYearEnergyPerTon: number // 去年同期吨水能耗
  lastPeriodEnergyPerTon: number // 上期吨水能耗
  unit: string // 单位
  timeRangeDesc: string // 时间范围描述
  indicatorName: string // 指标名称
  nodeName: string // 节点名称
}

// 获取能耗分析数据
export const getEnergyAnalysis = (data: EnergyAnalysisReqVO) => {
  return request.post<EnergyAnalysisRespVO>({ 
    url: '/energy/analysis/statistics', 
    data 
  })
}

// 能耗趋势分析请求参数
export interface EnergyTrendReqVO {
  nodeId?: number // 节点ID
  indicatorId?: number // 指标ID
  factoryId: number // 厂站ID
  startDate: string // 开始日期
  endDate: string // 结束日期
}

// 能耗趋势分析响应数据
export interface EnergyTrendRespVO {
  dates: string[] // 时间轴数据
  values: number[] // 能耗值数据
  unit: string // 单位
}

// 获取能耗趋势数据
export const getEnergyTrend = (data: EnergyTrendReqVO) => {
  return request.post<EnergyTrendRespVO>({ 
    url: '/energy/analysis/trend', 
    data 
  })
}

// 能耗对比分析响应数据
export interface EnergyCompareRespVO {
  currentData: {
    dates: string[] // 时间轴数据
    values: number[] // 当期能耗值数据
  }
  comparisonData: {
    dates: string[] // 时间轴数据
    values: number[] // 对比期能耗值数据
  }
  unit: string // 单位
  comparisonType: string // 对比类型（同比、环比等）
}

// 获取总能耗趋势数据
export const getTotalEnergyTrend = (data: EnergyAnalysisCommonReqVO) => {
  return request.post<EnergyTrendRespVO>({ 
    url: '/energy/analysis/trend/total', 
    data 
  })
}

// 获取能耗同比对比数据
export const getEnergyYearOnYearCompare = (data: EnergyAnalysisCommonReqVO) => {
  return request.post<EnergyCompareRespVO>({ 
    url: '/energy/analysis/compare/year-on-year', 
    data 
  })
}

// 获取吨水能耗趋势数据
export const getEnergyPerTonTrend = (data: EnergyAnalysisCommonReqVO) => {
  return request.post<EnergyTrendRespVO>({ 
    url: '/energy/analysis/trend/per-ton', 
    data 
  })
}

// 获取能耗对比分析数据
export const getEnergyCompare = (data: EnergyAnalysisCommonReqVO) => {
  return request.post<EnergyCompareRespVO>({ 
    url: '/energy/analysis/compare/analysis', 
    data 
  })
}

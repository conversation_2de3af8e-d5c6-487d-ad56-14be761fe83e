<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="dialog-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品编号" prop="sampleCode">
            <el-input v-model="formData.sampleCode" placeholder="请输入样品编号" :disabled="mode === 'view'" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检验类型" prop="testType">
            <el-select v-model="formData.testType" placeholder="请选择检验类型" style="width: 100%" :disabled="mode === 'view'">
              <el-option label="日常检验" value="daily" />
              <el-option label="专项检验" value="special" />
              <el-option label="委托检验" value="entrusted" />
              <el-option label="应急检验" value="emergency" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="24">
          <el-form-item label="检验项目" prop="testItems">
            <el-select 
              v-model="formData.testItems" 
              multiple 
              placeholder="请选择检验项目" 
              style="width: 100%"
              :disabled="mode === 'view'"
            >
              <el-option label="COD" value="COD" />
              <el-option label="BOD5" value="BOD5" />
              <el-option label="氨氮" value="氨氮" />
              <el-option label="总磷" value="总磷" />
              <el-option label="总氮" value="总氮" />
              <el-option label="pH" value="pH" />
              <el-option label="悬浮物" value="悬浮物" />
              <el-option label="色度" value="色度" />
              <el-option label="浊度" value="浊度" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="优先级" prop="priority">
            <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%" :disabled="mode === 'view'">
              <el-option label="高" value="high" />
              <el-option label="中" value="normal" />
              <el-option label="低" value="low" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="要求完成日期" prop="requiredDate">
            <el-date-picker
              v-model="formData.requiredDate"
              type="date"
              placeholder="选择要求完成日期"
              style="width: 100%"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="检验员" prop="tester">
            <el-select v-model="formData.tester" placeholder="请选择检验员" style="width: 100%" :disabled="mode === 'view'">
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="王五" value="王五" />
              <el-option label="赵六" value="赵六" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采样点" prop="samplingPoint">
            <el-input v-model="formData.samplingPoint" placeholder="请输入采样点" :disabled="mode === 'view'" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品性质" prop="sampleNature">
            <el-select v-model="formData.sampleNature" placeholder="请选择样品性质" style="width: 100%" :disabled="mode === 'view'">
              <el-option label="进水" value="进水" />
              <el-option label="出水" value="出水" />
              <el-option label="污泥" value="污泥" />
              <el-option label="其他" value="其他" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采样日期" prop="samplingDate">
            <el-date-picker
              v-model="formData.samplingDate"
              type="date"
              placeholder="选择采样日期"
              style="width: 100%"
              :disabled="mode === 'view'"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="备注" prop="description">
        <el-input 
          v-model="formData.description" 
          type="textarea" 
          :rows="3" 
          placeholder="请输入备注信息"
          :disabled="mode === 'view'"
        />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">{{ mode === 'view' ? '关闭' : '取消' }}</el-button>
        <el-button v-if="mode !== 'view'" type="primary" @click="handleConfirm" :loading="loading">
          确定
        </el-button>
        <el-button v-if="mode === 'view'" type="primary" @click="handlePrint">
          <el-icon><Printer /></el-icon>打印
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Printer } from '@element-plus/icons-vue'

defineOptions({ name: 'TestDispatchDialog' })

// Props
interface Props {
  modelValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const emit = defineEmits<Emits>()

// 状态
const mode = ref<'create' | 'update' | 'view'>('create')
const loading = ref(false)
const currentData = ref<any>(null)

// 表单数据
const formData = reactive({
  sampleCode: '',
  testType: '',
  testItems: [] as string[],
  priority: 'normal',
  requiredDate: '',
  tester: '',
  samplingPoint: '',
  sampleNature: '',
  samplingDate: '',
  description: ''
})

// 表单验证规则
const formRules = reactive({
  sampleCode: [{ required: true, message: '请输入样品编号', trigger: 'blur' }],
  testType: [{ required: true, message: '请选择检验类型', trigger: 'change' }],
  testItems: [{ required: true, message: '请选择检验项目', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  requiredDate: [{ required: true, message: '请选择要求完成日期', trigger: 'change' }],
  tester: [{ required: true, message: '请选择检验员', trigger: 'change' }],
  samplingPoint: [{ required: true, message: '请输入采样点', trigger: 'blur' }],
  sampleNature: [{ required: true, message: '请选择样品性质', trigger: 'change' }],
  samplingDate: [{ required: true, message: '请选择采样日期', trigger: 'change' }]
})

// 计算属性
const dialogVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const dialogTitle = computed(() => {
  const titleMap = {
    create: '新增检验下发',
    update: '编辑检验下发',
    view: '查看检验下发详情'
  }
  return titleMap[mode.value]
})

const formRef = ref()

// 打开对话框
const open = (type: 'create' | 'update' | 'view', data?: any) => {
  mode.value = type
  currentData.value = data
  
  if (data) {
    Object.assign(formData, data)
  } else {
    resetForm()
  }
  
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

// 重置表单
const resetForm = () => {
  Object.assign(formData, {
    sampleCode: '',
    testType: '',
    testItems: [],
    priority: 'normal',
    requiredDate: '',
    tester: '',
    samplingPoint: '',
    sampleNature: '',
    samplingDate: '',
    description: ''
  })
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认操作
const handleConfirm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const result = {
      ...formData,
      id: currentData.value?.id || Date.now(),
      dispatchDate: new Date().toISOString().split('T')[0],
      status: 'pending'
    }
    
    emit('confirm', result)
    ElMessage.success(mode.value === 'create' ? '新增成功' : '更新成功')
    handleClose()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    loading.value = false
  }
}

// 打印
const handlePrint = () => {
  window.print()
  ElMessage.success('检验下发单已发送到打印机')
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.dialog-form {
  padding: 0 20px;
}

.dialog-footer {
  text-align: right;
}

@media print {
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }
}
</style>

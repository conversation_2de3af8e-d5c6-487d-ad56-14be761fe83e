<template>
  <div class="operation-guide">
    <!-- 引导遮罩 -->
    <div v-if="visible" class="guide-overlay" @click="handleOverlayClick">
      <!-- 高亮区域 -->
      <div 
        class="highlight-area"
        :style="highlightStyle"
      ></div>
      
      <!-- 引导卡片 -->
      <div 
        class="guide-card"
        :style="cardStyle"
      >
        <div class="guide-header">
          <div class="step-info">
            <span class="step-number">{{ currentStepIndex + 1 }}</span>
            <span class="step-total">/ {{ steps.length }}</span>
          </div>
          <el-button 
            type="text" 
            size="small" 
            @click="close"
            class="close-btn"
          >
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        
        <div class="guide-content">
          <h4 class="guide-title">{{ currentStep.title }}</h4>
          <p class="guide-description">{{ currentStep.description }}</p>
          
          <!-- 操作提示 -->
          <div v-if="currentStep.action" class="guide-action">
            <el-icon class="action-icon"><InfoFilled /></el-icon>
            <span>{{ currentStep.action }}</span>
          </div>
          
          <!-- 注意事项 -->
          <div v-if="currentStep.warning" class="guide-warning">
            <el-icon class="warning-icon"><Warning /></el-icon>
            <span>{{ currentStep.warning }}</span>
          </div>
        </div>
        
        <div class="guide-footer">
          <div class="progress-dots">
            <span 
              v-for="(step, index) in steps" 
              :key="index"
              class="dot"
              :class="{ active: index === currentStepIndex, completed: index < currentStepIndex }"
              @click="goToStep(index)"
            ></span>
          </div>
          
          <div class="guide-actions">
            <el-button 
              v-if="currentStepIndex > 0" 
              size="small" 
              @click="prevStep"
            >
              上一步
            </el-button>
            <el-button 
              v-if="currentStepIndex < steps.length - 1" 
              type="primary" 
              size="small" 
              @click="nextStep"
            >
              下一步
            </el-button>
            <el-button 
              v-else 
              type="primary" 
              size="small" 
              @click="finish"
            >
              完成
            </el-button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 快速帮助按钮 -->
    <el-button 
      v-if="!visible && showHelpButton"
      class="help-button"
      type="primary"
      circle
      @click="start"
    >
      <el-icon><QuestionFilled /></el-icon>
    </el-button>
  </div>
</template>

<script lang="ts" setup>
import { ref, computed, nextTick } from 'vue'
import { Close, InfoFilled, Warning, QuestionFilled } from '@element-plus/icons-vue'

interface GuideStep {
  target: string // CSS选择器
  title: string
  description: string
  action?: string // 操作提示
  warning?: string // 注意事项
  position?: 'top' | 'bottom' | 'left' | 'right'
}

interface Props {
  steps: GuideStep[]
  showHelpButton?: boolean
  autoStart?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  showHelpButton: true,
  autoStart: false
})

const emit = defineEmits(['finish', 'step-change'])

const visible = ref(false)
const currentStepIndex = ref(0)

const currentStep = computed(() => props.steps[currentStepIndex.value])

// 计算高亮区域样式
const highlightStyle = computed(() => {
  if (!visible.value || !currentStep.value) return {}
  
  const element = document.querySelector(currentStep.value.target)
  if (!element) return {}
  
  const rect = element.getBoundingClientRect()
  return {
    left: `${rect.left - 4}px`,
    top: `${rect.top - 4}px`,
    width: `${rect.width + 8}px`,
    height: `${rect.height + 8}px`
  }
})

// 计算引导卡片位置
const cardStyle = computed(() => {
  if (!visible.value || !currentStep.value) return {}
  
  const element = document.querySelector(currentStep.value.target)
  if (!element) return {}
  
  const rect = element.getBoundingClientRect()
  const position = currentStep.value.position || 'bottom'
  
  let left = rect.left
  let top = rect.bottom + 10
  
  switch (position) {
    case 'top':
      top = rect.top - 200
      break
    case 'bottom':
      top = rect.bottom + 10
      break
    case 'left':
      left = rect.left - 320
      top = rect.top
      break
    case 'right':
      left = rect.right + 10
      top = rect.top
      break
  }
  
  // 确保卡片在视窗内
  const maxLeft = window.innerWidth - 300
  const maxTop = window.innerHeight - 200
  
  left = Math.max(10, Math.min(left, maxLeft))
  top = Math.max(10, Math.min(top, maxTop))
  
  return {
    left: `${left}px`,
    top: `${top}px`
  }
})

// 开始引导
const start = () => {
  visible.value = true
  currentStepIndex.value = 0
  scrollToTarget()
}

// 下一步
const nextStep = () => {
  if (currentStepIndex.value < props.steps.length - 1) {
    currentStepIndex.value++
    scrollToTarget()
    emit('step-change', currentStepIndex.value)
  }
}

// 上一步
const prevStep = () => {
  if (currentStepIndex.value > 0) {
    currentStepIndex.value--
    scrollToTarget()
    emit('step-change', currentStepIndex.value)
  }
}

// 跳转到指定步骤
const goToStep = (index: number) => {
  currentStepIndex.value = index
  scrollToTarget()
  emit('step-change', currentStepIndex.value)
}

// 完成引导
const finish = () => {
  visible.value = false
  emit('finish')
}

// 关闭引导
const close = () => {
  visible.value = false
}

// 点击遮罩层
const handleOverlayClick = (event: MouseEvent) => {
  // 点击高亮区域外关闭引导
  const target = event.target as HTMLElement
  if (target.classList.contains('guide-overlay')) {
    close()
  }
}

// 滚动到目标元素
const scrollToTarget = async () => {
  await nextTick()
  const element = document.querySelector(currentStep.value.target)
  if (element) {
    element.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center' 
    })
  }
}

// 自动开始
if (props.autoStart) {
  nextTick(() => {
    start()
  })
}

defineExpose({
  start,
  close,
  nextStep,
  prevStep,
  goToStep
})
</script>

<style scoped>
.operation-guide {
  position: relative;
}

/* 引导遮罩 */
.guide-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 9999;
}

/* 高亮区域 */
.highlight-area {
  position: absolute;
  background-color: transparent;
  border: 2px solid #409eff;
  border-radius: 4px;
  box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  }
  50% {
    box-shadow: 0 0 0 8px rgba(64, 158, 255, 0.1);
  }
  100% {
    box-shadow: 0 0 0 4px rgba(64, 158, 255, 0.2);
  }
}

/* 引导卡片 */
.guide-card {
  position: absolute;
  width: 300px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  z-index: 10000;
}

.guide-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 16px 0;
}

.step-info {
  font-size: 12px;
  color: #909399;
}

.step-number {
  font-weight: bold;
  color: #409eff;
}

.close-btn {
  padding: 4px;
}

.guide-content {
  padding: 16px;
}

.guide-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.guide-description {
  margin: 0 0 12px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.guide-action {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #f0f9ff;
  border-radius: 4px;
  margin-bottom: 8px;
  font-size: 13px;
  color: #409eff;
}

.guide-warning {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background-color: #fef0f0;
  border-radius: 4px;
  font-size: 13px;
  color: #f56c6c;
}

.guide-footer {
  padding: 0 16px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.progress-dots {
  display: flex;
  gap: 6px;
}

.dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: #dcdfe6;
  cursor: pointer;
  transition: all 0.3s;
}

.dot.active {
  background-color: #409eff;
}

.dot.completed {
  background-color: #67c23a;
}

.guide-actions {
  display: flex;
  gap: 8px;
}

/* 快速帮助按钮 */
.help-button {
  position: fixed;
  bottom: 80px;
  right: 30px;
  z-index: 1000;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .guide-card {
    width: 280px;
  }
  
  .help-button {
    bottom: 60px;
    right: 20px;
  }
}
</style>

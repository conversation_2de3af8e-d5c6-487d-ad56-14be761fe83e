<template>
  <el-dialog v-model="visible" title="指标分类管理" width="37.5rem" :close-on-click-modal="false">
    <div class="category-management">
      <div class="category-toolbar mb-4">
        <el-button type="primary" @click="handleAddRootCategory">
          <el-icon>
            <Plus />
          </el-icon>
          添加根分类
        </el-button>
        <el-button @click="handleExpandAll">
          <el-icon>
            <Expand />
          </el-icon>
          展开全部
        </el-button>
        <el-button @click="handleCollapseAll">
          <el-icon>
            <Fold />
          </el-icon>
          收起全部
        </el-button>
      </div>

      <div class="category-tree">
        <el-tree ref="categoryTreeRef" :key="treeKey" :data="categoryTreeData" node-key="id"
          :default-expand-all="defaultExpandAll" :allow-drop="allowDrop" :allow-drag="allowDrag" draggable
          @node-drop="handleNodeDrop">
          <template #default="{ data }">
            <div class="tree-node-content">
              <div v-if="!data.editing" class="node-display">
                <span class="node-label">{{ data.label }}</span>
                <div class="node-actions">
                  <el-button type="primary" link size="small" @click="handleAddChild(data)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                  </el-button>
                  <el-button type="warning" link size="small" @click="handleEdit(data)">
                    <el-icon>
                      <Edit />
                    </el-icon>
                  </el-button>
                  <el-button type="danger" link size="small" @click="handleDelete(data)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                  </el-button>
                </div>
              </div>
              <div v-else class="node-edit">
                <el-input ref="editInputRef" v-model="data.label" size="small" @blur="handleSaveEdit(data)"
                  @keyup.enter="handleSaveEdit(data)" />
                <div class="edit-actions">
                  <el-button type="success" link size="small" @click="handleSaveEdit(data)">
                    <el-icon>
                      <Check />
                    </el-icon>
                  </el-button>
                  <el-button type="info" link size="small" @click="handleCancelEdit(data)">
                    <el-icon>
                      <Close />
                    </el-icon>
                  </el-button>
                </div>
              </div>
            </div>
          </template>
        </el-tree>
      </div>

      <div class="category-info mt-4">
        <el-alert title="使用说明" type="info" :closable="false" show-icon>
          <template #default>
            <div>
              <p>• 支持拖拽调整分类层级和顺序</p>
              <p>• 删除分类时会同时删除所有子分类</p>
              <p>• 分类名称不能为空</p>
            </div>
          </template>
        </el-alert>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">
          保存
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, nextTick, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Expand, Fold, Edit, Delete, Check, Close } from '@element-plus/icons-vue'

// 分类树数据结构
interface CategoryNode {
  id: string
  label: string
  children?: CategoryNode[]
  editing?: boolean
}

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'save', data: CategoryNode[]): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const categoryTreeRef = ref()
const editInputRef = ref()
const treeKey = ref(0)
const defaultExpandAll = ref(false)

// 分类树数据
const categoryTreeData = ref<CategoryNode[]>([
  {
    id: '1',
    label: '电力系统',
    children: [
      { id: '1-1', label: '电能质量' },
      { id: '1-2', label: '设备效率' }
    ]
  },
  {
    id: '2',
    label: '水处理系统',
    children: [
      { id: '2-1', label: '水质监测' },
      { id: '2-2', label: '处理效果' }
    ]
  },
  {
    id: '3',
    label: '环境监测',
    children: [
      { id: '3-1', label: '噪声监测' },
      { id: '3-2', label: '温度监测' }
    ]
  },
  {
    id: '4',
    label: '设备运行'
  },
  {
    id: '5',
    label: '安全管理'
  }
])

// 生成唯一ID
const generateId = () => {
  return Date.now().toString() + Math.random().toString(36).substr(2, 9)
}

// 添加根分类
const handleAddRootCategory = () => {
  const newCategory: CategoryNode = {
    id: generateId(),
    label: '新分类',
    editing: true
  }
  categoryTreeData.value.push(newCategory)
  nextTick(() => {
    editInputRef.value?.focus()
  })
}

// 添加子分类
const handleAddChild = (parent: CategoryNode) => {
  if (!parent.children) {
    parent.children = []
  }
  const newChild: CategoryNode = {
    id: generateId(),
    label: '新子分类',
    editing: true
  }
  parent.children.push(newChild)
  nextTick(() => {
    editInputRef.value?.focus()
  })
}

// 编辑分类
const handleEdit = (node: CategoryNode) => {
  node.editing = true
  nextTick(() => {
    editInputRef.value?.focus()
  })
}

// 保存编辑
const handleSaveEdit = (node: CategoryNode) => {
  if (!node.label.trim()) {
    ElMessage.warning('分类名称不能为空')
    return
  }
  node.editing = false
}

// 取消编辑
const handleCancelEdit = (node: CategoryNode) => {
  node.editing = false
  // 如果是新添加的节点且取消编辑，则删除
  if (node.label === '新分类' || node.label === '新子分类') {
    handleDelete(node)
  }
}

// 删除分类
const handleDelete = async (node: CategoryNode) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除该分类吗？删除后将同时删除所有子分类。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 递归删除节点
    const deleteNode = (nodes: CategoryNode[], targetId: string): boolean => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].id === targetId) {
          nodes.splice(i, 1)
          return true
        }
        if (nodes[i].children && deleteNode(nodes[i].children!, targetId)) {
          return true
        }
      }
      return false
    }

    deleteNode(categoryTreeData.value, node.id)
  } catch {
    // 用户取消删除
  }
}

// 展开全部
const handleExpandAll = () => {
  defaultExpandAll.value = true
  treeKey.value++
  nextTick(() => {
    defaultExpandAll.value = false
  })
}

// 收起全部
const handleCollapseAll = () => {
  defaultExpandAll.value = false
  treeKey.value++
}

// 拖拽相关
const allowDrag = (draggingNode: any) => {
  return !draggingNode.data.editing
}

const allowDrop = (draggingNode: any, _dropNode: any, _type: string) => {
  return !draggingNode.data.editing
}

const handleNodeDrop = (_draggingNode: any, _dropNode: any, _dropType: string) => {
  // 拖拽完成，无需提示
}

// 对话框操作
const handleCancel = () => {
  visible.value = false
}

const handleSave = () => {
  emit('save', categoryTreeData.value)
  visible.value = false
}
</script>

<style scoped>
.category-management {
  .category-toolbar {
    display: flex;
    gap: 0.5rem;
  }

  .category-tree {
    max-height: 25rem;
    overflow-y: auto;
    border: 0.0625rem solid #dcdfe6;
    border-radius: 0.25rem;
    padding: 0.5rem;
  }

  .tree-node-content {
    display: flex;
    align-items: center;
    width: 100%;

    .node-display {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;

      .node-label {
        flex: 1;
        padding: 0.25rem 0.5rem;
      }

      .node-actions {
        display: flex;
        gap: 0.25rem;
        opacity: 0;
        transition: opacity 0.2s;
      }
    }

    .node-edit {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      width: 100%;

      .edit-actions {
        display: flex;
        gap: 0.25rem;
      }
    }

    &:hover .node-actions {
      opacity: 1;
    }
  }

  .category-info {
    :deep(.el-alert__content) {
      p {
        margin: 0.125rem 0;
      }
    }
  }
}
</style>

import request from '@/config/axios'
import { useAppStore } from '@/store/modules/app'

// ==================== 类型定义 ====================

// 实时监控概览相关类型
export interface AssayOverviewVO {
  factoryId: number
  statisticsTime: string
  overallStatistics: {
    totalPlans: number
    totalTasks: number
    totalSamples: number
    totalTestData: number
    totalReports: number
  }
  todayStatistics: {
    todayTasks: number
    todayCompletedSampling: number
    todayTestData: number
    todayAudits: number
    todayAlarms: number
  }
  monthStatistics: {
    monthTasks: number
    monthCompletionRate: number
    monthQualificationRate: number
    monthEfficiencyIndex: number
  }
  samplingStatus: {
    pending: number
    processing: number
    completed: number
    abnormal: number
    completionRate: number
  }
  testingStatus: {
    pending: number
    processing: number
    completed: number
    abnormal: number
    completionRate: number
  }
  auditStatus: {
    pending: number
    processing: number
    completed: number
    abnormal: number
    completionRate: number
  }
  qualityOverview: {
    qualifiedCount: number
    exceededCount: number
    warningCount: number
    qualificationRate: number
    exceedanceRate: number
    qualityLevel: string
  }
}

// 执行监控相关类型
export interface AssayExecutionMonitorVO {
  factoryId: number
  monitorTime: string
  samplingMonitor: {
    plannedSampling: number
    completedSampling: number
    processingSampling: number
    overdueSampling: number
    samplingCompletionRate: number
    avgSamplingDuration: number
    samplingAbnormalCount: number
    samplingPointDistribution: any[]
  }
  testingMonitor: {
    testingTasks: number
    completedTesting: number
    processingTesting: number
    overdueTesting: number
    testingCompletionRate: number
    avgTestingDuration: number
    testerWorkDistribution: any[]
    indicatorDistribution: any[]
  }
  auditMonitor: {
    pendingAudits: number
    completedAudits: number
    passedAudits: number
    rejectedAudits: number
    auditCompletionRate: number
    auditPassRate: number
    avgAuditDuration: number
    auditorWorkDistribution: any[]
  }
  overallEfficiency: {
    overallCompletionRate: number
    avgProcessDuration: number
    bottleneckStage: string
    efficiencyTrend: string
  }
}

// 趋势分析请求参数类型
export interface AssayTrendAnalysisReqVO {
  factoryId: number
  startDate: string
  endDate: string
  analysisType: 'quality' | 'efficiency' | 'volume' | 'compliance'
  dimension: 'daily' | 'weekly' | 'monthly'
  indicatorIds?: number[]
  samplingPointIds?: number[]
  projectIds?: number[]
  staffIds?: number[]
  includeComparison?: boolean
  compareStartDate?: string
  compareEndDate?: string
  includeForecast?: boolean
  forecastDays?: number
}

// 趋势分析响应类型
export interface AssayTrendAnalysisVO {
  factoryId: number
  analysisTime: string
  analysisPeriod: string
  analysisType: string
  dimension: string
  trendData: {
    dates: string[]
    series: {
      name: string
      data: number[]
      unit: string
      type: string
    }[]
  }
  comparisonData?: {
    dates: string[]
    series: {
      name: string
      data: number[]
      unit: string
      type: string
    }[]
  }
  forecastData?: {
    dates: string[]
    series: {
      name: string
      data: number[]
      unit: string
      type: string
    }[]
  }
  statisticalSummary: {
    totalDataPoints: number
    avgValue: number
    maxValue: number
    minValue: number
    trendDirection: string
    changeRate: number
  }
}

// 优化建议相关类型
export interface AssayOptimizationSuggestionVO {
  factoryId: number
  analysisTime: string
  overallScore: number
  optimizationPotential: string
  processOptimization: {
    bottlenecks: {
      stage: string
      cause: string
      impact: string
      solution: string
      priority: string
    }[]
    improvements: {
      currentProblem: string
      suggestion: string
      implementationDifficulty: string
      expectedBenefit: string
    }[]
    automationSuggestions: string[]
    standardizationSuggestions: string[]
  }
  resourceOptimization: {
    staffOptimization: {
      position: string
      currentStaffing: string
      suggestedStaffing: string
      reason: string
      skillRequirements: string[]
    }
    equipmentOptimization: string[]
    scheduleOptimization: string[]
    spaceOptimization: string[]
  }
  qualityImprovement: {
    qualityControlPoints: {
      stage: string
      currentControl: string
      suggestedControl: string
      monitoringIndicators: string[]
    }[]
    testMethodOptimizations: string[]
    qualityStandardImprovements: string[]
    trainingSuggestions: string[]
  }
  efficiencyImprovement: {
    timeOptimizations: string[]
    parallelSuggestions: string[]
    batchSuggestions: string[]
    technologySuggestions: string[]
  }
  costControl: {
    costSavingOpportunities: {
      area: string
      currentCost: string
      potentialSaving: string
      implementationCost: string
      paybackPeriod: string
    }[]
    utilizationImprovements: string[]
    procurementOptimizations: string[]
    energyOptimizations: string[]
  }
  riskManagement: {
    qualityRisks: {
      riskPoint: string
      riskLevel: string
      impact: string
      preventionMeasures: string[]
    }[]
    operationalRisks: string[]
    complianceRisks: string[]
    emergencyPlanSuggestions: string[]
  }
}

// ==================== API 接口 ====================

/**
 * 获取全局水厂ID
 */
const getFactoryId = (): number => {
  const appStore = useAppStore()
  return appStore.getCurrentFactoryId() || 1
}

/**
 * 化验分析管理模块API
 */
export const AssayDataAnalysisApi = {
  // ==================== 实时监控大屏 ====================
  
  /**
   * 获取化验管理实时监控概览
   */
  getOverview: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/overview',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取告警监控数据
   */
  getAlarmMonitor: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/alarm-monitor',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取多水厂对比数据
   */
  getFactoryComparison: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/factory-comparison',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 执行监控 ====================
  
  /**
   * 获取执行监控数据
   */
  getExecutionMonitor: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/execution-monitor',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取人员工作分配数据
   */
  getStaffWorkload: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/staff-workload',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取效率分析数据
   */
  getEfficiencyAnalysis: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/efficiency-analysis',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 趋势分析 ====================

  /**
   * 获取趋势分析数据
   */
  getTrendAnalysis: async (data: AssayTrendAnalysisReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-data-analysis/trend-analysis',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取质量趋势数据
   */
  getQualityTrend: async (startDate: string, endDate: string) => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/quality-trend',
      params: {
        factoryId: getFactoryId(),
        startDate,
        endDate
      }
    })
  },

  /**
   * 获取效率趋势数据
   */
  getEfficiencyTrend: async (startDate: string, endDate: string) => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/efficiency-trend',
      params: {
        factoryId: getFactoryId(),
        startDate,
        endDate
      }
    })
  },

  /**
   * 获取工作量趋势数据
   */
  getWorkloadTrend: async (startDate: string, endDate: string) => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/workload-trend',
      params: {
        factoryId: getFactoryId(),
        startDate,
        endDate
      }
    })
  },

  // ==================== 优化建议 ====================

  /**
   * 获取优化建议
   */
  getOptimizationSuggestions: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/optimization-suggestions',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取性能瓶颈分析
   */
  getPerformanceBottlenecks: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/performance-bottlenecks',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取资源优化建议
   */
  getResourceOptimization: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/resource-optimization',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取质量改进建议
   */
  getQualityImprovement: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/quality-improvement',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取成本控制建议
   */
  getCostControl: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/cost-control',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取风险管控建议
   */
  getRiskManagement: async () => {
    return await request.getOriginal({
      url: '/assay/assay-data-analysis/risk-management',
      params: {
        factoryId: getFactoryId()
      }
    })
  }
}

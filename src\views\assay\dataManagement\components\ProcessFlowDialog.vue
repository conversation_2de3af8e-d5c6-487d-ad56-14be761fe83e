<template>
  <el-dialog
    v-model="visible"
    title="检测数据管理流程说明"
    width="1000px"
    :close-on-click-modal="false"
  >
    <div class="process-content">
      <!-- 流程图 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>业务流程图</span>
        </template>
        <div ref="chartRef" class="chart-container"></div>
      </el-card>

      <!-- 流程说明 -->
      <el-card shadow="never">
        <template #header>
          <span>流程步骤说明</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="(step, index) in processSteps"
            :key="index"
            :icon="step.icon"
            :type="step.type"
            :color="step.color"
            :size="step.size"
            timestamp=""
          >
            <el-card shadow="hover" class="step-card">
              <div class="step-header">
                <h4>{{ step.title }}</h4>
                <el-tag :type="step.tagType" size="small">{{ step.stage }}</el-tag>
              </div>
              <p class="step-description">{{ step.description }}</p>
              <div class="step-details">
                <div class="detail-item" v-for="detail in step.details" :key="detail.label">
                  <span class="detail-label">{{ detail.label }}：</span>
                  <span class="detail-value">{{ detail.value }}</span>
                </div>
              </div>
              <div v-if="step.notes" class="step-notes">
                <el-alert :title="step.notes" type="info" :closable="false" />
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="printFlow">
          <el-icon><Printer /></el-icon>打印流程
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

defineOptions({ name: 'ProcessFlowDialog' })

// 对话框状态
const visible = ref(false)
const chartRef = ref<HTMLDivElement>()
let chart: echarts.ECharts | null = null

// 流程步骤数据
const processSteps = ref([
  {
    title: '检验下发',
    stage: '起始阶段',
    tagType: 'primary',
    type: 'primary',
    color: '#409eff',
    size: 'large',
    icon: 'el-icon-box',
    description: '接收来自采样执行模块的送检报告，系统自动创建检验任务并下发给检验员',
    details: [
      { label: '数据来源', value: '采样执行模块的送检报告' },
      { label: '处理人员', value: '检验管理员' },
      { label: '处理时间', value: '实时自动处理' },
      { label: '状态变更', value: '待接收 → 已下发' }
    ],
    notes: '系统会自动根据送检报告中的检测项目创建对应的检验任务，并分配给相应的检验员'
  },
  {
    title: '检验任务执行',
    stage: '执行阶段',
    tagType: 'info',
    type: 'info',
    color: '#909399',
    size: 'normal',
    icon: 'el-icon-timer',
    description: '检验员接收任务并执行检验工作，监控检验进度',
    details: [
      { label: '操作人员', value: '检验员' },
      { label: '主要功能', value: '任务接收、开始检验、暂停、进度监控' },
      { label: '状态流转', value: '待接收 → 进行中 → 已完成' },
      { label: '监控内容', value: '检验进度、预期完成时间' }
    ],
    notes: '此阶段主要用于任务监控，实际数据录入在下一阶段进行'
  },
  {
    title: '数据录入',
    stage: '核心阶段',
    tagType: 'warning',
    type: 'warning',
    color: '#e6a23c',
    size: 'large',
    icon: 'el-icon-edit',
    description: '检验员完成实验检测后，录入检测结果数据',
    details: [
      { label: '操作人员', value: '检验员' },
      { label: '录入内容', value: '检测值、单位、检测方法、备注' },
      { label: '数据校验', value: '自动校验数据合理性和范围' },
      { label: '状态变更', value: '待录入 → 已录入' }
    ],
    notes: '支持从检验报告导入数据，系统会自动校验数据的合理性，如超出正常范围会提示警告'
  },
  {
    title: '数据校验',
    stage: '质控阶段',
    tagType: 'info',
    type: 'info',
    color: '#909399',
    size: 'normal',
    icon: 'el-icon-check',
    description: '系统自动校验数据合理性，标记异常数据',
    details: [
      { label: '校验规则', value: '数值范围、逻辑关系、历史对比' },
      { label: '异常处理', value: '自动标记异常状态' },
      { label: '处理方式', value: '人工复核或重新检测' }
    ]
  },
  {
    title: '提交审核',
    stage: '流转阶段',
    tagType: 'success',
    type: 'success',
    color: '#67c23a',
    size: 'normal',
    icon: 'el-icon-upload',
    description: '化验员确认数据无误后提交到数据质量管理模块进行审核',
    details: [
      { label: '提交条件', value: '数据录入完成且无异常' },
      { label: '流转目标', value: '数据质量管理模块' },
      { label: '状态变更', value: '待审核' }
    ],
    notes: '数据审核功能已移至数据质量管理模块，确保审核流程的独立性'
  },
  {
    title: '数据查询',
    stage: '应用阶段',
    tagType: 'success',
    type: 'success',
    color: '#67c23a',
    size: 'normal',
    icon: 'el-icon-search',
    description: '提供已审核通过的检测数据查询、统计和分析功能',
    details: [
      { label: '查询范围', value: '已审核通过的数据' },
      { label: '功能特性', value: '多维度查询、数据对比、趋势分析' },
      { label: '导出功能', value: '支持Excel、PDF格式导出' }
    ]
  }
])

// 打开对话框
const open = () => {
  visible.value = true
  // 延迟渲染图表，确保DOM已挂载
  setTimeout(() => {
    initChart()
  }, 100)
}

// 关闭对话框
const handleClose = () => {
  visible.value = false
  if (chart) {
    chart.dispose()
    chart = null
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{b}: {c}'
    },
    series: [{
      type: 'graph',
      layout: 'none',
      symbolSize: 60,
      roam: false,
      label: {
        show: true,
        fontSize: 12
      },
      edgeSymbol: ['circle', 'arrow'],
      edgeSymbolSize: [4, 10],
      data: [
        { name: '检验下发', x: 80, y: 200, itemStyle: { color: '#409eff' } },
        { name: '任务执行', x: 200, y: 200, itemStyle: { color: '#909399' } },
        { name: '数据录入', x: 320, y: 200, itemStyle: { color: '#e6a23c' } },
        { name: '数据校验', x: 440, y: 200, itemStyle: { color: '#909399' } },
        { name: '异常处理', x: 440, y: 300, itemStyle: { color: '#f56c6c' } },
        { name: '提交审核', x: 560, y: 200, itemStyle: { color: '#67c23a' } },
        { name: '数据查询', x: 680, y: 200, itemStyle: { color: '#67c23a' } }
      ],
      links: [
        { source: '检验下发', target: '任务执行' },
        { source: '任务执行', target: '数据录入' },
        { source: '数据录入', target: '数据校验' },
        { source: '数据校验', target: '异常处理' },
        { source: '数据校验', target: '提交审核' },
        { source: '异常处理', target: '数据录入' },
        { source: '提交审核', target: '数据查询' }
      ],
      lineStyle: {
        opacity: 0.9,
        width: 2,
        curveness: 0.1
      }
    }]
  }
  
  chart.setOption(option)
}

// 打印流程
const printFlow = () => {
  window.print()
  ElMessage.success('流程图已发送到打印机')
}

// 组件卸载时清理图表
onUnmounted(() => {
  if (chart) {
    chart.dispose()
    chart = null
  }
})

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.process-content {
  max-height: 600px;
  overflow-y: auto;
}

.chart-container {
  height: 300px;
  width: 100%;
}

.step-card {
  margin-bottom: 10px;
}

.step-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.step-header h4 {
  margin: 0;
  color: #303133;
}

.step-description {
  color: #606266;
  margin-bottom: 15px;
  line-height: 1.5;
}

.step-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 8px;
  margin-bottom: 15px;
}

.detail-item {
  font-size: 14px;
}

.detail-label {
  color: #909399;
  font-weight: 500;
}

.detail-value {
  color: #303133;
}

.step-notes {
  margin-top: 10px;
}

.dialog-footer {
  text-align: right;
}

@media print {
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }
}
</style>

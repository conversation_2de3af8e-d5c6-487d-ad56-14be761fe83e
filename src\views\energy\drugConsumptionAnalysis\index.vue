<template>
  <div class="drug-consumption-analysis">
    <ContentWrap>
      <!-- 筛选条件 -->
      <div class="mb-4 flex items-center gap-4">
        <span class="text-gray-600 text-sm">配置选择:</span>
        <el-select v-model="selectedArea" placeholder="选择配置" @change="handleAreaChange" style="width: 200px;">
          <el-option
            v-for="node in areaOptions"
            :key="node.id"
            :label="node.name"
            :value="node.id?.toString()"
          />
        </el-select>
      </div>

      <!-- 药耗数据卡片和时间维度选择器 -->
      <el-card shadow="hover" class="mb-4">
        <div class="flex justify-between items-center mb-4">
          <div class="font-bold text-lg">{{ getIndicatorName }}概览</div>
        </div>
        
        <div class="grid grid-cols-4 gap-4">
          <div class="drug-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">当月总{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.currentTotal.value }} <span
                class="text-sm">{{ cardData.currentTotal.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span :class="cardData.currentTotal.change >= 0 ? 'text-green-500' : 'text-red-500'"
                class="text-xs flex items-center">
                <el-icon class="mr-1">
                  <ArrowUp v-if="cardData.currentTotal.change >= 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(cardData.currentTotal.change) }}% vs 上月({{ cardData.currentTotal.prevValue }}{{
                  cardData.currentTotal.unit }})
              </span>
            </div>
          </div>

          <div class="drug-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">同比去年{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.yearOnYear.value }} <span
                class="text-sm">{{ cardData.yearOnYear.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span :class="cardData.yearOnYear.change >= 0 ? 'text-green-500' : 'text-red-500'"
                class="text-xs flex items-center">
                <el-icon class="mr-1">
                  <ArrowUp v-if="cardData.yearOnYear.change >= 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(cardData.yearOnYear.change) }}% vs 去年({{ cardData.yearOnYear.prevValue }}{{
                  cardData.yearOnYear.unit }})
              </span>
            </div>
          </div>

          <div class="drug-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">环比上月{{ getIndicatorName }}</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.monthOnMonth.value }} <span
                class="text-sm">{{ cardData.monthOnMonth.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span :class="cardData.monthOnMonth.change >= 0 ? 'text-green-500' : 'text-red-500'"
                class="text-xs flex items-center">
                <el-icon class="mr-1">
                  <ArrowUp v-if="cardData.monthOnMonth.change >= 0" />
                  <ArrowDown v-else />
                </el-icon>
                {{ Math.abs(cardData.monthOnMonth.change) }}% vs 上月({{ cardData.monthOnMonth.prevValue }}{{
                  cardData.monthOnMonth.unit }})
              </span>
            </div>
          </div>

          <div class="drug-data-item">
            <div class="text-center text-sm text-gray-500 mb-2">吨水药耗</div>
            <div class="text-center text-2xl font-bold text-blue-500">{{ cardData.perTon.value }} <span
                class="text-sm">{{ cardData.perTon.unit }}</span></div>
            <div class="flex justify-center items-center mt-2">
              <span class="text-xs text-gray-400">
                <el-icon class="mr-1">
                  <InfoFilled />
                </el-icon>
                {{ cardData.perTon.date }} 数据
              </span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 药耗图表区域 -->
      <div class="grid grid-cols-2 gap-4 mb-4">
        <!-- 总药耗趋势图表 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold">总{{ getIndicatorName }}趋势</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.drugTrend.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="updateDrugTrendChart"
              />
            </div>
          </div>
          <div ref="drugTrendChart" class="w-full h-[19rem]"></div>
        </el-card>

        <!-- 同比药耗柱状图 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold text-base">{{ getIndicatorName }}同比对比</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.monthlyComparison.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="updateMonthlyComparisonChart"
              />
            </div>
          </div>
          <div class="flex gap-4 mb-2">
            <div class="flex items-center">
              <span class="w-3 h-3 inline-block bg-blue-500 rounded-sm mr-1"></span>
              <span class="text-sm">今年</span>
            </div>
            <div class="flex items-center">
              <span class="w-3 h-3 inline-block bg-green-500 rounded-sm mr-1"></span>
              <span class="text-sm">去年</span>
            </div>
          </div>
          <div ref="monthlyComparisonChart" class="w-full h-[16rem]"></div>
        </el-card>
      </div>

      <!-- 吨水药耗趋势图和能耗对比分析 -->
      <div class="grid grid-cols-2 gap-4">
        <!-- 吨水药耗趋势图 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold">吨水{{ getIndicatorName }}趋势</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.waterDrug.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="updateWaterDrugChart"
              />
            </div>
          </div>
          <div ref="waterDrugChart" class="w-full h-[19rem]"></div>
        </el-card>

        <!-- 能耗对比分析环形图 -->
        <el-card class="h-[24rem] chart-card">
          <div class="flex justify-between items-center mb-4">
            <div class="font-bold text-base">药耗对比分析</div>
            <div class="date-picker-container" style="width: 15rem; max-width: 15rem; overflow: hidden;">
              <el-date-picker
                v-model="chartSelectors.energyComparison.date"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始"
                end-placeholder="结束"
                value-format="YYYY-MM"
                format="YYYY-MM"
                size="small"
                class="compact-date-picker"
                style="width: 15rem; min-width: 15rem; max-width: 15rem;"
                @change="generateEnergyComparisonData"
              />
            </div>
          </div>
          <div ref="energyComparisonChart" class="w-full h-[19rem]"></div>
        </el-card>
      </div>
    </ContentWrap>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch, nextTick } from 'vue'
import * as echarts from 'echarts'
import { ArrowUp, ArrowDown, InfoFilled } from '@element-plus/icons-vue'
import { ContentWrap } from '@/components/ContentWrap'
import { getEnergyNodesTree, type EnergyNodeVO } from '@/api/energy/node'

// 暂时不使用后端API，使用模拟数据
// import {
//   getEnergyAnalysis,
//   getTotalEnergyTrend,
//   getEnergyYearOnYearCompare,
//   getEnergyPerTonTrend,
//   getEnergyCompare,
//   type EnergyAnalysisReqVO,
//   type EnergyAnalysisRespVO,
//   type EnergyTrendRespVO,
//   type EnergyCompareRespVO,
//   type EnergyAnalysisCommonReqVO
// } from '@/api/energy/analysis'
import { ElMessage } from 'element-plus'
import { useAppStore } from '@/store/modules/app'

// 筛选条件
const selectedArea = ref<string>('')

// 区域选择相关
const areaOptions = ref<EnergyNodeVO[]>([])



// 获取应用store
const appStore = useAppStore()

// 获取默认日期范围（当前时间往前推12个月）
const getDefaultDateRange = (): [string, string] => {
  const end = new Date()
  const start = new Date()
  start.setMonth(start.getMonth() - 11) // 设置为11个月前（加上当月共12个月）
  
  // 格式化为 YYYY-MM 格式
  const formatDate = (date: Date): string => {
    const year = date.getFullYear()
    const month = (date.getMonth() + 1).toString().padStart(2, '0')
    return `${year}-${month}`
  }
  
  return [formatDate(start), formatDate(end)]
}

// 日期范围 - 添加顶栏日期选择器
const dateRange = ref<[string, string]>(getDefaultDateRange())

// 图表选择器
const chartSelectors = reactive({
  drugTrend: {
    indicator: 'drug',
    date: getDefaultDateRange()
  },
  monthlyComparison: {
    indicator: 'drug',
    date: getDefaultDateRange()
  },
  waterDrug: {
    indicator: 'drug',
    date: getDefaultDateRange()
  },
  energyComparison: {
    date: getDefaultDateRange()
  }
})

// 卡片数据
const cardData = reactive({
  currentTotal: { value: 29.46, unit: 'kg', change: 5.2, prevValue: 28.01 },
  yearOnYear: { value: 27.83, unit: 'kg', change: 3.8, prevValue: 26.81 },
  monthOnMonth: { value: 28.92, unit: 'kg', change: 2.1, prevValue: 28.33 },
  perTon: { value: 37.5, unit: 'g/吨', date: '2023-04' }
})

// 图表引用
const drugTrendChart = ref<HTMLDivElement>()
const monthlyComparisonChart = ref<HTMLDivElement>()
const waterDrugChart = ref<HTMLDivElement>()
const energyComparisonChart = ref<HTMLDivElement>()

// 图表实例
let drugTrendChartInstance: echarts.ECharts | null = null
let monthlyComparisonChartInstance: echarts.ECharts | null = null
let waterDrugChartInstance: echarts.ECharts | null = null
let energyComparisonChartInstance: echarts.ECharts | null = null

// 计算属性
const getIndicatorName = computed(() => {
  return '药剂总用量'
})

// 获取数据 - 使用模拟数据
const fetchData = async () => {
  try {
    // 获取当前厂站信息
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      ElMessage.error('请先选择厂站')
      return
    }

    // 使用模拟数据更新卡片
    updateDefaultData()

    // 更新所有图表
    updateAllCharts()
  } catch (error) {
    console.error('获取数据失败:', error)
    ElMessage.error('获取数据失败，请稍后重试')

    // 失败时使用默认数据
    updateDefaultData()
  }
}

// 获取总药耗趋势数据 - 使用模拟数据
const fetchTotalDrugTrend = async (dateRange: [string, string]) => {
  try {
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return null
    }

    if (!selectedArea.value) {
      console.warn('未选择区域节点')
      return null
    }

    // 生成模拟数据
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const months: string[] = []

    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    // 生成模拟的药耗数据
    const values = months.map(() => +(Math.random() * 10 + 20).toFixed(2))

    return {
      dates: months,
      values: values,
      unit: 'kg'
    }
  } catch (error) {
    console.error('获取总药耗趋势数据失败:', error)
    return null
  }
}

// 获取药耗同比对比数据 - 使用模拟数据
const fetchDrugYearOnYearCompare = async (dateRange: [string, string]) => {
  try {
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return null
    }

    if (!selectedArea.value) {
      console.warn('未选择区域节点')
      return null
    }

    // 生成模拟数据
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const months: string[] = []

    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    // 生成今年和去年的模拟数据
    const currentValues = months.map(() => +(Math.random() * 5 + 25).toFixed(2))
    const comparisonValues = months.map(() => +(Math.random() * 5 + 23).toFixed(2))

    return {
      currentData: {
        dates: months,
        values: currentValues
      },
      comparisonData: {
        dates: months,
        values: comparisonValues
      },
      unit: 'kg'
    }
  } catch (error) {
    console.error('获取药耗同比对比数据失败:', error)
    return null
  }
}

// 获取吨水药耗趋势数据 - 使用模拟数据
const fetchDrugPerTonTrend = async (dateRange: [string, string]) => {
  try {
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      return null
    }

    if (!selectedArea.value) {
      console.warn('未选择区域节点')
      return null
    }

    // 生成模拟数据
    const startDate = new Date(dateRange[0])
    const endDate = new Date(dateRange[1])
    const months: string[] = []

    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }

    // 生成模拟的吨水药耗数据
    const values = months.map(() => +(Math.random() * 0.1 + 0.3).toFixed(3))

    return {
      dates: months,
      values: values,
      unit: 'kg/吨'
    }
  } catch (error) {
    console.error('获取吨水药耗趋势数据失败:', error)
    return null
  }
}

// 更新所有图表
const updateAllCharts = () => {
  updateDrugTrendChart()
  updateMonthlyComparisonChart()
  updateWaterDrugChart()
}

// 更新默认数据（API调用失败时使用）
const updateDefaultData = () => {
  cardData.currentTotal = { value: 29.46, unit: 'kg', change: 5.2, prevValue: 28.01 }
  cardData.yearOnYear = { value: 27.83, unit: 'kg', change: 3.8, prevValue: 26.81 }
  cardData.monthOnMonth = { value: 28.92, unit: 'kg', change: 2.1, prevValue: 28.33 }
  cardData.perTon = { value: 37.5, unit: 'g/吨', date: '2023-04' }
}

// 从API响应更新卡片数据 - 已移除，使用模拟数据

// 更新卡片数据 - 已移除，使用updateDefaultData函数

// 获取区域乘数因子
const getAreaMultiplier = (area: string): number => {
  switch(area) {
    case 'all': return 1.0;
    case 'wastewater': return 0.6;
    case 'secondary': return 0.25;
    case 'advanced': return 0.15;
    default: return 1.0;
  }
}


// 初始化图表
const initCharts = () => {
  initDrugTrendChart()
  initMonthlyComparisonChart()
  initWaterDrugChart()
}

// 初始化药耗趋势图
const initDrugTrendChart = () => {
  if (drugTrendChart.value) {
    drugTrendChartInstance = echarts.init(drugTrendChart.value)
    
    // 使用日期选择器的值作为日期范围
    const startDate = new Date(chartSelectors.drugTrend.date[0])
    const endDate = new Date(chartSelectors.drugTrend.date[1])
    const months: string[] = []
    
    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }
    
    const monthLabels = months.map(m => {
      const [, month] = m.split('-')
      return `${month}月`
    })
    
    // 根据选择的区域生成总药剂用量数据
    const areaMultiplier = getAreaMultiplier('all') // 使用默认值，因为这是模拟数据
    // 生成与月份数量相匹配的总药剂用量随机数据
    const data = months.map(() => +(Math.floor(Math.random() * 30) + 100 * areaMultiplier).toFixed(2))
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: monthLabels
      },
      yAxis: {
        type: 'value',
        name: '用量(kg)'
      },
      series: [
        {
          name: '药剂用量',
          type: 'line',
          smooth: true,
          data: data,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0)'
                }
              ]
            }
          }
        }
      ]
    }
    drugTrendChartInstance.setOption(option)
  }
}

// 初始化月度对比图
const initMonthlyComparisonChart = () => {
  if (monthlyComparisonChart.value) {
    monthlyComparisonChartInstance = echarts.init(monthlyComparisonChart.value)
    
    // 使用日期选择器的值作为日期范围
    const startDate = new Date(chartSelectors.monthlyComparison.date[0])
    const endDate = new Date(chartSelectors.monthlyComparison.date[1])
    const months: string[] = []
    
    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }
    
    const monthLabels = months.map(m => {
      const [, month] = m.split('-')
      return `${month}月`
    })
    
    // 根据选择的区域生成总药剂用量数据
    const areaMultiplier = getAreaMultiplier('all') // 使用默认值，因为这是模拟数据
    // 生成今年和去年的总药剂用量对比数据
    const currentYearData = months.map(() => +(Math.floor(Math.random() * 20) + 100 * areaMultiplier).toFixed(2))
    const previousYearData = months.map(() => +(Math.floor(Math.random() * 20) + 90 * areaMultiplier).toFixed(2))
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: monthLabels
      },
      yAxis: {
        type: 'value',
        name: '用量(kg)'
      },
      series: [
        {
          name: '今年',
          type: 'bar',
          data: currentYearData,
          itemStyle: {
            color: '#409EFF'
          }
        },
        {
          name: '去年',
          type: 'bar',
          data: previousYearData,
          itemStyle: {
            color: '#67C23A'
          }
        }
      ]
    }
    monthlyComparisonChartInstance.setOption(option)
  }
}

// 初始化吨水药耗图
const initWaterDrugChart = () => {
  if (waterDrugChart.value) {
    waterDrugChartInstance = echarts.init(waterDrugChart.value)
    
    // 使用日期选择器的值作为日期范围
    const startDate = new Date(chartSelectors.waterDrug.date[0])
    const endDate = new Date(chartSelectors.waterDrug.date[1])
    const months: string[] = []
    
    let currentDate = new Date(startDate)
    while (currentDate <= endDate) {
      months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
      currentDate.setMonth(currentDate.getMonth() + 1)
    }
    
    const monthLabels = months.map(m => {
      const [, month] = m.split('-')
      return `${month}月`
    })
    
    // 根据选择的区域生成总药剂吨水药耗数据
    const areaMultiplier = getAreaMultiplier('all') // 使用默认值，因为这是模拟数据
    const yAxisName = '单耗(g/吨)'
    // 生成总药剂吨水药耗数据
    const data = months.map(() => +(Math.floor(Math.random() * 20) + 130 * areaMultiplier).toFixed(2))
    
    const option = {
      tooltip: {
        trigger: 'axis'
      },
      xAxis: {
        type: 'category',
        data: monthLabels
      },
      yAxis: {
        type: 'value',
        name: yAxisName
      },
      series: [
        {
          name: '吨水药耗',
          type: 'line',
          smooth: true,
          data: data,
          lineStyle: {
            width: 2,
            color: '#409EFF'
          },
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            color: '#409EFF'
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(64, 158, 255, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(64, 158, 255, 0)'
                }
              ]
            }
          }
        }
      ]
    }
    waterDrugChartInstance.setOption(option)
  }
}



// 获取区域数据
const fetchAreaOptions = async () => {
  try {
    // 获取当前厂站ID
    const currentStation = appStore.getCurrentStation

    if (!currentStation || !currentStation.id) {
      ElMessage.error('请先选择厂站')
      return
    }

    const response = await getEnergyNodesTree(currentStation.id)

    // 过滤出药耗类型的父节点（parentId 为 null 或 undefined）
    const filterDrugParentNodes = (nodes: EnergyNodeVO[]): EnergyNodeVO[] => {
      const result: EnergyNodeVO[] = []

      const traverse = (nodeList: EnergyNodeVO[]) => {
        nodeList.forEach(node => {
          // 检查是否为药耗类型的父节点（根节点）
          if (node.nodeType === 'drug' &&
              (node.parentId === null || node.parentId === undefined)) {
            result.push(node)
          }

          // 递归处理子节点
          if (node.children && node.children.length > 0) {
            traverse(node.children)
          }
        })
      }

      traverse(nodes)
      return result
    }

    areaOptions.value = filterDrugParentNodes(response || [])

    // 自动选择ID最小的区域节点
    if (areaOptions.value.length > 0 && !selectedArea.value) {
      // 找出ID最小的节点
      const minIdNode = areaOptions.value.reduce((prev, current) => {
        // 如果当前节点的ID小于之前找到的最小ID节点，则更新
        if (current.id !== undefined && (prev.id === undefined || current.id < prev.id)) {
          return current;
        }
        return prev;
      }, areaOptions.value[0]);

      // 设置选中的区域为ID最小的节点
      selectedArea.value = minIdNode.id !== undefined ? minIdNode.id.toString() : '';
    }
  } catch (error) {
    console.error('获取区域数据失败:', error)
    ElMessage.error('获取区域数据失败')
  }
}



// 处理区域选择变化
const handleAreaChange = async () => {
  // 重新获取数据
  fetchData()
  // 更新环形图数据
  await generateEnergyComparisonData()
}





// 初始化
onMounted(async () => {
  // 等待下一个tick，确保所有响应式数据都已初始化
  await nextTick()

  // 初始化基础图表
  initDrugTrendChart()
  initMonthlyComparisonChart()
  initWaterDrugChart()

  // 添加短暂延迟，等待厂站信息加载
  setTimeout(async () => {
    // 初始获取区域数据
    await fetchAreaOptions()

    // 初始化数据
    await handleAreaChange()

    // 初始化依赖区域数据的图表
    initEnergyComparisonChart()
  }, 500)

  // 监听窗口大小变化，调整图表大小
  window.addEventListener('resize', () => {
    drugTrendChartInstance?.resize()
    monthlyComparisonChartInstance?.resize()
    waterDrugChartInstance?.resize()
    energyComparisonChartInstance?.resize()
  })
})

// 监听厂站变化
watch(() => appStore.getCurrentStation, async (newStation) => {
  if (newStation && newStation.id) {
    await fetchAreaOptions()
    await handleAreaChange()
  }
}, { immediate: false })

// 初始化能耗对比分析环形图
const initEnergyComparisonChart = async () => {
  if (energyComparisonChart.value) {
    energyComparisonChartInstance = echarts.init(energyComparisonChart.value)

    // 获取当前选中区域的节点数据
    await generateEnergyComparisonData()
  }
}

// 生成能耗对比分析数据
const generateEnergyComparisonData = async () => {
  try {
    // 获取当前厂站信息
    const currentStation = appStore.getCurrentStation
    if (!currentStation || !currentStation.id) {
      ElMessage.error('请先选择厂站')
      return
    }

    // 获取节点树数据
    const response = await getEnergyNodesTree(currentStation.id)

    // 找到当前选中区域的节点
    const findSelectedNode = (nodes: EnergyNodeVO[], targetId: number): EnergyNodeVO | null => {
      for (const node of nodes) {
        if (node.id === targetId) {
          return node
        }
        if (node.children && node.children.length > 0) {
          const found = findSelectedNode(node.children, targetId)
          if (found) return found
        }
      }
      return null
    }

    if (!selectedArea.value) return

    const selectedNode = findSelectedNode(response || [], Number(selectedArea.value))
    if (!selectedNode || !selectedNode.children) return

    // 分离二级和三级节点
    const secondLevelNodes: EnergyNodeVO[] = []
    const thirdLevelNodes: EnergyNodeVO[] = []

    selectedNode.children.forEach(child => {
      if (child.nodeType === 'drug') {
        secondLevelNodes.push(child)
        if (child.children && child.children.length > 0) {
          child.children.forEach(grandChild => {
            if (grandChild.nodeType === 'drug') {
              thirdLevelNodes.push(grandChild)
            }
          })
        }
      }
    })

    // 生成环形图数据
    const innerData = secondLevelNodes.map((node, index) => ({
      value: Math.floor(Math.random() * 50) + 30,
      name: node.name,
      itemStyle: {
        color: getNodeColor(index, 'inner')
      }
    }))

    const outerData = thirdLevelNodes.map((node, index) => ({
      value: Math.floor(Math.random() * 30) + 15,
      name: node.name,
      itemStyle: {
        color: getNodeColor(index, 'outer')
      }
    }))

    // 设置图表配置
    const option = {
      tooltip: {
        show: false
      },
      legend: {
        show: false
      },
      series: [
        {
          name: '二级节点',
          type: 'pie',
          radius: ['0%', '45%'],
          center: ['50%', '50%'],
          data: innerData,
          label: {
            show: true,
            position: 'inside',
            formatter: '{b}\n{c}kg',
            fontSize: 12,
            color: '#fff'
          },
          labelLine: {
            show: false
          }
        },
        {
          name: '三级节点',
          type: 'pie',
          radius: ['50%', '75%'],
          center: ['50%', '50%'],
          data: outerData,
          label: {
            show: true,
            position: 'outside',
            formatter: '{b}: {c}kg',
            fontSize: 12,
            color: '#333'
          },
          labelLine: {
            show: true,
            length: 12,
            length2: 8
          }
        }
      ]
    }

    energyComparisonChartInstance?.setOption(option)
  } catch (error) {
    console.error('生成能耗对比数据失败:', error)
  }
}

// 获取节点颜色
const getNodeColor = (index: number, type: 'inner' | 'outer'): string => {
  const innerColors = ['#409EFF', '#67C23A', '#E6A23C', '#F56C6C', '#909399']
  const outerColors = ['#79BBFF', '#95D475', '#EEBE77', '#F89898', '#B3B3B3']

  if (type === 'inner') {
    return innerColors[index % innerColors.length]
  } else {
    return outerColors[index % outerColors.length]
  }
}

// 更新总药耗趋势图
const updateDrugTrendChart = async () => {
  if (drugTrendChart.value && drugTrendChartInstance) {
    // 使用日期选择器的值作为日期范围
    const dateRange = chartSelectors.drugTrend.date
    
    // 获取API数据
    const apiData = await fetchTotalDrugTrend(dateRange)
    
    if (apiData) {
      // 使用API数据
      const xAxisData = apiData.dates.map(date => {
        const [, month] = date.split('-')
        return `${month}月`
      })
      
      const drugData = apiData.values
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...drugData) * 1.2
      const minValue = Math.min(...drugData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: `药耗 (${apiData.unit})`,
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '总药耗',
            type: 'line',
            data: drugData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      drugTrendChartInstance.setOption(option)
    } else {
      // 如果API调用失败，使用模拟数据
      const startDate = new Date(dateRange[0])
      const endDate = new Date(dateRange[1])
      const months: string[] = []
      
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }
      
      const xAxisData = months.map(m => {
        const [, month] = m.split('-')
        return `${month}月`
      })
      
      // 生成总药耗数据
      const drugData = months.map(() => +(Math.random() * 10 + 20).toFixed(2))
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...drugData) * 1.2
      const minValue = Math.min(...drugData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '药耗 (kg)',
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '总药耗',
            type: 'line',
            data: drugData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      drugTrendChartInstance.setOption(option)
    }
  }
}

// 更新药耗同比对比图表
const updateMonthlyComparisonChart = async () => {
  if (monthlyComparisonChart.value && monthlyComparisonChartInstance) {
    // 使用日期选择器的值作为日期范围
    const dateRange = chartSelectors.monthlyComparison.date
    
    // 获取API数据
    const apiData = await fetchDrugYearOnYearCompare(dateRange)
    
    if (apiData) {
      // 使用API数据
      const xAxisData = apiData.currentData.dates.map(date => {
        const [, month] = date.split('-')
        return `${month}月`
      })
      
      const currentData = apiData.currentData.values
      const comparisonData = apiData.comparisonData.values
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const allDataValues = [...currentData, ...comparisonData]
      const maxValue = Math.max(...allDataValues) * 1.2
      const minValue = Math.min(...allDataValues) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: `药耗 (${apiData.unit})`,
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '今年',
            type: 'bar',
            data: currentData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '去年',
            type: 'bar',
            data: comparisonData,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      monthlyComparisonChartInstance.setOption(option)
    } else {
      // 如果API调用失败，使用模拟数据
      const startDate = new Date(dateRange[0])
      const endDate = new Date(dateRange[1])
      const months: string[] = []
      
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }
      
      const xAxisData = months.map(m => {
        const [, month] = m.split('-')
        return `${month}月`
      })
      
      // 生成今年和去年药耗数据
      const currentData = months.map(() => +(Math.random() * 5 + 25).toFixed(2))
      const comparisonData = months.map(() => +(Math.random() * 5 + 23).toFixed(2))
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const allDataValues = [...currentData, ...comparisonData]
      const maxValue = Math.max(...allDataValues) * 1.2
      const minValue = Math.min(...allDataValues) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '药耗 (kg)',
          min: Math.floor(minValue),
          max: Math.ceil(maxValue)
        },
        series: [
          {
            name: '今年',
            type: 'bar',
            data: currentData,
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '去年',
            type: 'bar',
            data: comparisonData,
            itemStyle: {
              color: '#67C23A'
            }
          }
        ]
      }
      monthlyComparisonChartInstance.setOption(option)
    }
  }
}

// 更新吨水药耗趋势图
const updateWaterDrugChart = async () => {
  if (waterDrugChart.value && waterDrugChartInstance) {
    // 使用日期选择器的值作为日期范围
    const dateRange = chartSelectors.waterDrug.date
    
    // 获取API数据
    const apiData = await fetchDrugPerTonTrend(dateRange)
    
    if (apiData) {
      // 使用API数据
      const xAxisData = apiData.dates.map(date => {
        const [, month] = date.split('-')
        return `${month}月`
      })
      
      const waterDrugData = apiData.values
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...waterDrugData) * 1.2
      const minValue = Math.min(...waterDrugData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: `药耗 (${apiData.unit})`,
          min: Math.floor(minValue * 10) / 10,
          max: Math.ceil(maxValue * 10) / 10
        },
        series: [
          {
            name: '吨水药耗',
            type: 'line',
            data: waterDrugData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      
      waterDrugChartInstance.setOption(option)
    } else {
      // 如果API调用失败，使用模拟数据
      const startDate = new Date(dateRange[0])
      const endDate = new Date(dateRange[1])
      const months: string[] = []
      
      let currentDate = new Date(startDate)
      while (currentDate <= endDate) {
        months.push(`${currentDate.getFullYear()}-${(currentDate.getMonth() + 1).toString().padStart(2, '0')}`)
        currentDate.setMonth(currentDate.getMonth() + 1)
      }
      
      const xAxisData = months.map(m => {
        const [, month] = m.split('-')
        return `${month}月`
      })
      
      // 生成吨水药耗数据
      const waterDrugData = months.map(() => +(Math.random() * 0.1 + 0.3).toFixed(3))
      
      // 计算Y轴的最大值和最小值，确保图表显示完整
      const maxValue = Math.max(...waterDrugData) * 1.2
      const minValue = Math.min(...waterDrugData) * 0.8

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: xAxisData
        },
        yAxis: {
          type: 'value',
          name: '药耗 (kg/吨)',
          min: Math.floor(minValue * 10) / 10,
          max: Math.ceil(maxValue * 10) / 10
        },
        series: [
          {
            name: '吨水药耗',
            type: 'line',
            data: waterDrugData,
            smooth: true,
            lineStyle: {
              width: 2,
              color: '#409EFF'
            },
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF'
            },
            areaStyle: {
              color: {
                type: 'linear',
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: 'rgba(64, 158, 255, 0.2)'
                  },
                  {
                    offset: 1,
                    color: 'rgba(64, 158, 255, 0)'
                  }
                ]
              }
            }
          }
        ]
      }
      
      waterDrugChartInstance.setOption(option)
    }
  }
}
</script>

<style scoped lang="scss">
.drug-consumption-analysis {
  .drug-data-item {
    transition: all 0.3s;
    padding: 1rem;
    border-radius: 0.5rem;
    background-color: #f8f9fa;
    
    &:hover {
      transform: translateY(-0.3rem);
      box-shadow: 0 0.625rem 0.9375rem -0.1875rem rgba(0, 0, 0, 0.1),
        0 0.25rem 0.375rem -0.125rem rgba(0, 0, 0, 0.05);
    }
  }

  :deep(.el-card) {
    border-radius: 0.5rem;
    transition: all 0.3s;

    &.chart-card:hover {
      box-shadow: 0 0.5rem 0.75rem -0.125rem rgba(0, 0, 0, 0.1);
    }

    .el-card__body {
      padding: 1.25rem;
      height: 100%;
    }
  }

  :deep(.el-radio-button__inner) {
    padding: 0.5rem 1rem;
  }

  :deep(.el-radio-button.is-active .el-radio-button__inner) {
    background-color: #409EFF;
    border-color: #409EFF;
    color: white;
  }

  .el-icon-caret-top,
  .el-icon-caret-bottom {
    margin-right: 0.25rem;
  }

  // 紧凑型日期选择器样式 - 使用更强的选择器
  :deep(.el-date-editor.compact-date-picker) {
    width: 15rem !important;
    min-width: 15rem !important;
    max-width: 15rem !important;
    
    .el-input__wrapper,
    .el-range-editor {
      width: 15rem !important;
      min-width: 15rem !important;
      max-width: 15rem !important;
    }
    
    .el-range-input {
      width: 40% !important;
      font-size: 12px !important;
    }
    
    .el-range-separator {
      width: 10% !important;
      font-size: 12px !important;
      padding: 0 2px !important;
    }
    
    .el-range__close-icon,
    .el-range__icon {
      margin-left: 0 !important;
      font-size: 12px !important;
    }
  }
  
  // 直接强制覆盖 Element Plus 的内部样式
  :deep(.el-date-editor--monthrange) {
    width: 15rem !important;
  }
  
  // 覆盖父元素容器宽度
  .date-picker-container {
    width: 15rem !important;
    min-width: 15rem !important;
    max-width: 15rem !important;
  }
}
</style>
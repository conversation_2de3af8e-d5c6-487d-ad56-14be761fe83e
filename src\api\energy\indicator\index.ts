import request from '@/config/axios'

// 能耗指标接口定义
export interface EnergyIndicatorVO {
  id?: number
  indicatorName: string // 能耗指标名称
  indicatorCode: string // 指标编码（物联中台指标编码）
  unit: string // 单位
  nodeId: number // 关联节点ID（可以是分组或设备）
  factoryId?: number // 所属厂站ID
  timeGranularity?: 'hour' | 'day' | 'month' // 时间粒度：小时、天、月
  createTime?: Date
  updateTime?: Date
}

// 创建能耗指标请求参数
export interface EnergyIndicatorCreateReqVO {
  indicatorName: string // 能耗指标名称
  indicatorCode: string // 指标编码（物联中台指标编码）
  unit: string // 单位
  nodeId: number // 关联节点ID
  factoryId?: number // 所属厂站ID
  timeGranularity?: 'hour' | 'day' | 'month' // 时间粒度：小时、天、月
}

// 更新能耗指标请求参数
export interface EnergyIndicatorUpdateReqVO {
  id: number
  indicatorName: string // 能耗指标名称
  indicatorCode: string // 指标编码（物联中台指标编码）
  unit: string // 单位
  nodeId: number // 关联节点ID
  factoryId?: number // 所属厂站ID
  timeGranularity?: 'hour' | 'day' | 'month' // 时间粒度：小时、天、月
}

// 创建能耗指标
export const createEnergyIndicator = (data: EnergyIndicatorCreateReqVO) => {
   return request.post<number>({ url: '/energy/indicator/create', data })
}

// 更新能耗指标
export const updateEnergyIndicator = (data: EnergyIndicatorUpdateReqVO) => {
   return request.post<boolean>({ url: '/energy/indicator/update', data })
}

// 删除能耗指标
export const deleteEnergyIndicator = (id: number) => {
  return request.delete<boolean>({ url: `/energy/indicator/delete/${id}` })
}

// 根据节点ID获取能耗指标列表
export const getEnergyIndicatorsByNodeId = (nodeId: number) => {
   return request.get<EnergyIndicatorVO[]>({ url: `/energy/indicator/get-indicator-by-node-id`, params: { nodeId } })
}

// 获取所有能耗指标
export const getAllEnergyIndicators = () => {
  return request.get<EnergyIndicatorVO[]>({ url: '/energy/indicator/list' })
}

// 指标编码选项接口定义
export interface IndicatorCodeOption {
  deviceId: string
  deviceName: string
  points: {
    pointId: string
    pointCode: string
    pointName: string
    indicatorId: string
    pointType: string
    unit: string
  }[]
}

// 获取指标编码选项（从物联中台获取）
export const getIndicatorCodeOptions = (factoryId: number | string) => {
  return request.getOriginal({
    url: '/monitor/ods/pointName',
    params: { factoryId }
  })
}

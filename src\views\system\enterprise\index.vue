<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <el-form
      class="-mb-15px"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="企业名称" prop="name">
        <el-input
          v-model="queryParams.name"
          placeholder="请输入企业名称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="所在省份" prop="province">
        <el-input
          v-model="queryParams.province"
          placeholder="请输入省份"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="所在城市" prop="city">
        <el-input
          v-model="queryParams.city"
          placeholder="请输入城市"
          clearable
          @keyup.enter="handleQuery"
          class="!w-240px"
        />
      </el-form-item>
      <el-form-item label="风险评估" prop="riskAssessment">
        <el-select
          v-model="queryParams.riskAssessment"
          placeholder="请选择风险等级"
          clearable
          class="!w-240px"
        >
          <el-option label="低风险" value="低风险" />
          <el-option label="中风险" value="中风险" />
          <el-option label="高风险" value="高风险" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery"><Icon icon="ep:search" class="mr-5px" /> 搜索</el-button>
        <el-button @click="resetQuery"><Icon icon="ep:refresh" class="mr-5px" /> 重置</el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
        >
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button
          type="success"
          plain
          @click="handleExport"
          :loading="exportLoading"
        >
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe>
      <el-table-column label="企业ID" align="center" prop="id" width="80" />
      <el-table-column label="企业名称" align="center" prop="name" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="所在地区" align="center" min-width="120">
        <template #default="scope">
          {{ scope.row.province }}{{ scope.row.city }}
        </template>
      </el-table-column>
      <el-table-column label="注册资本" align="center" prop="registeredCapital" width="120">
        <template #default="scope">
          {{ scope.row.registeredCapital }}万元
        </template>
      </el-table-column>
      <el-table-column label="职工人数" align="center" prop="employeeCount" width="100" />
      <el-table-column label="厂站数量" align="center" prop="plantCount" width="100" />
      <el-table-column label="服务人口" align="center" prop="servicePopulation" width="120">
        <template #default="scope">
          {{ scope.row.servicePopulation }}万人
        </template>
      </el-table-column>
      <el-table-column label="服务面积" align="center" prop="serviceArea" width="120">
        <template #default="scope">
          {{ scope.row.serviceArea }}km²
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center" width="200" fixed="right">
        <template #default="scope">
          <el-button
            link
            type="primary"
            @click="openForm('update', scope.row.id)"
          >
            编辑
          </el-button>
          <el-button
            link
            type="info"
            @click="openDetail(scope.row)"
          >
            详情
          </el-button>
          <el-button
            link
            type="danger"
            @click="handleDelete(scope.row.id)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.pageNo"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <EnterpriseForm ref="formRef" @success="getList" />

  <!-- 详情弹窗 -->
  <EnterpriseDetail ref="detailRef" />
</template>

<script lang="ts" setup>
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import EnterpriseForm from './EnterpriseForm.vue'
import EnterpriseDetail from './EnterpriseDetail.vue'

defineOptions({ name: 'SystemEnterprise' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  name: undefined,
  province: undefined,
  city: undefined,
  riskAssessment: undefined
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    // TODO: 对接后端API
    // const data = await EnterpriseApi.getEnterprisePage(queryParams)
    // 模拟数据
    const mockData = {
      list: [
        {
          id: 1,
          name: '河南环保科技有限公司',
          province: '河南省',
          city: '洛阳市',
          registeredCapital: 5000,
          servicePopulation: 120,
          serviceArea: 850,
          employeeCount: 280,
          plantCount: 12,
          sewageTreatmentDesign: 50000,
          sewageTreatmentProduction: 45000,
          recycledWaterDesign: 20000,
          recycledWaterProduction: 18000,
          waterSupplyDesign: 80000,
          waterSupplyProduction: 75000,
          sludgeOutput: 150,
          renewableEnergyGeneration: 2500,
          pipelineLength: 1200,
          riskAssessment: '低风险',
          createTime: new Date('2023-01-15')
        },
        {
          id: 2,
          name: '华北水务集团',
          province: '河北省',
          city: '石家庄市',
          registeredCapital: 15000,
          servicePopulation: 300,
          serviceArea: 2100,
          employeeCount: 650,
          plantCount: 25,
          sewageTreatmentDesign: 120000,
          sewageTreatmentProduction: 110000,
          recycledWaterDesign: 50000,
          recycledWaterProduction: 45000,
          waterSupplyDesign: 200000,
          waterSupplyProduction: 185000,
          sludgeOutput: 380,
          renewableEnergyGeneration: 6800,
          pipelineLength: 3200,
          riskAssessment: '中风险',
          createTime: new Date('2022-08-20')
        },
        {
          id: 3,
          name: '江南环境科技股份有限公司',
          province: '江苏省',
          city: '南京市',
          registeredCapital: 8000,
          servicePopulation: 180,
          serviceArea: 1200,
          employeeCount: 420,
          plantCount: 18,
          sewageTreatmentDesign: 80000,
          sewageTreatmentProduction: 72000,
          recycledWaterDesign: 30000,
          recycledWaterProduction: 28000,
          waterSupplyDesign: 150000,
          waterSupplyProduction: 140000,
          sludgeOutput: 220,
          renewableEnergyGeneration: 4200,
          pipelineLength: 2100,
          riskAssessment: '低风险',
          createTime: new Date('2023-03-10')
        },
        {
          id: 4,
          name: '西部水务发展有限公司',
          province: '四川省',
          city: '成都市',
          registeredCapital: 12000,
          servicePopulation: 250,
          serviceArea: 1800,
          employeeCount: 580,
          plantCount: 22,
          sewageTreatmentDesign: 100000,
          sewageTreatmentProduction: 95000,
          recycledWaterDesign: 40000,
          recycledWaterProduction: 35000,
          waterSupplyDesign: 180000,
          waterSupplyProduction: 170000,
          sludgeOutput: 300,
          renewableEnergyGeneration: 5500,
          pipelineLength: 2800,
          riskAssessment: '高风险',
          createTime: new Date('2022-11-15')
        },
        {
          id: 5,
          name: '东海水业集团有限公司',
          province: '山东省',
          city: '青岛市',
          registeredCapital: 20000,
          servicePopulation: 400,
          serviceArea: 2500,
          employeeCount: 800,
          plantCount: 30,
          sewageTreatmentDesign: 150000,
          sewageTreatmentProduction: 145000,
          recycledWaterDesign: 60000,
          recycledWaterProduction: 55000,
          waterSupplyDesign: 250000,
          waterSupplyProduction: 240000,
          sludgeOutput: 450,
          renewableEnergyGeneration: 8200,
          pipelineLength: 4000,
          riskAssessment: '中风险',
          createTime: new Date('2021-06-08')
        }
      ],
      total: 5
    }
    list.value = mockData.list
    total.value = mockData.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (row: any) => {
  detailRef.value.open(row)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // TODO: 对接后端API
    // await EnterpriseApi.deleteEnterprise(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    // TODO: 对接后端API
    // const data = await EnterpriseApi.exportEnterprise(queryParams)
    // download.excel(data, '企业档案.xls')
    message.success('导出成功')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>

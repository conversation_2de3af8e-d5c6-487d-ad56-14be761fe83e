<template>
  <el-dialog v-model="visible" title="数据填报" width="80%" :close-on-click-modal="false" @close="handleClose">
    <div v-if="taskData" class="upload-container">
      <!-- 任务信息 -->
      <el-card class="mb-4">
        <template #header>
          <span class="font-bold">任务信息</span>
        </template>
        <el-descriptions :column="3" border size="small">
          <el-descriptions-item label="任务名称">{{ taskData.name }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="taskData.status === '已提交' ? 'success' : taskData.status === '异常' ? 'danger' : 'warning'">{{
              taskData.status }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="项目数量">{{ taskData.schemeData?.projects?.length || 0 }}个</el-descriptions-item>
          <el-descriptions-item label="周期">{{ taskData.period }}</el-descriptions-item>
          <el-descriptions-item label="启用时间">{{ taskData.enableTime || '-' }}</el-descriptions-item>
          <el-descriptions-item label="失效时间">{{ taskData.expireTime || taskData.dueDate }}</el-descriptions-item>
          <el-descriptions-item label="来自方案">{{ taskData.schemeName || taskData.schemeData?.name || '-'
            }}</el-descriptions-item>
          <el-descriptions-item label="任务描述" :span="2">{{ taskData.description || taskData.schemeData?.description ||
            '-'
            }}</el-descriptions-item>
        </el-descriptions>
      </el-card>





      <!-- 评估项目和指标数据填报 -->
      <el-card v-if="taskData.schemeData" class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="font-bold">评估项目和指标数据填报</span>
          </div>
        </template>
        <el-collapse v-model="activeProjects">
          <el-collapse-item v-for="project in taskData.schemeData.projects" :key="project.id" :name="project.id"
            :title="`${project.name} (权重: ${project.weight}%)`">

            <el-table :data="project.indicators" border size="small">
              <el-table-column prop="name" label="指标名称" width="200" />
              <el-table-column prop="unit" label="单位" width="100" align="center" />
              <el-table-column prop="formula" label="计算公式" min-width="200" show-overflow-tooltip />
              <el-table-column prop="threshold" label="阈值" width="120" align="center" />
              <el-table-column label="指标分值" width="100" align="center">
                <template #default="{ row }">
                  {{ getIndicatorScore(project, row) }}
                </template>
              </el-table-column>
              <el-table-column label="数值填报" width="150">
                <template #default="{ row }">
                  <el-input v-model="indicatorInputValues[`${project.id}-${row.id}`]" type="number" placeholder="请输入数值"
                    @input="(value) => handleInputChange(project.id, row.id, value)" />
                </template>
              </el-table-column>
              <el-table-column label="得分" width="100" align="center">
                <template #default="{ row }">
                  <el-input v-model="indicatorScoreValues[`${project.id}-${row.id}`]" type="number" placeholder="请输入得分"
                    @input="(value) => handleScoreChange(project.id, row.id, value)" />
                </template>
              </el-table-column>
              <el-table-column label="最后修改时间" width="150" align="center">
                <template #default="{ row }">
                  {{ getLastModifyTime(project.id, row.id) || '-' }}
                </template>
              </el-table-column>
              <el-table-column label="最后修改人" width="120" align="center">
                <template #default="{ row }">
                  {{ getLastModifyUser(project.id, row.id) || '-' }}
                </template>
              </el-table-column>
              <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
            </el-table>
          </el-collapse-item>
        </el-collapse>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="submitData" :loading="submitting">
          提交数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'


// 接口定义 - 与主页面保持一致
interface Indicator {
  id: string
  name: string
  unit: string
  formula: string
  applicableObject: string
  description?: string
  weight?: number
  threshold?: string
  expectedRange?: string
}

interface Project {
  id: string
  name: string
  weight: number
  scoreType: '加权平均' | '区间打分' | '减分制'
  fullScore: number
  indicators: Indicator[]
}

interface SchemeItem {
  id: string
  name: string
  status: '草稿' | '审批中' | '启用中' | '已失效' | '审批退回'
  targetType: '水厂' | '泵站' | '管网'
  cycle: '月度' | '季度' | '年度'
  startDate: string
  endDate: string
  description: string
  projects: Project[]
  createTime: string
  creator: string
}

interface TaskItem {
  id: string
  name: string
  schemeId: string
  schemeName: string
  objectName: string
  period: string
  status: '待提交' | '已提交' | '异常'
  dueDate: string
  schemeData?: SchemeItem
}



// Props 和 Emits
const props = defineProps<{
  modelValue: boolean
  taskData: TaskItem | null
  globalData?: Record<string, Record<string, {
    value?: number
    score?: number
    lastModifyTime?: string
    lastModifyUser?: string
  }>>
}>()

const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: any]
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const submitting = ref(false)
const activeProjects = ref<string[]>([])

// 指标数据存储 - 按项目ID和指标ID组织
const indicatorValues = ref<Record<string, Record<string, number>>>({})
const indicatorModifyTimes = ref<Record<string, Record<string, string>>>({})
const indicatorModifyUsers = ref<Record<string, Record<string, string>>>({})

// 用于v-model绑定的输入值
const indicatorInputValues = ref<Record<string, string>>({})

// 用于存储得分数据
const indicatorScores = ref<Record<string, Record<string, number>>>({})
const indicatorScoreValues = ref<Record<string, string>>({})

// Mock数据：模拟已有的指标数据（包含修改时间和修改人）
const mockIndicatorData = ref<Record<string, Record<string, {
  value?: number
  score?: number
  lastModifyTime?: string
  lastModifyUser?: string
}>>>({
  // 基本指标项目的mock数据
  'project_001': {
    'indicator_001': {
      value: 0.35,
      score: 68.5,
      lastModifyTime: '2025-01-15 14:30:25',
      lastModifyUser: '张三'
    },
    'indicator_002': {
      value: 0.88,
      score: 26.4,
      lastModifyTime: '2025-01-15 14:32:18',
      lastModifyUser: '李四'
    }
  },
  // 运行效率项目的mock数据
  'project_002': {
    'indicator_003': {
      value: 85.2,
      score: 42.6,
      lastModifyTime: '2025-01-15 15:15:42',
      lastModifyUser: '王五'
    },
    'indicator_004': {
      value: 92.8,
      score: 37.1,
      lastModifyTime: '2025-01-15 15:18:33',
      lastModifyUser: '赵六'
    },
    'indicator_005': {
      value: 78.5,
      score: 15.7,
      lastModifyTime: '2025-01-15 15:22:15',
      lastModifyUser: '钱七'
    }
  },
  // 能耗分析项目的mock数据
  'project_003': {
    'indicator_006': {
      value: 1.25,
      score: 18.8,
      lastModifyTime: '2025-01-15 16:05:28',
      lastModifyUser: '孙八'
    },
    'indicator_007': {
      value: 0.95,
      score: 14.3,
      lastModifyTime: '2025-01-15 16:08:45',
      lastModifyUser: '周九'
    }
  }
})

// 获取指标值
const getIndicatorValue = (projectId: string, indicatorId: string): number | undefined => {
  // 优先从用户输入的数据中获取
  const userValue = indicatorValues.value[projectId]?.[indicatorId]
  if (userValue !== undefined) {
    return userValue
  }

  // 然后从全局数据中获取
  const globalValue = props.globalData?.[projectId]?.[indicatorId]?.value
  if (globalValue !== undefined) {
    return globalValue
  }

  // 最后从mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.value
}

// 设置指标值
const setIndicatorValue = (projectId: string, indicatorId: string, value: number) => {
  if (!indicatorValues.value[projectId]) {
    indicatorValues.value[projectId] = {}
  }
  indicatorValues.value[projectId][indicatorId] = value

  // 记录修改时间和修改人
  const now = new Date().toLocaleString('zh-CN')
  const currentUser = '当前用户' // 这里应该从用户状态中获取

  if (!indicatorModifyTimes.value[projectId]) {
    indicatorModifyTimes.value[projectId] = {}
  }
  if (!indicatorModifyUsers.value[projectId]) {
    indicatorModifyUsers.value[projectId] = {}
  }

  indicatorModifyTimes.value[projectId][indicatorId] = now
  indicatorModifyUsers.value[projectId][indicatorId] = currentUser
}

// 处理输入变化
const handleInputChange = (projectId: string, indicatorId: string, value: string | number) => {
  console.log('handleInputChange called:', { projectId, indicatorId, value, type: typeof value })

  const key = `${projectId}-${indicatorId}`

  // 如果输入为空字符串，清除值
  if (value === '' || value === null || value === undefined) {
    // 清除值
    if (indicatorValues.value[projectId]) {
      delete indicatorValues.value[projectId][indicatorId]
    }
    // 清除修改时间和修改人（回到mock数据状态）
    if (indicatorModifyTimes.value[projectId]) {
      delete indicatorModifyTimes.value[projectId][indicatorId]
    }
    if (indicatorModifyUsers.value[projectId]) {
      delete indicatorModifyUsers.value[projectId][indicatorId]
    }
    // 同步清除输入值
    delete indicatorInputValues.value[key]
    console.log('Value cleared for:', projectId, indicatorId)
    return
  }

  // 转换为数字
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (!isNaN(numValue)) {
    console.log('Setting value:', projectId, indicatorId, numValue)
    setIndicatorValue(projectId, indicatorId, numValue)
    // 同步更新输入值
    indicatorInputValues.value[key] = value.toString()
  } else {
    console.log('Invalid number:', value)
  }
}

// 处理得分输入变化
const handleScoreChange = (projectId: string, indicatorId: string, value: string | number) => {
  console.log('handleScoreChange called:', { projectId, indicatorId, value, type: typeof value })

  const key = `${projectId}-${indicatorId}`

  // 如果输入为空字符串，清除值
  if (value === '' || value === null || value === undefined) {
    // 清除值
    if (indicatorScores.value[projectId]) {
      delete indicatorScores.value[projectId][indicatorId]
    }
    // 同步清除输入值
    delete indicatorScoreValues.value[key]
    console.log('Score cleared for:', projectId, indicatorId)
    return
  }

  // 转换为数字
  const numValue = typeof value === 'string' ? parseFloat(value) : value
  if (!isNaN(numValue)) {
    console.log('Setting score:', projectId, indicatorId, numValue)
    // 设置得分值
    if (!indicatorScores.value[projectId]) {
      indicatorScores.value[projectId] = {}
    }
    indicatorScores.value[projectId][indicatorId] = numValue

    // 记录修改时间和修改人（得分修改也要记录）
    const now = new Date().toLocaleString('zh-CN')
    const currentUser = '当前用户' // 这里应该从用户状态中获取

    if (!indicatorModifyTimes.value[projectId]) {
      indicatorModifyTimes.value[projectId] = {}
    }
    if (!indicatorModifyUsers.value[projectId]) {
      indicatorModifyUsers.value[projectId] = {}
    }

    indicatorModifyTimes.value[projectId][indicatorId] = now
    indicatorModifyUsers.value[projectId][indicatorId] = currentUser

    // 同步更新输入值
    indicatorScoreValues.value[key] = value.toString()
  } else {
    console.log('Invalid score number:', value)
  }
}

// 获取最后修改时间
const getLastModifyTime = (projectId: string, indicatorId: string): string | undefined => {
  // 优先从用户操作记录中获取
  const userModifyTime = indicatorModifyTimes.value[projectId]?.[indicatorId]
  if (userModifyTime) {
    return userModifyTime
  }

  // 然后从全局数据中获取
  const globalModifyTime = props.globalData?.[projectId]?.[indicatorId]?.lastModifyTime
  if (globalModifyTime) {
    return globalModifyTime
  }

  // 最后从mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.lastModifyTime
}

// 获取最后修改人
const getLastModifyUser = (projectId: string, indicatorId: string): string | undefined => {
  // 优先从用户操作记录中获取
  const userModifyUser = indicatorModifyUsers.value[projectId]?.[indicatorId]
  if (userModifyUser) {
    return userModifyUser
  }

  // 然后从全局数据中获取
  const globalModifyUser = props.globalData?.[projectId]?.[indicatorId]?.lastModifyUser
  if (globalModifyUser) {
    return globalModifyUser
  }

  // 最后从mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.lastModifyUser
}

// 获取指标实际得分值
const getIndicatorActualScoreValue = (projectId: string, indicatorId: string): number | undefined => {
  // 优先从用户输入的得分中获取
  const userScore = indicatorScores.value[projectId]?.[indicatorId]
  if (userScore !== undefined) {
    return userScore
  }

  // 然后从全局数据中获取
  const globalScore = props.globalData?.[projectId]?.[indicatorId]?.score
  if (globalScore !== undefined) {
    return globalScore
  }

  // 最后从mock数据中获取
  return mockIndicatorData.value[projectId]?.[indicatorId]?.score
}

// 获取指标分值（同一项目内所有指标分值相同）
const getIndicatorScore = (project: any, indicator: any): string => {
  // 同一项目内所有指标分值相同 = 项目总分 / 指标数量
  if (project.fullScore && project.indicators && project.indicators.length > 0) {
    const scorePerIndicator = (project.fullScore / project.indicators.length).toFixed(1)
    return scorePerIndicator
  }

  return '-'
}

// 获取指标实际得分
const getIndicatorActualScore = (projectId: string, indicatorId: string, project: any, indicator: any): string => {
  const value = getIndicatorValue(projectId, indicatorId)
  if (value === undefined || value === null || value === '') {
    return '-'
  }

  // 获取指标的实际满分（基于权重）
  let maxScore: number

  // 如果指标有权重，使用权重作为满分
  if (indicator.weight !== undefined && indicator.weight !== null) {
    maxScore = indicator.weight
  } else {
    // 如果没有权重，使用平均分值
    const avgScoreStr = getIndicatorScore(project, indicator)
    if (avgScoreStr === '-') {
      return '-'
    }
    maxScore = parseFloat(avgScoreStr)
    if (isNaN(maxScore)) {
      return '-'
    }
  }

  // 简单的评分逻辑示例（可根据实际需求调整）
  // 这里假设达到阈值就得满分，否则按比例计算
  const threshold = parseFloat(indicator.threshold) || 100
  const actualScore = Math.min(maxScore, (value / threshold) * maxScore)

  return actualScore.toFixed(1)
}



// 事件处理
const handleClose = () => {
  emit('update:modelValue', false)
}





const submitData = async () => {
  submitting.value = true

  // 收集所有项目的指标数据
  const submitDataObj = {
    taskId: props.taskData?.id,
    projects: props.taskData?.schemeData?.projects?.map(project => ({
      id: project.id,
      name: project.name,
      indicators: project.indicators.map(indicator => ({
        id: indicator.id,
        name: indicator.name,
        value: getIndicatorValue(project.id, indicator.id),
        score: getIndicatorActualScoreValue(project.id, indicator.id),
        lastModifyTime: getLastModifyTime(project.id, indicator.id),
        lastModifyUser: getLastModifyUser(project.id, indicator.id)
      }))
    })) || []
  }

  // 模拟提交
  setTimeout(() => {
    emit('submit', submitDataObj)
    submitting.value = false
  }, 1500)
}

// 监听任务数据变化，初始化项目展开状态
watch(() => props.taskData, (newTask) => {
  if (newTask?.schemeData?.projects) {
    // 默认展开第一个项目
    activeProjects.value = [newTask.schemeData.projects[0]?.id].filter(Boolean)

    // 初始化输入值
    newTask.schemeData.projects.forEach(project => {
      project.indicators.forEach(indicator => {
        const key = `${project.id}-${indicator.id}`

        // 初始化数值输入（包含mock数据）
        const value = getIndicatorValue(project.id, indicator.id)
        if (value !== undefined) {
          indicatorInputValues.value[key] = value.toString()
        }

        // 初始化得分输入（包含mock数据）
        const score = getIndicatorActualScoreValue(project.id, indicator.id)
        if (score !== undefined) {
          indicatorScoreValues.value[key] = score.toString()
        }
      })
    })
  }
}, { immediate: true })
</script>

<style scoped lang="scss">
.upload-container {
  .mb-4 {
    margin-bottom: 16px;
  }

  .font-bold {
    font-weight: bold;
  }

  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  .text-gray-400 {
    color: #9ca3af;
  }

  .text-red-500 {
    color: #ef4444;
  }

  .mt-4 {
    margin-top: 16px;
  }

  .error-input {
    :deep(.el-input__inner) {
      border-color: #f56565;
      background-color: #fed7d7;
    }
  }

  .warning-input {
    :deep(.el-input__inner) {
      border-color: #ed8936;
      background-color: #feebc8;
    }
  }

  .upload-demo {
    .el-upload {
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;
      transition: all 0.3s;

      &:hover {
        border-color: #409eff;
      }
    }

    .el-icon--upload {
      font-size: 67px;
      color: #c0c4cc;
      margin: 40px 0 16px;
      line-height: 50px;
    }
  }
}
</style>

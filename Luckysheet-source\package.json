{"name": "luckysheet", "version": "2.1.13", "main": "dist/luckysheet.cjs.js", "module": "dist/luckysheet.esm.js", "browser": "dist/luckysheet.umd.js", "devDependencies": {"@babel/core": "^7.12.3", "@babel/preset-env": "^7.12.1", "@babel/runtime-corejs3": "^7.12.1", "@commitlint/cli": "^9.1.1", "@commitlint/config-conventional": "^9.1.1", "@rollup/plugin-babel": "^5.2.1", "@rollup/plugin-commonjs": "^13.0.0", "@rollup/plugin-node-resolve": "^8.0.1", "browser-sync": "^2.26.7", "commitizen": "^4.1.2", "cross-env": "^7.0.2", "delete": "^1.1.0", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-clean-css": "^4.3.0", "gulp-concat": "^2.6.1", "gulp-if": "^3.0.0", "gulp-uglify": "^3.0.2", "gulp-useref": "^4.0.1", "http-proxy-middleware": "^1.0.6", "rollup": "^2.32.1", "rollup-plugin-terser": "^6.1.0", "standard-version": "^8.0.2", "uuid": "^8.3.2", "vuepress": "^1.5.0", "vuepress-plugin-baidu-autopush": "^1.0.1", "vuepress-plugin-code-copy": "^1.0.6", "vuepress-plugin-seo": "^0.1.4", "vuepress-plugin-sitemap": "^2.3.1"}, "dependencies": {"@babel/runtime": "^7.12.1", "dayjs": "^1.9.6", "esbuild": "^0.11.6", "escape-html": "^1.0.3", "flatpickr": "^4.6.6", "jquery": "^2.2.4", "nanoid": "^5.1.5", "numeral": "^2.0.6", "pako": "^1.0.11", "short-unique-id": "^5.2.2"}, "scripts": {"build": "cross-env NODE_ENV=production gulp build", "dev": "cross-env NODE_ENV=development gulp dev", "docs:dev": "vuepress dev docs", "docs:build": "vuepress build docs", "commit": "git-cz", "release": "standard-version"}, "files": ["dist"], "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}}
<template>
  <el-dialog
    v-model="dialogVisible"
    title="从检验报告创建任务"
    width="800px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="create-task-form"
    >
      <!-- 检验报告信息 -->
      <el-card class="form-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>检验报告信息</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="报告编号" prop="reportCode">
              <el-input 
                v-model="formData.reportCode" 
                placeholder="请输入检验报告编号"
                @blur="handleReportCodeBlur"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品编号" prop="sampleCode">
              <el-input v-model="formData.sampleCode" placeholder="自动获取" readonly />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="采样点" prop="samplingPoint">
              <el-input v-model="formData.samplingPoint" placeholder="自动获取" readonly />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采样日期" prop="samplingDate">
              <el-date-picker
                v-model="formData.samplingDate"
                type="date"
                placeholder="自动获取"
                readonly
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 检测项目选择 -->
      <el-card class="form-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><List /></el-icon>
            <span>检测项目选择</span>
          </div>
        </template>
        
        <el-form-item label="检测项目" prop="testItems">
          <el-checkbox-group v-model="formData.testItems" class="test-items-group">
            <el-checkbox
              v-for="item in availableTestItems"
              :key="item.id"
              :label="item.id"
              :disabled="item.disabled"
            >
              <div class="test-item-info">
                <span class="item-name">{{ item.name }}</span>
                <span class="item-method">{{ item.method }}</span>
                <el-tag v-if="item.priority === 'high'" type="danger" size="small">高优先级</el-tag>
                <el-tag v-else-if="item.priority === 'medium'" type="warning" size="small">中优先级</el-tag>
                <el-tag v-else type="info" size="small">低优先级</el-tag>
              </div>
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card>

      <!-- 任务配置 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>任务配置</span>
          </div>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检测人员" prop="assignedTo">
              <el-select v-model="formData.assignedTo" placeholder="请选择检测人员" style="width: 100%">
                <el-option label="张三" value="张三" />
                <el-option label="李四" value="李四" />
                <el-option label="王五" value="王五" />
                <el-option label="赵六" value="赵六" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="优先级" prop="priority">
              <el-select v-model="formData.priority" placeholder="请选择优先级" style="width: 100%">
                <el-option label="低" value="low" />
                <el-option label="正常" value="normal" />
                <el-option label="高" value="high" />
                <el-option label="紧急" value="urgent" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预计完成时间" prop="estimatedCompletionDate">
              <el-date-picker
                v-model="formData.estimatedCompletionDate"
                type="date"
                placeholder="请选择预计完成时间"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="批次处理">
              <el-switch
                v-model="formData.batchProcess"
                active-text="批量创建"
                inactive-text="单独创建"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="3"
            placeholder="请输入备注信息"
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ formData.batchProcess ? '批量创建任务' : '创建任务' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'CreateTaskFromReportDialog' })

const emit = defineEmits(['success'])

// 对话框状态
const dialogVisible = ref(false)
const loading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  reportCode: '',
  sampleCode: '',
  samplingPoint: '',
  samplingDate: '',
  testItems: [] as number[],
  assignedTo: '',
  priority: 'normal',
  estimatedCompletionDate: '',
  batchProcess: false,
  remark: ''
})

// 可用检测项目
const availableTestItems = ref([
  { id: 1, name: 'COD', method: '重铬酸钾法', priority: 'high', disabled: false },
  { id: 2, name: 'BOD5', method: '稀释与接种法', priority: 'medium', disabled: false },
  { id: 3, name: 'SS', method: '重量法', priority: 'low', disabled: false },
  { id: 4, name: '氨氮', method: '纳氏试剂分光光度法', priority: 'high', disabled: false },
  { id: 5, name: '总磷', method: '钼酸铵分光光度法', priority: 'medium', disabled: false },
  { id: 6, name: '重金属', method: '原子吸收分光光度法', priority: 'high', disabled: false }
])

// 表单验证规则
const formRules: FormRules = {
  reportCode: [{ required: true, message: '请输入检验报告编号', trigger: 'blur' }],
  testItems: [{ required: true, message: '请选择检测项目', trigger: 'change' }],
  assignedTo: [{ required: true, message: '请选择检测人员', trigger: 'change' }],
  priority: [{ required: true, message: '请选择优先级', trigger: 'change' }],
  estimatedCompletionDate: [{ required: true, message: '请选择预计完成时间', trigger: 'change' }]
}

// 打开对话框
const open = () => {
  dialogVisible.value = true
  resetForm()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    reportCode: '',
    sampleCode: '',
    samplingPoint: '',
    samplingDate: '',
    testItems: [],
    assignedTo: '',
    priority: 'normal',
    estimatedCompletionDate: '',
    batchProcess: false,
    remark: ''
  })
}

// 处理报告编号失焦事件
const handleReportCodeBlur = () => {
  if (formData.reportCode) {
    // 模拟根据报告编号获取相关信息
    formData.sampleCode = `SM${formData.reportCode.slice(-6)}`
    formData.samplingPoint = '进水总口'
    formData.samplingDate = new Date().toISOString().slice(0, 10)
    
    // 根据报告内容预选检测项目
    formData.testItems = [1, 2, 4] // 预选COD、BOD5、氨氮
    
    ElMessage.success('已自动获取报告相关信息')
  }
}

// 确认创建
const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟创建任务
    const tasks = formData.testItems.map((itemId, index) => {
      const testItem = availableTestItems.value.find(item => item.id === itemId)
      return {
        id: Date.now() + index,
        taskCode: `TT${Date.now().toString().slice(-6)}${index}`,
        sampleCode: formData.sampleCode,
        testItem: testItem?.name || '',
        testMethod: testItem?.method || '',
        samplingPoint: formData.samplingPoint,
        samplingDate: formData.samplingDate,
        assignedTo: formData.assignedTo,
        priority: formData.priority,
        status: 'pending',
        createDate: new Date().toISOString().slice(0, 10),
        estimatedCompletionDate: formData.estimatedCompletionDate,
        remark: formData.remark || `从检验报告${formData.reportCode}创建`
      }
    })
    
    setTimeout(() => {
      loading.value = false
      emit('success', tasks)
      ElMessage.success(`成功创建${tasks.length}个检验任务`)
      handleClose()
    }, 1000)
    
  } catch (error) {
    loading.value = false
    console.error('表单验证失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.create-task-form {
  .form-card {
    margin-bottom: 16px;
    
    .card-header {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 500;
    }
  }
  
  .test-items-group {
    display: flex;
    flex-direction: column;
    gap: 12px;
    
    .el-checkbox {
      margin-right: 0;
      
      .test-item-info {
        display: flex;
        align-items: center;
        gap: 12px;
        margin-left: 8px;
        
        .item-name {
          font-weight: 500;
          min-width: 60px;
        }
        
        .item-method {
          color: #606266;
          font-size: 12px;
          flex: 1;
        }
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

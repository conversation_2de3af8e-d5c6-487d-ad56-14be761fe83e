<template>
  <el-dialog v-model="visible" title="指标详情" width="50rem">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="指标名称">{{ selectedItem?.name }}</el-descriptions-item>
      <el-descriptions-item label="单位">{{ selectedItem?.unit }}</el-descriptions-item>
      <el-descriptions-item label="指标定义" :span="2">{{ selectedItem?.definition }}</el-descriptions-item>
      <el-descriptions-item label="指标分类">
        <el-tag :type="getCategoryType(selectedItem?.category)">
          {{ getCategoryLabel(selectedItem?.category) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="selectedItem?.status === 1 ? 'success' : 'danger'">
          {{ selectedItem?.status === 1 ? '启用' : '停用' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="评估周期">
        <el-tag type="primary" size="small">
          {{ getEvaluationPeriodLabel(selectedItem?.evaluationPeriod) }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="阈值设置">{{ selectedItem?.threshold }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ selectedItem?.createTime }}</el-descriptions-item>
      <el-descriptions-item label="基础数据项" :span="2">
        <div class="base-data-tags">
          <el-tag v-for="item in selectedItem?.baseData" :key="item" type="info" size="small" class="mr-1 mb-1">
            {{ item }}
          </el-tag>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="计算逻辑" :span="2">
        <div class="calculation-formula">
          <el-input :model-value="selectedItem?.calculation" type="textarea" :rows="3" readonly
            class="formula-display" />
          <div class="mt-2">
            <el-button size="small" @click="handleTestFormula" v-if="selectedItem?.calculation">
              <el-icon>
                <Operation />
              </el-icon>
              测试公式
            </el-button>
          </div>
        </div>
      </el-descriptions-item>
      <el-descriptions-item label="备注说明" :span="2">
        {{ selectedItem?.remark || '无' }}
      </el-descriptions-item>
      <el-descriptions-item label="创建人">{{ selectedItem?.creator }}</el-descriptions-item>
      <el-descriptions-item label="创建时间">{{ selectedItem?.createTime }}</el-descriptions-item>
    </el-descriptions>

    <!-- 公式测试区域 -->
    <div v-if="showFormulaTest" class="mt-4">
      <el-divider content-position="left">公式测试</el-divider>
      <FormulaTest :formula="selectedItem?.calculation || ''" :formula-params="formulaParams"
        :calculation-result="calculationResult" :calculation-error="calculationError" :can-calculate="canCalculate"
        @simulate="handleSimulateCalculation" @reset="handleResetTestValues" @param-change="handleParamValueChange"
        @close="handleCloseFormulaTest" />
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Operation } from '@element-plus/icons-vue'
import FormulaTest from './FormulaTest.vue'

// 定义接口类型
interface IndexItem {
  id: string
  name: string
  definition: string
  unit: string
  category: string
  threshold: string
  status: number
  evaluationPeriod: string
  baseData: string[]
  calculation: string
  remark: string
  createTime: string
  creator: string
}

// Props
interface Props {
  modelValue: boolean
  selectedItem: IndexItem | null
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'edit', item: IndexItem | null): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// 公式测试相关
const showFormulaTest = ref(false)
const formulaParams = ref<{ name: string; value: number }[]>([])
const calculationResult = ref<number | null>(null)
const calculationError = ref<string>('')

// 计算相关计算属性
const canCalculate = computed(() => {
  return formulaParams.value.length > 0 &&
    formulaParams.value.every(param =>
      typeof param.value === 'number' &&
      !isNaN(param.value)
    )
})

// 获取分类类型
const getCategoryType = (category?: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
  if (!category) return 'info'

  // 根据分类ID的前缀确定类型
  if (category.startsWith('1')) return 'warning'  // 电力系统
  if (category.startsWith('2')) return 'primary'  // 水处理系统
  if (category.startsWith('3')) return 'success'  // 环境监测
  if (category.startsWith('4')) return 'info'     // 设备运行
  if (category.startsWith('5')) return 'danger'   // 安全管理

  return 'info'
}

// 获取分类标签
const getCategoryLabel = (category?: string) => {
  if (!category) return '未知分类'

  const labelMap: Record<string, string> = {
    '1': '电力系统',
    '1-1': '电能质量',
    '1-2': '设备效率',
    '2': '水处理系统',
    '2-1': '水质监测',
    '2-2': '处理效果',
    '3': '环境监测',
    '3-1': '噪声监测',
    '3-2': '温度监测',
    '4': '设备运行',
    '5': '安全管理'
  }

  return labelMap[category] || '未知分类'
}

// 获取评估周期标签
const getEvaluationPeriodLabel = (period?: string) => {
  const labelMap: Record<string, string> = {
    hour: '时',
    day: '天',
    week: '周',
    month: '月度',
    quarter: '季度',
    year: '年度'
  }
  return labelMap[period || ''] || '未设置'
}

// 测试公式
const handleTestFormula = () => {
  if (!props.selectedItem?.baseData || props.selectedItem.baseData.length === 0) {
    ElMessage.warning('该指标没有配置基础数据项')
    return
  }

  // 设置公式参数用于测试
  formulaParams.value = props.selectedItem.baseData.map(variable => ({
    name: variable,
    value: 0
  }))

  showFormulaTest.value = true
  calculationResult.value = null
  calculationError.value = ''
}

// 安全的数学表达式计算
const evaluateFormula = (formula: string, variables: { [key: string]: number }): number => {
  if (!formula || !formula.trim()) {
    throw new Error('公式不能为空')
  }

  // 替换公式中的变量为实际值
  let expression = formula.trim()

  // 按变量名长度从长到短排序，避免短名称被误替换
  const sortedVariables = Object.keys(variables).sort((a, b) => b.length - a.length)

  for (const variable of sortedVariables) {
    const value = variables[variable]
    if (typeof value !== 'number' || !isFinite(value)) {
      throw new Error(`变量 "${variable}" 的值无效: ${value}`)
    }

    // 使用正则表达式确保完整匹配变量名
    const escapedVariable = variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const regex = new RegExp(`\\b${escapedVariable}\\b|${escapedVariable}(?=[^\\u4e00-\\u9fa5a-zA-Z0-9_]|$)`, 'g')
    expression = expression.replace(regex, `(${value})`)
  }

  // 替换数学符号
  expression = expression.replace(/×/g, '*').replace(/÷/g, '/')

  // 处理常用函数
  expression = expression.replace(/abs\(([^)]+)\)/g, 'Math.abs($1)')
  expression = expression.replace(/max\(([^)]+)\)/g, 'Math.max($1)')
  expression = expression.replace(/min\(([^)]+)\)/g, 'Math.min($1)')

  // 检查是否还有未替换的变量
  const remainingVariables = expression.match(/[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*/g)
  if (remainingVariables && remainingVariables.length > 0) {
    // 过滤掉Math函数
    const unknownVars = remainingVariables.filter(v => !v.startsWith('Math'))
    if (unknownVars.length > 0) {
      throw new Error(`公式中包含未定义的变量: ${unknownVars.join(', ')}`)
    }
  }

  // 验证表达式安全性
  const safePattern = /^[0-9+\-*/().\s,Math.abs|Math.max|Math.min|Math.pow|Math.sqrt|Math.floor|Math.ceil|Math.round]+$/
  if (!safePattern.test(expression)) {
    throw new Error('公式包含不支持的字符或函数')
  }

  // 检查括号匹配
  let parenthesesCount = 0
  for (const char of expression) {
    if (char === '(') parenthesesCount++
    if (char === ')') parenthesesCount--
    if (parenthesesCount < 0) {
      throw new Error('括号不匹配：多余的右括号')
    }
  }
  if (parenthesesCount !== 0) {
    throw new Error('括号不匹配：缺少右括号')
  }

  // 计算结果
  try {
    // 使用Function构造器安全执行表达式
    const result = new Function('Math', `"use strict"; return (${expression})`)(Math)

    if (typeof result !== 'number') {
      throw new Error('计算结果不是数字')
    }

    if (!isFinite(result)) {
      if (isNaN(result)) {
        throw new Error('计算结果为NaN，请检查公式是否正确')
      } else {
        throw new Error('计算结果为无穷大，可能存在除零错误')
      }
    }

    return result
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`计算错误: ${error.message}`)
    } else {
      throw new Error('计算过程中发生未知错误')
    }
  }
}

// 模拟计算
const handleSimulateCalculation = () => {
  try {
    calculationError.value = ''

    if (!canCalculate.value) {
      calculationError.value = '请输入所有变量的测试值'
      ElMessage.warning('请输入所有变量的测试值')
      return
    }

    if (!props.selectedItem?.calculation) {
      calculationError.value = '没有可计算的公式'
      ElMessage.warning('没有可计算的公式')
      return
    }

    // 构建变量映射
    const variables: { [key: string]: number } = {}
    formulaParams.value.forEach(param => {
      variables[param.name] = param.value
    })

    // 执行公式计算
    const result = evaluateFormula(props.selectedItem.calculation, variables)
    calculationResult.value = Number(result.toFixed(6))
    calculationError.value = ''

    ElMessage.success(`计算完成，结果: ${calculationResult.value}`)
  } catch (error) {
    calculationResult.value = null
    calculationError.value = error instanceof Error ? error.message : '计算过程中发生未知错误'
    ElMessage.error(`计算失败: ${calculationError.value}`)
  }
}

// 参数值变化处理
const handleParamValueChange = () => {
  calculationError.value = ''
}

// 重置测试值
const handleResetTestValues = () => {
  formulaParams.value.forEach(param => {
    param.value = 0
  })
  calculationResult.value = null
  calculationError.value = ''
}

// 关闭公式测试
const handleCloseFormulaTest = () => {
  showFormulaTest.value = false
  formulaParams.value = []
  calculationResult.value = null
  calculationError.value = ''
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

.base-data-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.25rem;
}

.calculation-formula {
  .formula-display {
    :deep(.el-textarea__inner) {
      background-color: #f5f7fa;
      border: 0.0625rem solid #e4e7ed;
      color: #606266;
      font-family: 'Courier New', monospace;
    }
  }
}

:deep(.el-descriptions__body) {
  .el-descriptions__table {
    .el-descriptions__cell {
      vertical-align: top;
    }
  }
}
</style>

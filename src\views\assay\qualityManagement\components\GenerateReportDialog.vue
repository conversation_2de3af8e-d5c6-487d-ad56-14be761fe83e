<template>
  <el-dialog
    v-model="dialogVisible"
    title="生成检验报告"
    width="1000px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="generate-report-container">
      <!-- 报告基本信息 -->
      <el-card class="report-info-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><Document /></el-icon>
            <span>报告基本信息</span>
          </div>
        </template>
        
        <el-form :model="formData" :rules="formRules" ref="formRef" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="报告编号" prop="reportCode">
                <el-input v-model="formData.reportCode" placeholder="自动生成" readonly />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报告类型" prop="reportType">
                <el-select v-model="formData.reportType" placeholder="请选择报告类型" style="width: 100%">
                  <el-option label="单项检验报告" value="single" />
                  <el-option label="综合检验报告" value="comprehensive" />
                  <el-option label="异常检验报告" value="exception" />
                  <el-option label="复检报告" value="recheck" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="报告格式" prop="reportFormat">
                <el-select v-model="formData.reportFormat" placeholder="请选择报告格式" style="width: 100%">
                  <el-option label="PDF格式" value="pdf" />
                  <el-option label="Excel格式" value="excel" />
                  <el-option label="Word格式" value="word" />
                  <el-option label="CSV格式" value="csv" />
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="报告标题" prop="reportTitle">
                <el-input v-model="formData.reportTitle" placeholder="请输入报告标题" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="生成日期" prop="generateDate">
                <el-date-picker
                  v-model="formData.generateDate"
                  type="date"
                  placeholder="请选择生成日期"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </el-card>

      <!-- 样品数据 -->
      <el-card class="sample-data-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><DataAnalysis /></el-icon>
            <span>样品检验数据</span>
          </div>
        </template>
        
        <el-table :data="sampleData" border style="width: 100%">
          <el-table-column prop="sampleCode" label="样品编号" width="120" />
          <el-table-column prop="testItem" label="检测项目" width="120" />
          <el-table-column prop="testValue" label="检测值" width="100">
            <template #default="{ row }">
              <span :class="{ 'exceed-value': row.isExceeded }">
                {{ row.testValue }} {{ row.unit }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="standardValue" label="标准值" width="100">
            <template #default="{ row }">
              {{ row.standardValue }} {{ row.unit }}
            </template>
          </el-table-column>
          <el-table-column prop="testDate" label="检测日期" width="120" />
          <el-table-column prop="tester" label="检测人员" width="100" />
          <el-table-column label="检测结果" width="100">
            <template #default="{ row }">
              <el-tag :type="row.isExceeded ? 'danger' : 'success'">
                {{ row.isExceeded ? '超标' : '合格' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
        </el-table>
      </el-card>

      <!-- 报告配置 -->
      <el-card class="report-config-card mb-4">
        <template #header>
          <div class="card-header">
            <el-icon><Setting /></el-icon>
            <span>报告配置</span>
          </div>
        </template>
        
        <el-form :model="formData" label-width="120px">
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="包含内容">
                <el-checkbox-group v-model="formData.includeContent">
                  <el-checkbox label="summary">检验摘要</el-checkbox>
                  <el-checkbox label="details">详细数据</el-checkbox>
                  <el-checkbox label="charts">图表分析</el-checkbox>
                  <el-checkbox label="conclusion">结论建议</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="附加信息">
                <el-checkbox-group v-model="formData.additionalInfo">
                  <el-checkbox label="method">检测方法</el-checkbox>
                  <el-checkbox label="equipment">检测设备</el-checkbox>
                  <el-checkbox label="environment">环境条件</el-checkbox>
                  <el-checkbox label="signature">签名信息</el-checkbox>
                </el-checkbox-group>
              </el-form-item>
            </el-col>
          </el-row>
          
          <el-form-item label="报告说明" prop="reportDescription">
            <el-input
              v-model="formData.reportDescription"
              type="textarea"
              :rows="3"
              placeholder="请输入报告说明"
            />
          </el-form-item>
          
          <el-form-item label="审核人员" prop="reviewer">
            <el-select v-model="formData.reviewer" placeholder="请选择审核人员" style="width: 200px">
              <el-option label="张三" value="张三" />
              <el-option label="李四" value="李四" />
              <el-option label="王五" value="王五" />
            </el-select>
          </el-form-item>
        </el-form>
      </el-card>

      <!-- 报告预览 -->
      <el-card class="report-preview-card">
        <template #header>
          <div class="card-header">
            <el-icon><View /></el-icon>
            <span>报告预览</span>
            <el-button type="primary" size="small" @click="handlePreview" style="margin-left: auto;">
              刷新预览
            </el-button>
          </div>
        </template>
        
        <div class="report-preview" v-loading="previewLoading">
          <div class="report-header">
            <h2>{{ formData.reportTitle || '检验报告' }}</h2>
            <div class="report-meta">
              <span>报告编号：{{ formData.reportCode }}</span>
              <span>生成日期：{{ formData.generateDate }}</span>
            </div>
          </div>
          
          <div class="report-content">
            <div v-if="formData.includeContent.includes('summary')" class="report-section">
              <h3>检验摘要</h3>
              <p>本次检验共涉及{{ sampleData.length }}个样品，{{ getQualifiedCount() }}个合格，{{ getExceededCount() }}个超标。</p>
            </div>
            
            <div v-if="formData.includeContent.includes('details')" class="report-section">
              <h3>详细数据</h3>
              <div class="data-table">
                <table>
                  <thead>
                    <tr>
                      <th>样品编号</th>
                      <th>检测项目</th>
                      <th>检测值</th>
                      <th>标准值</th>
                      <th>结果</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr v-for="item in sampleData" :key="item.sampleCode">
                      <td>{{ item.sampleCode }}</td>
                      <td>{{ item.testItem }}</td>
                      <td>{{ item.testValue }} {{ item.unit }}</td>
                      <td>{{ item.standardValue }} {{ item.unit }}</td>
                      <td>{{ item.isExceeded ? '超标' : '合格' }}</td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
            
            <div v-if="formData.includeContent.includes('conclusion')" class="report-section">
              <h3>结论建议</h3>
              <p>{{ getConclusion() }}</p>
            </div>
          </div>
          
          <div class="report-footer">
            <div class="signature-section">
              <span>检测人员：_____________</span>
              <span>审核人员：{{ formData.reviewer || '_____________' }}</span>
              <span>日期：{{ new Date().toLocaleDateString() }}</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="info" @click="handlePreview" :loading="previewLoading">预览</el-button>
        <el-button type="primary" @click="handleGenerate" :loading="loading">
          生成报告
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'GenerateReportDialog' })

const emit = defineEmits(['success'])

// 对话框状态
const dialogVisible = ref(false)
const loading = ref(false)
const previewLoading = ref(false)
const formRef = ref<FormInstance>()

// 表单数据
const formData = reactive({
  reportCode: '',
  reportType: 'single',
  reportFormat: 'pdf',
  reportTitle: '',
  generateDate: new Date().toISOString().slice(0, 10),
  includeContent: ['summary', 'details', 'conclusion'],
  additionalInfo: ['method', 'signature'],
  reportDescription: '',
  reviewer: ''
})

// 样品数据
const sampleData = ref([])

// 表单验证规则
const formRules: FormRules = {
  reportType: [{ required: true, message: '请选择报告类型', trigger: 'change' }],
  reportFormat: [{ required: true, message: '请选择报告格式', trigger: 'change' }],
  reportTitle: [{ required: true, message: '请输入报告标题', trigger: 'blur' }],
  generateDate: [{ required: true, message: '请选择生成日期', trigger: 'change' }],
  reviewer: [{ required: true, message: '请选择审核人员', trigger: 'change' }]
}

// 打开对话框
const open = (data: any) => {
  dialogVisible.value = true
  
  // 设置样品数据
  if (Array.isArray(data)) {
    sampleData.value = data
  } else {
    sampleData.value = [data]
  }
  
  // 生成报告编号
  formData.reportCode = `RPT${Date.now().toString().slice(-8)}`
  
  // 设置默认标题
  if (sampleData.value.length === 1) {
    formData.reportTitle = `${sampleData.value[0].sampleCode}检验报告`
  } else {
    formData.reportTitle = `批量检验报告(${sampleData.value.length}个样品)`
  }
}

// 获取合格数量
const getQualifiedCount = () => {
  return sampleData.value.filter(item => !item.isExceeded).length
}

// 获取超标数量
const getExceededCount = () => {
  return sampleData.value.filter(item => item.isExceeded).length
}

// 获取结论
const getConclusion = () => {
  const exceededCount = getExceededCount()
  if (exceededCount === 0) {
    return '所有检测项目均符合标准要求，水质状况良好。'
  } else {
    return `发现${exceededCount}个项目超标，建议加强处理工艺控制，必要时进行复检。`
  }
}

// 预览报告
const handlePreview = () => {
  previewLoading.value = true
  setTimeout(() => {
    previewLoading.value = false
    ElMessage.success('预览已更新')
  }, 500)
}

// 生成报告
const handleGenerate = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟生成报告
    setTimeout(() => {
      loading.value = false
      
      const reportInfo = {
        reportCode: formData.reportCode,
        reportType: formData.reportType,
        reportFormat: formData.reportFormat,
        reportTitle: formData.reportTitle,
        sampleCount: sampleData.value.length,
        generateDate: formData.generateDate,
        reviewer: formData.reviewer
      }
      
      emit('success', reportInfo)
      ElMessage.success(`检验报告已生成：${formData.reportCode}`)
      handleClose()
    }, 2000)
    
  } catch (error) {
    loading.value = false
    console.error('表单验证失败:', error)
  }
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

// 重置表单
const resetForm = () => {
  formRef.value?.resetFields()
  Object.assign(formData, {
    reportCode: '',
    reportType: 'single',
    reportFormat: 'pdf',
    reportTitle: '',
    generateDate: new Date().toISOString().slice(0, 10),
    includeContent: ['summary', 'details', 'conclusion'],
    additionalInfo: ['method', 'signature'],
    reportDescription: '',
    reviewer: ''
  })
  sampleData.value = []
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.generate-report-container {
  .card-header {
    display: flex;
    align-items: center;
    gap: 8px;
    font-weight: 500;
  }
  
  .exceed-value {
    color: #f56c6c;
    font-weight: bold;
  }
  
  .report-preview {
    border: 1px solid #e4e7ed;
    border-radius: 6px;
    padding: 20px;
    background: white;
    min-height: 400px;
    
    .report-header {
      text-align: center;
      border-bottom: 2px solid #409eff;
      padding-bottom: 16px;
      margin-bottom: 20px;
      
      h2 {
        margin: 0 0 8px 0;
        color: #303133;
      }
      
      .report-meta {
        display: flex;
        justify-content: space-between;
        color: #606266;
        font-size: 14px;
      }
    }
    
    .report-content {
      .report-section {
        margin-bottom: 20px;
        
        h3 {
          color: #409eff;
          border-left: 4px solid #409eff;
          padding-left: 8px;
          margin-bottom: 12px;
        }
        
        .data-table {
          table {
            width: 100%;
            border-collapse: collapse;
            
            th, td {
              border: 1px solid #e4e7ed;
              padding: 8px;
              text-align: left;
            }
            
            th {
              background: #f5f7fa;
              font-weight: 500;
            }
          }
        }
      }
    }
    
    .report-footer {
      border-top: 1px solid #e4e7ed;
      padding-top: 16px;
      margin-top: 20px;
      
      .signature-section {
        display: flex;
        justify-content: space-between;
        color: #606266;
      }
    }
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>

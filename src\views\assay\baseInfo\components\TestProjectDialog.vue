<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="800px">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="检测项目名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入检测项目名称，如：有机物检测" />
      </el-form-item>
      
      <el-form-item label="检测项目代码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入检测项目代码，如：WATER_ORGANIC" />
      </el-form-item>
      
      <el-form-item label="所属类型" prop="categoryId">
        <el-select
          v-model="formData.categoryId"
          placeholder="请选择所属类型"
          style="width: 100%"
          :disabled="isDefaultCategory"
        >
          <el-option
            v-for="category in categoryOptions"
            :key="category.id"
            :label="category.name"
            :value="category.id"
          />
        </el-select>
      </el-form-item>
      
      <el-form-item label="检测项目描述" prop="description">
        <el-input 
          v-model="formData.description" 
          type="textarea" 
          :rows="3"
          placeholder="请输入检测项目描述" 
        />
      </el-form-item>
      
      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio :label="0">启用</el-radio>
          <el-radio :label="1">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { AssayBaseInfoApi } from '@/api/assay/baseInfo'
import { useAppStore } from '@/store/modules/app'

defineOptions({ name: 'TestProjectDialog' })

const appStore = useAppStore()

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')
const categoryOptions = ref([]) // 类型选项
const isDefaultCategory = ref(false) // 是否有默认类型（不允许编辑）

// 表单数据
const formData = ref({
  id: undefined,
  name: '',
  code: '',
  categoryId: undefined,
  description: '',
  status: 0
})

// 表单校验规则
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '检测项目名称不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '检测项目代码不能为空', trigger: 'blur' }],
  categoryId: [{ required: true, message: '所属类型不能为空', trigger: 'change' }]
})

const formRef = ref()
const emit = defineEmits(['success'])

// 加载类型选项
const loadCategoryOptions = async () => {
  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    console.warn('⚠️ 未获取到当前水厂ID')
    return
  }

  try {
    const res = await AssayBaseInfoApi.getTestCategorySimpleList(factoryId)
    if (res && res.data) {
      categoryOptions.value = res.data
    }
  } catch (error) {
    console.error('❌ 获取检测项目类型选项失败:', error)
  }
}

// 打开对话框
const open = async (type: string, data?: any, categoryId?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增检测项目' : '编辑检测项目'
  formType.value = type
  resetForm()

  // 加载类型选项
  await loadCategoryOptions()

  // 如果是新增模式且指定了类型ID，设置默认值并禁用编辑
  if (type === 'create' && categoryId) {
    formData.value.categoryId = categoryId
    isDefaultCategory.value = true // 禁用类型选择
  } else {
    isDefaultCategory.value = false // 允许类型选择
  }
  
  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    formData.value = JSON.parse(JSON.stringify(data))
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  const factoryId = appStore.currentStation?.id
  if (!factoryId) {
    ElMessage.error('水厂ID未获取，无法提交')
    return
  }

  formLoading.value = true
  try {
    const data = {
      id: formData.value.id,
      factoryId,
      code: formData.value.code,
      name: formData.value.name,
      categoryId: formData.value.categoryId,
      description: formData.value.description,
      isEnabled: formData.value.status === 0
    }

    if (formType.value === 'create') {
      await AssayBaseInfoApi.createTestProject(data)
    } else {
      await AssayBaseInfoApi.updateTestProject(data)
    }

    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    code: '',
    categoryId: undefined,
    description: '',
    status: 0
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.el-form-item {
  margin-bottom: 18px;
}
</style>

// 评估方案管理组件导出
export { default as SchemeFormDialog } from './SchemeFormDialog.vue'
export { default as SchemeDetailDialog } from './SchemeDetailDialog.vue'
export { default as IndicatorSelector } from './IndicatorSelector.vue'

// 类型定义导出
export interface Indicator {
  id: string
  name: string
  unit: string
  formula: string
  description?: string
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  weight?: number
}

export interface Project {
  id: string
  name: string
  weight: number
  scoreType: '加权平均' | '打分制'
  fullScore: number
  indicators: Indicator[]
}

export interface SchemeItem {
  id: string
  name: string
  status: '启用中' | '已失效'
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  startDate: string
  endDate: string
  description: string
  projects: Project[]
  createTime: string
  creator: string
}

export interface SchemeFormData {
  name: string
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  startDate: string
  endDate: string
  description: string
  projects: Project[]
}

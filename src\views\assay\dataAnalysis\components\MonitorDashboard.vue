<template>
  <div class="monitor-dashboard">
    <el-row :gutter="20">
      <!-- 顶部统计卡片 -->
      <el-col :span="6" v-for="(card, index) in statisticsCards" :key="index">
        <el-card shadow="hover" class="statistics-card" :body-style="{ padding: '1.2rem' }">
          <div class="card-content">
            <div class="card-icon" :style="{ backgroundColor: card.color }">
              <el-icon><component :is="card.icon" /></el-icon>
            </div>
            <div class="card-info">
              <div class="card-title">{{ card.title }}</div>
              <div class="card-value">
                <CountTo
                  :start-val="0"
                  :end-val="card.value"
                  :duration="2000"
                  :decimals="card.decimals || 0"
                />
                <span class="unit">{{ card.unit }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <!-- 左侧任务执行趋势 -->
      <el-col :span="16">
        <el-card shadow="hover" class="full-height-card">
          <template #header>
            <div class="card-header">
              <span>任务执行趋势</span>
              <div class="header-right">
                <span class="update-time">更新时间: {{ formatTime(latestUpdateTime) }}</span>
                <el-select v-model="currentPlant" placeholder="选择处理厂" size="small" class="ml-2">
                  <el-option
                    v-for="item in plantOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
                <!-- <el-button
                  type="primary"
                  size="small"
                  @click="handleManualRefresh"
                  :loading="refreshLoading"
                  class="ml-2"
                >
                  <el-icon><Refresh /></el-icon>刷新
                </el-button> -->
                <!-- <el-dropdown trigger="click" @command="handleRefreshIntervalChange" class="ml-2">
                  <el-button size="small">
                    {{ refreshIntervalLabel }}
                    <el-icon class="el-icon--right">
                      <arrow-down />
                    </el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item :command="0">关闭自动刷新</el-dropdown-item>
                      <el-dropdown-item :command="30">30秒</el-dropdown-item>
                      <el-dropdown-item :command="60">1分钟</el-dropdown-item>
                      <el-dropdown-item :command="300">5分钟</el-dropdown-item>
                      <el-dropdown-item :command="600">10分钟</el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown> -->
              </div>
            </div>
          </template>
          <div class="monitor-charts">
            <div ref="qualityChart" class="quality-chart"></div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 右侧告警信息 -->
      <el-col :span="8">
        <el-card shadow="hover" class="full-height-card">
          <template #header>
            <div class="card-header">
              <span>检测数据异常告警</span>
              <el-tag v-if="alarms.length > 0" type="danger" class="alarm-tag">
                {{ alarms.length }}个异常
              </el-tag>
            </div>
          </template>
          <div class="alarm-list">
            <el-empty v-if="alarms.length === 0" description="暂无异常数据" />
            <div v-else>
              <el-timeline>
                <el-timeline-item
                  v-for="(alarm, index) in alarms"
                  :key="index"
                  :type="alarm.level === 'high' ? 'danger' : (alarm.level === 'medium' ? 'warning' : 'info')"
                  :timestamp="alarm.time"
                >
                  <el-card class="alarm-card">
                    <div class="alarm-title">
                      <span>{{ alarm.title }}</span>
                      <el-tag size="small" :type="alarm.level === 'high' ? 'danger' : (alarm.level === 'medium' ? 'warning' : 'info')">
                        {{ alarm.level === 'high' ? '高' : (alarm.level === 'medium' ? '中' : '低') }}
                      </el-tag>
                    </div>
                    <div class="alarm-content">
                      {{ alarm.content }}
                    </div>
                    <div class="alarm-footer">
                      <span>位置: {{ alarm.location }}</span>
                      <el-button link type="primary" size="small" @click="handleViewDetail(alarm)">查看详情</el-button>
                    </div>
                  </el-card>
                </el-timeline-item>
              </el-timeline>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="mt-4">
      <!-- 各水厂检测数据比较 -->
      <el-col :span="24">
        <el-card shadow="hover" class="comparison-card">
          <template #header>
            <div class="card-header">
              <span>各水厂关键指标对比</span>
              <div class="header-right">
                <el-select v-model="comparisonIndicator" placeholder="选择指标" size="small">
                  <el-option
                    v-for="item in indicatorOptions"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </div>
            </div>
          </template>
          <div class="comparison-chart-container">
            <div ref="comparisonChart" class="comparison-chart"></div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
import { ref, computed, onMounted, onUnmounted, nextTick, defineComponent, watch } from 'vue'
import * as echarts from 'echarts'
import { CountTo } from '@/components/CountTo'
import { ElMessage } from 'element-plus'
import { DataAnalysis, CircleCheck, Histogram, Warning, Refresh, ArrowDown } from '@element-plus/icons-vue'
import { ChartManager, ChartUtils } from '@/utils/chartUtils'

// 导入API
import { AssayDataAnalysisApi, type AssayOverviewVO } from '@/api/assay/dataAnalysis'

// 定义组件并设置默认导出
export default defineComponent({
  name: 'MonitorDashboard',
  components: {
    CountTo,
    DataAnalysis,
    CircleCheck,
    Histogram,
    Warning,
    Refresh,
    ArrowDown
  },
  setup() {
    // ✅ 优化后的水厂选项 - 基于合肥地区实际污水厂
    const plantOptions = ref([
      { label: '北涝圩污水厂', value: 'north', capacity: '30万吨/日', status: 'normal' },
      { label: '南湖污水厂', value: 'south', capacity: '25万吨/日', status: 'normal' },
      { label: '西区污水厂', value: 'west', capacity: '20万吨/日', status: 'warning' },
      { label: '东部污水厂', value: 'east', capacity: '15万吨/日', status: 'normal' },
      { label: '滨湖污水厂', value: 'binhu', capacity: '10万吨/日', status: 'normal' },
      { label: '新站污水厂', value: 'xinzhan', capacity: '12万吨/日', status: 'normal' }
    ])

    // 当前选择的水厂
    const currentPlant = ref('north')

    // ✅ 优化后的指标选项 - 与基础信息模块保持一致
    const indicatorOptions = ref([
      { label: 'pH值', value: 'pH', unit: '无量纲', standard: '6-9', priority: 'high' },
      { label: 'COD', value: 'COD', unit: 'mg/L', standard: '≤50', priority: 'high' },
      { label: 'BOD5', value: 'BOD5', unit: 'mg/L', standard: '≤10', priority: 'high' },
      { label: '氨氮', value: 'NH3-N', unit: 'mg/L', standard: '≤5', priority: 'high' },
      { label: '总磷', value: 'TP', unit: 'mg/L', standard: '≤0.5', priority: 'high' },
      { label: '总氮', value: 'TN', unit: 'mg/L', standard: '≤15', priority: 'high' },
      { label: '悬浮物', value: 'SS', unit: 'mg/L', standard: '≤10', priority: 'medium' },
      { label: '浊度', value: 'Turbidity', unit: 'NTU', standard: '≤5', priority: 'medium' }
    ])

    // 当前选择的比较指标
    const comparisonIndicator = ref('COD')

    // 图表引用
    const qualityChart = ref<HTMLElement | null>(null)
    const comparisonChart = ref<HTMLElement | null>(null)

    // 图表管理器
    const chartManager = new ChartManager()

    // 最近更新时间
    const latestUpdateTime = ref(new Date())

    // 定时器
    let timer: number | null = null

    // 刷新状态
    const refreshLoading = ref(false)

    // 自动刷新间隔（秒）
    const refreshInterval = ref(30)

    // 刷新间隔标签
    const refreshIntervalLabel = computed(() => {
      if (refreshInterval.value === 0) return '关闭自动刷新'
      if (refreshInterval.value < 60) return `${refreshInterval.value}秒`
      return `${Math.floor(refreshInterval.value / 60)}分钟`
    })

    // ✅ 化验管理业务统计卡片数据 - 贴近计划->采样->检验->数据录入->审核流程
    const statisticsCards = ref([
      {
        title: '今日采样任务',
        value: 28,  // 基于采样计划模块的任务数量
        icon: 'DataAnalysis',
        color: '#409EFF',
        unit: '个',
        trend: '+3',  // 相比昨日增长
        description: '包含常规和临时采样任务'
      },
      {
        title: '样品检测完成率',
        value: 87.5,  // 基于检验数据管理模块的完成率
        decimals: 1,
        icon: 'CircleCheck',
        color: '#67C23A',
        unit: '%',
        trend: '+2.3%',
        description: '已完成检测的样品比例'
      },
      {
        title: '待审核数据',
        value: 12,  // 基于质量管理模块的待审核数据
        icon: 'Histogram',
        color: '#E6A23C',
        unit: '项',
        trend: '+2',
        description: '需要质量审核的检测数据'
      },
      {
        title: '异常数据',
        value: 4,  // 基于质量管理模块的待审核数据
        icon: 'Warning',
        color: '#F56C6C',
        unit: '项',
        trend: '+1',
        description: '检测异常的数据'
      } 
    ])

    interface Alarm {
      id: number
      title: string
      content: string
      level: 'high' | 'medium' | 'low'
      time: string
      location: string
      sampleCode?: string
      testValue?: string
      standardValue?: string
      unit?: string
    }

    // ✅ 化验管理业务告警数据 - 基于任务和质量管理
    const alarms = ref<Alarm[]>([
      {
        id: 1,
        title: '样品检测超时告警',
        content: '样品SP20240120003检测时间超过规定期限，需要紧急处理',
        level: 'high',
        time: '2024-01-20 14:23:45',
        location: '北涝圩污水厂-化验室',
        sampleCode: 'SP20240120003',
        testValue: '超时48小时',
        standardValue: '≤24小时',
        unit: ''
      },
      {
        id: 2,
        title: '检测数据异常告警',
        content: '总磷检测值异常偏高，疑似样品污染或设备故障',
        level: 'high',
        time: '2024-01-20 09:30:15',
        location: '北涝圩污水厂-化验室',
        sampleCode: 'SP20240120001',
        testValue: '1.2',
        standardValue: '≤0.5',
        unit: 'mg/L'
      },
      {
        id: 3,
        title: '样品保存期限预警',
        content: '5个样品即将超过保存期限，请及时处理',
        level: 'medium',
        time: '2024-01-20 08:15:30',
        location: '北涝圩污水厂-样品库',
        sampleCode: 'SP20240115002等',
        testValue: '剩余12小时',
        standardValue: '≤72小时',
        unit: ''
      },
      {
        id: 4,
        title: '质量审核待处理',
        content: '12项检测数据等待质量审核，请及时处理',
        level: 'medium',
        time: '2024-01-20 07:45:12',
        location: '北涝圩污水厂-质量管理',
        sampleCode: '多个样品',
        testValue: '12项',
        standardValue: '≤5项',
        unit: ''
      },
      {
        id: 5,
        title: '采样计划提醒',
        content: '明日有8个采样任务需要执行，请提前准备',
        level: 'low',
        time: '2024-01-20 06:30:00',
        location: '北涝圩污水厂-采样点',
        sampleCode: '计划任务',
        testValue: '8个任务',
        standardValue: '正常',
        unit: ''
      },
      {
        id: 6,
        title: '设备校准提醒',
        content: 'pH计等检测设备需要进行定期校准',
        level: 'low',
        time: '2024-01-19 16:20:30',
        location: '北涝圩污水厂-化验室',
        sampleCode: '设备维护',
        testValue: '7天未校准',
        standardValue: '≤7天',
        unit: ''
      }
    ])

    // 格式化时间
    const formatTime = (time: Date): string => {
      if (!time) return ''
      const date = new Date(time)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hour = String(date.getHours()).padStart(2, '0')
      const minute = String(date.getMinutes()).padStart(2, '0')
      const second = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`
    }

    // 初始化任务执行趋势图表
    const initQualityChart = async () => {
      if (!qualityChart.value) return

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'cross'
          },
          formatter: function(params: any) {
            let result = params[0].name + '<br/>'
            params.forEach((item: any) => {
              result += `${item.marker}${item.seriesName}: ${item.value}${item.seriesName.includes('率') ? '%' : '个'}<br/>`
            })
            return result
          }
        },
        legend: {
          data: ['计划任务', '完成任务', '异常任务', '完成率'],
          bottom: 5,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '5%',
          right: '8%',
          bottom: '20%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: true,
          data: (() => {
            const now = new Date()
            const res: string[] = []
            for (let i = 6; i >= 0; i--) {
              const date = new Date(now.getTime() - i * 24 * 3600000)
              res.push(`${date.getMonth() + 1}/${date.getDate()}`)
            }
            return res
          })()
        },
        yAxis: [
          {
            type: 'value',
            name: '任务数量',
            min: 0,
            max: 50,
            interval: 10,
            axisLabel: {
              formatter: '{value}个'
            }
          },
          {
            type: 'value',
            name: '完成率',
            min: 0,
            max: 100,
            interval: 20,
            axisLabel: {
              formatter: '{value}%'
            }
          }
        ],
        series: [
          {
            name: '计划任务',
            type: 'bar',
            data: [32, 28, 35, 30, 42, 38, 28],
            itemStyle: {
              color: '#409EFF'
            }
          },
          {
            name: '完成任务',
            type: 'bar',
            data: [30, 26, 32, 28, 35, 33, 25],
            itemStyle: {
              color: '#67C23A'
            }
          },
          {
            name: '异常任务',
            type: 'bar',
            data: [2, 2, 3, 2, 7, 5, 3],
            itemStyle: {
              color: '#F56C6C'
            }
          },
          {
            name: '完成率',
            type: 'line',
            smooth: true,
            yAxisIndex: 1,
            data: [93.8, 92.9, 91.4, 93.3, 83.3, 86.8, 89.3],
            itemStyle: {
              color: '#E6A23C'
            },
            lineStyle: {
              width: 3
            },
            markLine: {
              silent: true,
              data: [
                { yAxis: 90, lineStyle: { color: '#F56C6C', type: 'dashed' }, label: { formatter: '目标完成率: 90%' } }
              ]
            }
          }
        ]
      }

      const chart = await ChartUtils.safeInitChart(qualityChart.value, option)
      chartManager.register('quality', chart)
    }

    // 初始化水厂对比图表
    const initComparisonChart = async () => {
      if (!comparisonChart.value) return

      const option = {
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['进水', '出水', '标准值'],
          top: 0,
          textStyle: {
            fontSize: 12
          }
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',  // 增加底部空间
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: ['北涝圩', '南湖', '西区', '东部'],
          axisLabel: {
            interval: 0,
            rotate: 30,  // 旋转标签
            fontSize: 14
          }
        },
        yAxis: {
          type: 'value',
          name: 'COD (mg/L)',
          nameTextStyle: {
            fontSize: 12
          },
          axisLabel: {
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '进水',
            type: 'bar',
            data: [320, 290, 310, 330],
            itemStyle: {
              color: '#E6A23C'
            },
            barWidth: '20%',
            barGap: '20%'
          },
          {
            name: '出水',
            type: 'bar',
            data: [48, 42, 45, 40],
            itemStyle: {
              color: '#409EFF'
            },
            barWidth: '20%'
          },
          {
            name: '标准值',
            type: 'line',
            data: [50, 50, 50, 50],
            symbolSize: 0,
            lineStyle: {
              color: '#F56C6C',
              width: 2,
              type: 'dashed'
            }
          }
        ]
      }

      const chart = await ChartUtils.safeInitChart(comparisonChart.value, option)
      chartManager.register('comparison', chart)
    }

    // 更新任务执行趋势图表数据
    const updateChartData = () => {
      latestUpdateTime.value = new Date()

      // 更新任务执行趋势图表数据
      const qualityChartInstance = chartManager.get('quality')
      if (qualityChartInstance) {
        const option = qualityChartInstance.getOption()
        const series = option.series as any
        const xAxis = (option.xAxis as any).data

        // 更新X轴日期（添加新的一天，移除最旧的一天）
        const now = new Date()
        const newDate = `${now.getMonth() + 1}/${now.getDate()}`
        xAxis.shift()
        xAxis.push(newDate)

        // 更新各系列数据
        series.forEach((item: any) => {
          const data = item.data
          data.shift()

          // 模拟新的任务数据
          if (item.name === '计划任务') {
            data.push(Math.floor(Math.random() * 15 + 25)) // 25-40个任务
          } else if (item.name === '完成任务') {
            const planned = series.find((s: any) => s.name === '计划任务').data[data.length - 1]
            data.push(Math.floor(planned * (0.8 + Math.random() * 0.15))) // 80-95%完成率
          } else if (item.name === '异常任务') {
            data.push(Math.floor(Math.random() * 5 + 1)) // 1-6个异常任务
          } else if (item.name === '完成率') {
            const planned = series.find((s: any) => s.name === '计划任务').data[data.length - 1]
            const completed = series.find((s: any) => s.name === '完成任务').data[data.length - 1]
            data.push(Math.round((completed / planned) * 100 * 10) / 10) // 计算完成率
          }
        })

        qualityChartInstance.setOption({
          xAxis: {
            data: xAxis
          },
          series: series
        })
      }
    }
    
    // 更新对比图表
    const updateComparisonChart = (indicator: string) => {
      const comparisonChartInstance = chartManager.get('comparison')
      if (!comparisonChartInstance) return
      
      // 根据不同指标设置不同的数据范围和单位
      let unit = 'mg/L'
      let inData = [320, 290, 310, 330]
      let outData = [48, 42, 45, 40]
      let standardValue = 50
      
      switch (indicator) {
        case 'pH':
          unit = 'pH'
          inData = [6.2, 6.4, 6.3, 6.5]
          outData = [7.2, 7.4, 7.3, 7.1]
          standardValue = 6.5
          break
        case 'NH3-N':
          inData = [35, 32, 38, 36]
          outData = [4.8, 5.2, 4.5, 5.0]
          standardValue = 5
          break
        case 'TP':
          inData = [4.2, 4.5, 4.0, 4.3]
          outData = [0.8, 0.7, 0.9, 0.6]
          standardValue = 1.0
          break
        case 'TN':
          inData = [45, 48, 42, 46]
          outData = [14, 15, 13, 16]
          standardValue = 15
          break
      }
      
      comparisonChartInstance.setOption({
        grid: {
          left: '3%',
          right: '4%',
          bottom: '15%',
          top: '15%'
        },
        xAxis: {
          axisLabel: {
            interval: 0,
            rotate: 30,
            fontSize: 14
          }
        },
        yAxis: {
          name: `${indicatorOptions.value.find(item => item.value === indicator)?.label || indicator} (${unit})`,
          nameTextStyle: {
            fontSize: 12
          },
          axisLabel: {
            fontSize: 12
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '进水',
            data: inData
          },
          {
            name: '出水',
            data: outData
          },
          {
            name: '标准值',
            data: [standardValue, standardValue, standardValue, standardValue],
            symbolSize: 0,
            lineStyle: {
              color: '#F56C6C',
              width: 2,
              type: 'dashed'
            }
          }
        ]
      })
      
      // 强制重绘确保显示正确
      setTimeout(() => {
        ChartUtils.safeResizeChart(comparisonChartInstance)
      }, 200)
    }

    // 查看告警详情
    const handleViewDetail = (alarm: Alarm) => {
      ElMessage.info(`查看告警详情: ${alarm.title}`)
    }

    // 加载监控概览数据
    const loadOverviewData = async () => {
      try {
        const res = await AssayDataAnalysisApi.getOverview()
        if (res.data) {
          const overview: AssayOverviewVO = res.data

          // 更新统计卡片数据
          statisticsCards.value[0].value = overview.todayStatistics.todayTasks
          statisticsCards.value[1].value = overview.samplingStatus.completionRate
          statisticsCards.value[2].value = overview.todayStatistics.todayCompletedSampling
          statisticsCards.value[3].value = overview.auditStatus.pending

          // 更新最新时间
          latestUpdateTime.value = new Date(overview.statisticsTime)
        }
      } catch (error) {
        console.error('加载概览数据失败:', error)
        ElMessage.error('加载概览数据失败')
      }
    }

    // 手动刷新数据
    const handleManualRefresh = async () => {
      refreshLoading.value = true
      try {
        // 加载最新的概览数据
        await loadOverviewData()

        // 更新图表数据
        updateChartData()

        ElMessage.success('数据已刷新')
      } catch (error) {
        console.error('刷新数据失败', error)
        ElMessage.error('刷新数据失败')
      } finally {
        refreshLoading.value = false
      }
    }

    // 处理刷新间隔变更
    const handleRefreshIntervalChange = (interval: number) => {
      refreshInterval.value = interval

      // 清除现有定时器
      if (timer) {
        clearInterval(timer)
        timer = null
      }

      // 设置新的定时器
      if (interval > 0) {
        timer = window.setInterval(() => {
          updateChartData()
        }, interval * 1000)
        ElMessage.success(`自动刷新间隔已设置为${refreshIntervalLabel.value}`)
      } else {
        ElMessage.success('已关闭自动刷新')
      }
    }

    // 强制重绘图表
    const forceRenderCharts = async () => {
      // 重新初始化对比图表
      await initComparisonChart()

      // 更新当前选中的指标数据
      updateComparisonChart(comparisonIndicator.value)

      // 延迟执行resize确保渲染完成
      setTimeout(() => {
        chartManager.resizeAll()
      }, 300)
    }
    


    // 组件挂载
    onMounted(() => {
      nextTick(async () => {
        // 加载概览数据
        await loadOverviewData()

        // 使用更长的延迟确保容器完全渲染
        setTimeout(async () => {
          await initQualityChart()
          await initComparisonChart()

          // 初始化后立即更新一次对比图表
          updateComparisonChart(comparisonIndicator.value)

          // 启用窗口大小变化监听
          chartManager.enableResizeListener(300)

          // 强制重绘一次确保显示正确
          setTimeout(() => {
            forceRenderCharts()
          }, 500)
        }, 500) // 增加延迟确保容器准备就绪

        // 任务数据不需要频繁更新，由用户手动刷新或按需更新
      })

      // 监听指标变化，更新对比图表
      watch(comparisonIndicator, (newVal) => {
        updateComparisonChart(newVal)
      })
    })

    // 组件卸载
    onUnmounted(() => {
      if (timer) {
        clearInterval(timer)
        timer = null
      }

      // 销毁所有图表和清理监听器
      chartManager.disposeAll()
    })

    return {
      plantOptions,
      currentPlant,
      indicatorOptions,
      comparisonIndicator,
      qualityChart,
      comparisonChart,
      latestUpdateTime,
      statisticsCards,
      alarms,
      refreshLoading,
      refreshInterval,
      refreshIntervalLabel,
      formatTime,
      handleViewDetail,
      handleManualRefresh,
      handleRefreshIntervalChange
    }
  }
})
</script>

<style lang="scss" scoped>
.monitor-dashboard {
  width: 100%;
}

.statistics-card {
  height: 10rem;
  
  .card-content {
    display: flex;
    align-items: center;
  }
  
  .card-icon {
    width: 4rem;
    height: 4rem;
    border-radius: 0.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 1rem;
    
    .el-icon {
      font-size: 2.4rem;
      color: #fff;
    }
  }
  
  .card-info {
    flex: 1;
    
    .card-title {
      font-size: 1rem;
      color: #909399;
      margin-bottom: 0.5rem;
    }
    
    .card-value {
      font-size: 2rem;
      font-weight: bold;
      color: #303133;
      display: flex;
      align-items: baseline;
      
      .unit {
        font-size: 1rem;
        color: #909399;
        margin-left: 0.5rem;
      }
    }
  }
}

.full-height-card {
  height: 36rem;
}

.comparison-card {
  height: auto;
  margin-bottom: 2rem;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .header-right {
    display: flex;
    align-items: center;
    
    .update-time {
      font-size: 0.85rem;
      color: #909399;
    }
  }
}

.monitor-charts {
  min-height: 30rem;
  // padding: 10px 0;
}

.quality-chart {
  width: 100%;
  height: 100%;
  min-height: 380px;
}

.alarm-list {
  height: 32rem;
  overflow-y: auto;
}

.alarm-tag {
  margin-left: 0.5rem;
}

.alarm-card {
  --el-card-padding: 0.8rem;
  margin-bottom: 0.5rem;
  
  .alarm-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-weight: bold;
    margin-bottom: 0.5rem;
  }
  
  .alarm-content {
    color: #606266;
    margin-bottom: 0.5rem;
  }
  
  .alarm-footer {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 0.85rem;
    color: #909399;
  }
}

.comparison-chart-container {
  height: 28rem;
  position: relative;
  overflow: visible;
}

.comparison-chart {
  width: 100%;
  height: 100%;
}
</style> 
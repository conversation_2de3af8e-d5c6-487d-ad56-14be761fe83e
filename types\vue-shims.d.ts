// Vue Language Server 类型修复
declare global {
  // 修复 __VLS_intrinsicElements 错误
  namespace JSX {
    interface IntrinsicElements {
      [elem: string]: any
    }
  }

  // Vue Language Server 内部类型
  const __VLS_intrinsicElements: JSX.IntrinsicElements
  const __VLS_components: Record<string, any>

  // 修复 Vue 模板类型检查
  interface __VLS_GlobalComponents {}

  // Vue 模板引用类型
  type __VLS_TemplateResult = {
    slots: Record<string, any>
    refs: Record<string, any>
    attrs: Record<string, any>
  }

  // 修复函数式组件上下文类型
  function __VLS_pickFunctionalComponentCtx<T, K>(comp: T, compInstance: K): any

  // VLS 内部函数和变量
  function __VLS_getVForSourceType(source: any): any
  function __VLS_getSlotParams(slot: any): any
  function __VLS_normalizeSlot(slot: any): any
  function __VLS_resolveComponent(name: string): any
  function __VLS_withScope(scope: any, slot: any): any
  function __VLS_makeOptional<T>(t: T): T
  function __VLS_nonNullable<T>(t: T): T
  function __VLS_asFunctionalComponent<T>(t: T): T
  function __VLS_functionalComponentProps<T>(comp: T): any
  function __VLS_elementAsFunctionalComponent<T>(t: T): T

  // VLS 类型变量
  const __VLS_ctx: any
  const __VLS_localComponents: Record<string, any>
  const __VLS_otherComponents: Record<string, any>
  const __VLS_name: string | undefined
  const __VLS_internalComponent: any
  const __VLS_template: any
  const __VLS_SelfComponent: any
  type __VLS_SelfComponent = any

  // VLS 模板相关
  type __VLS_WithTemplateSlots<T, S> = T & { new(): { $slots: S } }
  type __VLS_Prettify<T> = { [K in keyof T]: T[K] } & {}
  type __VLS_WithDefaults<P, D> = {
    [K in keyof P]: K extends keyof D ? P[K] | D[K] : P[K]
  }
  type __VLS_WithComponent<T, U, V, W, X, Y> = T & U & V & W & X & Y
}

// Vue 组件类型增强
declare module '@vue/runtime-core' {
  interface ComponentCustomProperties {
    // 可以在这里添加全局属性的类型定义
  }
}

export {}

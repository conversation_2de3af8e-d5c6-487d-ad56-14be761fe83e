<template>
  <Dialog v-model="dialogVisible" :title="'检测报告查看 - ' + formData.name" width="80%">
    <div v-loading="loading" class="report-container">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="报告编号">{{ formData.code }}</el-descriptions-item>
        <el-descriptions-item label="报告类型">{{ getReportTypeName(formData.type) }}</el-descriptions-item>
        <el-descriptions-item label="创建日期">{{ formData.createDate }}</el-descriptions-item>
        <el-descriptions-item label="创建人">{{ formData.creator }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="center">报告文件</el-divider>
      
      <div v-if="fileUrl" class="report-preview">
        <div class="preview-toolbar">
          <el-button-group>
            <el-button type="primary" @click="handleDownload">
              <el-icon><Download /></el-icon>下载
            </el-button>
            <el-button type="primary" @click="handlePrint">
              <el-icon><Printer /></el-icon>打印
            </el-button>
            <el-button type="primary" @click="handleZoomIn" :disabled="zoom >= 1.5">
              <el-icon><ZoomIn /></el-icon>放大
            </el-button>
            <el-button type="primary" @click="handleZoomOut" :disabled="zoom <= 0.5">
              <el-icon><ZoomOut /></el-icon>缩小
            </el-button>
            <el-button type="primary" @click="handleRotate">
              <el-icon><Refresh /></el-icon>旋转
            </el-button>
          </el-button-group>
          <span>第 {{ currentPage }} / {{ totalPages }} 页</span>
          <el-pagination
            small
            layout="prev, pager, next"
            :total="totalPages * 10"
            :current-page="currentPage"
            @current-change="handlePageChange"
          />
        </div>
        
        <div class="preview-content" :style="{ transform: `scale(${zoom}) rotate(${rotation}deg)` }">
          <!-- 使用iframe预览PDF文件 -->
          <iframe v-if="isPdf" :src="fileUrl" frameborder="0"></iframe>
          <!-- 使用img预览图片文件 -->
          <img v-else-if="isImage" :src="fileUrl" alt="报告预览" />
          <!-- 其他文件类型提示下载 -->
          <div v-else class="no-preview">
            <el-icon class="no-preview-icon"><Document /></el-icon>
            <p>当前文件类型不支持预览，请下载后查看</p>
            <el-button type="primary" @click="handleDownload">下载文件</el-button>
          </div>
        </div>
      </div>
      
      <div v-else class="no-file">
        <el-empty description="暂无报告文件" />
      </div>
      
      <el-divider content-position="center">访问记录</el-divider>
      
      <el-table :data="accessRecords" border style="width: 100%">
        <el-table-column prop="id" label="ID" min-width="5rem" />
        <el-table-column prop="userName" label="访问人" min-width="8rem" />
        <el-table-column prop="accessTime" label="访问时间" min-width="12rem" />
        <el-table-column prop="accessType" label="访问类型" min-width="8rem">
          <template #default="{ row }">
            <el-tag :type="row.accessType === 'view' ? 'info' : 'success'">
              {{ row.accessType === 'view' ? '查看' : '下载' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="ip" label="IP地址" min-width="12rem" />
        <el-table-column prop="device" label="设备信息" min-width="15rem" show-overflow-tooltip />
      </el-table>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Download, Printer, ZoomIn, ZoomOut, Refresh, Document } from '@element-plus/icons-vue'

defineOptions({ name: 'ReportViewDialog' })

const dialogVisible = ref(false)
const loading = ref(false)
const fileUrl = ref('')
const zoom = ref(1)
const rotation = ref(0)
const currentPage = ref(1)
const totalPages = ref(1)

interface ReportFormData {
  id?: number;
  code: string;
  name: string;
  type: string;
  createDate: string;
  creator: string;
  fileUrl: string;
}

interface AccessRecord {
  id: number;
  userName: string;
  accessTime: string;
  accessType: 'view' | 'download';
  ip: string;
  device: string;
}

// 表单数据
const formData = ref<ReportFormData>({
  code: '',
  name: '',
  type: '',
  createDate: '',
  creator: '',
  fileUrl: ''
})

// 访问记录
const accessRecords = ref<AccessRecord[]>([
  {
    id: 1,
    userName: '张三',
    accessTime: '2023-07-15 10:30:45',
    accessType: 'view',
    ip: '*************',
    device: 'Chrome 96.0.4664.110 / Windows 10'
  },
  {
    id: 2,
    userName: '李四',
    accessTime: '2023-07-15 14:20:33',
    accessType: 'download',
    ip: '*************',
    device: 'Firefox 95.0 / macOS 12.0'
  },
  {
    id: 3,
    userName: '王五',
    accessTime: '2023-07-16 09:15:22',
    accessType: 'view',
    ip: '*************',
    device: 'Safari 15.2 / iOS 15.2'
  }
])

// 判断是否为PDF文件
const isPdf = computed(() => {
  return fileUrl.value.toLowerCase().endsWith('.pdf')
})

// 判断是否为图片文件
const isImage = computed(() => {
  const url = fileUrl.value.toLowerCase()
  return url.endsWith('.jpg') || url.endsWith('.jpeg') || url.endsWith('.png') || url.endsWith('.gif')
})

// 获取报告类型名称
const getReportTypeName = (type: string): string => {
  switch (type) {
    case 'water':
      return '水质报告'
    case 'sludge':
      return '污泥报告'
    case 'gas':
      return '气体报告'
    default:
      return '其他报告'
  }
}

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  loading.value = true
  
  try {
    formData.value = {
      id: data.id,
      code: data.code,
      name: data.name,
      type: data.type,
      createDate: data.createDate,
      creator: data.creator,
      fileUrl: data.fileUrl
    }
    
    // 模拟API调用获取文件URL
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (data.fileUrl) {
      fileUrl.value = data.fileUrl
      
      // 模拟记录访问日志
      accessRecords.value.unshift({
        id: accessRecords.value.length + 1,
        userName: '当前用户',
        accessTime: new Date().toLocaleString(),
        accessType: 'view',
        ip: '*************',
        device: 'Chrome / Windows 10'
      })
      
      // 模拟PDF页数
      totalPages.value = Math.floor(Math.random() * 10) + 1
      currentPage.value = 1
    } else {
      fileUrl.value = ''
    }
  } catch (error) {
    console.error('获取报告失败:', error)
    ElMessage.error('获取报告失败，请重试')
  } finally {
    loading.value = false
  }
}
defineExpose({ open })

// 处理下载
const handleDownload = () => {
  if (!fileUrl.value) {
    ElMessage.warning('暂无可下载的文件')
    return
  }
  
  // 模拟下载操作
  ElMessage.success('文件下载已开始')
  
  // 模拟记录下载日志
  accessRecords.value.unshift({
    id: accessRecords.value.length + 1,
    userName: '当前用户',
    accessTime: new Date().toLocaleString(),
    accessType: 'download',
    ip: '*************',
    device: 'Chrome / Windows 10'
  })
}

// 处理打印
const handlePrint = () => {
  if (!fileUrl.value) {
    ElMessage.warning('暂无可打印的文件')
    return
  }
  
  // 模拟打印操作
  ElMessage.success('文件打印已开始')
}

// 处理放大
const handleZoomIn = () => {
  if (zoom.value < 1.5) {
    zoom.value += 0.1
  }
}

// 处理缩小
const handleZoomOut = () => {
  if (zoom.value > 0.5) {
    zoom.value -= 0.1
  }
}

// 处理旋转
const handleRotate = () => {
  rotation.value = (rotation.value + 90) % 360
}

// 处理页面切换
const handlePageChange = (page: number) => {
  currentPage.value = page
}
</script>

<style lang="scss" scoped>
.report-container {
  height: 70vh;
  overflow-y: auto;
}

.report-preview {
  margin-top: 1rem;
  margin-bottom: 1rem;
  display: flex;
  flex-direction: column;
  height: 60vh;
  border: 1px solid #e0e0e0;
  border-radius: 0.25rem;
}

.preview-toolbar {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  border-bottom: 1px solid #e0e0e0;
  background-color: #f5f7fa;
  
  span {
    margin: 0 1rem;
  }
  
  .el-pagination {
    margin-left: auto;
  }
}

.preview-content {
  flex: 1;
  overflow: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  transition: transform 0.3s ease;
  
  iframe {
    width: 100%;
    height: 100%;
    border: none;
  }
  
  img {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
  }
}

.no-preview {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  color: #909399;
  
  .no-preview-icon {
    font-size: 4rem;
    margin-bottom: 1rem;
  }
}

.no-file {
  height: 20rem;
  display: flex;
  align-items: center;
  justify-content: center;
}
</style> 
<template>
  <el-dialog v-model="visible" :title="dialogType === 'add' ? '新建方案' : '编辑方案'" width="60rem"
    :close-on-click-modal="false" :destroy-on-close="true" @close="handleClose">
    <el-form ref="formRef" :model="localFormData" :rules="formRules" label-width="8rem" class="scheme-form">
      <!-- 基本信息 -->
      <el-card class="mb-4">
        <template #header>
          <span class="font-bold">方案基本信息</span>
        </template>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="方案名称" prop="name">
              <el-input v-model="localFormData.name" placeholder="请输入方案名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评估周期" prop="cycle">
              <el-select v-model="localFormData.cycle" placeholder="请选择评估周期" style="width: 100%">
                <el-option label="时" value="时" />
                <el-option label="天" value="天" />
                <el-option label="周" value="周" />
                <el-option label="月度" value="月度" />
                <el-option label="季度" value="季度" />
                <el-option label="年度" value="年度" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="12">
            <el-form-item label="启用时间" prop="startDate">
              <el-date-picker v-model="localFormData.startDate" type="date" placeholder="请选择启用时间" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="失效时间" prop="endDate">
              <el-date-picker v-model="localFormData.endDate" type="date" placeholder="请选择失效时间" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="方案说明">
          <el-input v-model="localFormData.description" type="textarea" :rows="3" placeholder="请输入方案说明" />
        </el-form-item>
      </el-card>

      <!-- 评估项目配置 -->
      <el-card class="mb-4">
        <template #header>
          <div class="flex justify-between items-center">
            <span class="font-bold">评估项目配置</span>
            <el-button type="primary" size="small" @click="addProject">
              <el-icon>
                <Plus />
              </el-icon>
              添加项目
            </el-button>
          </div>
        </template>

        <div v-if="localFormData.projects.length === 0" class="text-center text-gray-500 py-8">
          暂无评估项目，请点击"添加项目"按钮添加
        </div>

        <VueDraggable v-else v-model="localFormData.projects" :animation="200" handle=".project-drag-handle"
          item-key="id" class="projects-container">
          <template #item="{ element: project, index: projectIndex }">
            <el-card class="project-card mb-4" shadow="hover">
              <template #header>
                <div class="flex justify-between items-center">
                  <div class="flex items-center gap-2">
                    <el-icon class="project-drag-handle cursor-move text-gray-400">
                      <Rank />
                    </el-icon>
                    <span class="font-medium">项目 {{ projectIndex + 1 }}: {{ project.name || '未命名项目' }}</span>
                  </div>
                  <el-button type="danger" size="small" link @click="removeProject(projectIndex)">
                    <el-icon>
                      <Delete />
                    </el-icon>
                    删除
                  </el-button>
                </div>
              </template>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="项目名称" :prop="`projects.${projectIndex}.name`">
                    <el-input v-model="project.name" placeholder="请输入项目名称" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="评分规则" :prop="`projects.${projectIndex}.scoreType`">
                    <el-select v-model="project.scoreType" placeholder="请选择评分规则" style="width: 100%">
                      <el-option label="加权平均" value="加权平均" />
                      <el-option label="打分制" value="打分制" />
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

              <el-row :gutter="16">
                <el-col :span="12">
                  <el-form-item label="项目权重(%)" :prop="`projects.${projectIndex}.weight`">
                    <el-input-number v-model="project.weight" :min="0" :max="100" :precision="1" :controls="true"
                      controls-position="right" style="width: 100%;" :step="0.1" placeholder="0.0" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="指标分值" :prop="`projects.${projectIndex}.fullScore`">
                    <el-input-number v-model="project.fullScore" :min="1" :max="1000" :controls="true"
                      controls-position="right" style="width: 100%;" :step="1" placeholder="100" />
                  </el-form-item>
                </el-col>
              </el-row>

              <!-- 指标绑定区域 -->
              <el-divider content-position="left">
                <span class="text-sm text-gray-600">绑定指标</span>
              </el-divider>

              <div class="indicators-section">
                <div class="flex justify-between items-center mb-3">
                  <span class="text-sm font-medium">已绑定指标 ({{ project.indicators.length }})</span>
                  <el-button type="primary" size="small" @click="openIndicatorSelector(projectIndex)">
                    <el-icon>
                      <Plus />
                    </el-icon>
                    绑定指标
                  </el-button>
                </div>

                <div v-if="project.indicators.length === 0"
                  class="text-center text-gray-400 py-4 border border-dashed border-gray-300 rounded">
                  暂无绑定指标，请点击"绑定指标"按钮添加
                </div>

                <VueDraggable v-else v-model="project.indicators" :animation="200" handle=".indicator-drag-handle"
                  item-key="id" class="indicators-list">
                  <template #item="{ element: indicator, index: indicatorIndex }">
                    <div class="indicator-item">
                      <div class="flex items-center gap-3 p-3 border border-gray-200 rounded mb-2 bg-gray-50">
                        <el-icon class="indicator-drag-handle cursor-move text-gray-400">
                          <Rank />
                        </el-icon>

                        <div class="flex-1">
                          <div class="flex items-center justify-between">
                            <div class="flex items-center gap-2">
                              <span class="font-medium">{{ indicator.name }}</span>
                              <el-tag size="small" type="info">{{ indicator.unit }}</el-tag>
                            </div>
                            <div class="flex items-center gap-2">
                              <template v-if="project.scoreType === '加权平均'">
                                <span class="text-sm text-gray-500">权重:</span>
                                <el-input-number v-model="indicator.weight" :min="0" :max="100" :precision="1"
                                  size="small" :controls="true" controls-position="right"
                                  style="width: 130px; min-width: 130px;" :step="1" placeholder="0" />
                              </template>
                              <el-button type="danger" size="small" link
                                @click="removeIndicator(projectIndex, indicatorIndex)">
                                <el-icon>
                                  <Delete />
                                </el-icon>
                              </el-button>
                            </div>
                          </div>
                          <div class="text-sm text-gray-600 mt-1">
                            <span>公式: {{ indicator.formula }}</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                </VueDraggable>

                <!-- 权重校验提示 -->
                <div v-if="project.scoreType === '加权平均' && project.indicators.length > 0" class="mt-2">
                  <el-alert :title="`当前权重总和: ${getProjectWeightSum(project)}%`"
                    :type="getProjectWeightSum(project) === 100 ? 'success' : 'warning'" :closable="false" show-icon>
                    <template #default>
                      <span v-if="getProjectWeightSum(project) !== 100">
                        权重总和应为100%，当前为{{ getProjectWeightSum(project) }}%
                      </span>
                      <span v-else>
                        权重配置正确
                      </span>
                    </template>
                  </el-alert>
                </div>
              </div>
            </el-card>
          </template>
        </VueDraggable>

        <!-- 总权重校验 -->
        <div v-if="localFormData.projects.length > 0" class="mt-4">
          <el-alert :title="`所有项目权重总和: ${getTotalWeightSum()}%`"
            :type="getTotalWeightSum() === 100 ? 'success' : 'error'" :closable="false" show-icon>
            <template #default>
              <span v-if="getTotalWeightSum() !== 100">
                所有项目权重总和必须为100%，当前为{{ getTotalWeightSum() }}%
              </span>
              <span v-else>
                项目权重配置正确
              </span>
            </template>
          </el-alert>
        </div>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          <el-icon>
            <Close />
          </el-icon>
          取消
        </el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          <el-icon>
            <Check />
          </el-icon>
          {{ dialogType === 'add' ? '创建方案' : '保存修改' }}
        </el-button>
      </div>
    </template>

    <!-- 指标选择器 -->
    <IndicatorSelector v-model="indicatorSelectorVisible" :indicator-library="localIndicatorLibrary"
      :scheme-cycle="localFormData.cycle" @confirm="handleIndicatorSelect" />
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, reactive } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import {
  Plus,
  Delete,
  Check,
  Close,
  Rank
} from '@element-plus/icons-vue'
import VueDraggable from 'vuedraggable'
import IndicatorSelector from './IndicatorSelector.vue'

// 定义接口类型
interface Indicator {
  id: string
  name: string
  unit: string
  formula: string
  description?: string
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  weight?: number
}

interface Project {
  id: string
  name: string
  weight: number
  scoreType: '加权平均' | '打分制'
  fullScore: number
  indicators: Indicator[]
}

interface SchemeFormData {
  name: string
  cycle: '时' | '天' | '周' | '月度' | '季度' | '年度'
  startDate: string
  endDate: string
  description: string
  projects: Project[]
}

// 定义属性
const props = defineProps<{
  modelValue: boolean
  dialogType: 'add' | 'edit'
  formData: SchemeFormData
  indicatorLibrary: Indicator[]
}>()

// 定义事件
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'submit': [data: SchemeFormData]
  'cancel': []
}>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)
const indicatorSelectorVisible = ref(false)
const currentProjectIndex = ref(-1)
const localIndicatorLibrary = ref<Indicator[]>([])
const loadingIndicators = ref(false)

// 本地表单数据副本，避免直接修改props
const localFormData = reactive<SchemeFormData>({
  name: '',
  cycle: '月度',
  startDate: '',
  endDate: '',
  description: '',
  projects: []
})

// 监听props变化，同步到本地数据
watch(() => props.formData, (newData) => {
  if (newData) {
    Object.assign(localFormData, {
      ...newData,
      projects: newData.projects.map(project => ({
        ...project,
        indicators: project.indicators.map(indicator => ({ ...indicator }))
      }))
    })
  }
}, { immediate: true, deep: true })

// 监听props中的indicatorLibrary变化，同步到本地
watch(() => props.indicatorLibrary, (newLibrary) => {
  if (newLibrary && newLibrary.length > 0) {
    localIndicatorLibrary.value = [...newLibrary]
  }
}, { immediate: true })

// 获取评估指标库数据 - 优先使用mock数据
const fetchIndicatorLibrary = async () => {
  try {
    loadingIndicators.value = true

    // 优先使用mock数据
    const mockIndicators: Indicator[] = [
      // 能耗类指标
      {
        id: 'ind_001',
        name: '单位能耗',
        formula: '总能耗 / 产出量',
        unit: 'kWh/m³',
        cycle: '月度',
        description: '单位处理水量的能源消耗'
      },
      {
        id: 'ind_002',
        name: '功率因数',
        formula: '有功功率 / 视在功率',
        unit: '无量纲',
        cycle: '月度',
        description: '电力系统效率指标'
      },
      {
        id: 'ind_013',
        name: '电能利用率',
        formula: '有效电能 / 总电能消耗 × 100',
        unit: '%',
        cycle: '月度',
        description: '电能有效利用程度'
      },
      {
        id: 'ind_014',
        name: '峰谷电价比',
        formula: '峰时电费 / 谷时电费',
        unit: '无量纲',
        cycle: '月度',
        description: '峰谷电价使用优化指标'
      },
      // 水质类指标
      {
        id: 'ind_003',
        name: 'COD去除率',
        formula: '(进水COD - 出水COD) / 进水COD × 100',
        unit: '%',
        cycle: '天',
        description: '化学需氧量去除效率'
      },
      {
        id: 'ind_004',
        name: 'BOD去除率',
        formula: '(进水BOD - 出水BOD) / 进水BOD × 100',
        unit: '%',
        cycle: '天',
        description: '生化需氧量去除效率'
      },
      {
        id: 'ind_005',
        name: 'SS去除率',
        formula: '(进水SS - 出水SS) / 进水SS × 100',
        unit: '%',
        cycle: '天',
        description: '悬浮物去除效率'
      },
      {
        id: 'ind_006',
        name: '氨氮去除率',
        formula: '(进水氨氮 - 出水氨氮) / 进水氨氮 × 100',
        unit: '%',
        cycle: '天',
        description: '氨氮去除效率'
      },
      // 运行效率类指标
      {
        id: 'ind_007',
        name: '设备运行率',
        formula: '运行时间 / 总时间 × 100',
        unit: '%',
        cycle: '周',
        description: '设备正常运行时间占比'
      },
      {
        id: 'ind_008',
        name: '处理负荷率',
        formula: '实际处理量 / 设计处理量 × 100',
        unit: '%',
        cycle: '天',
        description: '实际处理负荷与设计负荷的比值'
      },
      {
        id: 'ind_009',
        name: '药剂消耗率',
        formula: '药剂消耗量 / 处理水量',
        unit: 'kg/m³',
        cycle: '月度',
        description: '单位处理水量的药剂消耗'
      },
      // 安全环保类指标
      {
        id: 'ind_010',
        name: '安全事故率',
        formula: '事故次数 / 作业次数 × 1000',
        unit: '‰',
        cycle: '季度',
        description: '安全事故发生频率'
      },
      {
        id: 'ind_011',
        name: '环境噪声达标率',
        formula: '达标次数 / 总检测次数 × 100',
        unit: '%',
        cycle: '周',
        description: '环境噪声监测达标率'
      },
      {
        id: 'ind_012',
        name: '废气排放达标率',
        formula: '达标次数 / 总检测次数 × 100',
        unit: '%',
        cycle: '月度',
        description: '废气排放监测达标率'
      }
    ]

    // 直接使用mock数据
    localIndicatorLibrary.value = mockIndicators

  } catch (error) {
    console.error('获取指标库数据失败:', error)
    // 确保至少有基础mock数据可用
    localIndicatorLibrary.value = [
      {
        id: 'ind_001',
        name: '单位能耗',
        formula: '总能耗 / 产出量',
        unit: 'kWh/m³',
        cycle: '月度',
        description: '单位处理水量的能源消耗'
      },
      {
        id: 'ind_002',
        name: '功率因数',
        formula: '有功功率 / 视在功率',
        unit: '无量纲',
        cycle: '月度',
        description: '电力系统效率指标'
      }
    ]
  } finally {
    loadingIndicators.value = false
  }
}

// 表单验证规则
const formRules = {
  name: [
    { required: true, message: '请输入方案名称', trigger: 'blur' }
  ],
  cycle: [
    { required: true, message: '请选择评估周期', trigger: 'change' }
  ],
  startDate: [
    { required: true, message: '请选择启用时间', trigger: 'change' }
  ],
  endDate: [
    { required: true, message: '请选择失效时间', trigger: 'change' }
  ]
}

// 计算项目权重总和
const getProjectWeightSum = (project: Project): number => {
  if (project.scoreType !== '加权平均') return 0
  return project.indicators.reduce((sum, indicator) => sum + (indicator.weight || 0), 0)
}

// 计算所有项目权重总和
const getTotalWeightSum = (): number => {
  return localFormData.projects.reduce((sum, project) => sum + project.weight, 0)
}

// 项目管理
const addProject = () => {
  const newProject: Project = {
    id: Date.now().toString(),
    name: '',
    weight: 0,
    scoreType: '加权平均',
    fullScore: 100,
    indicators: []
  }

  localFormData.projects.push(newProject)
}

const removeProject = (index: number) => {
  localFormData.projects.splice(index, 1)
}

// 指标管理
const openIndicatorSelector = (projectIndex: number) => {
  currentProjectIndex.value = projectIndex
  indicatorSelectorVisible.value = true
}

const handleIndicatorSelect = (selectedIndicators: Indicator[]) => {
  if (currentProjectIndex.value === -1) return

  const project = localFormData.projects[currentProjectIndex.value]
  if (!project) return

  // 过滤掉已经绑定的指标
  const newIndicators = selectedIndicators.filter(indicator =>
    !project.indicators.some(existing => existing.id === indicator.id)
  )

  // 添加到项目中，设置默认权重
  newIndicators.forEach(indicator => {
    const newIndicator: Indicator = {
      ...indicator,
      weight: project.scoreType === '加权平均' ? 0 : undefined
    }
    project.indicators.push(newIndicator)
  })

  // 如果是加权平均，平均分配权重
  if (project.scoreType === '加权平均' && project.indicators.length > 0) {
    const averageWeight = Math.floor(100 / project.indicators.length)
    const remainder = 100 % project.indicators.length

    project.indicators.forEach((indicator, index) => {
      indicator.weight = averageWeight + (index < remainder ? 1 : 0)
    })
  }

  ElMessage.success(`成功绑定 ${newIndicators.length} 个指标`)
}

const removeIndicator = (projectIndex: number, indicatorIndex: number) => {
  const project = localFormData.projects[projectIndex]
  if (!project) return

  project.indicators.splice(indicatorIndex, 1)

  // 如果是加权平均，重新分配权重
  if (project.scoreType === '加权平均' && project.indicators.length > 0) {
    const averageWeight = Math.floor(100 / project.indicators.length)
    const remainder = 100 % project.indicators.length

    project.indicators.forEach((indicator, index) => {
      indicator.weight = averageWeight + (index < remainder ? 1 : 0)
    })
  }
}



// 表单操作
const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()

    // 验证权重总和
    const totalWeight = getTotalWeightSum()
    if (localFormData.projects.length > 0 && totalWeight !== 100) {
      ElMessage.error('所有项目权重总和必须为100%')
      return
    }

    // 验证每个项目的指标权重
    for (const project of localFormData.projects) {
      if (project.scoreType === '加权平均') {
        const projectWeight = getProjectWeightSum(project)
        if (project.indicators.length > 0 && projectWeight !== 100) {
          ElMessage.error(`项目"${project.name}"的指标权重总和必须为100%`)
          return
        }
      }
    }

    submitting.value = true
    emit('submit', localFormData)
  } catch (error) {
    console.error('Form validation failed:', error)
  } finally {
    submitting.value = false
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handleClose = () => {
  emit('update:modelValue', false)
}

// 组件挂载时获取指标库数据
onMounted(() => {
  fetchIndicatorLibrary()
})
</script>

<style scoped lang="scss">
// 对话框样式
.scheme-form {
  max-height: 70vh;
  overflow-y: auto;

  .el-card {
    margin-bottom: 1rem;

    :deep(.el-card__header) {
      background-color: #f8f9fa;
      border-bottom: 0.0625rem solid #e4e7ed;
    }
  }
}

// 项目配置样式
.projects-container {
  .project-card {
    border: 0.125rem solid #e4e7ed;
    transition: all 0.3s ease;

    &:hover {
      border-color: #409eff;
      box-shadow: 0 0.125rem 0.75rem rgba(64, 158, 255, 0.1);
    }

    :deep(.el-card__header) {
      background-color: #f0f9ff;
      border-bottom: 0.0625rem solid #d1ecf1;
    }
  }

  .project-drag-handle {
    cursor: move;

    &:hover {
      color: #409eff;
    }
  }
}

// 指标列表样式
.indicators-section {
  .indicators-list {
    .indicator-item {
      .indicator-drag-handle {
        cursor: move;

        &:hover {
          color: #409eff;
        }
      }
    }
  }
}

// 输入框样式
:deep(.el-input-number) {
  position: relative;

  .el-input__wrapper {
    width: 100%;
    min-height: 32px;
    position: relative;
  }

  .el-input__inner {
    text-align: center;
    font-weight: 500;
    font-size: 14px;
    line-height: 1.5;
    color: #606266;
    padding: 0 12px;
    min-width: 60px;

    &:focus {
      outline: none;
    }
  }

  &.is-controls-right {
    .el-input__wrapper {
      padding-right: 2.2rem;
    }

    .el-input__inner {
      padding-right: 2.2rem;
      padding-left: 8px;
    }

    .el-input-number__increase,
    .el-input-number__decrease {
      width: 30px;
      height: 15px;
      line-height: 15px;
      font-size: 12px;
      border-left: 1px solid #dcdfe6;
      background: #fafafa;
      cursor: pointer;
      position: absolute;
      right: 1px;
      z-index: 1;

      &:hover {
        background-color: #f5f7fa;
      }

      &:active {
        background-color: #e4e7ed;
      }
    }

    .el-input-number__increase {
      top: 1px;
      border-bottom: 1px solid #dcdfe6;
      border-top-right-radius: 4px;
    }

    .el-input-number__decrease {
      bottom: 1px;
      border-bottom-right-radius: 4px;
    }
  }

  // 确保小尺寸输入框也能正常显示
  &.el-input-number--small {
    .el-input__inner {
      font-size: 13px;
      padding: 0 10px;
      min-width: 50px;
    }

    .el-input__wrapper {
      min-height: 28px;
    }

    &.is-controls-right {
      .el-input__wrapper {
        padding-right: 2rem;
      }

      .el-input__inner {
        padding-right: 2rem;
        padding-left: 8px;
      }

      .el-input-number__increase,
      .el-input-number__decrease {
        width: 28px;
        height: 13px;
        line-height: 13px;
        font-size: 11px;
        border-left: 1px solid #dcdfe6;
        background: #fafafa;
        cursor: pointer;
        position: absolute;
        right: 1px;
        z-index: 1;

        &:hover {
          background-color: #f5f7fa;
        }

        &:active {
          background-color: #e4e7ed;
        }
      }

      .el-input-number__increase {
        top: 1px;
        border-bottom: 1px solid #dcdfe6;
        border-top-right-radius: 4px;
      }

      .el-input-number__decrease {
        bottom: 1px;
        border-bottom-right-radius: 4px;
      }
    }
  }
}

// 对话框底部样式
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

// 拖拽时的样式
.sortable-ghost {
  opacity: 0.5;
  background-color: #f0f9ff;
  border: 0.125rem dashed #409eff;
}

.sortable-chosen {
  background-color: #f0f9ff;
}
</style>

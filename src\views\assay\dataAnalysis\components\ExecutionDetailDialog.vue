<template>
  <el-dialog v-model="visible" title="执行详情" width="80%" class="execution-detail-dialog">
    <div v-if="currentExecution" class="execution-detail-content">
      <!-- 基本信息 -->
      <el-card class="detail-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span class="card-title">基本信息</span>
            <el-tag :type="getStatusType(currentExecution.status)" size="small">
              {{ getStatusText(currentExecution.status) }}
            </el-tag>
          </div>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>计划名称：</label>
              <span>{{ currentExecution.planName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>计划类型：</label>
              <el-tag :type="currentExecution.planType === 'regular' ? 'primary' : 'warning'" size="small">
                {{ currentExecution.planType === 'regular' ? '常规计划' : '临时计划' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>执行日期：</label>
              <span>{{ currentExecution.date }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="20" style="margin-top: 16px;">
          <el-col :span="8">
            <div class="detail-item">
              <label>采样点：</label>
              <span>{{ currentExecution.samplingPoint }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>执行进度：</label>
              <el-progress :percentage="currentExecution.progress" :color="getProgressColor(currentExecution.progress)" />
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>完成率：</label>
              <span class="completion-rate" :class="getCompletionClass(currentExecution.completionRate)">
                {{ currentExecution.completionRate }}%
              </span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 执行人员 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">执行人员</span>
        </template>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>采样人员：</label>
              <span>{{ currentExecution.sampler || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>检测人员：</label>
              <span>{{ currentExecution.tester || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>审核人员：</label>
              <span>{{ currentExecution.reviewer || '未分配' }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>

      <!-- 检测项目 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">检测项目</span>
        </template>
        <el-table :data="testItemsData" border class="execution-detail-table">
          <el-table-column prop="name" label="项目名称" min-width="120" />
          <el-table-column prop="method" label="检测方法" min-width="150" show-overflow-tooltip />
          <el-table-column prop="unit" label="单位" width="80" align="center" />
          <el-table-column prop="standardValue" label="标准值" width="120" align="center" />
          <el-table-column prop="actualValue" label="检测值" width="120" align="center">
            <template #default="{ row }">
              <span v-if="row.actualValue" :class="getValueClass(row)">{{ row.actualValue }}</span>
              <span v-else class="no-data">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="status" label="状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getItemStatusType(row.status)" size="small">
                {{ getItemStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 执行时间线 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;">
        <template #header>
          <span class="card-title">执行时间线</span>
        </template>
        <el-timeline>
          <el-timeline-item
            v-for="record in executionTimeline"
            :key="record.id"
            :timestamp="record.time"
            :type="record.type"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ record.action }}</div>
              <div class="timeline-desc" v-if="record.description">{{ record.description }}</div>
              <div class="timeline-operator" v-if="record.operator">操作人：{{ record.operator }}</div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </el-card>

      <!-- 异常记录 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="exceptionRecords.length > 0">
        <template #header>
          <span class="card-title">异常记录</span>
        </template>
        <el-table :data="exceptionRecords" border>
          <el-table-column prop="time" label="时间" width="160" />
          <el-table-column prop="type" label="异常类型" width="120">
            <template #default="{ row }">
              <el-tag type="danger" size="small">{{ row.type }}</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="异常描述" show-overflow-tooltip />
          <el-table-column prop="handler" label="处理人" width="100" />
          <el-table-column prop="status" label="处理状态" width="100">
            <template #default="{ row }">
              <el-tag :type="row.status === 'resolved' ? 'success' : 'warning'" size="small">
                {{ row.status === 'resolved' ? '已处理' : '处理中' }}
              </el-tag>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <!-- 备注信息 -->
      <el-card class="detail-card" shadow="never" style="margin-top: 16px;" v-if="currentExecution.remark">
        <template #header>
          <span class="card-title">备注信息</span>
        </template>
        <p class="remark-content">{{ currentExecution.remark }}</p>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="visible = false">关闭</el-button>
        <el-button type="primary" @click="handleExport">导出报告</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'ExecutionDetailDialog' })

// 弹窗显示状态
const visible = ref(false)

// 当前执行记录
const currentExecution = ref<any>(null)

// 检测项目数据
const testItemsData = computed(() => {
  if (!currentExecution.value?.testItems) return []
  
  return currentExecution.value.testItems.map((item: string) => ({
    name: item,
    method: getTestMethod(item),
    unit: getTestUnit(item),
    standardValue: getStandardValue(item),
    actualValue: generateMockValue(item),
    status: Math.random() > 0.8 ? 'exception' : 'completed'
  }))
})

// 执行时间线
const executionTimeline = computed(() => {
  if (!currentExecution.value) return []
  
  const timeline = []
  const baseDate = currentExecution.value.date
  
  // 计划创建
  timeline.push({
    id: 1,
    action: '计划创建',
    description: '执行计划已创建',
    operator: '系统',
    time: `${baseDate} 08:00:00`,
    type: 'primary'
  })
  
  // 任务分配
  timeline.push({
    id: 2,
    action: '任务分配',
    description: '已分配执行人员',
    operator: '计划管理员',
    time: `${baseDate} 08:30:00`,
    type: 'success'
  })
  
  // 开始执行
  if (currentExecution.value.progress > 0) {
    timeline.push({
      id: 3,
      action: '开始执行',
      description: '开始采样和检测工作',
      operator: currentExecution.value.sampler,
      time: `${baseDate} 09:00:00`,
      type: 'warning'
    })
  }
  
  // 完成执行
  if (currentExecution.value.progress === 100) {
    timeline.push({
      id: 4,
      action: '执行完成',
      description: '所有检测项目已完成',
      operator: currentExecution.value.tester,
      time: `${baseDate} 16:00:00`,
      type: 'success'
    })
  }
  
  return timeline
})

// 异常记录
const exceptionRecords = computed(() => {
  if (!currentExecution.value || currentExecution.value.status !== 'exception') return []
  
  return [
    {
      id: 1,
      time: `${currentExecution.value.date} 14:30:00`,
      type: '数值超标',
      description: '总磷检测值超出标准限值',
      handler: currentExecution.value.reviewer,
      status: 'resolved'
    }
  ]
})

// 打开弹窗
const open = (execution: any) => {
  currentExecution.value = {
    ...execution,
    testItems: execution.testItems?.split(', ') || []
  }
  visible.value = true
}

// 获取检测方法
const getTestMethod = (item: string) => {
  const methods: Record<string, string> = {
    'COD': '重铬酸钾法',
    'BOD5': '稀释接种法',
    '氨氮': '纳氏试剂比色法',
    '总磷': '钼酸铵分光光度法',
    '总氮': '碱性过硫酸钾消解法',
    'pH值': '玻璃电极法',
    '悬浮物': '重量法'
  }
  return methods[item] || '待确定'
}

// 获取检测单位
const getTestUnit = (item: string) => {
  const units: Record<string, string> = {
    'COD': 'mg/L',
    'BOD5': 'mg/L',
    '氨氮': 'mg/L',
    '总磷': 'mg/L',
    '总氮': 'mg/L',
    'pH值': '无量纲',
    '悬浮物': 'mg/L'
  }
  return units[item] || '-'
}

// 获取标准值
const getStandardValue = (item: string) => {
  const standards: Record<string, string> = {
    'COD': '≤50',
    'BOD5': '≤10',
    '氨氮': '≤5',
    '总磷': '≤0.5',
    '总氮': '≤15',
    'pH值': '6-9',
    '悬浮物': '≤10'
  }
  return standards[item] || '-'
}

// 生成模拟检测值
const generateMockValue = (item: string) => {
  const values: Record<string, number> = {
    'COD': 42.5,
    'BOD5': 8.2,
    '氨氮': 3.8,
    '总磷': 0.35,
    '总氮': 12.6,
    'pH值': 7.2,
    '悬浮物': 6.8
  }
  return values[item]?.toString() || '-'
}

// 状态相关方法
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'exception': 'danger'
  }
  return typeMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待执行',
    'processing': '执行中',
    'completed': '已完成',
    'exception': '异常'
  }
  return textMap[status] || status
}

const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

const getCompletionClass = (rate: number) => {
  if (rate >= 90) return 'high'
  if (rate >= 70) return 'medium'
  return 'low'
}

const getValueClass = (row: any) => {
  // 简单的超标判断逻辑
  if (row.name === '总磷' && parseFloat(row.actualValue) > 0.5) return 'exceeded'
  if (row.name === 'COD' && parseFloat(row.actualValue) > 50) return 'exceeded'
  return 'normal'
}

const getItemStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    'pending': 'info',
    'processing': 'warning',
    'completed': 'success',
    'exception': 'danger'
  }
  return typeMap[status] || 'info'
}

const getItemStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    'pending': '待检测',
    'processing': '检测中',
    'completed': '已完成',
    'exception': '异常'
  }
  return textMap[status] || status
}

// 导出报告
const handleExport = () => {
  ElMessage.success('正在导出执行报告...')
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.execution-detail-dialog {
  .execution-detail-content {
    max-height: 70vh;
    overflow-y: auto;
  }

  .detail-card {
    border: 1px solid #ebeef5;
    border-radius: 4px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .card-title {
      font-weight: 600;
      color: #303133;
    }
  }

  .detail-item {
    margin-bottom: 12px;

    label {
      font-weight: 500;
      color: #606266;
      margin-right: 8px;
    }

    span {
      color: #303133;
    }
  }

  .completion-rate {
    font-weight: 600;
    
    &.high {
      color: #67c23a;
    }
    
    &.medium {
      color: #e6a23c;
    }
    
    &.low {
      color: #f56c6c;
    }
  }

  .execution-detail-table {
    margin-top: 16px;
    
    .exceeded {
      color: #f56c6c;
      font-weight: 600;
    }
    
    .normal {
      color: #303133;
    }
    
    .no-data {
      color: #c0c4cc;
    }
  }

  .timeline-content {
    .timeline-title {
      font-weight: 500;
      color: #303133;
      margin-bottom: 4px;
    }

    .timeline-desc {
      color: #606266;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .timeline-operator {
      color: #909399;
      font-size: 12px;
    }
  }

  .remark-content {
    color: #606266;
    line-height: 1.6;
    margin: 0;
  }

  .dialog-footer {
    text-align: right;
  }
}
</style>

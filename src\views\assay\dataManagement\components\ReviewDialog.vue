<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-descriptions :column="2" border>
        <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
        <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
        <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
        <el-descriptions-item label="检测值">
          <span :class="{ 'text-danger': isExceeded, 'text-warning': isWarning }">
            {{ formData.testValue }} {{ formData.unit }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="检测日期">{{ formData.testDate }}</el-descriptions-item>
        <el-descriptions-item label="检测人">{{ formData.tester }}</el-descriptions-item>
        <el-descriptions-item label="检测方法">{{ formData.method }}</el-descriptions-item>
        <el-descriptions-item label="检测仪器">{{ formData.instrument }}</el-descriptions-item>
      </el-descriptions>
      
      <div v-if="showStandardInfo" class="standard-info mt-4">
        <el-alert
          :title="standardTitle"
          :type="isExceeded ? 'error' : isWarning ? 'warning' : 'info'"
          :description="standardDescription"
          show-icon
          :closable="false"
        />
      </div>
      
      <el-divider content-position="center">{{ actionType === 'approve' ? '审核通过' : '审核驳回' }}</el-divider>
      
      <el-form-item label="审核人" prop="reviewer">
        <el-select v-model="formData.reviewer" placeholder="请选择审核人">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.name" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核日期" prop="reviewDate">
        <el-date-picker v-model="formData.reviewDate" type="date" placeholder="请选择审核日期" />
      </el-form-item>
      <el-form-item v-if="actionType === 'reject'" label="驳回原因" prop="rejectReason">
        <el-select v-model="formData.rejectReason" placeholder="请选择驳回原因">
          <el-option label="数据错误" value="dataError" />
          <el-option label="超出正常范围" value="outOfRange" />
          <el-option label="方法不符" value="wrongMethod" />
          <el-option label="仪器问题" value="instrumentIssue" />
          <el-option label="其他原因" value="other" />
        </el-select>
      </el-form-item>
      <el-form-item label="审核意见" prop="comments">
        <el-input v-model="formData.comments" type="textarea" :rows="4" placeholder="请输入审核意见" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'ReviewDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)
const actionType = ref<'approve' | 'reject'>('approve')

const dialogTitle = computed(() => {
  return actionType.value === 'approve' ? '数据审核通过' : '数据审核驳回'
})

// 用户选项（模拟数据）
const userOptions = [
  { id: 1, name: '张三' },
  { id: 2, name: '李四' },
  { id: 3, name: '王五' },
  { id: 4, name: '赵六' },
  { id: 5, name: '钱七' }
]

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  testValue: '',
  unit: '',
  testDate: '',
  tester: '',
  method: '',
  instrument: '',
  dataStatus: '',
  standard: null as { min: number; max: number; unit: string } | null,
  reviewer: '',
  reviewDate: new Date(),
  rejectReason: '',
  comments: ''
})

// 计算是否超标或警告
const isExceeded = computed(() => {
  if (!formData.value.standard) return false
  return parseFloat(formData.value.testValue) > formData.value.standard.max
})

const isWarning = computed(() => {
  if (!formData.value.standard) return false
  const value = parseFloat(formData.value.testValue)
  const max = formData.value.standard.max
  // 警告阈值设为标准值的80%
  return value > max * 0.8 && value <= max
})

// 显示标准信息
const showStandardInfo = computed(() => {
  return formData.value.standard !== null
})

// 标准信息标题
const standardTitle = computed(() => {
  if (isExceeded.value) {
    return `检测值超出标准限值！`
  } else if (isWarning.value) {
    return `检测值接近标准限值，请注意！`
  } else {
    return `检测值在标准范围内`
  }
})

// 标准信息描述
const standardDescription = computed(() => {
  if (!formData.value.standard) return ''
  const { min, max, unit } = formData.value.standard
  return `标准范围：${min} - ${max} ${unit}`
})

// 表单校验规则
const formRules = reactive<FormRules>({
  reviewer: [{ required: true, message: '请选择审核人', trigger: 'change' }],
  reviewDate: [{ required: true, message: '请选择审核日期', trigger: 'change' }],
  rejectReason: [{ required: true, message: '请选择驳回原因', trigger: 'change' }],
  comments: [{ required: true, message: '请输入审核意见', trigger: 'blur' }]
})

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 获取检测项目的标准范围
const getTestItemStandard = (testItem: string) => {
  // 模拟从配置或API获取标准值
  switch (testItem) {
    case 'COD':
      return { min: 0, max: 50, unit: 'mg/L' }
    case 'BOD5':
      return { min: 0, max: 10, unit: 'mg/L' }
    case '氨氮':
    case 'NH3-N':
      return { min: 0, max: 5, unit: 'mg/L' }
    case '总磷':
    case 'TP':
      return { min: 0, max: 0.5, unit: 'mg/L' }
    case '总氮':
    case 'TN':
      return { min: 0, max: 15, unit: 'mg/L' }
    default:
      return null
  }
}

// 打开对话框
const open = async (data: any, type: 'approve' | 'reject') => {
  if (!data) return
  
  dialogVisible.value = true
  actionType.value = type
  
  formData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    samplingPoint: data.samplingPoint,
    testValue: data.testValue,
    unit: data.unit || 'mg/L',
    testDate: data.testDate || '',
    tester: data.tester || '',
    method: data.method || '重铬酸钾法', // 模拟数据
    instrument: data.instrument || '分光光度计A', // 模拟数据
    dataStatus: data.dataStatus || 'normal',
    standard: getTestItemStandard(data.testItem),
    reviewer: '',
    reviewDate: new Date(),
    rejectReason: '',
    comments: ''
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    if (actionType.value === 'approve') {
      ElMessage.success('审核通过成功')
    } else {
      ElMessage.success('审核驳回成功')
    }
    
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

.standard-info {
  margin-bottom: 1rem;
}
</style> 
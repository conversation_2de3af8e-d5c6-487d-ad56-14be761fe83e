#luckysheet-vchart-setting-dialog {
  padding: 10px;
  position: absolute;
  overflow: hidden;
  right: 0;
  top: 0;
  width: 320px;
  height: calc(100% - 20px);
  box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2),
    0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
  z-index: 9999;
  background-color: #fff;
  color: #444;
  display: flex;
  flex-direction: column;
}

.luckysheet-vchart-setting-dialog-title {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  /* border-bottom: solid 1px #dadce0; */
  padding-bottom: 10px;
}

#luckysheet-vchart-setting-dialog-close {
  cursor: pointer;
  width: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.luckysheet-vchart-setting-dialog-footer {
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.luckysheet-vchart-setting-dialog-footer span {
  margin-left: 10px;
  width: 75px;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  white-space: nowrap;
  cursor: pointer;
  background: #fff;
  border: 1px solid #dcdfe6;
  color: #606266;
  text-align: center;
  box-sizing: border-box;
  outline: none;
  transition: 0.1s;
  font-weight: 500;
  font-size: 14px;
  border-radius: 4px;
}

.luckysheet-vchart-setting-dialog-footer .cancel:hover {
  color: var(--luckysheet-main-color);
  background-color: var(--luckysheet-main-color-a2);
  border-color: transparent;
}
.luckysheet-vchart-setting-dialog-footer .confirm {
  background-color: var(--luckysheet-main-color-a8);
  border-color: transparent;
  color: #fff;
}
.luckysheet-vchart-setting-dialog-footer .confirm:hover {
  /* color: var(--luckysheet-main-color); */
}

/* 内容区 */
.luckysheet-vchart-setting-dialog-body {
  height: calc(100% - 32px - 32px - 10px);
  padding: 10px 0;
  overflow: hidden;
}
.luckysheet-vchart-setting-dialog-body-tabs {
  height: 30px;
  border-bottom: solid #dadce0 2px;
}
.luckysheet-vchart-setting-dialog-body-tabs .tab {
  display: inline-block;
  height: 100%;
  padding: 0 10px;
  cursor: pointer;
  transition: all 0.3s;
}
.luckysheet-vchart-setting-dialog-body-tabs .tab:hover,
.luckysheet-vchart-setting-dialog-body-tabs .active {
  border-bottom: solid 2px var(--luckysheet-main-color);
  color: var(--luckysheet-main-color);
}

.luckysheet-vchart-setting-dialog-body-content {
  padding: 10px;
  height: calc(100% - 30px - 20px);
  overflow: hidden;
  overflow-y: auto;
}
/* 滚动条样式 */
.luckysheet-vchart-setting-dialog-body-content::-webkit-scrollbar {
  width: 4px;
}
/* 滑块样式 */
.luckysheet-vchart-setting-dialog-body-content::-webkit-scrollbar-thumb {
  background-color: var(--luckysheet-main-color);
  border-radius: 2px;
}
/* 滚动条轨道样式 */
.luckysheet-vchart-setting-dialog-body-content::-webkit-scrollbar-track {
  background-color: #f2f2f2;
  border-radius: 2px;
}
.luckysheet-vchart-setting-dialog-body-content .vchart-type-item {
  width: 48%;
  user-select: none;
  display: inline-block;
  box-shadow: 0 6px 20px var(--luckysheet-main-color-a1);
  border-radius: 8px;
  overflow: hidden;
  border: solid 2px transparent;
  transition: all 0.3s;
  cursor: pointer;
}
.luckysheet-vchart-setting-dialog-body-content .tips {
  user-select: none;
  font-size: 16px;
}
.luckysheet-vchart-setting-dialog-body-content .vchart-type-item:hover {
  border-color: var(--luckysheet-main-color);
}
.luckysheet-vchart-setting-dialog-body-content .vchart-type-item img {
  width: 100%;
}
.luckysheet-vchart-setting-dialog-body-content .vchart-type-item div {
  text-align: center;
  width: 100%;
  background: linear-gradient(0deg, #f6f8fe00, #f6f8fe);
  height: 32px;
  line-height: 32px;
  font-size: 12px;
  line-height: 32px;
  letter-spacing: 0.639896px;
  color: #000000b3;
}

/* style content */
.luckysheet-vchart-setting-dialog-body-content .vchart-style-item {
  user-select: none;
  display: flex;
  align-items: center;
}

.luckysheet-vchart-setting-dialog-body-content .vchart-style-item-label {
  padding-right: 10px;
  font-size: 12px;
  color: #909399;
}

<template>
  <el-dialog
    v-model="dialogVisible"
    title="样品详情"
    width="900px"
    :before-close="handleClose"
  >
    <div v-loading="loading" class="sample-detail">
      <!-- 基本信息 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-tag :type="getStatusType(sampleData.status)">
              {{ getStatusText(sampleData.status) }}
            </el-tag>
          </div>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="样品编号">
            {{ sampleData.sampleCode }}
          </el-descriptions-item>
          <el-descriptions-item label="样品类型">
            <el-tag :type="getSampleTypeColor(sampleData.sampleType)">
              {{ getSampleTypeText(sampleData.sampleType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="采样点">
            {{ sampleData.samplingPoint }}
          </el-descriptions-item>
          <el-descriptions-item label="采样日期">
            {{ sampleData.samplingDate }}
          </el-descriptions-item>
          <el-descriptions-item label="样品数量">
            {{ sampleData.quantity }}{{ sampleData.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="存储位置">
            {{ sampleData.storageLocation }}
          </el-descriptions-item>
          <el-descriptions-item label="存储温度">
            {{ sampleData.temperature }}°C
          </el-descriptions-item>
          <el-descriptions-item label="有效期">
            <span :class="{ 'text-red-500': isExpiringSoon(sampleData.expiryDate) }">
              {{ sampleData.expiryDate }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 检测信息 -->
      <el-card shadow="never" class="mb-4">
        <template #header>
          <span>检测信息</span>
        </template>
        <el-descriptions :column="2" border>
          <el-descriptions-item label="外观">
            {{ sampleData.appearance }}
          </el-descriptions-item>
          <el-descriptions-item label="检测进度">
            <el-progress :percentage="sampleData.testProgress" :color="getProgressColor(sampleData.testProgress)" />
          </el-descriptions-item>
          <el-descriptions-item label="余样处置">
            <el-tag :type="sampleData.remainingSampleDisposal ? 'success' : 'info'">
              {{ sampleData.remainingSampleDisposal ? '已处置' : '未处置' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ sampleData.createTime }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 备注信息 -->
      <el-card shadow="never" v-if="sampleData.description">
        <template #header>
          <span>备注信息</span>
        </template>
        <p class="text-gray-600">{{ sampleData.description }}</p>
      </el-card>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button type="primary" @click="handlePrint">
          <el-icon><Printer /></el-icon>打印详情
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Printer } from '@element-plus/icons-vue'

defineOptions({ name: 'SampleDetailDialog' })

// 对话框状态
const dialogVisible = ref(false)
const loading = ref(false)

// 样品数据
const sampleData = reactive({
  id: 0,
  sampleCode: '',
  sampleType: '',
  samplingPoint: '',
  samplingDate: '',
  quantity: 0,
  unit: '',
  storageLocation: '',
  temperature: 0,
  expiryDate: '',
  appearance: '',
  testProgress: 0,
  remainingSampleDisposal: false,
  status: '',
  description: '',
  createTime: ''
})

// 打开对话框
const open = (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  loading.value = true
  
  // 模拟加载详情数据
  setTimeout(() => {
    Object.assign(sampleData, {
      ...data,
      createTime: data.createTime || new Date().toISOString().split('T')[0]
    })
    loading.value = false
  }, 500)
}

// 关闭对话框
const handleClose = () => {
  dialogVisible.value = false
}

// 打印详情
const handlePrint = () => {
  window.print()
  ElMessage.success('样品详情已发送到打印机')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap = {
    'collected': 'warning',
    'stored': 'primary',
    'testing': 'info',
    'completed': 'success',
    'destroyed': 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap = {
    'collected': '已采集',
    'stored': '已入库',
    'testing': '检验中',
    'completed': '已完成',
    'destroyed': '已销毁'
  }
  return textMap[status] || status
}

// 获取样品类型颜色
const getSampleTypeColor = (type: string) => {
  const colorMap = {
    'inlet': 'primary',
    'outlet': 'success',
    'sludge': 'warning',
    'process': 'info'
  }
  return colorMap[type] || 'info'
}

// 获取样品类型文本
const getSampleTypeText = (type: string) => {
  const textMap = {
    'inlet': '进水样品',
    'outlet': '出水样品',
    'sludge': '污泥样品',
    'process': '中间过程样品'
  }
  return textMap[type] || type
}

// 检查是否即将过期
const isExpiringSoon = (expiryDate: string) => {
  if (!expiryDate) return false
  const expiry = new Date(expiryDate)
  const now = new Date()
  const diffDays = Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24))
  return diffDays <= 3 && diffDays >= 0
}

// 获取进度颜色
const getProgressColor = (percentage: number) => {
  if (percentage < 30) return '#f56c6c'
  if (percentage < 70) return '#e6a23c'
  return '#67c23a'
}

// 暴露方法
defineExpose({
  open
})
</script>

<style scoped>
.sample-detail {
  max-height: 600px;
  overflow-y: auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dialog-footer {
  text-align: right;
}

.text-red-500 {
  color: #f56c6c;
}

.text-gray-600 {
  color: #909399;
  line-height: 1.5;
}

@media print {
  .el-dialog__header,
  .el-dialog__footer {
    display: none;
  }
}
</style>

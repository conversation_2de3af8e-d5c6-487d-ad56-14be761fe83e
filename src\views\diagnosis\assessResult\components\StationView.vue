<template>
  <div class="station-view">
    <!-- 1️⃣ 顶部筛选区域 -->
    <el-card class="mb-4">
      <div class="filter-header">
        <el-form :model="filters" inline>
          <el-form-item label="评估方案">
            <el-select v-model="filters.schemeId" placeholder="请选择评估方案" style="width: 280px"
              @change="handleSchemeChange">
              <el-option v-for="scheme in schemes" :key="scheme.id" :label="scheme.name" :value="scheme.id" />
            </el-select>
          </el-form-item>
          <el-form-item label="评估周期">
            <el-select v-model="filters.period" placeholder="请选择周期" style="width: 180px" @change="handlePeriodChange">
              <el-option v-for="period in availablePeriods" :key="period" :label="period" :value="period" />
            </el-select>
          </el-form-item>
        </el-form>

        <div class="filter-actions">
          <el-button type="primary" @click="handleExportPDF">
            <el-icon>
              <Document />
            </el-icon>
            导出PDF
          </el-button>
        </div>
      </div>
    </el-card>

    <!-- 2️⃣ 综合得分总览卡片 -->
    <el-card class="mb-4">
      <template #header>
        <span class="font-bold">综合得分总览</span>
      </template>
      <div class="summary-container">
        <div class="score-main">
          <div class="score-value">{{ summary.score }}</div>
          <div class="score-label">综合得分</div>
        </div>
        <div class="score-details">
          <div class="detail-item">
            <span class="detail-label">环比变化</span>
            <span class="detail-value" :class="getChangeClass(summary.change)">{{ summary.change }}</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">异常项数量</span>
            <span class="detail-value abnormal">{{ summary.abnormalCount }} 项</span>
          </div>
          <div class="detail-item">
            <span class="detail-label">评分等级</span>
            <el-tag :type="getLevelType(summary.level)" size="large">{{ summary.level }}</el-tag>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 3️⃣ 指标得分明细表格 -->
    <el-card class="mb-4">
      <template #header>
        <span class="font-bold">指标得分明细</span>
      </template>
      <el-table :data="indicatorScores" border stripe>
        <el-table-column prop="name" label="指标名称" width="150" />
        <el-table-column prop="unit" label="单位" width="80" align="center" />
        <el-table-column prop="value" label="实际值" width="100" align="center" />
        <el-table-column prop="score" label="得分" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getScoreType(row.score)">{{ row.score }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100" align="center">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">{{ row.status }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="advice" label="建议" min-width="200">
          <template #default="{ row }">
            <span v-if="row.advice" class="advice-text">{{ row.advice }}</span>
            <span v-else class="no-advice">-</span>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 4️⃣ 指标综合分析图 -->
    <el-card class="mb-4">
      <template #header>
        <div class="flex justify-between items-center">
          <span class="font-bold">{{ getChartTitle() }}</span>
          <div class="chart-controls">
            <el-radio-group v-model="chartType" size="small">
              <el-radio-button label="radar">雷达图</el-radio-button>
              <el-radio-button label="bar">柱状图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </template>
      <div class="analysis-chart">
        <!-- 雷达图 -->
        <div v-if="chartType === 'radar'" class="radar-chart">
          <svg class="radar-svg" :width="chartWidth" :height="chartHeight" viewBox="0 0 600 400">
            <!-- 雷达图背景网格 -->
            <g class="radar-grid" transform="translate(300, 200)">
              <!-- 同心圆 -->
              <g v-for="level in 5" :key="level">
                <circle :r="level * 30" fill="none" stroke="#e0e0e0" stroke-width="1" />
                <text :x="level * 30 + 5" y="-5" font-size="10" fill="#999">{{ level * 20 }}</text>
              </g>

              <!-- 雷达轴线 -->
              <g v-for="(indicator, index) in currentIndicators" :key="indicator">
                <line :x1="0" :y1="0" :x2="getRadarX(index, 150)" :y2="getRadarY(index, 150)" stroke="#d0d0d0"
                  stroke-width="1" />
                <text :x="getRadarX(index, 170)" :y="getRadarY(index, 170)" text-anchor="middle" font-size="12"
                  fill="#333">
                  {{ indicator }}
                </text>
              </g>

              <!-- 数据多边形 -->
              <polygon :points="getRadarPolygonPoints()" fill="rgba(64, 158, 255, 0.2)" stroke="#409eff"
                stroke-width="2" />

              <!-- 数据点 -->
              <g v-for="(indicator, index) in currentIndicators" :key="indicator">
                <circle :cx="getRadarX(index, getRadarRadius(getIndicatorCurrentScore(indicator)))"
                  :cy="getRadarY(index, getRadarRadius(getIndicatorCurrentScore(indicator)))" r="4" fill="#409eff"
                  stroke="#fff" stroke-width="2" class="radar-point"
                  @mouseover="showTooltip($event, indicator, getIndicatorCurrentScore(indicator))"
                  @mouseout="hideTooltip" />
              </g>
            </g>
          </svg>
        </div>

        <!-- 柱状图 -->
        <div v-if="chartType === 'bar'" class="bar-chart">
          <svg class="bar-svg" :width="chartWidth" :height="chartHeight" viewBox="0 0 800 400">
            <!-- 坐标轴 -->
            <g class="axes">
              <!-- Y轴 -->
              <line x1="80" y1="50" x2="80" y2="320" stroke="#333" stroke-width="2" />
              <!-- X轴 -->
              <line x1="80" y1="320" x2="720" y2="320" stroke="#333" stroke-width="2" />

              <!-- Y轴刻度 -->
              <g v-for="tick in [0, 20, 40, 60, 80, 100]" :key="tick">
                <line :x1="75" :x2="85" :y1="getYPosition(tick)" :y2="getYPosition(tick)" stroke="#333"
                  stroke-width="1" />
                <text :x="70" :y="getYPosition(tick) + 5" text-anchor="end" font-size="12" fill="#666">{{ tick }}</text>
              </g>
            </g>

            <!-- 柱状图数据 -->
            <g class="bars">
              <g v-for="(indicator, index) in currentIndicators" :key="indicator">
                <rect :x="getBarXPosition(index)" :y="getYPosition(getIndicatorCurrentScore(indicator))"
                  :width="barWidth" :height="320 - getYPosition(getIndicatorCurrentScore(indicator))"
                  :fill="getBarColor(index)" class="bar-rect"
                  @mouseover="showTooltip($event, indicator, getIndicatorCurrentScore(indicator))"
                  @mouseout="hideTooltip" />

                <!-- 数据标签 -->
                <text :x="getBarXPosition(index) + barWidth / 2"
                  :y="getYPosition(getIndicatorCurrentScore(indicator)) - 5" text-anchor="middle" font-size="12"
                  fill="#333" font-weight="bold">
                  {{ getIndicatorCurrentScore(indicator) }}
                </text>

                <!-- X轴标签 -->
                <text :x="getBarXPosition(index) + barWidth / 2" y="340" text-anchor="middle" font-size="11"
                  fill="#666">
                  {{ indicator }}
                </text>
              </g>
            </g>
          </svg>
        </div>
      </div>
    </el-card>



    <!-- 评估报告弹窗 -->
    <el-dialog v-model="reportDialogVisible" title="评估报告" width="80%" :before-close="handleCloseReport">
      <div class="report-container" ref="reportRef" :style="getReportStyle()">
        <!-- 报告头部 -->
        <div class="report-header">
          <h1 class="report-title">{{ reportConfig.title || `${stationInfo.name} 评估报告` }}</h1>
          <h2 v-if="reportConfig.subtitle" class="report-subtitle">{{ reportConfig.subtitle }}</h2>
          <div class="report-meta">
            <p><strong>评估方案：</strong>{{ schemeInfo.name }}</p>
            <p><strong>评估周期：</strong>{{ period }}</p>
            <p><strong>生成时间：</strong>{{ new Date().toLocaleString() }}</p>
          </div>
        </div>

        <!-- 1. 评估对象简介 -->
        <div v-if="reportConfig.modules.includes('intro')" class="report-section">
          <h2>1. 评估对象简介</h2>
          <div class="section-content">
            <p>{{ stationInfo.name }}位于{{ stationInfo.area }}，是一座现代化的{{ stationInfo.type
              }}。该厂站承担着重要的水处理任务，在保障区域水环境安全方面发挥着关键作用。</p>
            <div class="basic-info">
              <div class="info-item">
                <span class="label">厂站名称：</span>
                <span class="value">{{ stationInfo.name }}</span>
              </div>
              <div class="info-item">
                <span class="label">所在区域：</span>
                <span class="value">{{ stationInfo.area }}</span>
              </div>
              <div class="info-item">
                <span class="label">厂站类型：</span>
                <span class="value">{{ stationInfo.type }}</span>
              </div>
              <div class="info-item">
                <span class="label">评估周期：</span>
                <span class="value">{{ period }}</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 2. 综合评估结果 -->
        <div v-if="reportConfig.modules.includes('result')" class="report-section">
          <h2>2. 综合评估结果</h2>
          <div class="section-content">
            <div class="score-summary">
              <div class="overall-score">
                <span class="score-label">综合得分</span>
                <span class="score-value">{{ overallScore }}</span>
                <span class="score-unit">分</span>
              </div>
              <div class="score-level">
                <el-tag :type="getScoreTagType(overallScore)" size="large">{{ getScoreLevel(overallScore) }}</el-tag>
              </div>
            </div>
            <div class="editable-conclusion">
              <label><strong>综合评价：</strong></label>
              <el-input v-model="editableConclusion" type="textarea" :rows="4" placeholder="请输入综合评价..."
                class="conclusion-input" />
            </div>
          </div>
        </div>

        <!-- 3. 评估指标清单 -->
        <div v-if="reportConfig.modules.includes('indicators')" class="report-section">
          <h2>3. 评估指标清单</h2>
          <div class="section-content">
            <!-- 按评估方案项目结构分组显示 -->
            <div v-for="project in schemeProjects" :key="project.id" class="project-group">
              <h3 class="project-title">
                {{ project.name }}
                <el-tag size="small" type="info">权重: {{ project.weight }}%</el-tag>
              </h3>
              <el-table :data="getProjectIndicators(project.id)" border size="small" class="indicator-table">
                <el-table-column type="index" label="序号" width="60" align="center" />
                <el-table-column prop="name" label="指标名称" min-width="150" />
                <el-table-column prop="unit" label="单位" width="80" align="center" />
                <el-table-column prop="weight" label="权重" width="80" align="center">
                  <template #default="{ row }">
                    <span class="weight-text">{{ row.weight }}%</span>
                  </template>
                </el-table-column>
                <el-table-column prop="threshold" label="一级阈值" width="120" align="center">
                  <template #default="{ row }">
                    <span class="threshold-text">{{ getIndicatorThreshold(row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="value" label="实际值" width="100" align="center">
                  <template #default="{ row }">
                    <span class="actual-value">{{ row.value }}{{ row.unit }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="scoringRule" label="评分规则" min-width="200" show-overflow-tooltip>
                  <template #default="{ row }">
                    <span class="scoring-rule">{{ getIndicatorScoringRule(row) }}</span>
                  </template>
                </el-table-column>
                <el-table-column prop="score" label="得分" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getScoreTagType(row.score)">{{ row.score }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="status" label="状态" width="100" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getStatusTagType(row.status)">{{ row.status }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="description" label="说明" min-width="150" show-overflow-tooltip />
              </el-table>
            </div>
          </div>
        </div>

        <!-- 4. 评估指标分析 -->
        <div v-if="reportConfig.modules.includes('analysis')" class="report-section">
          <h2>4. 评估指标分析</h2>
          <div class="section-content">
            <!-- 雷达图 -->
            <div v-if="reportConfig.chartTypes.includes('radar')" class="chart-section">
              <h3>4.1 指标综合表现雷达图</h3>
              <div class="chart-container">
                <svg class="report-radar-svg" width="500" height="350" viewBox="0 0 500 350">
                  <!-- 雷达图背景网格 -->
                  <g class="radar-grid" transform="translate(250, 175)">
                    <!-- 同心圆 -->
                    <g v-for="level in 5" :key="level">
                      <circle :r="level * 25" fill="none" stroke="#e0e0e0" stroke-width="1" />
                      <text :x="level * 25 + 5" y="-5" font-size="10" fill="#999">{{ level * 20 }}</text>
                    </g>

                    <!-- 雷达轴线 -->
                    <g v-for="(indicator, index) in currentIndicators" :key="indicator">
                      <line :x1="0" :y1="0" :x2="getReportRadarX(index, 125)" :y2="getReportRadarY(index, 125)"
                        stroke="#d0d0d0" stroke-width="1" />
                      <text :x="getReportRadarX(index, 140)" :y="getReportRadarY(index, 140)" text-anchor="middle"
                        font-size="11" fill="#333">
                        {{ indicator }}
                      </text>
                    </g>

                    <!-- 数据多边形 -->
                    <polygon :points="getReportRadarPolygonPoints()" fill="rgba(64, 158, 255, 0.2)" stroke="#409eff"
                      stroke-width="2" />

                    <!-- 数据点 -->
                    <g v-for="(indicator, index) in currentIndicators" :key="indicator">
                      <circle :cx="getReportRadarX(index, getReportRadarRadius(getIndicatorCurrentScore(indicator)))"
                        :cy="getReportRadarY(index, getReportRadarRadius(getIndicatorCurrentScore(indicator)))" r="3"
                        fill="#409eff" stroke="#fff" stroke-width="1" />
                    </g>
                  </g>
                </svg>
              </div>
            </div>

            <!-- 指标分析文本 -->
            <div class="analysis-text">
              <h3>4.2 指标分析说明</h3>
              <div v-for="project in schemeProjects" :key="project.id" class="project-analysis">
                <h4>{{ project.name }}项目分析</h4>
                <div v-for="(indicator, index) in project.indicators" :key="indicator.id" class="indicator-analysis">
                  <h5>{{ index + 1 }}. {{ indicator.name }}</h5>
                  <p><strong>得分：</strong>{{ indicator.score }}分 <strong>指标分值：</strong>{{ getIndicatorScoreValue(project,
                    indicator) }}分</p>
                  <div class="editable-analysis">
                    <label><strong>分析说明：</strong></label>
                    <el-input v-model="editableAnalysis[indicator.id]" type="textarea" :rows="3"
                      placeholder="请输入指标分析说明..." class="analysis-input" />
                  </div>
                  <div class="editable-suggestion" v-if="indicator.advice || editableSuggestions[indicator.id]">
                    <label><strong>改进建议：</strong></label>
                    <el-input v-model="editableSuggestions[indicator.id]" type="textarea" :rows="2"
                      placeholder="请输入改进建议..." class="suggestion-input" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 5. 评估数据表 -->
        <div v-if="reportConfig.modules.includes('data')" class="report-section">
          <h2>5. 评估数据表</h2>
          <div class="section-content">
            <el-table :data="detailedData" border size="small" class="data-table">
              <el-table-column prop="indicator" label="指标项" min-width="150" />
              <el-table-column prop="project" label="所属项目" width="120" />
              <el-table-column prop="value" label="数值" width="100" align="center" />
              <el-table-column prop="unit" label="单位" width="80" align="center" />
              <el-table-column prop="benchmark" label="基准值" width="100" align="center" />
              <el-table-column prop="deviation" label="偏差" width="100" align="center">
                <template #default="{ row }">
                  <span :class="getDeviationClass(row.deviation)">{{ row.deviation }}</span>
                </template>
              </el-table-column>
              <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
            </el-table>
          </div>
        </div>

        <!-- 报告结尾 -->
        <div class="report-footer">
          <p class="footer-text">本报告基于{{ schemeInfo.name }}生成，数据截止至{{ period }}。</p>
          <p class="footer-text">报告生成时间：{{ new Date().toLocaleString() }}</p>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCloseReport">关闭</el-button>
          <el-button type="primary" @click="handlePrintReport">打印报告</el-button>
          <el-button type="success" @click="handleDownloadReport">下载PDF</el-button>
        </div>
      </template>
    </el-dialog>

  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage, ElLoading } from 'element-plus'
import { Document, Download, Back, TrendCharts, ArrowUp, ArrowDown, Minus } from '@element-plus/icons-vue'
import html2canvas from 'html2canvas'
import jsPDF from 'jspdf'

// 接口定义
interface SchemeInfo {
  schemeId: string
  name: string
  cycle: string
}

interface StationInfo {
  stationId: string
  name: string
  area: string
  type: string
}

interface Summary {
  score: number
  change: string
  abnormalCount: number
  level: string
}

interface IndicatorScore {
  id: string
  name: string
  unit: string
  value: number
  score: number
  weight: number
  status: string
  advice: string
  description: string
  formula: string
  threshold?: string
  standard?: string
  projectId: string
}

interface SchemeProject {
  id: string
  name: string
  weight: number
  fullScore: number
  scoreType: string
  description?: string
  indicators: IndicatorScore[]
}

interface Report {
  conclusion: string
  analysis: string[]
  suggestions: string[]
}

// 响应式数据
const selectedIndicator = ref('')
const chartType = ref('radar') // 默认显示雷达图

// 报告弹窗控制
const reportDialogVisible = ref(false)
const reportRef = ref()

// 可编辑的分析和建议内容
const editableAnalysis = ref<Record<string, string>>({})
const editableSuggestions = ref<Record<string, string>>({})

// 可编辑的综合评价内容
const editableConclusion = ref('')

// 简化的报告配置
const reportConfig = ref({
  title: '',
  subtitle: '',
  modules: ['intro', 'result', 'indicators', 'analysis', 'data'],
  chartTypes: ['radar', 'table'],
  primaryColor: '#409eff',
  fontSize: 14,
  margin: 20
})

// 图表尺寸
const chartWidth = ref(800)
const chartHeight = ref(400)

// 当前指标数据（从指标得分明细中获取）
const currentIndicators = computed(() => {
  return indicatorScores.value.map(item => item.name)
})

// 柱状图宽度
const barWidth = computed(() => {
  const availableWidth = 640 // 总宽度减去边距
  const spacing = 20 // 柱子间距
  const totalSpacing = (currentIndicators.value.length - 1) * spacing
  return Math.max(40, (availableWidth - totalSpacing) / currentIndicators.value.length)
})

// URL参数获取（模拟）
const urlParams = {
  schemeId: 'scheme01',
  period: '2025年3月',
  stationId: 'station07'
}

// 筛选条件
const filters = ref({
  schemeId: urlParams.schemeId || 'scheme_001',
  period: urlParams.period || '2025年3月'
})

// 固定的厂站信息（从URL参数获取）
const currentStationId = ref(urlParams.stationId || 'plant_001')

// 方案数据 - 基于真实净水厂业务场景，丰富完善
const schemes = ref([
  {
    id: 'scheme_001',
    name: '净水厂月度运营评估方案',
    periodType: '月度',
    description: '针对大型净水厂的月度运营效率和水质安全评估',
    stations: [
      { id: 'plant_001', name: '经开净水厂', code: 'JKJSC001', type: '净水厂', capacity: '50万吨/日', region: '经开区', buildYear: '2018' },
      { id: 'plant_002', name: '望塘净水厂', code: 'WTJSC002', type: '净水厂', capacity: '35万吨/日', region: '包河区', buildYear: '2016' },
      { id: 'plant_003', name: '塘西河净水厂', code: 'TXHJSC003', type: '净水厂', capacity: '45万吨/日', region: '蜀山区', buildYear: '2019' },
      { id: 'plant_004', name: '北涝圩净水厂', code: 'BLWJSC004', type: '净水厂', capacity: '30万吨/日', region: '庐阳区', buildYear: '2017' },
      { id: 'plant_005', name: '长岗净水厂', code: 'CGJSC005', type: '净水厂', capacity: '60万吨/日', region: '瑶海区', buildYear: '2020' },
      { id: 'plant_006', name: '滨湖净水厂', code: 'BHJSC006', type: '净水厂', capacity: '42万吨/日', region: '滨湖新区', buildYear: '2021' },
      { id: 'plant_007', name: '新站净水厂', code: 'XZJSC007', type: '净水厂', capacity: '38万吨/日', region: '新站区', buildYear: '2019' }
    ]
  },
  {
    id: 'scheme_002',
    name: '乡镇净水站季度评估方案',
    periodType: '季度',
    description: '针对乡镇级净水站的季度运行状况和服务质量评估',
    stations: [
      { id: 'station_001', name: '长临河镇净水站', code: 'CLHJSZ001', type: '净水站', capacity: '6万吨/日', region: '肥东县', buildYear: '2020' },
      { id: 'station_002', name: '元疃镇净水站', code: 'YTJSZ002', type: '净水站', capacity: '4万吨/日', region: '肥东县', buildYear: '2019' },
      { id: 'station_003', name: '梁园镇净水站', code: 'LYJSZ003', type: '净水站', capacity: '4万吨/日', region: '肥东县', buildYear: '2021' },
      { id: 'station_004', name: '石塘镇净水站', code: 'STJSZ004', type: '净水站', capacity: '5万吨/日', region: '肥东县', buildYear: '2020' },
      { id: 'station_005', name: '旺兴塘净水站', code: 'WXTJSZ005', type: '净水站', capacity: '8万吨/日', region: '肥西县', buildYear: '2018' },
      { id: 'station_006', name: '官亭镇净水站', code: 'GTJSZ006', type: '净水站', capacity: '3.5万吨/日', region: '肥西县', buildYear: '2022' },
      { id: 'station_007', name: '花岗镇净水站', code: 'HGJSZ007', type: '净水站', capacity: '4.5万吨/日', region: '肥西县', buildYear: '2021' }
    ]
  },
  {
    id: 'scheme_003',
    name: '区域净水中心年度综合评估方案',
    periodType: '年度',
    description: '针对区域性净水中心的年度综合绩效和发展潜力评估',
    stations: [
      { id: 'center_001', name: '巢湖净水中心', code: 'CHJSZX001', type: '净水中心', capacity: '55万吨/日', region: '巢湖市', buildYear: '2017' },
      { id: 'center_002', name: '肥东净水中心', code: 'FDJSZX002', type: '净水中心', capacity: '50万吨/日', region: '肥东县', buildYear: '2018' },
      { id: 'center_003', name: '西部组团净水一厂', code: 'XBZTJSC001', type: '净水厂', capacity: '40万吨/日', region: '高新区', buildYear: '2019' },
      { id: 'center_004', name: '西部组团净水二厂', code: 'XBZTJSC002', type: '净水厂', capacity: '35万吨/日', region: '高新区', buildYear: '2020' },
      { id: 'center_005', name: '庐江净水中心', code: 'LJJSZX005', type: '净水中心', capacity: '28万吨/日', region: '庐江县', buildYear: '2021' }
    ]
  },
  {
    id: 'scheme_004',
    name: '应急供水保障评估方案',
    periodType: '半年度',
    description: '针对应急供水能力和保障体系的专项评估',
    stations: [
      { id: 'emergency_001', name: '应急净水一厂', code: 'YJJSC001', type: '应急净水厂', capacity: '15万吨/日', region: '市中心', buildYear: '2022' },
      { id: 'emergency_002', name: '应急净水二厂', code: 'YJJSC002', type: '应急净水厂', capacity: '12万吨/日', region: '东部新城', buildYear: '2023' },
      { id: 'backup_001', name: '备用水源净水站', code: 'BYSYJSZ001', type: '备用净水站', capacity: '8万吨/日', region: '南部山区', buildYear: '2021' }
    ]
  }
])

// 计算属性
const selectedScheme = computed(() => {
  return schemes.value.find(scheme => scheme.id === filters.value.schemeId)
})

const availableStations = computed(() => {
  return selectedScheme.value?.stations || []
})

const availablePeriods = computed(() => {
  if (!selectedScheme.value) return []

  const periodType = selectedScheme.value.periodType
  switch (periodType) {
    case '月度':
      return ['2024年10月', '2024年11月', '2024年12月', '2025年1月', '2025年2月', '2025年3月']
    case '季度':
      return ['2024年Q3', '2024年Q4', '2025年Q1', '2025年Q2']
    case '年度':
      return ['2022年', '2023年', '2024年', '2025年']
    default:
      return ['2025年Q1', '2025年Q2', '2025年Q3']
  }
})

// Mock数据 - 使用计算属性来确保数据同步
const schemeInfo = computed(() => {
  const scheme = selectedScheme.value
  return {
    schemeId: filters.value.schemeId,
    name: scheme?.name || '2025年水厂运营效率评估方案',
    cycle: scheme?.periodType || '月度'
  }
})

const stationInfo = computed(() => {
  // 从所有方案中查找当前厂站信息
  let station: any = null
  for (const scheme of schemes.value) {
    station = scheme.stations.find((s: any) => s.id === currentStationId.value)
    if (station) break
  }

  // 根据不同厂站返回详细信息
  const stationDetails: Record<string, any> = {
    'plant_001': {
      name: '经开净水厂',
      location: '经开区繁华大道与翡翠路交口',
      commissionDate: '2018年6月',
      treatmentProcess: '预处理+常规处理+深度处理+消毒',
      waterSource: '巢湖水源地',
      serviceArea: '经开区、滨湖新区部分区域',
      designCapacity: '50万吨/日',
      currentLoad: '85%',
      staffCount: 45,
      landArea: '12.5公顷',
      investmentAmount: '8.5亿元'
    },
    'plant_002': {
      name: '望塘净水厂',
      location: '包河区望塘路与南二环交口',
      commissionDate: '2016年3月',
      treatmentProcess: '常规处理+活性炭吸附+消毒',
      waterSource: '董铺水库',
      serviceArea: '包河区、政务区',
      designCapacity: '35万吨/日',
      currentLoad: '92%',
      staffCount: 38,
      landArea: '9.2公顷',
      investmentAmount: '6.2亿元'
    },
    'plant_003': {
      name: '塘西河净水厂',
      location: '蜀山区塘西河路与科学大道交口',
      commissionDate: '2019年9月',
      treatmentProcess: '预处理+常规处理+膜过滤+消毒',
      waterSource: '大房郢水库',
      serviceArea: '蜀山区、高新区',
      designCapacity: '45万吨/日',
      currentLoad: '78%',
      staffCount: 42,
      landArea: '11.8公顷',
      investmentAmount: '7.8亿元'
    }
  }

  const defaultInfo = stationDetails[currentStationId.value] || stationDetails['plant_001']

  return {
    stationId: currentStationId.value,
    name: station?.name || defaultInfo.name,
    area: '安徽省合肥市',
    type: station?.type || '净水厂',
    code: station?.code || 'JKJSC001',
    capacity: station?.capacity || defaultInfo.designCapacity,
    region: station?.region || '经开区',
    buildYear: station?.buildYear || '2018',
    location: defaultInfo.location,
    commissionDate: defaultInfo.commissionDate,
    operator: '合肥水投集团有限公司',
    contact: '0551-62888888',
    treatmentProcess: defaultInfo.treatmentProcess,
    waterSource: defaultInfo.waterSource,
    serviceArea: defaultInfo.serviceArea,
    currentLoad: defaultInfo.currentLoad,
    staffCount: defaultInfo.staffCount,
    landArea: defaultInfo.landArea,
    investmentAmount: defaultInfo.investmentAmount,
    certifications: ['ISO9001质量管理体系', 'ISO14001环境管理体系', 'OHSAS18001职业健康安全管理体系'],
    awards: ['全国优秀水厂', '省级文明单位', '市级安全生产标准化企业']
  }
})

const period = computed(() => filters.value.period)

// 报告相关计算属性
const overallScore = computed(() => {
  // 基于项目权重计算综合得分
  let totalScore = 0
  let totalWeight = 0

  schemeProjects.value.forEach(project => {
    // 计算项目得分（项目内指标的加权平均）
    let projectScore = 0
    let projectIndicatorWeight = 0

    project.indicators.forEach(indicator => {
      projectScore += indicator.score * (indicator.weight || 0)
      projectIndicatorWeight += indicator.weight || 0
    })

    // 项目平均得分
    const avgProjectScore = projectIndicatorWeight > 0 ? projectScore / projectIndicatorWeight : 0

    // 按项目权重累加到总分
    totalScore += avgProjectScore * (project.weight || 0)
    totalWeight += project.weight || 0
  })

  // 计算最终综合得分
  const finalScore = totalWeight > 0 ? totalScore / totalWeight : 0
  return Math.round(finalScore * 10) / 10
})

// 详细数据表 - 基于评估方案项目结构
const detailedData = computed(() => {
  const data: any[] = []

  schemeProjects.value.forEach(project => {
    project.indicators.forEach(indicator => {
      // 根据指标生成对应的详细数据
      const detailItem = {
        indicator: indicator.name,
        project: project.name,
        value: indicator.value,
        unit: indicator.unit,
        benchmark: getBenchmarkValue(indicator),
        deviation: getDeviationValue(indicator),
        remark: getRemarkText(indicator)
      }
      data.push(detailItem)
    })
  })

  return data
})

// 获取基准值
const getBenchmarkValue = (indicator: IndicatorScore) => {
  const benchmarkMap: Record<string, number> = {
    '单位能耗': 0.75,
    '功率因数': 0.90,
    'COD去除率': 90,
    '氨氮去除率': 85,
    'SS去除率': 90,
    '设备运行率': 95,
    '处理负荷率': 90,
    '污泥含水率': 80
  }
  return benchmarkMap[indicator.name] || 0
}

// 计算偏差值
const getDeviationValue = (indicator: IndicatorScore) => {
  const benchmark = getBenchmarkValue(indicator)
  if (benchmark === 0) return '-'

  const deviation = ((indicator.value - benchmark) / benchmark * 100).toFixed(1)
  return deviation.startsWith('-') ? deviation + '%' : '+' + deviation + '%'
}

// 获取备注文本
const getRemarkText = (indicator: IndicatorScore) => {
  const remarkMap: Record<string, string> = {
    '单位能耗': '能耗控制情况',
    '功率因数': '电力系统效率',
    'COD去除率': '化学需氧量去除效果',
    '氨氮去除率': '氨氮处理效果',
    'SS去除率': '悬浮物去除效果',
    '设备运行率': '设备运行状态',
    '处理负荷率': '处理负荷情况',
    '污泥含水率': '污泥脱水效果'
  }
  return remarkMap[indicator.name] || '运行状态正常'
}

// 趋势分析表格数据
const trendTableData = computed(() => {
  if (!selectedIndicator.value) return []

  const scores = trendData.value[selectedIndicator.value] || []
  return trendPeriods.value.map((period, index) => {
    const currentScore = scores[index] || 0
    const previousScore = index > 0 ? scores[index - 1] : currentScore
    const change = index > 0 ? currentScore - previousScore : 0
    const changeText = change > 0 ? `+${change.toFixed(1)}` : change.toFixed(1)

    let trend = 'stable'
    if (change > 2) trend = 'up'
    else if (change < -2) trend = 'down'

    let analysis = ''
    if (currentScore >= 90) {
      analysis = '表现优秀，继续保持'
    } else if (currentScore >= 80) {
      analysis = '表现良好，可进一步提升'
    } else if (currentScore >= 70) {
      analysis = '达到合格标准，需要关注改进'
    } else {
      analysis = '表现待改进，需要重点关注'
    }

    if (trend === 'up') {
      analysis += '，呈上升趋势'
    } else if (trend === 'down') {
      analysis += '，呈下降趋势，需要分析原因'
    }

    return {
      period,
      score: currentScore,
      change: index > 0 ? changeText : '-',
      trend,
      analysis
    }
  })
})

const summary = computed<Summary>(() => {
  const score = overallScore.value
  const abnormalCount = indicatorScores.value.filter(item =>
    item.status === '偏高' || item.status === '偏低' || item.status === '异常'
  ).length

  let level = '待改进'
  if (score >= 90) level = '优秀'
  else if (score >= 80) level = '良好'
  else if (score >= 70) level = '合格'

  return {
    score,
    change: '+2.3', // 这里可以根据历史数据计算
    abnormalCount,
    level
  }
})

// 所有方案的项目结构数据 - 基于净水厂实际业务，全面丰富
const allSchemeProjects: Record<string, SchemeProject[]> = {
  'scheme_001': [
    {
      id: 'project_001',
      name: '水质安全指标',
      weight: 35,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估净水厂出水水质安全性和稳定性',
      indicators: [
        {
          id: 'ind_001',
          name: '出水浊度',
          unit: 'NTU',
          value: 0.08,
          score: 95,
          weight: 20,
          status: '优秀',
          advice: '出水浊度控制良好，符合国家标准要求',
          description: '净水厂出水浊度，反映水质净化效果',
          formula: '浊度仪检测值',
          threshold: '≤0.3',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        },
        {
          id: 'ind_002',
          name: '余氯含量',
          unit: 'mg/L',
          value: 0.45,
          score: 92,
          weight: 15,
          status: '优秀',
          advice: '',
          description: '出水余氯含量，保证管网输送过程中的消毒效果',
          formula: '余氯检测仪检测值',
          threshold: '0.3-4.0',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        },
        {
          id: 'ind_003',
          name: '出水水质达标率',
          unit: '%',
          value: 99.8,
          score: 98,
          weight: 25,
          status: '优秀',
          advice: '',
          description: '出水水质各项指标达到国家标准的比例',
          formula: '达标检测次数 / 总检测次数 × 100',
          threshold: '≥99%',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        },
        {
          id: 'ind_004',
          name: 'pH值稳定性',
          unit: '无',
          value: 7.2,
          score: 94,
          weight: 15,
          status: '优秀',
          advice: 'pH值控制稳定，在标准范围内',
          description: '出水pH值，反映水质酸碱度控制情况',
          formula: 'pH计检测值',
          threshold: '6.5-8.5',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        },
        {
          id: 'ind_005',
          name: '细菌总数合格率',
          unit: '%',
          value: 100,
          score: 100,
          weight: 25,
          status: '优秀',
          advice: '',
          description: '出水细菌总数检测合格率',
          formula: '细菌总数合格次数 / 总检测次数 × 100',
          threshold: '100%',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        }
      ]
    },
    {
      id: 'project_002',
      name: '生产运营效率',
      weight: 30,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估净水厂生产运营效率和资源利用水平',
      indicators: [
        {
          id: 'ind_006',
          name: '单位产水能耗',
          unit: 'kWh/m³',
          value: 0.28,
          score: 88,
          weight: 25,
          status: '良好',
          advice: '能耗控制在合理范围内，建议进一步优化工艺参数',
          description: '净水厂单位产水量的电能消耗',
          formula: '总电耗 / 产水量',
          threshold: '≤0.35',
          standard: '行业标准',
          projectId: 'project_002'
        },
        {
          id: 'ind_007',
          name: '产水率',
          unit: '%',
          value: 96.5,
          score: 92,
          weight: 20,
          status: '优秀',
          advice: '',
          description: '净水厂产水量与原水取水量的比值',
          formula: '产水量 / 取水量 × 100',
          threshold: '≥95%',
          standard: '行业标准',
          projectId: 'project_002'
        },
        {
          id: 'ind_008',
          name: '药剂单耗',
          unit: 'g/m³',
          value: 18.5,
          score: 85,
          weight: 20,
          status: '良好',
          advice: '药剂消耗合理，建议根据原水水质变化优化投药量',
          description: '净水处理过程中单位产水量的药剂消耗',
          formula: '药剂总用量 / 产水量',
          threshold: '≤20',
          standard: '行业标准',
          projectId: 'project_002'
        },
        {
          id: 'ind_009',
          name: '处理负荷率',
          unit: '%',
          value: 85.2,
          score: 90,
          weight: 20,
          status: '优秀',
          advice: '负荷率适中，运行稳定高效',
          description: '实际处理量与设计处理量的比值',
          formula: '实际处理量 / 设计处理量 × 100',
          threshold: '80-90%',
          standard: '设计标准',
          projectId: 'project_002'
        },
        {
          id: 'ind_010',
          name: '原水利用率',
          unit: '%',
          value: 98.5,
          score: 95,
          weight: 15,
          status: '优秀',
          advice: '',
          description: '原水的有效利用程度',
          formula: '有效利用原水量 / 总取水量 × 100',
          threshold: '≥95%',
          standard: '环保要求',
          projectId: 'project_002'
        }
      ]
    },
    {
      id: 'project_003',
      name: '设备运行管理',
      weight: 20,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估净水厂设备运行状态和维护管理水平',
      indicators: [
        {
          id: 'ind_011',
          name: '设备完好率',
          unit: '%',
          value: 98.2,
          score: 95,
          weight: 30,
          status: '优秀',
          advice: '',
          description: '净水厂主要设备完好运行的比例',
          formula: '完好设备数 / 总设备数 × 100',
          threshold: '≥95%',
          standard: '设备管理标准',
          projectId: 'project_003'
        },
        {
          id: 'ind_012',
          name: '供水保证率',
          unit: '%',
          value: 99.5,
          score: 98,
          weight: 35,
          status: '优秀',
          advice: '',
          description: '净水厂正常供水时间占总时间的比例',
          formula: '正常供水时间 / 总时间 × 100',
          threshold: '≥99%',
          standard: '供水服务标准',
          projectId: 'project_003'
        },
        {
          id: 'ind_013',
          name: '故障响应时间',
          unit: '分钟',
          value: 8.5,
          score: 92,
          weight: 20,
          status: '优秀',
          advice: '故障响应及时，维护团队专业高效',
          description: '设备故障平均响应处理时间',
          formula: '故障处理总时间 / 故障次数',
          threshold: '≤15分钟',
          standard: '运维标准',
          projectId: 'project_003'
        },
        {
          id: 'ind_014',
          name: '预防性维护完成率',
          unit: '%',
          value: 96.8,
          score: 94,
          weight: 15,
          status: '优秀',
          advice: '',
          description: '按计划完成预防性维护的比例',
          formula: '完成维护项目数 / 计划维护项目数 × 100',
          threshold: '≥95%',
          standard: '维护管理标准',
          projectId: 'project_003'
        }
      ]
    },
    {
      id: 'project_004',
      name: '安全环保管理',
      weight: 15,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估净水厂安全生产和环境保护管理水平',
      indicators: [
        {
          id: 'ind_015',
          name: '安全事故发生率',
          unit: '次/年',
          value: 0,
          score: 100,
          weight: 40,
          status: '优秀',
          advice: '',
          description: '年度安全事故发生次数',
          formula: '年度安全事故次数',
          threshold: '0次',
          standard: '安全生产标准',
          projectId: 'project_004'
        },
        {
          id: 'ind_016',
          name: '环保达标率',
          unit: '%',
          value: 100,
          score: 100,
          weight: 30,
          status: '优秀',
          advice: '',
          description: '环保指标达标情况',
          formula: '达标项目数 / 总检查项目数 × 100',
          threshold: '100%',
          standard: '环保法规',
          projectId: 'project_004'
        },
        {
          id: 'ind_017',
          name: '应急演练完成率',
          unit: '%',
          value: 100,
          score: 100,
          weight: 20,
          status: '优秀',
          advice: '',
          description: '按计划完成应急演练的比例',
          formula: '完成演练次数 / 计划演练次数 × 100',
          threshold: '100%',
          standard: '应急管理标准',
          projectId: 'project_004'
        },
        {
          id: 'ind_018',
          name: '员工安全培训覆盖率',
          unit: '%',
          value: 100,
          score: 100,
          weight: 10,
          status: '优秀',
          advice: '',
          description: '员工安全培训参与覆盖率',
          formula: '参训员工数 / 总员工数 × 100',
          threshold: '100%',
          standard: '培训管理标准',
          projectId: 'project_004'
        }
      ]
    }
  ],
  'scheme_002': [
    {
      id: 'project_001',
      name: '水质安全指标',
      weight: 60,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估净水站出水水质安全性',
      indicators: [
        {
          id: 'ind_001',
          name: '出水浊度',
          unit: 'NTU',
          value: 0.12,
          score: 90,
          weight: 40,
          status: '优秀',
          advice: '出水浊度控制良好',
          description: '净水站出水浊度',
          formula: '浊度仪检测值',
          threshold: '≤0.3',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        },
        {
          id: 'ind_002',
          name: '出水水质达标率',
          unit: '%',
          value: 99.5,
          score: 95,
          weight: 60,
          status: '优秀',
          advice: '',
          description: '出水水质达标比例',
          formula: '达标检测次数 / 总检测次数 × 100',
          threshold: '≥99%',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        }
      ]
    },
    {
      id: 'project_002',
      name: '运行管理',
      weight: 40,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估净水站运行效率和管理水平',
      indicators: [
        {
          id: 'ind_003',
          name: '设备完好率',
          unit: '%',
          value: 96.8,
          score: 92,
          weight: 50,
          status: '优秀',
          advice: '',
          description: '净水站设备完好运行比例',
          formula: '完好设备数 / 总设备数 × 100',
          threshold: '≥95%',
          standard: '设备管理标准',
          projectId: 'project_002'
        },
        {
          id: 'ind_004',
          name: '供水保证率',
          unit: '%',
          value: 98.8,
          score: 95,
          weight: 50,
          status: '优秀',
          advice: '',
          description: '正常供水时间占比',
          formula: '正常供水时间 / 总时间 × 100',
          threshold: '≥98%',
          standard: '供水服务标准',
          projectId: 'project_002'
        }
      ]
    }
  ],
  'scheme_003': [
    {
      id: 'project_001',
      name: '综合运营指标',
      weight: 100,
      fullScore: 100,
      scoreType: '加权平均',
      description: '评估区域净水中心综合运营水平',
      indicators: [
        {
          id: 'ind_001',
          name: '出水水质达标率',
          unit: '%',
          value: 99.9,
          score: 98,
          weight: 50,
          status: '优秀',
          advice: '',
          description: '出水水质达标比例',
          formula: '达标检测次数 / 总检测次数 × 100',
          threshold: '≥99%',
          standard: 'GB5749-2022',
          projectId: 'project_001'
        },
        {
          id: 'ind_002',
          name: '供水保证率',
          unit: '%',
          value: 99.8,
          score: 98,
          weight: 50,
          status: '优秀',
          advice: '',
          description: '正常供水时间占比',
          formula: '正常供水时间 / 总时间 × 100',
          threshold: '≥99%',
          standard: '供水服务标准',
          projectId: 'project_001'
        }
      ]
    }
  ]
}

// 当前方案的项目数据（计算属性）
const schemeProjects = computed(() => {
  return allSchemeProjects[filters.value.schemeId] || allSchemeProjects['scheme_001']
})

// 扁平化的指标数据（用于兼容现有图表）
const indicatorScores = computed(() => {
  const allIndicators: IndicatorScore[] = []
  schemeProjects.value.forEach(project => {
    allIndicators.push(...project.indicators)
  })
  return allIndicators
})

const trendPeriods = ref(['2025-Q1', '2025-Q2', '2025-Q3'])

const trendData = ref({
  '单位能耗': [70, 62, 65],
  '出水水质': [88, 92, 90],
  '处理效率': [85, 88, 87],
  '设备完好率': [92, 95, 94],
  '污泥含水率': [65, 58, 60]
})

const report = ref<Report>({
  conclusion: '东南水厂在2025年Q2评估周期内整体运行良好，综合得分89.4分，达到良好等级。但存在单位能耗和污泥含水率偏高的问题，需要重点关注和改进。',
  analysis: [
    '单位能耗得分偏低（62分），存在设备能效不达标问题，主要原因是鼓风机运行策略不够优化。',
    '污泥含水率偏高（82.1%），影响污泥处置效果，需要优化脱水工艺参数。',
    '出水水质和设备完好率表现优秀，说明工艺控制和设备维护工作到位。'
  ],
  suggestions: [
    '优化鼓风机运行参数，建立基于溶解氧的智能控制策略，预计可降低能耗15-20%。',
    '调整污泥脱水工艺，增加絮凝剂投加量，优化脱水机运行参数。',
    '建立能耗监控预警机制，实时监控关键设备能耗指标。',
    '加强运行人员培训，提高精细化操作水平。'
  ]
})

// 工具方法
const getScoreType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return 'warning'
  if (score >= 70) return ''
  return 'danger'
}

const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    '优秀': 'success',
    '正常': '',
    '偏高': 'warning',
    '偏低': 'warning',
    '异常': 'danger'
  }
  return typeMap[status] || ''
}

const getChangeClass = (change: string) => {
  if (change.startsWith('+')) return 'text-green-500'
  if (change.startsWith('-')) return 'text-red-500'
  return 'text-gray-500'
}

const getLevelType = (level: string) => {
  const typeMap: Record<string, string> = {
    '优秀': 'success',
    '良好': '',
    '需改进': 'warning',
    '较差': 'danger'
  }
  return typeMap[level] || ''
}

const getTrendScore = (indicator: string, periodIndex: number) => {
  return trendData.value[indicator]?.[periodIndex] || 0
}

// 图表坐标计算方法
const getXPosition = (index: number) => {
  const startX = 80
  const endX = 720
  const spacing = (endX - startX) / (trendPeriods.value.length - 1)
  return startX + index * spacing
}

const getYPosition = (score: number) => {
  const startY = 320
  const endY = 50
  const range = startY - endY
  return startY - (score / 100) * range
}

const getTrendLinePoints = () => {
  if (!selectedIndicator.value) return ''

  const points = trendPeriods.value.map((period, index) => {
    const x = getXPosition(index)
    const y = getYPosition(getTrendScore(selectedIndicator.value, index))
    return `${x},${y}`
  })

  return points.join(' ')
}

// 工具提示方法
const showTooltip = (event: MouseEvent, period: string, score: number) => {
  const tooltip = document.createElement('div')
  tooltip.className = 'chart-tooltip'
  tooltip.innerHTML = `
    <div><strong>${period}</strong></div>
    <div>${selectedIndicator.value}: ${score}分</div>
  `
  tooltip.style.cssText = `
    position: absolute;
    background: rgba(0,0,0,0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    pointer-events: none;
    z-index: 1000;
    left: ${event.pageX + 10}px;
    top: ${event.pageY - 10}px;
  `
  document.body.appendChild(tooltip)
}

const hideTooltip = () => {
  const tooltips = document.querySelectorAll('.chart-tooltip')
  tooltips.forEach(tooltip => tooltip.remove())
}

// 表格相关方法
const getScoreTagType = (score: number) => {
  if (score >= 90) return 'success'
  if (score >= 80) return ''
  if (score >= 70) return 'warning'
  return 'danger'
}

const getTrendColor = (trend: string) => {
  switch (trend) {
    case 'up': return '#67c23a'
    case 'down': return '#f56c6c'
    case 'stable': return '#909399'
    default: return '#909399'
  }
}

const getTrendIcon = (trend: string) => {
  switch (trend) {
    case 'up': return ArrowUp
    case 'down': return ArrowDown
    case 'stable': return Minus
    default: return Minus
  }
}

// 获取项目的指标列表
const getProjectIndicators = (projectId: string) => {
  const project = schemeProjects.value.find(p => p.id === projectId)
  return project?.indicators || []
}

// 计算指标分值（同一项目中的指标分值一致）
const getIndicatorScoreValue = (project: SchemeProject, indicator: IndicatorScore) => {
  return project.fullScore || 100
}

// 获取指标阈值
const getIndicatorThreshold = (indicator: IndicatorScore) => {
  // 优先使用指标自身的threshold字段
  if (indicator.threshold) {
    return indicator.threshold
  }

  // 如果没有，则根据指标名称返回对应的一级阈值
  const thresholdMap: Record<string, string> = {
    '单位能耗': '≤0.75',
    '功率因数': '≥0.90',
    'COD去除率': '≥90%',
    '氨氮去除率': '≥85%',
    'SS去除率': '≥90%',
    '设备运行率': '≥95%',
    '处理负荷率': '85-95%',
    '污泥含水率': '≤80%'
  }
  return thresholdMap[indicator.name] || '待设定'
}

// 获取指标评分规则
const getIndicatorScoringRule = (indicator: IndicatorScore) => {
  // 根据指标名称返回对应的评分规则
  const scoringRuleMap: Record<string, string> = {
    '单位能耗': '≤0.5得100分，0.5-0.75线性递减至60分，>0.75得0分',
    '功率因数': '≥0.95得100分，0.90-0.95线性递减至80分，<0.90得0分',
    'COD去除率': '≥95%得100分，90-95%线性递减至80分，<90%得0分',
    '氨氮去除率': '≥90%得100分，85-90%线性递减至80分，<85%得0分',
    'SS去除率': '≥95%得100分，90-95%线性递减至80分，<90%得0分',
    '设备运行率': '≥98%得100分，95-98%线性递减至80分，<95%得0分',
    '处理负荷率': '85-95%得100分，80-85%或95-100%得80分，其他得0分',
    '污泥含水率': '≤75%得100分，75-80%线性递减至80分，>80%得0分'
  }
  return scoringRuleMap[indicator.name] || '按标准评分规则执行'
}

// 新增图表相关方法
const getIndicatorCurrentScore = (indicator: string) => {
  const indicatorItem = indicatorScores.value.find(item => item.name === indicator)
  return indicatorItem?.score || 0
}

// 雷达图相关方法
const getRadarX = (index: number, radius: number) => {
  const angle = (index * 2 * Math.PI) / currentIndicators.value.length - Math.PI / 2
  return Math.cos(angle) * radius
}

const getRadarY = (index: number, radius: number) => {
  const angle = (index * 2 * Math.PI) / currentIndicators.value.length - Math.PI / 2
  return Math.sin(angle) * radius
}

const getRadarRadius = (score: number) => {
  // 将得分(0-100)转换为雷达图半径(0-150)
  return (score / 100) * 150
}

const getRadarPolygonPoints = () => {
  const points = currentIndicators.value.map((indicator, index) => {
    const score = getIndicatorCurrentScore(indicator)
    const radius = getRadarRadius(score)
    const x = getRadarX(index, radius)
    const y = getRadarY(index, radius)
    return `${x},${y}`
  })
  return points.join(' ')
}

// 柱状图相关方法
const getBarXPosition = (index: number) => {
  const startX = 80
  const spacing = 20
  return startX + index * (barWidth.value + spacing)
}

const getBarColor = (index: number) => {
  const colors = ['#409eff', '#67c23a', '#e6a23c', '#f56c6c', '#909399']
  return colors[index % colors.length]
}

// 获取图表标题
const getChartTitle = () => {
  switch (chartType.value) {
    case 'radar':
      return '指标综合表现雷达图'
    case 'bar':
      return '各指标得分对比'
    default:
      return '指标综合分析'
  }
}

// 事件处理方法
const handleSchemeChange = (schemeId: string) => {
  // 获取新方案的信息
  const newScheme = schemes.value.find(scheme => scheme.id === schemeId)

  if (newScheme && newScheme.stations.length > 0) {
    // 切换到新方案的第一个厂站
    currentStationId.value = newScheme.stations[0].id

    // 重置周期选择
    filters.value.period = '2025年3月'

    // 重新初始化可编辑内容
    initEditableContent()

    ElMessage.success(`已切换到方案：${newScheme.name}，当前厂站：${newScheme.stations[0].name}`)
  } else {
    ElMessage.warning('方案切换失败，请重试')
  }
}

const handleStationChange = (stationId: string) => {
  // 厂站信息会通过计算属性自动更新
  const station = availableStations.value.find(s => s.id === stationId)

  ElMessage.info(`已切换到厂站: ${station?.name}`)
}

const handlePeriodChange = (period: string) => {
  ElMessage.info(`周期已切换到: ${period}`)
}

const handleIndicatorChange = (indicator: string) => {
  ElMessage.info(`已选择指标: ${indicator}`)
}

const handleExportPDF = () => {
  reportDialogVisible.value = true
}



// 报告相关方法
const getScoreLevel = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '合格'
  return '待改进'
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case '正常': return 'success'
    case '关注': return 'warning'
    case '异常': return 'danger'
    default: return 'info'
  }
}

const getEvaluationConclusion = () => {
  const score = overallScore.value
  if (score >= 90) {
    return `${stationInfo.value.name}在${period.value}评估周期内表现优秀，各项指标均达到预期目标，运行状态良好，建议继续保持当前管理水平。`
  } else if (score >= 80) {
    return `${stationInfo.value.name}在${period.value}评估周期内表现良好，大部分指标达到预期，个别指标有提升空间，建议针对性优化。`
  } else if (score >= 70) {
    return `${stationInfo.value.name}在${period.value}评估周期内达到合格标准，但存在一些需要改进的方面，建议制定专项提升计划。`
  } else {
    return `${stationInfo.value.name}在${period.value}评估周期内表现待改进，多项指标未达预期，需要重点关注并采取有效措施。`
  }
}

const getIndicatorAnalysis = (indicator: any) => {
  const score = indicator.score
  if (score >= 90) {
    return `${indicator.name}表现优秀，得分${score}分，超出预期目标，运行状态良好，建议继续保持。`
  } else if (score >= 80) {
    return `${indicator.name}表现良好，得分${score}分，基本达到预期，可进一步优化提升。`
  } else if (score >= 70) {
    return `${indicator.name}达到合格标准，得分${score}分，存在改进空间，建议重点关注。`
  } else {
    return `${indicator.name}表现待改进，得分${score}分，需要制定专项提升措施。`
  }
}

// 报告雷达图相关方法
const getReportRadarX = (index: number, radius: number) => {
  const angle = (index * 2 * Math.PI) / currentIndicators.value.length - Math.PI / 2
  return Math.cos(angle) * radius
}

const getReportRadarY = (index: number, radius: number) => {
  const angle = (index * 2 * Math.PI) / currentIndicators.value.length - Math.PI / 2
  return Math.sin(angle) * radius
}

const getReportRadarRadius = (score: number) => {
  return (score / 100) * 125
}

const getReportRadarPolygonPoints = () => {
  const points = currentIndicators.value.map((indicator, index) => {
    const score = getIndicatorCurrentScore(indicator)
    const radius = getReportRadarRadius(score)
    const x = getReportRadarX(index, radius)
    const y = getReportRadarY(index, radius)
    return `${x},${y}`
  })
  return points.join(' ')
}

// 弹窗控制方法
const handleCloseReport = () => {
  reportDialogVisible.value = false
}

const handlePrintReport = () => {
  window.print()
}

const handleDownloadReport = async () => {
  try {
    // 显示加载提示
    const loading = ElLoading.service({
      lock: true,
      text: '正在生成PDF，请稍候...',
      background: 'rgba(0, 0, 0, 0.7)'
    })

    // 获取报告容器元素
    const reportElement = reportRef.value
    if (!reportElement) {
      ElMessage.error('无法找到报告内容')
      loading.close()
      return
    }

    // 临时添加PDF导出样式类
    const originalClass = reportElement.className
    reportElement.className += ' pdf-export'

    // 等待样式应用
    await new Promise(resolve => setTimeout(resolve, 200))

    // 创建PDF文档
    const pdf = new jsPDF('p', 'mm', 'a4')
    const pageWidth = 210 // A4纸宽度(mm)
    const pageHeight = 297 // A4纸高度(mm)
    const margin = 10 // 页边距
    const contentWidth = pageWidth - 2 * margin
    const contentHeight = pageHeight - 2 * margin

    // 获取所有报告章节
    const sections = reportElement.querySelectorAll('.report-section')
    let currentY = margin
    let isFirstPage = true

    for (let i = 0; i < sections.length; i++) {
      const section = sections[i] as HTMLElement

      // 为每个章节生成canvas
      const sectionCanvas = await html2canvas(section, {
        scale: 2,
        useCORS: true,
        allowTaint: true,
        backgroundColor: '#ffffff',
        logging: false,
        removeContainer: true,
        width: section.scrollWidth,
        height: section.scrollHeight
      })

      // 计算章节在PDF中的尺寸
      const sectionImgWidth = contentWidth
      const sectionImgHeight = (sectionCanvas.height * sectionImgWidth) / sectionCanvas.width

      // 检查是否需要新页面
      if (!isFirstPage && (currentY + sectionImgHeight > pageHeight - margin)) {
        pdf.addPage()
        currentY = margin
      }

      // 如果章节太高，需要分页处理
      if (sectionImgHeight > contentHeight) {
        // 章节需要分页
        const imgData = sectionCanvas.toDataURL('image/png')
        let remainingHeight = sectionImgHeight
        let sourceY = 0

        while (remainingHeight > 0) {
          const availableHeight = pageHeight - currentY - margin
          const heightToUse = Math.min(remainingHeight, availableHeight)

          // 计算源图片的裁剪区域
          const sourceHeight = (heightToUse * sectionCanvas.height) / sectionImgHeight

          // 创建临时canvas来裁剪图片
          const tempCanvas = document.createElement('canvas')
          const tempCtx = tempCanvas.getContext('2d')
          tempCanvas.width = sectionCanvas.width
          tempCanvas.height = sourceHeight

          const img = new Image()
          await new Promise((resolve) => {
            img.onload = () => {
              tempCtx?.drawImage(img, 0, sourceY, sectionCanvas.width, sourceHeight, 0, 0, sectionCanvas.width, sourceHeight)
              resolve(null)
            }
            img.src = imgData
          })

          // 添加裁剪后的图片到PDF
          pdf.addImage(tempCanvas.toDataURL('image/png'), 'PNG', margin, currentY, sectionImgWidth, heightToUse)

          remainingHeight -= heightToUse
          sourceY += sourceHeight

          if (remainingHeight > 0) {
            pdf.addPage()
            currentY = margin
          } else {
            currentY += heightToUse
          }
        }
      } else {
        // 章节可以完整放在当前页或新页
        pdf.addImage(sectionCanvas.toDataURL('image/png'), 'PNG', margin, currentY, sectionImgWidth, sectionImgHeight)
        currentY += sectionImgHeight + 5 // 添加一些间距
      }

      isFirstPage = false
    }

    // 恢复原始样式类
    reportElement.className = originalClass

    // 生成文件名
    const fileName = `${stationInfo.value.name}_评估报告_${period.value.replace(/[\/\\:*?"<>|]/g, '_')}.pdf`

    // 下载PDF
    pdf.save(fileName)

    loading.close()
    ElMessage.success('PDF导出成功！')
  } catch (error) {
    console.error('PDF导出失败:', error)
    ElMessage.error('PDF导出失败，请重试')

    // 确保在出错时也恢复样式
    const reportElement = reportRef.value
    if (reportElement && reportElement.className.includes('pdf-export')) {
      reportElement.className = reportElement.className.replace(' pdf-export', '')
    }
  }
}

// 偏差样式类
const getDeviationClass = (deviation: string) => {
  if (deviation.startsWith('+')) {
    const value = parseFloat(deviation.substring(1))
    return value > 5 ? 'text-green-600 font-semibold' : 'text-green-500'
  } else if (deviation.startsWith('-')) {
    const value = parseFloat(deviation.substring(1))
    return value > 5 ? 'text-red-600 font-semibold' : 'text-red-500'
  }
  return 'text-gray-500'
}

// 获取报告样式
const getReportStyle = () => {
  return {
    fontSize: `${reportConfig.value.fontSize}px`,
    padding: `${reportConfig.value.margin}px`,
    '--primary-color': reportConfig.value.primaryColor
  }
}

// 初始化可编辑内容
const initEditableContent = () => {
  // 初始化分析说明
  schemeProjects.value.forEach(project => {
    project.indicators.forEach(indicator => {
      if (!editableAnalysis.value[indicator.id]) {
        editableAnalysis.value[indicator.id] = getIndicatorAnalysis(indicator)
      }
      if (!editableSuggestions.value[indicator.id] && indicator.advice) {
        editableSuggestions.value[indicator.id] = indicator.advice
      }
    })
  })

  // 初始化综合评价
  if (!editableConclusion.value) {
    editableConclusion.value = getEvaluationConclusion()
  }
}

// 初始化方法
const initPage = () => {
  // 模拟根据URL参数加载数据
  console.log('页面初始化，参数:', urlParams)

  // 初始化可编辑内容
  initEditableContent()

  // 可以在这里添加loading效果
  setTimeout(() => {
    ElMessage.success('页面数据加载完成')
  }, 500)
}

// 生命周期
onMounted(() => {
  initPage()
})
</script>

<style scoped lang="scss">
.station-view {
  background-color: #f5f7fa;
  min-height: 100vh;

  .mb-4 {
    margin-bottom: 16px;
  }

  .font-bold {
    font-weight: bold;
  }

  .flex {
    display: flex;
  }

  .justify-between {
    justify-content: space-between;
  }

  .items-center {
    align-items: center;
  }

  // 文本颜色类
  .text-green-500 {
    color: #10b981;
  }

  .text-red-500 {
    color: #ef4444;
  }

  .text-gray-500 {
    color: #6b7280;
  }

  // 综合得分总览样式
  .summary-container {
    display: flex;
    align-items: center;
    gap: 40px;

    .score-main {
      text-align: center;

      .score-value {
        font-size: 48px;
        font-weight: bold;
        color: #409eff;
        line-height: 1;
      }

      .score-label {
        font-size: 16px;
        color: #6b7280;
        margin-top: 8px;
      }
    }

    .score-details {
      flex: 1;
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;

      .detail-item {
        text-align: center;
        padding: 16px;
        background-color: #f9fafb;
        border-radius: 8px;

        .detail-label {
          display: block;
          font-size: 14px;
          color: #6b7280;
          margin-bottom: 8px;
        }

        .detail-value {
          font-size: 18px;
          font-weight: bold;

          &.abnormal {
            color: #f56565;
          }
        }
      }
    }
  }

  // 表格样式
  .advice-text {
    color: #f56565;
    font-size: 12px;
  }

  .no-advice {
    color: #9ca3af;
  }

  // 趋势图样式
  .trend-chart {
    .chart-placeholder {
      height: 300px;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      border: 1px solid #e5e7eb;
      border-radius: 6px;
      background-color: #f9fafb;
      font-size: 18px;
      color: #6b7280;

      .trend-data {
        margin-top: 20px;

        .trend-line {
          font-size: 14px;

          .trend-point {
            margin-right: 15px;
            padding: 4px 8px;
            background-color: #e0e7ff;
            border-radius: 4px;
            display: inline-block;
            margin-bottom: 8px;
          }
        }
      }
    }
  }

  // 报告内容样式
  .report-content {
    .report-section {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }

      h3 {
        font-size: 18px;
        font-weight: bold;
        color: #2c3e50;
        margin-bottom: 12px;
        border-left: 4px solid #409eff;
        padding-left: 12px;
      }

      p {
        line-height: 1.6;
        color: #4a5568;
        margin-bottom: 12px;
      }

      ul,
      ol {
        padding-left: 20px;

        li {
          line-height: 1.6;
          color: #4a5568;
          margin-bottom: 8px;
        }
      }
    }
  }

  // 筛选头部样式
  .filter-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .el-form {
      margin: 0;
    }

    .filter-actions {
      .el-button {
        display: flex;
        align-items: center;
        gap: 6px;
      }
    }

    // 响应式设计
    @media (max-width: 768px) {
      flex-direction: column;
      gap: 16px;
      align-items: stretch;

      .filter-actions {
        display: flex;
        justify-content: center;
      }
    }
  }

  // 指标分析图表样式
  .analysis-chart {
    .chart-controls {
      .el-radio-group {
        .el-radio-button {
          margin-right: 0;
        }
      }
    }

    .chart-placeholder {
      height: 400px;
      display: flex;
      align-items: center;
      justify-content: center;
      background-color: #fafafa;
      border: 2px dashed #d0d0d0;
      border-radius: 8px;

      .placeholder-content {
        text-align: center;
        color: #999;

        p {
          margin-top: 12px;
          font-size: 16px;
        }
      }
    }

    // 雷达图样式
    .radar-chart {
      .radar-svg {
        width: 100%;
        height: 400px;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        background-color: #fafafa;

        .radar-point {
          cursor: pointer;
          transition: r 0.2s ease;

          &:hover {
            r: 6;
          }
        }
      }
    }

    // 柱状图样式
    .bar-chart {
      .bar-svg {
        width: 100%;
        height: 400px;
        border: 1px solid #e5e7eb;
        border-radius: 6px;
        background-color: #fafafa;

        .bar-rect {
          cursor: pointer;
          transition: opacity 0.2s ease;

          &:hover {
            opacity: 0.8;
          }
        }
      }
    }
  }

  // 评估报告弹窗样式
  .report-container {
    max-height: 70vh;
    overflow-y: auto;
    padding: 20px;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;

    // PDF导出优化样式
    &.pdf-export {
      max-height: none;
      overflow: visible;
      background: white;
      padding: 0;

      // 确保所有内容在PDF中可见
      * {
        -webkit-print-color-adjust: exact !important;
        color-adjust: exact !important;
        print-color-adjust: exact !important;
      }

      // 报告章节样式优化
      .report-section {
        page-break-inside: avoid;
        margin-bottom: 20px;
        padding: 15px;
        background: white;
        border: none;
        box-shadow: none;

        h2 {
          margin-top: 0;
          margin-bottom: 15px;
          font-size: 18px;
          color: #1f2937;
          border-left: 4px solid #3b82f6;
          padding-left: 12px;
        }

        h3 {
          margin-top: 15px;
          margin-bottom: 10px;
          font-size: 16px;
          color: #374151;
        }
      }

      // 优化表格在PDF中的显示
      .el-table {
        page-break-inside: avoid;
        margin-bottom: 15px;

        .el-table__header,
        .el-table__body {
          page-break-inside: avoid;
        }

        .el-table__header-wrapper,
        .el-table__body-wrapper {
          overflow: visible !important;
        }

        // 确保表格边框在PDF中显示
        th,
        td {
          border: 1px solid #e5e7eb !important;
          padding: 8px !important;
          font-size: 12px !important;
        }
      }

      // 优化图表在PDF中的显示
      .chart-container,
      .radar-chart,
      .bar-chart {
        page-break-inside: avoid;
        margin-bottom: 15px;

        svg {
          background: white;
        }
      }

      // 优化评分卡片显示
      .score-summary {
        page-break-inside: avoid;
        margin-bottom: 15px;
        padding: 15px;
        background: #f8fafc;
        border-radius: 6px;
        border: 1px solid #e5e7eb;
      }

      // 优化可编辑区域在PDF中的显示
      .editable-conclusion,
      .editable-analysis,
      .editable-suggestion {
        .el-textarea {
          .el-textarea__inner {
            border: 1px solid #d1d5db !important;
            background: #f9fafb !important;
            color: #374151 !important;
            font-size: 13px !important;
            line-height: 1.5 !important;
          }
        }
      }

      // 隐藏不必要的滚动条
      ::-webkit-scrollbar {
        display: none;
      }
    }

    .report-header {
      text-align: center;
      margin-bottom: 30px;
      border-bottom: 2px solid #e5e7eb;
      padding-bottom: 20px;

      .report-title {
        font-size: 24px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 15px;
      }

      .report-meta {
        color: #6b7280;
        font-size: 14px;

        p {
          margin: 5px 0;
        }
      }
    }

    .report-section {
      margin-bottom: 30px;

      h2 {
        font-size: 18px;
        font-weight: bold;
        color: #1f2937;
        margin-bottom: 15px;
        border-left: 4px solid #3b82f6;
        padding-left: 12px;
      }

      h3 {
        font-size: 16px;
        font-weight: 600;
        color: #374151;
        margin: 20px 0 10px 0;
      }

      h4 {
        font-size: 14px;
        font-weight: 600;
        color: #4b5563;
        margin: 15px 0 8px 0;
      }

      .section-content {
        padding-left: 16px;

        p {
          line-height: 1.6;
          color: #4b5563;
          margin-bottom: 10px;
        }

        .basic-info {
          display: grid;
          grid-template-columns: repeat(2, 1fr);
          gap: 15px;
          margin-top: 15px;

          .info-item {
            display: flex;
            align-items: center;

            .label {
              font-weight: 600;
              color: #374151;
              min-width: 80px;
            }

            .value {
              color: #1f2937;
            }
          }
        }

        .score-summary {
          display: flex;
          align-items: center;
          gap: 20px;
          margin-bottom: 20px;
          padding: 20px;
          background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
          border-radius: 8px;

          .overall-score {
            display: flex;
            align-items: baseline;
            gap: 8px;

            .score-label {
              font-size: 16px;
              color: #374151;
            }

            .score-value {
              font-size: 36px;
              font-weight: bold;
              color: #1f2937;
            }

            .score-unit {
              font-size: 16px;
              color: #6b7280;
            }
          }
        }

        .conclusion {
          background: #f8fafc;
          padding: 15px;
          border-radius: 6px;
          border-left: 4px solid #3b82f6;
          font-style: italic;
        }

        .editable-conclusion {
          margin-top: 20px;

          label {
            display: block;
            margin-bottom: 8px;
            font-size: 14px;
            font-weight: 600;
            color: #374151;
          }

          .conclusion-input {
            width: 100%;

            :deep(.el-textarea__inner) {
              font-size: 14px;
              line-height: 1.6;
              background: #f8fafc;
              border: 2px solid #e5e7eb;
              border-radius: 6px;
              padding: 15px;

              &:focus {
                border-color: #3b82f6;
                background: #ffffff;
              }
            }
          }
        }

        .chart-section {
          margin: 20px 0;

          .chart-container {
            display: flex;
            justify-content: center;
            margin: 15px 0;

            .report-radar-svg {
              border: 1px solid #e5e7eb;
              border-radius: 6px;
              background-color: #fafafa;
            }
          }
        }

        .analysis-text {
          .project-analysis {
            margin-bottom: 25px;

            h4 {
              font-size: 16px;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 15px;
              padding-bottom: 8px;
              border-bottom: 2px solid #e5e7eb;
            }
          }

          .indicator-analysis {
            margin-bottom: 20px;
            padding: 15px;
            background: #f9fafb;
            border-radius: 8px;
            border-left: 4px solid #10b981;

            h5 {
              margin-top: 0;
              margin-bottom: 8px;
              color: #065f46;
              font-size: 14px;
              font-weight: 600;
            }

            p {
              margin: 8px 0;
              font-size: 13px;
              color: #374151;
            }

            .editable-analysis,
            .editable-suggestion {
              margin-top: 12px;

              label {
                display: block;
                margin-bottom: 6px;
                font-size: 13px;
                font-weight: 600;
                color: #374151;
              }

              .analysis-input,
              .suggestion-input {
                width: 100%;

                :deep(.el-textarea__inner) {
                  font-size: 12px;
                  line-height: 1.4;
                }
              }
            }

            .editable-suggestion {
              border-top: 1px solid #e5e7eb;
              padding-top: 12px;
              margin-top: 15px;
            }
          }
        }

        .project-group {
          margin-bottom: 30px;

          .project-title {
            font-size: 16px;
            font-weight: bold;
            color: #1f2937;
            margin-bottom: 15px;
            padding: 12px 16px;
            background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
            border-radius: 8px;
            border-left: 4px solid #3b82f6;

            .el-tag {
              margin-left: 10px;
            }
          }

          .indicator-table {
            margin-top: 10px;

            .indicator-score {
              font-weight: 600;
              color: #059669;
            }
          }
        }

        .indicator-table,
        .data-table {
          margin-top: 15px;

          .el-table {
            font-size: 12px;
          }

          .weight-text {
            font-weight: 600;
            color: #409eff;
          }

          .threshold-text {
            font-weight: 500;
            color: #67c23a;
          }

          .actual-value {
            font-weight: 600;
            color: #303133;
          }

          .scoring-rule {
            font-size: 11px;
            line-height: 1.4;
            color: #606266;
          }
        }
      }
    }

    .report-footer {
      margin-top: 40px;
      padding-top: 20px;
      border-top: 1px solid #e5e7eb;
      text-align: center;

      .footer-text {
        color: #6b7280;
        font-size: 12px;
        margin: 5px 0;
      }
    }

    // 自定义滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: #f1f1f1;
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: #c1c1c1;
      border-radius: 3px;

      &:hover {
        background: #a8a8a8;
      }
    }
  }



  // 报告样式变量应用
  .report-container {
    .report-title {
      color: var(--primary-color, #1f2937);
    }

    .report-subtitle {
      font-size: 18px;
      color: var(--primary-color, #374151);
      margin-bottom: 10px;
      text-align: center;
    }

    .report-section h2 {
      border-left-color: var(--primary-color, #3b82f6);
    }
  }

  // 打印样式
  @media print {
    .report-container {
      max-height: none;
      overflow: visible;
      padding: 0;

      .report-section {
        page-break-inside: avoid;
      }

      .chart-container {
        page-break-inside: avoid;
      }
    }
  }
}
</style>
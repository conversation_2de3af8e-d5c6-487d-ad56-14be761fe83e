<template>
  <Dialog v-model="dialogVisible" title="检测数据详情">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
      <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
      <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
      <el-descriptions-item label="采样日期">{{ formData.samplingDate }}</el-descriptions-item>
      <el-descriptions-item label="检测值">
        <span :class="{ 'text-danger': formData.status === 'exceed', 'text-warning': formData.status === 'warning' }">
          {{ formData.testValue }} {{ formData.unit }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag :type="formData.status === 'normal' ? 'success' : formData.status === 'warning' ? 'warning' : 'danger'">
          {{ formData.status === 'normal' ? '正常' : formData.status === 'warning' ? '警告' : '超标' }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="检测日期">{{ formData.testDate }}</el-descriptions-item>
      <el-descriptions-item label="检测人">{{ formData.tester }}</el-descriptions-item>
      <el-descriptions-item label="检测方法">{{ formData.method }}</el-descriptions-item>
      <el-descriptions-item label="检测仪器">{{ formData.instrument }}</el-descriptions-item>
      <el-descriptions-item label="备注" :span="2">{{ formData.remark || '无' }}</el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">历史趋势</el-divider>
    
    <div ref="chartRef" class="chart-container"></div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
      <el-button type="primary" @click="handleExport">导出数据</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import * as echarts from 'echarts'

defineOptions({ name: 'DataDetailDialog' })

const dialogVisible = ref(false)
const chartRef = ref()
let chart: echarts.ECharts | null = null

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  samplingDate: '',
  testValue: '',
  unit: '',
  testDate: '',
  tester: '',
  method: '',
  instrument: '',
  status: '',
  remark: ''
})

// 历史数据接口
interface HistoryData {
  dates: string[];
  values: number[];
}

// 历史数据（模拟数据）
const historyData = reactive<HistoryData>({
  dates: [],
  values: []
})

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  
  // 设置表单数据
  formData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    samplingPoint: data.samplingPoint,
    samplingDate: data.samplingDate || data.testDate,
    testValue: data.testValue || '',
    unit: data.unit || 'mg/L',
    testDate: data.testDate || '',
    tester: data.tester || '',
    method: data.method || '重铬酸钾法', // 模拟数据
    instrument: data.instrument || '分光光度计A', // 模拟数据
    status: data.status || 'normal',
    remark: data.remark || ''
  }
  
  // 获取历史数据
  await fetchHistoryData(data.testItem, data.samplingPoint)
  
  // 等待DOM更新后初始化图表
  await nextTick()
  initChart()
}
defineExpose({ open })

// 获取历史数据
const fetchHistoryData = async (testItem: string, samplingPoint: string) => {
  // 模拟API调用获取历史数据
  // 这里使用模拟数据
  const today = new Date()
  let tempDates: string[] = [];
  let tempValues: number[] = [];
  
  // 生成过去30天的模拟数据
  for (let i = 30; i >= 0; i--) {
    const date = new Date(today)
    date.setDate(date.getDate() - i)
    const dateStr = `${date.getMonth() + 1}/${date.getDate()}`
    tempDates.push(dateStr)
    
    // 根据检测项目生成不同范围的模拟数据
    let baseValue = 0
    let variance = 0
    
    switch (testItem) {
      case 'COD':
        baseValue = samplingPoint === '进水总口' ? 300 : 40
        variance = samplingPoint === '进水总口' ? 50 : 10
        break
      case 'BOD5':
        baseValue = samplingPoint === '进水总口' ? 150 : 8
        variance = samplingPoint === '进水总口' ? 30 : 2
        break
      case '氨氮':
        baseValue = samplingPoint === '进水总口' ? 25 : 3
        variance = samplingPoint === '进水总口' ? 5 : 2
        break
      case '总磷':
        baseValue = samplingPoint === '进水总口' ? 3 : 0.4
        variance = samplingPoint === '进水总口' ? 0.5 : 0.1
        break
      case '总氮':
        baseValue = samplingPoint === '进水总口' ? 40 : 12
        variance = samplingPoint === '进水总口' ? 8 : 3
        break
      default:
        baseValue = 100
        variance = 20
    }
    
    // 生成随机值
    const value = baseValue + (Math.random() * 2 - 1) * variance
    tempValues.push(Number(value.toFixed(2)))
  }
  
  // 直接赋值而不是使用push
  historyData.dates = tempDates;
  historyData.values = tempValues;
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  // 如果已经有图表实例，先销毁
  if (chart) {
    chart.dispose()
  }
  
  // 创建图表实例
  chart = echarts.init(chartRef.value)
  
  // 设置标准线值
  let standardValue = 0
  switch (formData.value.testItem) {
    case 'COD':
      standardValue = 50
      break
    case 'BOD5':
      standardValue = 10
      break
    case '氨氮':
      standardValue = 5
      break
    case '总磷':
      standardValue = 0.5
      break
    case '总氮':
      standardValue = 15
      break
    default:
      standardValue = 0
  }
  
  // 设置图表选项
  const option = {
    title: {
      text: `${formData.value.testItem}历史趋势图`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    xAxis: {
      type: 'category',
      data: historyData.dates
    },
    yAxis: {
      type: 'value',
      name: formData.value.unit
    },
    series: [
      {
        name: formData.value.testItem,
        type: 'line',
        data: historyData.values,
        markLine: standardValue > 0 ? {
          data: [
            {
              name: '标准限值',
              yAxis: standardValue,
              lineStyle: {
                color: '#ff4949'
              },
              label: {
                formatter: `标准限值: ${standardValue}${formData.value.unit}`
              }
            }
          ]
        } : undefined
      }
    ]
  }
  
  // 渲染图表
  chart.setOption(option)
  
  // 窗口大小变化时重新调整图表大小
  window.addEventListener('resize', () => {
    chart?.resize()
  })
}

// 导出数据
const handleExport = () => {
  ElMessage.success(`已导出${formData.value.testItem}检测数据`)
}

// 组件卸载时销毁图表
onMounted(() => {
  return () => {
    if (chart) {
      chart.dispose()
      chart = null
    }
  }
})
</script>

<style scoped>
.chart-container {
  height: 20rem;
  margin-top: 1rem;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}
</style> 
<template>
  <Dialog v-model="dialogVisible" title="标准对比分析" width="70%">
    <div v-loading="loading" class="compare-container">
      <!-- 样品信息 -->
      <el-descriptions :column="3" border size="small" class="mb-4">
        <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
        <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
        <el-descriptions-item label="检测值">
          <span :class="{ 'text-danger': isExceeded, 'text-warning': isWarning }">
            {{ formData.testValue }} {{ formData.unit }}
          </span>
        </el-descriptions-item>
        <el-descriptions-item label="检测日期">{{ formData.testDate }}</el-descriptions-item>
        <el-descriptions-item label="检测人员">{{ formData.tester }}</el-descriptions-item>
        <el-descriptions-item label="检测方法">{{ formData.method || '-' }}</el-descriptions-item>
      </el-descriptions>
      
      <el-divider content-position="center">标准限值对比</el-divider>
      
      <!-- 标准对比表格 -->
      <el-table :data="standardsData" border style="width: 100%" row-key="id">
        <el-table-column prop="name" label="标准名称" min-width="20rem" />
        <el-table-column prop="value" label="限值" min-width="8rem" />
        <el-table-column prop="unit" label="单位" min-width="5rem" />
        <el-table-column label="达标情况" min-width="8rem">
          <template #default="{ row }">
            <el-tag v-if="!isCompareExceeded(row.value)" type="success">达标</el-tag>
            <el-tag v-else type="danger">超标 ({{ getExceedRatio(row.value) }}倍)</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="标准类型" min-width="8rem">
          <template #default="{ row }">
            <el-tag :type="getStandardTypeTagType(row.type)">{{ row.type }}</el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <el-divider content-position="center">历史数据对比</el-divider>
      
      <!-- 历史数据图表 -->
      <div class="chart-container">
        <div ref="chartRef" class="chart"></div>
      </div>
      
      <el-divider content-position="center">超标风险分析</el-divider>
      
      <!-- 超标风险 -->
      <el-alert
        v-if="riskLevel === 'high'"
        type="error"
        :closable="false"
        title="高风险"
        description="当前检测值已严重超过标准限值，属于高风险级别，建议立即采取措施处理。"
        show-icon
        class="mb-4"
      />
      <el-alert
        v-else-if="riskLevel === 'medium'"
        type="warning"
        :closable="false"
        title="中等风险"
        description="当前检测值超过标准限值，属于中等风险级别，需要关注并采取适当措施。"
        show-icon
        class="mb-4"
      />
      <el-alert
        v-else-if="riskLevel === 'low'"
        type="info"
        :closable="false"
        title="低风险"
        description="当前检测值接近标准限值，属于低风险级别，建议保持关注。"
        show-icon
        class="mb-4"
      />
      <el-alert
        v-else
        type="success"
        :closable="false"
        title="无风险"
        description="当前检测值在标准限值范围内，无风险。"
        show-icon
        class="mb-4"
      />
      
      <!-- 建议操作 -->
      <el-card class="box-card">
        <template #header>
          <div class="card-header">
            <span>建议操作</span>
          </div>
        </template>
        <div class="suggestion-list">
          <div v-for="(suggestion, index) in suggestions" :key="index" class="suggestion-item">
            <el-icon><Check /></el-icon>
            <span>{{ suggestion }}</span>
          </div>
        </div>
      </el-card>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
      <el-button type="primary" @click="handleExport">导出分析报告</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { Check } from '@element-plus/icons-vue'
import * as echarts from 'echarts/core'
import { LineChart } from 'echarts/charts'
import {
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent
} from 'echarts/components'
import { CanvasRenderer } from 'echarts/renderers'

// 注册必要的组件
echarts.use([
  TitleComponent,
  TooltipComponent,
  GridComponent,
  LegendComponent,
  MarkLineComponent,
  LineChart,
  CanvasRenderer
])

defineOptions({ name: 'StandardCompareDialog' })

const dialogVisible = ref(false)
const loading = ref(false)
const chartRef = ref()
let chart: echarts.ECharts | null = null

interface FormData {
  id?: number;
  sampleCode: string;
  testItem: string;
  testValue: string;
  unit: string;
  testDate: string;
  tester: string;
  method?: string;
}

interface StandardData {
  id: number;
  name: string;
  value: string;
  unit: string;
  type: string;
}

// 表单数据
const formData = ref<FormData>({
  sampleCode: '',
  testItem: '',
  testValue: '',
  unit: '',
  testDate: '',
  tester: '',
  method: ''
})

// 标准数据
const standardsData = ref<StandardData[]>([])

// 历史数据
const historyData = ref<{date: string; value: number}[]>([])

// 风险等级
const riskLevel = computed(() => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/') {
    return 'none'
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  if (testValue > stdValue * 1.5) {
    return 'high'
  } else if (testValue > stdValue) {
    return 'medium'
  } else if (testValue > stdValue * 0.9) {
    return 'low'
  }
  
  return 'none'
})

// 操作建议
const suggestions = computed(() => {
  const baseSuggestions = [
    '记录完整的检测数据和分析结果',
    '与历史数据进行对比分析，查找变化趋势'
  ]
  
  if (riskLevel.value === 'high') {
    return [
      ...baseSuggestions,
      '立即启动应急预案，采取应对措施',
      '安排复检确认检测结果',
      '进行原因分析，查找超标来源',
      '上报相关部门负责人'
    ]
  } else if (riskLevel.value === 'medium') {
    return [
      ...baseSuggestions,
      '安排复检确认检测结果',
      '加强监测频次，密切关注变化趋势',
      '检查相关工艺参数，进行必要调整'
    ]
  } else if (riskLevel.value === 'low') {
    return [
      ...baseSuggestions,
      '加强监测频次，关注数据变化',
      '检查工艺运行情况，预防超标风险'
    ]
  }
  
  return [
    ...baseSuggestions,
    '继续保持常规监测频次',
    '确保检测流程和数据质量'
  ]
})

// 计算标准限值
const standardValue = computed((): string => {
  switch (formData.value.testItem) {
    case 'COD':
      return '50'
    case 'BOD5':
      return '10'
    case '氨氮':
      return '5'
    case '总磷':
      return '0.5'
    case '总氮':
      return '15'
    default:
      return '/'
  }
})

// 计算是否超标
const isExceeded = computed((): boolean => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/') {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  return testValue > stdValue
})

// 计算是否警告（接近超标）
const isWarning = computed((): boolean => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/' || isExceeded.value) {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  return testValue > stdValue * 0.9
})

// 判断与特定标准值的对比是否超标
const isCompareExceeded = (standardValue: string): boolean => {
  if (!formData.value.testValue || !standardValue) {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue)
  
  return testValue > stdValue
}

// 获取超标倍数
const getExceedRatio = (standardValue: string): string => {
  if (!formData.value.testValue || !standardValue) {
    return '0'
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue)
  
  if (testValue <= stdValue) {
    return '0'
  }
  
  return (testValue / stdValue).toFixed(2)
}

// 获取标准类型标签样式
const getStandardTypeTagType = (type: string): string => {
  switch (type) {
    case '国家标准':
      return 'danger'
    case '地方标准':
      return 'warning'
    case '行业标准':
      return 'success'
    case '企业标准':
      return 'info'
    default:
      return 'info'
  }
}

// 初始化图表
const initChart = () => {
  if (!chartRef.value) return
  
  chart = echarts.init(chartRef.value)
  
  const option = {
    title: {
      text: `${formData.value.testItem}浓度历史变化趋势`,
      left: 'center'
    },
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      data: ['检测值', '标准限值'],
      bottom: 10
    },
    grid: {
      left: '3%',
      right: '4%',
      bottom: '15%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      boundaryGap: false,
      data: historyData.value.map(item => item.date)
    },
    yAxis: {
      type: 'value',
      name: formData.value.unit,
      axisLabel: {
        formatter: `{value} ${formData.value.unit}`
      }
    },
    series: [
      {
        name: '检测值',
        type: 'line',
        data: historyData.value.map(item => item.value),
        markPoint: {
          data: [
            { type: 'max', name: '最大值' },
            { type: 'min', name: '最小值' }
          ]
        }
      },
      {
        name: '标准限值',
        type: 'line',
        data: Array(historyData.value.length).fill(parseFloat(standardValue.value)),
        lineStyle: {
          type: 'dashed',
          color: '#F56C6C'
        },
        symbol: 'none'
      }
    ]
  }
  
  chart.setOption(option)
}

// 打开对话框
const open = async (data: any): Promise<void> => {
  if (!data) return
  
  dialogVisible.value = true
  loading.value = true
  
  try {
    formData.value = {
      id: data.id,
      sampleCode: data.sampleCode,
      testItem: data.testItem,
      testValue: data.testValue || '',
      unit: data.unit || 'mg/L',
      testDate: data.testDate || '',
      tester: data.tester || '',
      method: data.method || '标准方法'
    }
    
    // 模拟加载标准数据
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 生成标准数据
    standardsData.value = [
      {
        id: 1,
        name: '城镇污水处理厂污染物排放标准(GB 18918-2002)一级A标准',
        value: standardValue.value,
        unit: formData.value.unit,
        type: '国家标准'
      },
      {
        id: 2,
        name: '水污染物排放限值(DB44/26-2001)一级标准',
        value: (parseFloat(standardValue.value) * 1.2).toFixed(1),
        unit: formData.value.unit,
        type: '地方标准'
      },
      {
        id: 3,
        name: '城市污水再生利用工业用水水质(GB/T 19923-2005)',
        value: (parseFloat(standardValue.value) * 0.8).toFixed(1),
        unit: formData.value.unit,
        type: '国家标准'
      },
      {
        id: 4,
        name: '企业内控标准',
        value: (parseFloat(standardValue.value) * 0.9).toFixed(1),
        unit: formData.value.unit,
        type: '企业标准'
      }
    ]
    
    // 生成模拟历史数据
    const days = 30
    const baseValue = parseFloat(formData.value.testValue) * 0.8
    const currentDate = new Date(formData.value.testDate || new Date())
    
    historyData.value = []
    for (let i = days - 1; i >= 0; i--) {
      const date = new Date(currentDate)
      date.setDate(date.getDate() - i)
      const dateStr = `${date.getMonth() + 1}/${date.getDate()}`
      
      // 生成波动的历史数据
      const fluctuation = Math.random() * 0.4 + 0.8 // 0.8 ~ 1.2
      let value = baseValue * fluctuation
      
      // 最后一天是当前值
      if (i === 0) {
        value = parseFloat(formData.value.testValue)
      }
      
      historyData.value.push({
        date: dateStr,
        value
      })
    }
    
    await nextTick()
    initChart()
  } catch (error) {
    console.error('加载数据失败:', error)
    ElMessage.error('加载数据失败，请重试')
  } finally {
    loading.value = false
  }
}
defineExpose({ open })

// 导出分析报告
const handleExport = () => {
  ElMessage.success('分析报告导出成功')
}

// 监听窗口大小变化，调整图表尺寸
onMounted(() => {
  window.addEventListener('resize', () => {
    chart?.resize()
  })
})
</script>

<style scoped>
.compare-container {
  min-height: 60vh;
}

.chart-container {
  height: 25rem;
  margin: 1rem 0;
}

.chart {
  width: 100%;
  height: 100%;
}

.suggestion-list {
  padding: 0.5rem;
}

.suggestion-item {
  display: flex;
  align-items: center;
  margin-bottom: 0.5rem;
}

.suggestion-item .el-icon {
  margin-right: 0.5rem;
  color: #67c23a;
}

.card-header {
  font-weight: bold;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}
</style> 
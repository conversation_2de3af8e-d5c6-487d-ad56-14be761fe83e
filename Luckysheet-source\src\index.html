<!DOCTYPE html>
<html>
	<head lang="zh">
		<meta charset="utf-8" />
		<meta http-equiv="X-UA-Compatible" content="IE=edge" />
		<meta name="renderer" content="webkit" />
		<meta
			name="viewport"
			content="width=device-width, initial-scale=1,user-scalable=0"
		/>
		<title>Luckysheet</title>
		<link rel="stylesheet" href="./plugins/css/pluginsCss.css" />
		<link rel="stylesheet" href="./plugins/plugins.css" />
		<link rel="stylesheet" href="./css/luckysheet.css" />
		<link rel="stylesheet" href="./assets/iconfont/iconfont.css" />
		<script src="./plugins/js/plugin.js"></script>

		<!-- rollup luckysheet.js -->
		<script src="./luckysheet.umd.js"></script>
		<script src="./demoData/sheetVchart.js"></script>
		<script src="./demoData/sheetChart.js"></script>
		<script src="./demoData/sheetCell.js"></script>
		<script src="./demoData/sheetPicture.js"></script>
	</head>

	<body>
		<div
			id="luckysheet"
			style="
				margin: 0px;
				padding: 0px;
				position: absolute;
				width: 100%;
				height: 100%;
				left: 0px;
				top: 0px;
			"
		></div>

		<script>
			$(function () {
				const id = Math.random().toString(16).slice(2, 8);
				const username = `user_${id}`;

				const options = {
					container: "luckysheet",
					lang: "zh",
					// showinfobar: false,
					plugins: ['chart',"vchart",'fileImport',"fileExport"],
					data: [sheetCell],
					allowUpdate: true, // 配置协同功能
					loadUrl: "http://localhost:9000/loadSheetData?gridkey=gridkey_demo",
					updateUrl: `http://localhost:9000?type=luckysheet&userid=${id}&username=${username}&gridkey=gridkey`, // 协同服务转发服务

					menuHandler: {
						exit() {
							console.log("==> 点击了退出按钮");
						},
						shear() {
							console.log("==> 点击了分享按钮");
						},

						// 菜单解密
						decrypt(password) {
							// 执行后续 server 操作
							console.log("==> 解密文档：", password);
							// 解密过程需要校验密码，因此需要提供返回值
							return password === "123456";
						},

						// 菜单加密
						encryption: (password) => {
							// 拿到password可执行后续 server操作
							console.log("==> 文档已加密:", password);
						},

						// 打开文档输入密码 标记文档是否加密
						// openDocumentPassword: (password) => {
						// 	console.log("==> 用户输入密码:", password);
						// 	return password === "123456";
						// },
					},
				};
				options.loading = {
					image: () => {
						return `<svg viewBox="25 25 50 50" class="circular">
					<circle cx="50" cy="50" r="20" fill="none"></circle>
					</svg>`;
					},
					imageClass: "loadingAnimation",
				};
				luckysheet.create(options);
			});
		</script>
	</body>
</html>

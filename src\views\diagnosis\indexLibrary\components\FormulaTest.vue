<template>
  <el-card class="formula-test-card">
    <template #header>
      <div class="flex justify-between items-center">
        <span>
          <el-icon>
            <Operation />
          </el-icon>
          模拟计算测试
        </span>
        <div class="flex gap-2">
          <el-button size="small" @click="$emit('reset')">
            <el-icon>
              <Refresh />
            </el-icon>
            重置
          </el-button>
          <el-button type="primary" size="small" @click="$emit('simulate')" :disabled="!canCalculate">
            <el-icon>
              <Operation />
            </el-icon>
            计算
          </el-button>
          <el-button size="small" @click="$emit('close')" class="close-btn">
            <el-icon>
              <Close />
            </el-icon>
            关闭
          </el-button>
        </div>
      </div>
    </template>

    <!-- 变量输入提示 -->
    <div v-if="formulaParams.length > 0" class="mb-4">
      <el-alert title="请为以下变量输入测试值" type="info" :closable="false" show-icon>
        <template #default>
          <div class="text-sm">
            公式：<code>{{ formula }}</code>
          </div>
        </template>
      </el-alert>
    </div>

    <!-- 变量输入区域 -->
    <div v-if="formulaParams.length > 0" class="variable-inputs">
      <!-- 多变量提示 -->
      <div v-if="formulaParams.length > 6" class="mb-3">
        <el-alert :title="`共有 ${formulaParams.length} 个数据项需要输入测试值`" type="warning" :closable="false" show-icon>
          <template #default>
            <div class="text-sm">
              数据项较多，请仔细为每个变量输入合适的测试值
            </div>
          </template>
        </el-alert>
      </div>

      <div class="variable-grid" :class="getGridClass(formulaParams.length)">
        <div v-for="(param, index) in formulaParams" :key="index" class="variable-item">
          <el-form-item :label="param.name" size="small" class="variable-form-item">
            <el-input-number v-model="param.value" :precision="4" style="width: 100%" placeholder="输入测试值"
              :controls="true" size="small" @change="$emit('param-change')" />
          </el-form-item>
        </div>
      </div>
    </div>

    <!-- 计算状态提示 -->
    <div v-if="!canCalculate && formulaParams.length > 0" class="mb-4">
      <el-alert title="请输入所有变量的测试值" type="warning" :closable="false" show-icon />
    </div>

    <el-divider />

    <!-- 计算结果显示 -->
    <div class="calculation-result">
      <div class="result-header mb-2">
        <el-text type="primary" size="large" class="font-semibold">
          <el-icon>
            <TrendCharts />
          </el-icon>
          计算结果
        </el-text>
      </div>

      <div class="result-content">
        <div v-if="calculationResult !== null" class="result-success">
          <el-tag type="success" size="large" class="result-tag">
            {{ calculationResult }}
          </el-tag>
          <div class="result-details mt-2 text-sm text-gray-600">
            <div>计算公式：{{ formula }}</div>
            <div>
              变量值：
              <span v-for="(param, index) in formulaParams" :key="index">
                {{ param.name }}={{ param.value }}{{ index < formulaParams.length - 1 ? ', ' : '' }} </span>
            </div>
          </div>
        </div>

        <div v-else-if="calculationError" class="result-error">
          <el-alert :title="calculationError" type="error" :closable="false" show-icon />
        </div>

        <div v-else class="result-placeholder">
          <el-text type="info">请输入变量值并点击计算按钮</el-text>
        </div>
      </div>
    </div>
  </el-card>
</template>

<script lang="ts" setup>
import {
  Operation,
  Refresh,
  TrendCharts,
  Close
} from '@element-plus/icons-vue'

// Props
interface Props {
  formula: string
  formulaParams: { name: string; value: number }[]
  calculationResult: number | null
  calculationError: string
  canCalculate: boolean
}

// Emits
interface Emits {
  (e: 'simulate'): void
  (e: 'reset'): void
  (e: 'param-change'): void
  (e: 'close'): void
}

const props = defineProps<Props>()
defineEmits<Emits>()

// 计算网格布局类名
const getGridClass = (paramCount: number): string => {
  // 检查是否有长变量名（超过6个字符）
  const hasLongNames = props.formulaParams.some((param: { name: string; value: number }) => param.name.length > 6)

  // 检查是否有超长变量名（超过10个字符）
  const hasVeryLongNames = props.formulaParams.some((param: { name: string; value: number }) => param.name.length > 10)

  if (paramCount === 1) return 'grid-1'
  if (paramCount === 2) return hasVeryLongNames ? 'grid-1' : 'grid-2'
  if (paramCount === 3) return hasVeryLongNames ? 'grid-2' : hasLongNames ? 'grid-2' : 'grid-3'
  if (paramCount === 4) return hasVeryLongNames ? 'grid-2' : hasLongNames ? 'grid-2' : 'grid-4'
  if (paramCount <= 6) return hasVeryLongNames ? 'grid-2' : hasLongNames ? 'grid-3' : 'grid-4'
  if (paramCount <= 8) return hasVeryLongNames ? 'grid-2' : hasLongNames ? 'grid-3' : 'grid-4'
  return hasVeryLongNames ? 'grid-3' : hasLongNames ? 'grid-3' : 'grid-4'
}
</script>

<style scoped>
.formula-test-card {
  background-color: #f8f9fa;

  :deep(.el-card__header) {
    background-color: #e9ecef;
    border-bottom: 1px solid #dee2e6;
  }

  .close-btn {
    color: #909399;
    border-color: #dcdfe6;

    &:hover {
      color: #f56c6c;
      border-color: #f56c6c;
      background-color: #fef0f0;
    }
  }

  .variable-inputs {
    background-color: #fff;
    padding: 1rem;
    border-radius: 0.375rem;
    border: 0.0625rem solid #e4e7ed;

    .variable-grid {
      display: grid;
      gap: 1rem;
      width: 100%;

      &.grid-1 {
        grid-template-columns: 1fr;
        max-width: 400px;
        margin: 0 auto;
      }

      &.grid-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      &.grid-3 {
        grid-template-columns: repeat(3, 1fr);
      }

      &.grid-4 {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      }
    }

    .variable-item {
      min-width: 0;
      /* 防止内容溢出 */
      display: flex;
      flex-direction: column;
      width: 100%;
    }

    .variable-form-item {
      margin-bottom: 0;

      :deep(.el-form-item__label) {
        font-size: 0.8125rem;
        font-weight: 500;
        color: #606266;
        line-height: 1.4;
        word-break: break-word;
        white-space: normal;
        min-height: auto;
        padding-bottom: 0.375rem;
        display: block;
        width: 100%;
        overflow-wrap: break-word;
        hyphens: auto;
        max-width: 100%;
      }

      :deep(.el-form-item__content) {
        line-height: 1.2;
        margin-left: 0 !important;
      }

      :deep(.el-input-number) {
        width: 100%;

        .el-input__inner {
          font-size: 0.8125rem;
        }
      }
    }
  }

  .result-success {
    text-align: center;

    .result-tag {
      font-size: 1.125rem;
      padding: 0.5rem 1rem;
      font-weight: bold;
    }

    .result-details {
      background-color: #f0f9ff;
      padding: 0.75rem;
      border-radius: 0.375rem;
      border-left: 0.25rem solid #409eff;
    }
  }

  .result-error {
    :deep(.el-alert) {
      margin: 0;
    }
  }

  .result-placeholder {
    text-align: center;
    padding: 1.25rem;
    color: #909399;
  }
}

/* 响应式优化 */
@media (max-width: 48rem) {
  .formula-test-card .variable-inputs {
    padding: 0.75rem;

    .variable-grid {
      gap: 0.75rem;

      &.grid-2 {
        grid-template-columns: repeat(2, 1fr);
      }

      &.grid-3,
      &.grid-4 {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
      }
    }

    .variable-form-item {
      :deep(.el-form-item__label) {
        font-size: 0.75rem;
        padding-bottom: 0.25rem;
        line-height: 1.3;
      }
    }
  }
}

@media (max-width: 30rem) {
  .formula-test-card .variable-inputs {
    padding: 0.5rem;

    .variable-grid {
      gap: 0.5rem;
      grid-template-columns: 1fr !important;
    }

    .variable-form-item {
      :deep(.el-form-item__label) {
        font-size: 0.75rem;
        padding-bottom: 0.1875rem;
      }

      :deep(.el-input-number) {
        .el-input__inner {
          font-size: 0.75rem;
        }
      }
    }
  }
}
</style>

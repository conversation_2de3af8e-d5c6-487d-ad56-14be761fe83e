<template>
  <el-dialog
    v-model="dialogVisible"
    title="生成送检单"
    width="500px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="inspection-form"
    >
      <!-- 基本信息展示 -->
      <div class="info-section">
        <h4>采样任务信息</h4>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务编号">
              <span>{{ currentTask?.taskCode || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采样点">
              <span>{{ currentTask?.samplingPoint || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="检测项目">
              <span>{{ currentTask?.testItems || '-' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="采样人员">
              <span>{{ currentTask?.samplerName || '-' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </div>

      <!-- 送检信息填写 -->
      <div class="form-section">
        <h4>送检信息</h4>
        <el-form-item label="紧急程度" prop="urgency">
          <el-select v-model="formData.urgency" placeholder="请选择紧急程度">
            <el-option label="普通" value="normal" />
            <el-option label="紧急" value="urgent" />
          </el-select>
        </el-form-item>

        <el-form-item label="特殊要求" prop="specialRequirements">
          <el-input
            v-model="formData.specialRequirements"
            type="textarea"
            :rows="3"
            placeholder="请输入特殊要求（可选）"
          />
        </el-form-item>

        <el-form-item label="送检备注" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="请输入送检备注信息（可选）"
          />
        </el-form-item>
      </div>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          确认生成送检单
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'

// 组件引用
const formRef = ref<FormInstance>()
const dialogVisible = ref(false)
const loading = ref(false)
const currentTask = ref<any>(null)

// 表单数据
const formData = reactive({
  urgency: 'normal',
  specialRequirements: '',
  remark: ''
})

// 表单验证规则（放宽限制）
const formRules: FormRules = {
  urgency: [
    { required: true, message: '请选择紧急程度', trigger: 'change' }
  ],
  specialRequirements: [
    { max: 2000, message: '长度不能超过 2000 个字符', trigger: 'blur' }
  ],
  remark: [
    { max: 2000, message: '长度不能超过 2000 个字符', trigger: 'blur' }
  ]
}



// 打开对话框
const open = (task: any) => {
  currentTask.value = task

  // 重置表单数据为默认值
  formData.urgency = 'normal'
  formData.specialRequirements = ''
  formData.remark = `${task.samplingPoint}采样任务送检`

  dialogVisible.value = true
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
}

// 确认生成送检单
const handleConfirm = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    loading.value = true

    // 构建送检单数据
    const inspectionData = {
      id: currentTask.value.id, // 采样执行记录ID
      urgency: formData.urgency,
      specialRequirements: formData.specialRequirements,
      remark: formData.remark
    }

    // 这里应该调用API创建送检记录
    // await generateInspectionForm(inspectionData)

    // 模拟API调用
    setTimeout(() => {
      ElMessage.success('送检单生成成功，送检记录已创建')
      loading.value = false
      dialogVisible.value = false
      resetForm()

      // 触发父组件刷新
      emit('success', inspectionData)
    }, 1000)

  } catch (error) {
    loading.value = false
    console.error('生成送检单失败:', error)
  }
}

// 重置表单
const resetForm = () => {
  if (formRef.value) {
    formRef.value.resetFields()
  }
  currentTask.value = null
}

// 暴露方法给父组件
defineExpose({
  open
})

// 定义事件
const emit = defineEmits<{
  success: [data: any]
}>()
</script>

<script lang="ts">
export default {
  name: 'CreateInspectionFormDialog'
}
</script>

<style scoped>
.inspection-form {
  max-height: 60vh;
  overflow-y: auto;
}

.info-section {
  background-color: #f8f9fa;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
}

.info-section h4 {
  margin: 0 0 1rem 0;
  color: #409eff;
  font-size: 1rem;
  font-weight: 600;
}

.form-section h4 {
  margin: 0 0 1rem 0;
  color: #303133;
  font-size: 1rem;
  font-weight: 600;
  border-bottom: 2px solid #e4e7ed;
  padding-bottom: 0.5rem;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>

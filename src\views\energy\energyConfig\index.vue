<template>
  <div class="energy-config-container">
    <el-card>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="电耗配置" name="electric">
          <div class="tab-content">
            <ElectricConfig />
          </div>
        </el-tab-pane>
        <el-tab-pane label="药耗配置" name="chemical">
          <div class="tab-content">
            <DrugConfig />
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
  </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import ElectricConfig from './electricConfig.vue'
import DrugConfig from './drugConfig.vue'

// 活动标签页
const activeTab = ref<'electric' | 'chemical'>('electric');
</script>

<style scoped lang="scss">
.energy-config-container {
  width: 100%;
  height: calc(100vh - 170px);
  padding: 1rem;
  box-sizing: border-box;
}

.el-card {
  height: 100%;
}

.tab-content {
  margin-top: 1rem;
  height: calc(100vh - 260px);
  overflow: auto;
}
</style>

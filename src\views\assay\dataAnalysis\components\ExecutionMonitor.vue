<template>
  <div class="execution-monitor">
    <!-- 核心指标卡片 -->
    <div class="metrics-grid">
      <el-card v-for="metric in coreMetrics" :key="metric.key" class="metric-card" shadow="hover">
        <div class="metric-content">
          <div class="metric-icon">
            <el-icon :size="32" :color="metric.color">
              <component :is="metric.icon" />
            </el-icon>
          </div>
          <div class="metric-info">
            <div class="metric-value">
              <CountTo :start-val="0" :end-val="metric.value" :duration="2000" />
              <span class="metric-unit">{{ metric.unit }}</span>
            </div>
            <div class="metric-label">{{ metric.label }}</div>
            <div class="metric-trend">
              <el-icon :color="metric.trend > 0 ? '#67C23A' : '#F56C6C'">
                <ArrowUp v-if="metric.trend > 0" />
                <ArrowDown v-else />
              </el-icon>
              <span :class="metric.trend > 0 ? 'trend-up' : 'trend-down'">
                {{ Math.abs(metric.trend) }}%
              </span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 视图切换 -->
    <div class="view-toggle">
      <el-radio-group v-model="currentView" @change="handleViewChange">
        <el-radio-button label="chart">图表视图</el-radio-button>
        <el-radio-button label="calendar">日历视图</el-radio-button>
      </el-radio-group>
    </div>

    <!-- 图表视图 -->
    <div v-show="currentView === 'chart'" class="chart-view">
      <!-- 执行概览 -->
      <div class="overview-section">
        <el-row :gutter="20">
          <!-- 计划执行率 -->
          <el-col :span="12">
            <el-card title="计划执行率" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>计划执行率</span>
                  <el-tag :type="executionRate >= 90 ? 'success' : executionRate >= 80 ? 'warning' : 'danger'">
                    {{ executionRate }}%
                  </el-tag>
                </div>
              </template>
              <div ref="executionChartRef" class="chart-container"></div>
            </el-card>
          </el-col>

          <!-- 任务分配情况 -->
          <el-col :span="12">
            <el-card title="任务分配情况" shadow="hover">
              <template #header>
                <div class="card-header">
                  <span>任务分配情况</span>
                  <el-button type="primary" size="small" @click="handleTaskAssignment">
                    <el-icon><Plus /></el-icon>分配任务
                  </el-button>
                </div>
              </template>
              <div ref="taskChartRef" class="chart-container"></div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>

    <!-- 日历视图 -->
    <div v-show="currentView === 'calendar'" class="calendar-view">
      <el-card shadow="hover">
        <template #header>
          <div class="card-header">
            <span>任务日历</span>
            <div class="calendar-controls">
              <el-button-group>
                <el-button @click="handlePrevMonth">
                  <el-icon><ArrowLeft /></el-icon>
                </el-button>
                <el-button @click="handleToday">今天</el-button>
                <el-button @click="handleNextMonth">
                  <el-icon><ArrowRight /></el-icon>
                </el-button>
              </el-button-group>
              <span class="current-month">{{ currentMonthLabel }}</span>
            </div>
          </div>
        </template>

        <!-- 状态图例 -->
        <div class="calendar-legend">
          <div class="legend-item">
            <div class="legend-dot task-completed"></div>
            <span>按时完成</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot task-delayed"></div>
            <span>延期完成</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot task-ongoing"></div>
            <span>进行中</span>
          </div>
          <div class="legend-item">
            <div class="legend-dot task-pending"></div>
            <span>未开始</span>
          </div>
        </div>

        <div class="calendar-container">
          <el-calendar v-model="calendarDate" class="task-calendar">
            <template #date-cell="{ data }">
              <div
                class="calendar-cell"
                :class="{
                  'has-tasks': getTasksForDate(data.day).length > 0,
                  'clickable': getTasksForDate(data.day).length > 0
                }"
                @click="getTasksForDate(data.day).length > 0 && handleDateClick(data.day)"
              >
                <div class="date-number">{{ data.day.split('-').pop() }}</div>

                <div v-if="getTasksForDate(data.day).length > 0" class="task-content">
                  <!-- 任务状态点 -->
                  <div class="task-indicators">
                    <div
                      v-for="task in getTasksForDate(data.day).slice(0, 4)"
                      :key="task.id"
                      :class="['task-dot', `task-${task.status}`]"
                      :title="`${task.planName} - ${getStatusLabel(task.status)}`"
                      @click.stop="handleTaskClick(task)"
                    ></div>
                    <div
                      v-if="getTasksForDate(data.day).length > 4"
                      class="task-more"
                      :title="`点击查看全部 ${getTasksForDate(data.day).length} 个任务`"
                    >
                      +{{ getTasksForDate(data.day).length - 4 }}
                    </div>
                  </div>

                  <!-- 任务统计 -->
                  <div class="task-summary">
                    <div class="task-count">{{ getTasksForDate(data.day).length }}个任务</div>
                    <div class="task-progress" v-if="getDateTaskStats(data.day)">
                      <div class="progress-bar">
                        <div
                          class="progress-fill"
                          :style="{
                            width: `${(getDateTaskStats(data.day).completed / getDateTaskStats(data.day).total) * 100}%`
                          }"
                        ></div>
                      </div>
                      <span class="progress-text">
                        {{ getDateTaskStats(data.day).completed }}/{{ getDateTaskStats(data.day).total }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </el-calendar>
        </div>
      </el-card>
    </div>

    <!-- 详细数据表格 -->
    <div class="data-section">
      <el-card title="执行详情" shadow="hover">
        <template #header>
          <div class="card-header">
            <span>执行详情</span>
            <div class="header-actions">
              <el-select v-model="selectedPeriod" size="small" style="width: 120px; margin-right: 10px;">
                <el-option label="本周" value="week" />
                <el-option label="本月" value="month" />
                <el-option label="本季度" value="quarter" />
              </el-select>
              <el-button type="success" size="small" @click="handleExport">
                <el-icon><Download /></el-icon>导出报告
              </el-button>
            </div>
          </div>
        </template>

        <!-- 筛选条件 -->
        <div class="filter-section">
          <el-form :inline="true" :model="filterForm" class="filter-form">
            <el-form-item label="水厂">
              <el-select v-model="filterForm.plant" placeholder="选择水厂" clearable style="width: 150px;">
                <el-option label="全部水厂" value="" />
                <el-option label="第一水厂" value="plant1" />
                <el-option label="第二水厂" value="plant2" />
                <el-option label="第三水厂" value="plant3" />
              </el-select>
            </el-form-item>
            <el-form-item label="执行状态">
              <el-select v-model="filterForm.status" placeholder="选择状态" clearable style="width: 120px;">
                <el-option label="全部状态" value="" />
                <el-option label="按时完成" value="completed" />
                <el-option label="延期完成" value="delayed" />
                <el-option label="进行中" value="ongoing" />
                <el-option label="未开始" value="pending" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" @click="handleFilter">
                <el-icon><Search /></el-icon>筛选
              </el-button>
              <el-button @click="resetFilter">
                <el-icon><Refresh /></el-icon>重置
              </el-button>
            </el-form-item>
          </el-form>
        </div>

        <!-- 数据表格 -->
        <el-table v-loading="loading" :data="executionData" border style="width: 100%">
          <el-table-column prop="planName" label="计划名称" min-width="150" />
          <el-table-column prop="plant" label="水厂" width="100" />
          <el-table-column prop="assignee" label="负责人" width="100" />
          <el-table-column prop="plannedDate" label="计划时间" width="180" />
          <el-table-column prop="actualDate" label="实际时间" width="180" />
          <el-table-column prop="samplesPlanned" label="计划采样" width="100" align="center">
            <template #default="{ row }">
              <span>{{ row.samplesPlanned }}个</span>
            </template>
          </el-table-column>
          <el-table-column prop="samplesCompleted" label="完成采样" width="100" align="center">
            <template #default="{ row }">
              <span>{{ row.samplesCompleted }}个</span>
            </template>
          </el-table-column>
          <el-table-column prop="completionRate" label="完成率" width="100" align="center">
            <template #default="{ row }">
              <el-progress 
                :percentage="row.completionRate" 
                :color="getProgressColor(row.completionRate)"
                :stroke-width="8"
              />
            </template>
          </el-table-column>
          <el-table-column prop="status" label="执行状态" width="100" align="center">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)">
                {{ getStatusLabel(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="efficiency" label="效率评级" width="100" align="center">
            <template #default="{ row }">
              <el-rate 
                v-model="row.efficiency" 
                :max="5" 
                disabled 
                show-score 
                text-color="#ff9900"
              />
            </template>
          </el-table-column>
          <el-table-column label="操作" width="80" fixed="right">
            <template #default="{ row }">
              <el-button link type="primary" @click="handleViewDetail(row)">详情</el-button>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :background="true"
            layout="total, sizes, prev, pager, next, jumper"
            :total="pagination.total"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
    </div>

    <!-- 任务详情对话框 -->
    <el-dialog
      v-model="taskDetailVisible"
      :title="`${selectedDate} 任务详情`"
      width="60%"
      destroy-on-close
    >
      <div v-if="selectedDateTasks.length > 0" class="task-detail-list">
        <el-card
          v-for="task in selectedDateTasks"
          :key="task.id"
          class="task-detail-card"
          shadow="hover"
        >
          <div class="task-detail-content">
            <div class="task-header">
              <h4>{{ task.planName }}</h4>
              <el-tag :type="getStatusType(task.status)">
                {{ getStatusLabel(task.status) }}
              </el-tag>
            </div>
            <div class="task-info">
              <el-row :gutter="20">
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">水厂：</span>
                    <span>{{ task.plant }}</span>
                  </div>
                  <div class="info-item">
                    <span class="label">负责人：</span>
                    <span>{{ task.assignee }}</span>
                  </div>
                </el-col>
                <el-col :span="12">
                  <div class="info-item">
                    <span class="label">计划采样：</span>
                    <span>{{ task.samplesPlanned }}个</span>
                  </div>
                  <div class="info-item">
                    <span class="label">完成采样：</span>
                    <span>{{ task.samplesCompleted }}个</span>
                  </div>
                </el-col>
              </el-row>
              <div class="progress-info">
                <span class="label">完成进度：</span>
                <el-progress
                  :percentage="task.completionRate"
                  :color="getProgressColor(task.completionRate)"
                  :stroke-width="8"
                  style="width: 200px;"
                />
              </div>
            </div>
          </div>
        </el-card>
      </div>
      <el-empty v-else description="当天无任务安排" />
    </el-dialog>

    <!-- 执行详情弹窗 -->
    <ExecutionDetailDialog ref="executionDetailDialogRef" />
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, onBeforeUnmount, nextTick, computed } from 'vue'
import * as echarts from 'echarts'
import { CountTo } from '@/components/CountTo'
import ExecutionDetailDialog from './ExecutionDetailDialog.vue'
import { ElMessage } from 'element-plus'
import { ChartManager, ChartUtils } from '@/utils/chartUtils'

// 导入API
import { AssayDataAnalysisApi, type AssayExecutionMonitorVO } from '@/api/assay/dataAnalysis'
import {
  Search,
  Refresh,
  Download,
  Plus,
  ArrowUp,
  ArrowDown,
  ArrowLeft,
  ArrowRight,
  Target,
  UserFilled,
  DataAnalysis,
  Timer
} from '@element-plus/icons-vue'

defineOptions({ name: 'ExecutionMonitor' })

// 当前视图
const currentView = ref('chart')

// 日历相关
const calendarDate = ref(new Date('2023-07-20')) // 设置为有数据的月份
const taskDetailVisible = ref(false)
const selectedDate = ref('')
const selectedDateTasks = ref([])

// 当前月份标签
const currentMonthLabel = computed(() => {
  const date = calendarDate.value
  return `${date.getFullYear()}年${date.getMonth() + 1}月`
})

// 核心指标
const coreMetrics = ref([
  {
    key: 'plans',
    label: '本月计划数',
    value: 156,
    unit: '个',
    icon: 'Target',
    color: '#409EFF',
    trend: 12.5
  },
  {
    key: 'completed',
    label: '已完成任务',
    value: 142,
    unit: '个',
    icon: 'DataAnalysis',
    color: '#67C23A',
    trend: 8.3
  },
  {
    key: 'personnel',
    label: '参与人员',
    value: 28,
    unit: '人',
    icon: 'UserFilled',
    color: '#E6A23C',
    trend: -2.1
  },
  {
    key: 'efficiency',
    label: '平均效率',
    value: 91,
    unit: '%',
    icon: 'Timer',
    color: '#F56C6C',
    trend: 5.7
  }
])

// 执行率
const executionRate = ref(91)

// 图表引用
const executionChartRef = ref<HTMLElement>()
const taskChartRef = ref<HTMLElement>()

// 弹窗引用
const executionDetailDialogRef = ref()

// 图表管理器
const chartManager = new ChartManager()

// 筛选表单
const filterForm = reactive({
  plant: '',
  status: ''
})

// 选择的时间周期
const selectedPeriod = ref('month')

// 加载状态
const loading = ref(false)

// 分页
const pagination = reactive({
  current: 1,
  pageSize: 20,
  total: 156
})

// 执行数据
const executionData = ref([
  {
    id: 1,
    planName: '第一水厂日常监测',
    plant: '第一水厂',
    assignee: '张三',
    plannedDate: '2023-07-20 09:00',
    actualDate: '2023-07-20 08:45',
    samplesPlanned: 12,
    samplesCompleted: 12,
    completionRate: 100,
    status: 'completed',
    efficiency: 5,
    date: '2023-07-20'
  },
  {
    id: 2,
    planName: '第二水厂周检计划',
    plant: '第二水厂',
    assignee: '李四',
    plannedDate: '2023-07-21 10:00',
    actualDate: '2023-07-21 11:30',
    samplesPlanned: 8,
    samplesCompleted: 7,
    completionRate: 87.5,
    status: 'delayed',
    efficiency: 3,
    date: '2023-07-21'
  },
  {
    id: 3,
    planName: '第三水厂应急检测',
    plant: '第三水厂',
    assignee: '王五',
    plannedDate: '2023-07-22 14:00',
    actualDate: '',
    samplesPlanned: 15,
    samplesCompleted: 10,
    completionRate: 66.7,
    status: 'ongoing',
    efficiency: 4,
    date: '2023-07-22'
  },
  {
    id: 4,
    planName: '第一水厂夜间检测',
    plant: '第一水厂',
    assignee: '赵六',
    plannedDate: '2023-07-23 20:00',
    actualDate: '',
    samplesPlanned: 6,
    samplesCompleted: 0,
    completionRate: 0,
    status: 'pending',
    efficiency: 0,
    date: '2023-07-23'
  },
  {
    id: 5,
    planName: '第二水厂设备检查',
    plant: '第二水厂',
    assignee: '钱七',
    plannedDate: '2023-07-24 10:00',
    actualDate: '',
    samplesPlanned: 10,
    samplesCompleted: 5,
    completionRate: 50,
    status: 'ongoing',
    efficiency: 3,
    date: '2023-07-24'
  },
  {
    id: 6,
    planName: '第三水厂月度检测',
    plant: '第三水厂',
    assignee: '孙八',
    plannedDate: '2023-07-25 14:00',
    actualDate: '2023-07-25 13:45',
    samplesPlanned: 20,
    samplesCompleted: 20,
    completionRate: 100,
    status: 'completed',
    efficiency: 5,
    date: '2023-07-25'
  },
  {
    id: 7,
    planName: '第一水厂应急检测',
    plant: '第一水厂',
    assignee: '周九',
    plannedDate: '2023-07-26 08:00',
    actualDate: '2023-07-26 09:30',
    samplesPlanned: 8,
    samplesCompleted: 6,
    completionRate: 75,
    status: 'delayed',
    efficiency: 3,
    date: '2023-07-26'
  },
  {
    id: 8,
    planName: '第二水厂夜间监测',
    plant: '第二水厂',
    assignee: '吴十',
    plannedDate: '2023-07-27 22:00',
    actualDate: '',
    samplesPlanned: 12,
    samplesCompleted: 0,
    completionRate: 0,
    status: 'pending',
    efficiency: 0,
    date: '2023-07-27'
  },
  {
    id: 9,
    planName: '第三水厂设备维护',
    plant: '第三水厂',
    assignee: '郑十一',
    plannedDate: '2023-07-28 10:00',
    actualDate: '',
    samplesPlanned: 15,
    samplesCompleted: 8,
    completionRate: 53,
    status: 'ongoing',
    efficiency: 3,
    date: '2023-07-28'
  },
  {
    id: 10,
    planName: '第一水厂周末检查',
    plant: '第一水厂',
    assignee: '王五',
    plannedDate: '2023-07-29 16:00',
    actualDate: '2023-07-29 15:30',
    samplesPlanned: 6,
    samplesCompleted: 6,
    completionRate: 100,
    status: 'completed',
    efficiency: 5,
    date: '2023-07-29'
  }
])

// 获取指定日期的任务
const getTasksForDate = (date: string) => {
  return executionData.value.filter(task => task.date === date)
}

// 获取日期任务统计
const getDateTaskStats = (date: string) => {
  const tasks = getTasksForDate(date)
  if (tasks.length === 0) return null

  const stats = {
    total: tasks.length,
    completed: tasks.filter(t => t.status === 'completed').length,
    delayed: tasks.filter(t => t.status === 'delayed').length,
    ongoing: tasks.filter(t => t.status === 'ongoing').length,
    pending: tasks.filter(t => t.status === 'pending').length
  }

  return stats
}



// 视图切换
const handleViewChange = (view: string) => {
  if (view === 'chart') {
    // 切换到图表视图时，确保容器已渲染
    nextTick(() => {
      setTimeout(async () => {
        await initCharts()
        chartManager.handleTabSwitch(200)
      }, 200) // 增加延迟确保视图切换完成
    })
  }
}

// 日历相关方法
const handlePrevMonth = () => {
  const date = new Date(calendarDate.value)
  date.setMonth(date.getMonth() - 1)
  calendarDate.value = date
}

const handleNextMonth = () => {
  const date = new Date(calendarDate.value)
  date.setMonth(date.getMonth() + 1)
  calendarDate.value = date
}

const handleToday = () => {
  calendarDate.value = new Date()
}

const handleDateClick = (date: string) => {
  selectedDate.value = date
  selectedDateTasks.value = getTasksForDate(date)
  taskDetailVisible.value = true
}

const handleTaskClick = (task: any) => {
  selectedDate.value = task.date
  selectedDateTasks.value = [task]
  taskDetailVisible.value = true
}

// 加载执行监控数据
const loadExecutionData = async () => {
  try {
    const res = await AssayDataAnalysisApi.getExecutionMonitor()
    if (res.data) {
      const monitor: AssayExecutionMonitorVO = res.data

      // 更新核心指标
      coreMetrics.value[0].value = monitor.samplingMonitor.plannedSampling
      coreMetrics.value[1].value = monitor.testingMonitor.testingTasks
      coreMetrics.value[2].value = monitor.auditMonitor.pendingAudits
      coreMetrics.value[3].value = monitor.samplingMonitor.completedSampling

      // 更新执行率
      executionRate.value = Math.round(monitor.overallEfficiency.overallCompletionRate)
    }
  } catch (error) {
    console.error('加载执行监控数据失败:', error)
    ElMessage.error('加载执行监控数据失败')
  }
}

// 页面加载
onMounted(() => {
  // 加载执行监控数据
  loadExecutionData()

  // 使用多层延迟确保DOM完全渲染
  nextTick(() => {
    // 使用更长的延迟确保tab切换完成
    setTimeout(async () => {
      if (currentView.value === 'chart') {
        await initCharts()
        // 启用窗口大小变化监听
        chartManager.enableResizeListener(300)
      }
    }, 500) // 增加延迟时间确保容器渲染完成
  })
})

// 初始化图表
const initCharts = async () => {
  console.log('[ExecutionMonitor] 开始初始化所有图表')

  try {
    await Promise.all([
      initExecutionChart(),
      initTaskChart()
    ])

    console.log('[ExecutionMonitor] 所有图表初始化完成')
  } catch (error) {
    console.error('[ExecutionMonitor] 初始化图表失败:', error)
  }
}

// 初始化执行率图表
const initExecutionChart = async () => {
  if (!executionChartRef.value) {
    console.warn('[ExecutionMonitor] 执行率图表容器未找到')
    return
  }

  const option = {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: ['40%', '70%'],
        center: ['50%', '50%'],
        data: [
          { value: 91, name: '按时完成', itemStyle: { color: '#67C23A' } },
          { value: 6, name: '延期完成', itemStyle: { color: '#E6A23C' } },
          { value: 3, name: '未完成', itemStyle: { color: '#F56C6C' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        },
        label: {
          show: true,
          formatter: '{b}: {c}%'
        }
      }
    ]
  }

  const chart = await ChartUtils.safeInitChart(executionChartRef.value, option)
  chartManager.register('execution', chart)
}

// 初始化任务分配图表
const initTaskChart = async () => {
  if (!taskChartRef.value) {
    console.warn('[ExecutionMonitor] 任务分配图表容器未找到')
    return
  }

  const option = {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'shadow'
      }
    },
    xAxis: {
      type: 'category',
      data: ['张三', '李四', '王五', '赵六', '钱七']
    },
    yAxis: {
      type: 'value',
      name: '任务数'
    },
    series: [
      {
        name: '已分配',
        type: 'bar',
        data: [12, 8, 15, 10, 6],
        itemStyle: { color: '#409EFF' }
      },
      {
        name: '已完成',
        type: 'bar',
        data: [11, 7, 10, 9, 6],
        itemStyle: { color: '#67C23A' }
      }
    ]
  }

  const chart = await ChartUtils.safeInitChart(taskChartRef.value, option)
  chartManager.register('task', chart)
}

// 获取进度条颜色
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#67C23A'
  if (percentage >= 70) return '#E6A23C'
  return '#F56C6C'
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap = {
    'completed': 'success',
    'delayed': 'warning',
    'ongoing': 'info',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签
const getStatusLabel = (status: string) => {
  const labelMap = {
    'completed': '按时完成',
    'delayed': '延期完成',
    'ongoing': '进行中',
    'pending': '未开始'
  }
  return labelMap[status] || '未知'
}

// 筛选
const handleFilter = () => {
  ElMessage.success('筛选功能')
}

// 重置筛选
const resetFilter = () => {
  filterForm.plant = ''
  filterForm.status = ''
  ElMessage.success('已重置筛选条件')
}

// 任务分配
const handleTaskAssignment = () => {
  ElMessage.info('任务分配功能')
}

// 导出报告
const handleExport = () => {
  ElMessage.success('正在导出执行报告...')
}

// 查看详情
const handleViewDetail = (row: any) => {
  executionDetailDialogRef.value?.open(row)
}



// 分页处理
const handleSizeChange = () => {
  // 重新加载数据
}

const handleCurrentChange = () => {
  // 重新加载数据
}

// 窗口大小变化处理
const handleResize = () => {
  if (currentView.value === 'chart') {
    chartManager.resizeAll()
  }
}

// 组件销毁前清理
onBeforeUnmount(() => {
  console.log('[ExecutionMonitor] 组件销毁，清理图表资源')

  // 销毁所有图表和清理监听器
  chartManager.disposeAll()
})


</script>

<style lang="scss" scoped>
.execution-monitor {
  .metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
  }

  .metric-card {
    .metric-content {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .metric-icon {
      flex-shrink: 0;
    }

    .metric-info {
      flex: 1;
    }

    .metric-value {
      font-size: 1.5rem;
      font-weight: bold;
      color: #303133;
      
      .metric-unit {
        font-size: 0.875rem;
        color: #909399;
        margin-left: 0.25rem;
      }
    }

    .metric-label {
      font-size: 0.875rem;
      color: #606266;
      margin: 0.25rem 0;
    }

    .metric-trend {
      display: flex;
      align-items: center;
      gap: 0.25rem;
      font-size: 0.75rem;
      
      .trend-up {
        color: #67C23A;
      }
      
      .trend-down {
        color: #F56C6C;
      }
    }
  }

  .overview-section {
    margin-bottom: 1.5rem;
  }

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-container {
    height: 400px;
    width: 100%;
    min-height: 400px;
    position: relative;

    // 确保容器有明确的尺寸
    &::before {
      content: '';
      display: block;
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      z-index: -1;
    }
  }

  .filter-section {
    margin-bottom: 1rem;
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 0.25rem;
  }

  .filter-form {
    margin: 0;
  }

  .header-actions {
    display: flex;
    align-items: center;
  }

  .pagination-container {
    display: flex;
    justify-content: flex-end;
    margin-top: 1rem;
  }

  // 视图切换
  .view-toggle {
    margin-bottom: 1.5rem;
    text-align: center;
  }

  // 日历视图
  .calendar-view {
    .calendar-controls {
      display: flex;
      align-items: center;
      gap: 1rem;
    }

    .current-month {
      font-size: 1.125rem;
      font-weight: 600;
      color: #303133;
    }

    // 状态图例
    .calendar-legend {
      display: flex;
      justify-content: center;
      gap: 2rem;
      margin-bottom: 1rem;
      padding: 1rem;
      background-color: #f8f9fa;
      border-radius: 0.5rem;

      .legend-item {
        display: flex;
        align-items: center;
        gap: 0.5rem;
        font-size: 0.875rem;
        color: #606266;

        .legend-dot {
          width: 12px;
          height: 12px;
          border-radius: 50%;

          &.task-completed {
            background-color: #67C23A;
          }

          &.task-delayed {
            background-color: #E6A23C;
          }

          &.task-ongoing {
            background-color: #409EFF;
          }

          &.task-pending {
            background-color: #909399;
          }
        }
      }
    }

    .calendar-container {
      .task-calendar {
        ::v-deep(.el-calendar__body) {
          padding: 0;
        }

        ::v-deep(.el-calendar-table) {
          .el-calendar-day {
            height: 100px;
            padding: 6px;
            border: 1px solid #ebeef5;
            transition: all 0.2s;

            &:hover {
              background-color: #f5f7fa;
            }
          }
        }
      }

      .calendar-cell {
        height: 100%;
        display: flex;
        flex-direction: column;
        position: relative;
        padding: 4px;
        transition: all 0.2s;

        &.has-tasks {
          background-color: rgba(64, 158, 255, 0.05);
          border-left: 3px solid #409EFF;

          .date-number {
            color: #409EFF;
            font-weight: 600;
          }
        }

        &.clickable {
          cursor: pointer;

          &:hover {
            background-color: rgba(64, 158, 255, 0.1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }
        }

        .date-number {
          font-size: 16px;
          font-weight: 500;
          color: #303133;
          margin-bottom: 6px;
          align-self: flex-start;
        }

        .task-content {
          flex: 1;
          display: flex;
          flex-direction: column;
          justify-content: space-between;
        }

        .task-indicators {
          display: flex;
          flex-wrap: wrap;
          gap: 3px;
          align-items: center;
          margin-bottom: 6px;

          .task-dot {
            width: 10px;
            height: 10px;
            border-radius: 50%;
            cursor: pointer;
            transition: all 0.2s;
            border: 1px solid rgba(255, 255, 255, 0.8);
            box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

            &:hover {
              transform: scale(1.3);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
              z-index: 10;
            }

            &.task-completed {
              background-color: #67C23A;
              border-color: #5daf34;
            }

            &.task-delayed {
              background-color: #E6A23C;
              border-color: #cf9236;
            }

            &.task-ongoing {
              background-color: #409EFF;
              border-color: #3a8ee6;
            }

            &.task-pending {
              background-color: #909399;
              border-color: #82848a;
            }
          }

          .task-more {
            font-size: 9px;
            color: #606266;
            cursor: pointer;
            padding: 1px 3px;
            border-radius: 2px;
            background-color: #f0f2f5;
            border: 1px solid #d3d4d6;
            transition: all 0.2s;
            line-height: 1;

            &:hover {
              background-color: #e4e7ed;
              color: #409EFF;
              border-color: #409EFF;
            }
          }
        }

        .task-summary {
          margin-top: auto;

          .task-count {
            font-size: 10px;
            color: #606266;
            text-align: center;
            margin-bottom: 2px;
          }

          .task-progress {
            display: flex;
            align-items: center;
            gap: 4px;

            .progress-bar {
              flex: 1;
              height: 4px;
              background-color: #f0f2f5;
              border-radius: 2px;
              overflow: hidden;

              .progress-fill {
                height: 100%;
                background-color: #67C23A;
                transition: width 0.3s ease;
              }
            }

            .progress-text {
              font-size: 8px;
              color: #909399;
              white-space: nowrap;
            }
          }
        }
      }
    }
  }

  // 任务详情对话框
  .task-detail-list {
    max-height: 500px;
    overflow-y: auto;

    .task-detail-card {
      margin-bottom: 1rem;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .task-detail-content {
      .task-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        h4 {
          margin: 0;
          color: #303133;
        }
      }

      .task-info {
        .info-item {
          display: flex;
          margin-bottom: 0.5rem;

          .label {
            font-weight: 500;
            color: #606266;
            min-width: 80px;
          }
        }

        .progress-info {
          display: flex;
          align-items: center;
          gap: 1rem;
          margin-top: 1rem;

          .label {
            font-weight: 500;
            color: #606266;
          }
        }
      }
    }
  }
}
</style>

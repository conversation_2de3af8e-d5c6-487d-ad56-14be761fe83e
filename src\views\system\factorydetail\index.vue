<template>
  <!-- 数据统计卡片 -->
  <ContentWrap>
    <el-row :gutter="24" class="mb-4">
      <el-col :span="12">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon total">
              <Icon icon="ep:office-building" size="2rem" />
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ statsData.totalFactories || 0 }}</div>
              <div class="stats-label">水厂总数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon capacity">
              <Icon icon="ep:data-analysis" size="2rem" />
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ formatCapacity(statsData.averageCapacity) }}</div>
              <div class="stats-label">平均处理能力</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </ContentWrap>

  <!-- 搜索区域 -->
  <ContentWrap>
    <el-form
      class="-mb-0.9375rem"
      :model="queryParams"
      ref="queryFormRef"
      :inline="true"
      label-width="80px"
    >
      <el-form-item label="水厂简称" prop="stationName">
        <el-input
          v-model="queryParams.stationName"
          placeholder="请输入水厂简称"
          clearable
          @keyup.enter="handleQuery"
          class="!w-15rem"
        />
      </el-form-item>
      <el-form-item label="厂站类型" prop="type">
        <el-select
          v-model="queryParams.type"
          placeholder="请选择厂站类型"
          clearable
          class="!w-15rem"
        >
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FACTORY_DETAIL_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="规模等级" prop="stpScale">
        <el-select
          v-model="queryParams.stpScale"
          placeholder="请选择规模等级"
          clearable
          class="!w-15rem"
        >
          <el-option
            v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_FACTORY_DETAIL_SCALE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>

      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" />搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" />重置
        </el-button>
        <el-button
          type="primary"
          plain
          @click="openForm('create')"
          v-hasPermi="['system:factory-detail:create']"
        >
          <Icon icon="ep:plus" /> 新增
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 数据表格 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" stripe>
      <el-table-column label="序号" type="index" width="60" align="center" />
      <el-table-column label="水厂简称" align="center" prop="stationName" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="站点编码" align="center" prop="stationCode" :show-overflow-tooltip="true" min-width="120" />
      <el-table-column label="厂站类型" align="center" prop="type" min-width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_FACTORY_DETAIL_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="处理工艺" align="center" prop="trmPro" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="设计规模" align="center" prop="desCap" min-width="120">
        <template #default="scope">
          <span v-if="scope.row.desCap">{{ scope.row.desCap.toLocaleString() }} m³/d</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="重点项目" align="center" prop="blStp" min-width="100">
        <template #default="scope">
          <el-tag v-if="scope.row.blStp" type="primary" size="small">是</el-tag>
          <el-tag v-else type="info" size="small">否</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="规模等级" align="center" prop="stpScale" min-width="100">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_FACTORY_DETAIL_SCALE" :value="scope.row.stpScale" />
        </template>
      </el-table-column>
      <el-table-column label="建设单位" align="center" prop="stpUnit" :show-overflow-tooltip="true" min-width="150" />
      <el-table-column label="投运时间" align="center" prop="stpDate" min-width="120" />
      <el-table-column label="操作" align="center" width="160" fixed="right">
        <template #default="scope">
          <div class="flex items-center justify-center gap-2">
            <el-button
              type="primary"
              link
              @click="openForm('update', scope.row.id)"
              v-hasPermi="['system:factory-detail:update']"
            >
              <Icon icon="ep:edit" />修改
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(scope.row.id)"
              v-hasPermi="['system:factory-detail:delete']"
            >
              <Icon icon="ep:delete" />删除
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页组件 -->
    <Pagination
      :total="total"
      v-model:page="queryParams.currentPage"
      v-model:limit="queryParams.pageSize"
      @pagination="getList"
    />
  </ContentWrap>

  <!-- 添加或修改水厂档案对话框 -->
  <FactoryDetailForm ref="formRef" @success="getList" />
</template>

<script lang="ts" setup>
import * as FactoryDetailApi from '@/api/system/factorydetail'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { checkPermi } from '@/utils/permission'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { DICT_TYPE, getStrDictOptions, getIntDictOptions, getBoolDictOptions } from '@/utils/dict'
import FactoryDetailForm from './FactoryDetailForm.vue'

defineOptions({ name: 'SystemFactoryDetail' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const exportLoading = ref(false) // 导出的加载中
const statsData = ref({
  totalFactories: 0,
  averageCapacity: 0
}) // 统计数据

// 查询参数
const queryParams = reactive({
  currentPage: 1,
  pageSize: 10,
  stationName: undefined,
  type: undefined,
  trmPro: undefined,
  stpUnit: undefined,
  stpScale: undefined,
  isUse: undefined
})

const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await FactoryDetailApi.getFactoryDetailPage(queryParams)
    list.value = data.records
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 获取统计数据 */
const getStats = async () => {
  try {
    statsData.value = await FactoryDetailApi.getFactoryDetailStats()
  } catch (error) {
    console.error('获取统计数据失败:', error)
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.currentPage = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value?.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await FactoryDetailApi.deleteFactoryDetail(id)
    message.success(t('common.delSuccess'))
    // 刷新列表
    await getList()
    // 刷新统计数据
    await getStats()
  } catch {}
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await FactoryDetailApi.exportFactoryDetail(queryParams)
    download.excel(data, '水厂档案.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 格式化处理能力显示 */
const formatCapacity = (capacity: number) => {
  if (!capacity) return '-'
  if (capacity >= 10000) {
    return (capacity / 10000).toFixed(1) + '万m³/d'
  }
  return capacity.toLocaleString() + 'm³/d'
}



/** 初始化 **/
onMounted(async () => {
  await getList()
  await getStats()
})
</script>

<style scoped lang="scss">
// 统计卡片样式
.stats-card {
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.06);

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    transform: translateY(-2px);
  }

  :deep(.el-card__body) {
    padding: 16px 20px;
  }
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

  &.total {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  &.capacity {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    color: #409eff;
  }
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: 700;
  color: #303133;
  line-height: 1.2;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #606266;
  font-weight: 500;
}

// 表格样式优化
:deep(.el-table) {
  .el-table__header {
    th {
      background-color: #f8f9fa;
      color: #495057;
      font-weight: 600;
    }
  }

  .el-table__row {
    &:hover {
      background-color: #f8f9fa;
    }
  }
}

// 搜索表单样式
:deep(.el-form--inline) {
  .el-form-item {
    margin-bottom: 12px;
  }
}

// 状态标签样式
.el-tag {
  border-radius: 4px;
  font-weight: 500;
}

// 操作按钮样式
:deep(.el-button.is-link) {
  padding: 4px 8px;
  font-size: 14px;
}
</style>

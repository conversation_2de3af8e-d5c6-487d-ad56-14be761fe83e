import request from '@/config/axios'

// ==================== 类型定义 ====================

/** 水厂详细信息查询参数 */
export interface FactoryDetailPageReqVO {
  currentPage?: number
  pageSize?: number
  stationCode?: string
  name?: string
  type?: string
  stpUnit?: string
  stpDate?: string
  stpRange?: string
  trmPro?: string
  stpScale?: number
  stpLoad?: string
  rwTrtm?: string
  isUse?: boolean
  isWarning?: boolean
  target?: string
  isWeb?: boolean
  userId?: string
  blStp?: boolean
  isMoreStation?: boolean
  isMrg?: boolean
}

/** 水厂详细信息响应对象 */
export interface FactoryDetailRespVO {
  id: number
  stationName?: string
  stationCode?: string
  name?: string
  type?: string
  level?: number
  longitude?: number
  latitude?: number
  region?: string
  stpLoc?: string
  stpUnit?: string
  stpDate?: string
  stpRange?: string
  trmPro?: string
  desCap?: number
  stpScale?: number
  rwOut?: number
  rwWq?: string
  blProject?: string
  stpLoad?: string
  rwTrtm?: string
  isActive?: boolean
  deptId?: number
  isUse?: boolean
  isWarning?: boolean
  target?: string
  isWeb?: boolean
  userId?: string
  blStp?: boolean
  isMoreStation?: boolean
  isMrg?: boolean
  createTime?: Date
  updateTime?: Date
}

/** 水厂详细信息保存请求对象 */
export interface FactoryDetailSaveReqVO {
  id?: number
  stationName?: string
  stationCode?: string
  name?: string
  type?: string
  level?: number
  longitude?: number
  latitude?: number
  region?: string
  stpLoc?: string
  stpUnit?: string
  stpDate?: string
  stpRange?: string
  trmPro?: string
  desCap?: number
  stpScale?: number
  rwOut?: number
  rwWq?: string
  blProject?: string
  stpLoad?: string
  rwTrtm?: string
  isActive?: boolean
  isUse?: boolean
  isWarning?: boolean
  target?: string
  isWeb?: boolean
  userId?: string
  blStp?: boolean
  isMoreStation?: boolean
  isMrg?: boolean
}

/** 水厂统计信息响应对象 */
export interface FactoryStatusCountRespVO {
  totalFactories: number
  runningFactories: number
  warningFactories: number
  averageCapacity: number
}

// ==================== API 接口 ====================

/** 分页查询水厂详细信息 */
export const getFactoryDetailPage = (params: FactoryDetailPageReqVO) => {
  return request.post({ url: '/system/factory-detail/page', data: params })
}

/** 根据ID查询水厂详细信息 */
export const getFactoryDetail = (id: number) => {
  return request.get({ url: `/system/factory-detail/getById/${id}` })
}

/** 新增水厂详细信息 */
export const createFactoryDetail = (data: FactoryDetailSaveReqVO) => {
  return request.post({ url: '/system/factory-detail/create', data })
}

/** 更新水厂详细信息 */
export const updateFactoryDetail = (data: FactoryDetailSaveReqVO) => {
  return request.put({ url: '/system/factory-detail/update', data })
}

/** 删除水厂详细信息 */
export const deleteFactoryDetail = (id: number) => {
  return request.delete({ url: `/system/factory-detail/delete?id=${id}` })
}

/** 获取水厂统计信息 */
export const getFactoryDetailStats = () => {
  return request.get({ url: '/system/factory-detail/stats' })
}

/** 导出水厂详细信息 */
export const exportFactoryDetail = (params: FactoryDetailPageReqVO) => {
  return request.download({ url: '/system/factory-detail/export', data: params })
}

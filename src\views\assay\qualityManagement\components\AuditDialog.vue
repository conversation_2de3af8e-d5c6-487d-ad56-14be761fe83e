<template>
  <Dialog v-model="dialogVisible" title="数据审核">
    <!-- 基础信息卡片 -->
    <el-card class="info-card" shadow="never">
      <template #header>
        <div class="card-header">
          <span class="card-title">基础信息</span>
          <el-tag v-if="isExceeded" type="danger" size="small">
            <el-icon><Warning /></el-icon>超标数据
          </el-tag>
          <el-tag v-else-if="isWarning" type="warning" size="small">
            <el-icon><Warning /></el-icon>预警数据
          </el-tag>
          <el-tag v-else type="success" size="small">
            <el-icon><Check /></el-icon>正常数据
          </el-tag>
        </div>
      </template>

      <el-descriptions :column="2" border>
        <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
        <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
        <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
        <el-descriptions-item label="采样日期">{{ formData.samplingDate }}</el-descriptions-item>
        <el-descriptions-item label="检测值">
          <div class="test-value-display">
            <span :class="{ 'text-danger': isExceeded, 'text-warning': isWarning }">
              {{ formData.testValue }} {{ formData.unit }}
            </span>
            <el-progress
              v-if="standardValue !== '/'"
              :percentage="getValuePercentage()"
              :color="getProgressColor()"
              :stroke-width="6"
              class="value-progress" />
          </div>
        </el-descriptions-item>
        <el-descriptions-item label="标准限值">
          {{ standardValue }} {{ formData.unit }}
        </el-descriptions-item>
        <el-descriptions-item label="检测日期">{{ formData.testDate }}</el-descriptions-item>
        <el-descriptions-item label="检测人">{{ formData.tester }}</el-descriptions-item>
        <el-descriptions-item label="检测方法">{{ formData.method }}</el-descriptions-item>
        <el-descriptions-item label="检测仪器">{{ formData.instrument }}</el-descriptions-item>
        <el-descriptions-item label="备注" :span="2">{{ formData.remark || '无' }}</el-descriptions-item>
      </el-descriptions>
    </el-card>
    
    <!-- 审核表单卡片 -->
    <el-card class="audit-card" shadow="never" style="margin-top: 20px;">
      <template #header>
        <div class="card-header">
          <span class="card-title">审核信息</span>
          <el-tag type="info" size="small">待审核</el-tag>
        </div>
      </template>

      <el-form
        ref="formRef"
        v-loading="formLoading"
        :model="auditForm"
        :rules="formRules"
        label-width="100px"
      >
        <el-form-item label="审核结果" prop="result">
          <el-radio-group v-model="auditForm.result">
            <el-radio label="pass">
              <el-icon><Check /></el-icon>通过
            </el-radio>
            <el-radio label="reject">
              <el-icon><Close /></el-icon>退回
            </el-radio>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="审核意见" prop="comment">
          <el-input
            v-model="auditForm.comment"
            type="textarea"
            :rows="4"
            placeholder="请输入审核意见"
            :maxlength="200"
            show-word-limit />
        </el-form-item>

        <el-form-item v-if="isExceeded" label="超标处理" prop="exceedAction">
          <el-select v-model="auditForm.exceedAction" placeholder="请选择超标处理方式" style="width: 100%;">
            <el-option label="标记并通知" value="mark">
              <div class="option-with-icon">
                <el-icon><Bell /></el-icon>
                <span>标记并通知</span>
              </div>
            </el-option>
            <el-option label="安排复检" value="recheck">
              <div class="option-with-icon">
                <el-icon><RefreshRight /></el-icon>
                <span>安排复检</span>
              </div>
            </el-option>
            <el-option label="上报管理部门" value="report">
              <div class="option-with-icon">
                <el-icon><Upload /></el-icon>
                <span>上报管理部门</span>
              </div>
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item v-if="auditForm.result === 'pass'" label="质量评分" prop="qualityScore">
          <el-rate
            v-model="auditForm.qualityScore"
            :colors="['#F56C6C', '#E6A23C', '#67C23A']"
            :texts="['较差', '一般', '良好', '优秀', '极佳']"
            show-text />
        </el-form-item>
      </el-form>

      <!-- 历史审核记录 -->
      <div v-if="auditHistory.length > 0" class="audit-history">
        <div class="history-header">
          <el-icon><Timer /></el-icon>
          <span>历史审核记录</span>
        </div>
        <el-timeline>
          <el-timeline-item
            v-for="(history, index) in auditHistory"
            :key="index"
            :type="history.result === 'pass' ? 'success' : 'danger'"
            :timestamp="history.auditTime"
          >
            <div class="history-item">
              <div class="history-title">
                {{ history.result === 'pass' ? '审核通过' : '审核退回' }}
                <el-tag size="small" :type="history.result === 'pass' ? 'success' : 'danger'">
                  {{ history.result === 'pass' ? '通过' : '退回' }}
                </el-tag>
              </div>
              <div class="history-content">{{ history.comment }}</div>
              <div class="history-footer">
                <span>审核人: {{ history.auditor }}</span>
              </div>
            </div>
          </el-timeline-item>
        </el-timeline>
      </div>
    </el-card>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'
import { Check, Close, Bell, RefreshRight, Upload, Timer, Warning } from '@element-plus/icons-vue'

defineOptions({ name: 'AuditDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

interface FormData {
  id?: number;
  sampleCode: string;
  testItem: string;
  samplingPoint: string;
  samplingDate: string;
  testValue: string;
  unit: string;
  testDate: string;
  tester: string;
  method: string;
  instrument: string;
  remark: string;
}

interface AuditForm {
  result: 'pass' | 'reject';
  comment: string;
  exceedAction: string;
  qualityScore: number;
}

interface AuditHistoryItem {
  result: 'pass' | 'reject';
  comment: string;
  auditor: string;
  auditTime: string;
}

// 表单数据
const formData = ref<FormData>({
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  samplingDate: '',
  testValue: '',
  unit: '',
  testDate: '',
  tester: '',
  method: '',
  instrument: '',
  remark: ''
})

// 审核表单
const auditForm = ref<AuditForm>({
  result: 'pass',
  comment: '',
  exceedAction: '',
  qualityScore: 5
})

// 审核历史记录
const auditHistory = ref<AuditHistoryItem[]>([])

// 表单校验规则
const formRules = reactive<FormRules>({
  result: [{ required: true, message: '请选择审核结果', trigger: 'change' }],
  comment: [{ required: true, message: '请输入审核意见', trigger: 'blur' }],
  exceedAction: [{ required: true, message: '请选择超标处理方式', trigger: 'change' }]
})

// 计算标准限值
const standardValue = computed((): string => {
  switch (formData.value.testItem) {
    case 'COD':
      return '50'
    case 'BOD5':
      return '10'
    case '氨氮':
      return '5'
    case '总磷':
      return '0.5'
    case '总氮':
      return '15'
    default:
      return '/'
  }
})

// 计算是否超标
const isExceeded = computed((): boolean => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/') {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  return testValue > stdValue
})

// 计算是否警告（接近超标）
const isWarning = computed((): boolean => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/' || isExceeded.value) {
    return false
  }
  
  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)
  
  return testValue > stdValue * 0.9
})

// 获取检测值百分比
const getValuePercentage = (): number => {
  if (!formData.value.testValue || !standardValue.value || standardValue.value === '/') {
    return 0
  }

  const testValue = parseFloat(formData.value.testValue)
  const stdValue = parseFloat(standardValue.value)

  return Math.min((testValue / stdValue) * 100, 100)
}

// 获取进度条颜色
const getProgressColor = (): string => {
  const percentage = getValuePercentage()
  if (percentage > 100) return '#f56c6c'
  if (percentage > 90) return '#e6a23c'
  return '#67c23a'
}

// 获取审核历史
const fetchAuditHistory = async (dataId: number) => {
  // 模拟API调用
  auditHistory.value = [
    {
      result: 'reject',
      comment: '数据异常，建议重新检测',
      auditor: '张三',
      auditTime: '2023-07-10 14:30:00'
    },
    {
      result: 'pass',
      comment: '复检后数据正常，可以入库',
      auditor: '李四',
      auditTime: '2023-07-11 09:15:00'
    }
  ]
}

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any): Promise<void> => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    samplingPoint: data.samplingPoint,
    samplingDate: data.samplingDate || data.testDate,
    testValue: data.testValue || '',
    unit: data.unit || 'mg/L',
    testDate: data.testDate || '',
    tester: data.tester || '',
    method: data.method || '重铬酸钾法', // 模拟数据
    instrument: data.instrument || '分光光度计A', // 模拟数据
    remark: data.remark || ''
  }
  
  // 重置审核表单
  auditForm.value = {
    result: 'pass',
    comment: '',
    exceedAction: isExceeded.value ? 'mark' : '',
    qualityScore: 5
  }

  // 模拟获取审核历史
  fetchAuditHistory(data.id)
}
defineExpose({ open })

// 提交表单
const submitForm = async (): Promise<void> => {
  // 表单校验
  if (!formRef.value) return
  
  // 如果超标但未选择处理方式，则提示
  if (isExceeded.value && !auditForm.value.exceedAction) {
    ElMessage.warning('请选择超标处理方式')
    return
  }
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    ElMessage.success(`审核${auditForm.value.result === 'pass' ? '通过' : '退回'}成功`)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
/* 卡片样式 */
.info-card, .audit-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 检测值显示样式 */
.test-value-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.value-progress {
  width: 200px;
}

.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}

/* 选项样式 */
.option-with-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 审核历史样式 */
.audit-history {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.history-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.history-item {
  padding: 12px;
  background-color: #f8f9fa;
  border-radius: 6px;
  border-left: 3px solid #409eff;
}

.history-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  margin-bottom: 8px;
}

.history-content {
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.history-footer {
  font-size: 12px;
  color: #909399;
}

/* 表单样式优化 */
.el-form-item {
  margin-bottom: 20px;
}

.el-radio {
  margin-right: 20px;
}

.el-rate {
  margin-top: 4px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .value-progress {
    width: 100%;
  }

  .card-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
</style>
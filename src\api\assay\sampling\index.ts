import request from '@/config/axios'
import { useAppStore } from '@/store/modules/app'

// ==================== 类型定义 ====================

// 采样执行相关类型
export interface AssaySamplingExecutionVO {
  id?: number
  factoryId: number
  taskId: number
  name: string
  samplingPoint: string
  testItem: string
  planDate: string
  executor: string
  status: 'pending' | 'confirmed' | 'completed' | 'abnormal' | 'submitted'
  confirmTime?: string
  completeTime?: string
  abnormalTime?: string
  actualSampleAppearance?: string
  actualSampleQuantity?: number
  sampleStatus?: string
  abnormalReason?: string
  handleMeasures?: string
  remark?: string
  createTime?: string
  updateTime?: string
  // 预期采样信息字段（从任务表获取）
  expectedSampleQuantity?: number
  expectedSampleNature?: string
  expectedSampleAppearance?: string
  expectedSupernatant?: string
  samplingInstructions?: string
}

// 样品管理相关类型
export interface AssaySampleManagementVO {
  id?: number
  factoryId: number
  sampleCode: string
  executionId: number
  sampleType: string
  volume: number
  appearance: string
  preservationMethod: string
  storageLocation: string
  storageTemperature: number
  expiryDate: string
  testItem: number
  samplingPersonId: number
  samplingDate: string
  status: 'stored' | 'testing' | 'completed' | 'destroyed'
  destroyReason?: string
  destroyTime?: string
  destroyOperatorId?: number
  remark?: string
  createTime?: string
  updateTime?: string
}

// 送检记录相关类型
export interface AssaySubmissionRecordVO {
  id?: number
  factoryId: number
  submissionCode: string
  executionId: number
  urgency: 'normal' | 'urgent'
  specialRequirements?: string
  submissionDate: string
  submitterName: string
  submissionLab: string
  status: 'pending' | 'submitted' | 'received' | 'completed'
  remark?: string
  createTime?: string
  updateTime?: string
}

// 请求参数类型
export interface SamplingExecutionConfirmReqVO {
  id: number
  actualLocation?: string
  actualSampleAppearance?: string
  actualSampleQuantity?: number
  sampleStatus?: string
  confirmTime: string
}

export interface SamplingExecutionCompleteReqVO {
  id: number
  actualSampleAppearance?: string
  actualSampleQuantity?: number
  sampleStatus?: string
  completeTime: string
}

export interface SamplingExecutionAbnormalReqVO {
  id: number
  abnormalReason?: string
  handleMeasures?: string
}

export interface GenerateInspectionFormReqVO {
  id: number
  urgency?: 'normal' | 'urgent'
  specialRequirements?: string
  remark?: string
}

export interface SampleManagementCreateReqVO {
  factoryId: number
  sampleCode: string
  executionId: number
  sampleType: string
  volume: number
  appearance: string
  preservationMethod: string
  storageLocation: string
  storageTemperature: number
  expiryDate: string
  testItem: number
  samplingPersonId: number
  samplingDate: string
  remark?: string
}

export interface SampleManagementUpdateReqVO {
  id: number
  factoryId: number
  sampleCode: string
  executionId: number
  sampleType: string
  volume: number
  appearance: string
  preservationMethod: string
  storageLocation: string
  storageTemperature: number
  expiryDate: string
  testItem: number
  samplingPersonId: number
  samplingDate: string
  status: 'stored' | 'testing' | 'completed' | 'destroyed'
  destroyReason?: string
  destroyTime?: string
  destroyOperatorId?: number
  remark?: string
}

export interface SubmissionRecordCreateReqVO {
  factoryId: number
  executionId: number
  urgency: 'normal' | 'urgent'
  specialRequirements?: string
  submissionLab: string
  remark?: string
}

// ==================== API 接口 ====================

/**
 * 获取全局水厂ID
 */
const getFactoryId = (): number => {
  const appStore = useAppStore()
  return appStore.currentStation?.id || 1
}

/**
 * 采样执行管理模块API
 */
export const AssaySamplingApi = {
  // ==================== 采样执行管理 ====================
  
  /**
   * 确认采样
   */
  confirmSampling: async (data: SamplingExecutionConfirmReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-execution/confirm',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 完成采样
   */
  completeSampling: async (data: SamplingExecutionCompleteReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-execution/complete',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 记录异常
   */
  recordAbnormal: async (data: SamplingExecutionAbnormalReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-execution/record-abnormal',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 生成送检单
   */
  generateInspectionForm: async (data: GenerateInspectionFormReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-execution/generate-inspection-form',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取采样任务分页列表
   */
  getSamplingExecutionPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-execution/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取采样执行详情
   */
  getSamplingExecution: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-execution/get',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 样品管理 ====================
  
  /**
   * 创建样品管理记录
   */
  createSampleManagement: async (data: SampleManagementCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sample-management/create',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 更新样品管理记录
   */
  updateSampleManagement: async (data: SampleManagementUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-sample-management/update',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 删除样品管理记录
   */
  deleteSampleManagement: async (id: number) => {
    return await request.deleteOriginal({
      url: '/assay/assay-sample-management/delete',
      params: { 
        id, 
        factoryId: getFactoryId() 
      }
    })
  },

  /**
   * 获取样品管理分页列表
   */
  getSampleManagementPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-sample-management/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取样品管理详情
   */
  getSampleManagement: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sample-management/get',
      params: { 
        id, 
        factoryId: getFactoryId() 
      }
    })
  },

  // ==================== 送检记录管理 ====================
  
  /**
   * 创建送检记录
   */
  createSubmissionRecord: async (data: SubmissionRecordCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-submission-record/create',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 送检操作
   */
  submitRecord: async (id: number) => {
    return await request.postOriginal({
      url: '/assay/assay-submission-record/submit',
      data: { 
        id, 
        factoryId: getFactoryId() 
      }
    })
  },

  /**
   * 获取送检记录分页列表
   */
  getSubmissionRecordPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-submission-record/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取送检记录详情
   */
  getSubmissionRecord: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/assay-submission-record/get',
      params: { 
        id, 
        factoryId: getFactoryId() 
      }
    })
  }
}

import request from '@/config/axios'

export interface EnterpriseVO {
  id: number | undefined
  name: string
  province: string
  city: string
  registeredCapital: number
  servicePopulation: number
  serviceArea: number
  employeeCount: number
  plantCount: number
  sewageTreatmentDesign: number
  sewageTreatmentProduction: number
  recycledWaterDesign: number
  recycledWaterProduction: number
  waterSupplyDesign: number
  waterSupplyProduction: number
  sludgeOutput: number
  renewableEnergyGeneration: number
  pipelineLength: number
  riskAssessment: string
  remark: string
  createTime: Date
}

export interface EnterprisePageReqVO extends PageParam {
  name?: string
  province?: string
  city?: string
  riskAssessment?: string
}

// 查询企业档案分页
export const getEnterprisePage = (params: EnterprisePageReqVO) => {
  return request.get({ url: '/system/enterprise/page', params })
}

// 查询企业档案详情
export const getEnterprise = (id: number) => {
  return request.get({ url: '/system/enterprise/get?id=' + id })
}

// 新增企业档案
export const createEnterprise = (data: EnterpriseVO) => {
  return request.post({ url: '/system/enterprise/create', data })
}

// 修改企业档案
export const updateEnterprise = (data: EnterpriseVO) => {
  return request.put({ url: '/system/enterprise/update', data })
}

// 删除企业档案
export const deleteEnterprise = (id: number) => {
  return request.delete({ url: '/system/enterprise/delete?id=' + id })
}

// 导出企业档案 Excel
export const exportEnterprise = (params: EnterprisePageReqVO) => {
  return request.download({ url: '/system/enterprise/export-excel', params })
}

// 获取企业档案精简信息列表
export const getEnterpriseSimpleList = () => {
  return request.get({ url: '/system/enterprise/simple-list' })
}

// 获取企业统计信息
export const getEnterpriseStatistics = () => {
  return request.get({ url: '/system/enterprise/statistics' })
}

// 批量导入企业档案
export const importEnterprise = (file: File) => {
  const formData = new FormData()
  formData.append('file', file)
  return request.upload({ url: '/system/enterprise/import', data: formData })
}

// 下载企业档案导入模板
export const downloadEnterpriseTemplate = () => {
  return request.download({ url: '/system/enterprise/get-import-template' })
}

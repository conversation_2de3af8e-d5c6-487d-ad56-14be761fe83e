<template>
  <Dialog v-model="dialogVisible" title="修正追踪" width="900px">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
      <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
      <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
      <el-descriptions-item label="原检测值">
        <span class="text-danger">
          {{ formData.originalValue }} {{ formData.unit }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="审核结果">
        <el-tag type="danger">已退回</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="退回原因">
        {{ formData.auditComment }}
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">修正进度追踪</el-divider>
    
    <el-steps :active="currentStep" finish-status="success" align-center>
      <el-step title="数据退回" description="审核员退回数据"/>
      <el-step title="重新检测" description="安排重新检测"/>
      <el-step title="数据录入" description="录入新的检测数据"/>
      <el-step title="重新审核" description="提交重新审核"/>
      <el-step title="修正完成" description="审核通过"/>
    </el-steps>
    
    <el-divider content-position="center">修正记录</el-divider>
    
    <el-table :data="revisionRecords" border style="width: 100%">
      <el-table-column prop="step" label="步骤" width="120">
        <template #default="{ row }">
          <el-tag :type="getStepTagType(row.status)">
            {{ row.step }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="description" label="操作描述" min-width="200" />
      <el-table-column prop="operator" label="操作人" width="100" />
      <el-table-column prop="operateTime" label="操作时间" width="160" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <el-tag :type="getStatusTagType(row.status)">
            {{ getStatusLabel(row.status) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" min-width="150" show-overflow-tooltip />
    </el-table>
    
    <el-divider content-position="center">当前状态</el-divider>
    
    <el-alert
      :title="getCurrentStatusTitle()"
      :description="getCurrentStatusDescription()"
      :type="getCurrentStatusType()"
      show-icon
      :closable="false"
    />
    
    <div v-if="formData.revisionStatus === 'pending_retest'" class="mt-4">
      <el-card>
        <template #header>
          <span>安排重新检测</span>
        </template>
        <el-form :model="retestForm" label-width="100px">
          <el-form-item label="检测人员">
            <el-select v-model="retestForm.tester" placeholder="请选择检测人员">
              <el-option label="张三" value="zhangsan" />
              <el-option label="李四" value="lisi" />
              <el-option label="王五" value="wangwu" />
            </el-select>
          </el-form-item>
          <el-form-item label="预计完成时间">
            <el-date-picker
              v-model="retestForm.expectedTime"
              type="datetime"
              placeholder="选择预计完成时间"
            />
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="retestForm.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleArrangeRetest">安排重检</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
      <el-button v-if="formData.revisionStatus === 'completed'" type="success" @click="handleViewResult">
        查看最终结果
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { Dialog } from '@/components/Dialog'
import { ElMessage } from 'element-plus'

defineOptions({ name: 'RevisionTrackDialog' })

// 对话框显示状态
const dialogVisible = ref(false)

// 表单数据
const formData = reactive({
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  originalValue: '',
  unit: '',
  auditComment: '',
  revisionStatus: 'pending_retest' // pending_retest, retesting, pending_entry, pending_audit, completed
})

// 重检表单
const retestForm = reactive({
  tester: '',
  expectedTime: '',
  remark: ''
})

// 修正记录
const revisionRecords = ref([
  {
    step: '数据退回',
    description: '审核员退回数据，原因：数据异常，需要重新检测',
    operator: '审核员',
    operateTime: '2023-07-20 14:30:00',
    status: 'completed',
    remark: '数据超出正常范围'
  },
  {
    step: '安排重检',
    description: '质量管理员安排重新检测',
    operator: '质量管理员',
    operateTime: '2023-07-20 15:00:00',
    status: 'pending',
    remark: '已通知检测人员'
  }
])

// 当前步骤
const currentStep = computed(() => {
  const statusStepMap = {
    'pending_retest': 1,
    'retesting': 2,
    'pending_entry': 2,
    'pending_audit': 3,
    'completed': 4
  }
  return statusStepMap[formData.revisionStatus] || 0
})

// 打开对话框
const open = (data: any) => {
  Object.assign(formData, data)
  
  // 生成修正记录（实际项目中应该从API获取）
  generateRevisionRecords(data)
  
  dialogVisible.value = true
}

// 生成修正记录
const generateRevisionRecords = (data: any) => {
  const records = [
    {
      step: '数据退回',
      description: `审核员退回数据，原因：${data.auditComment}`,
      operator: data.auditor || '审核员',
      operateTime: data.auditDate || '2023-07-20 14:30:00',
      status: 'completed',
      remark: data.auditComment
    }
  ]
  
  if (data.revisionStatus !== 'pending_retest') {
    records.push({
      step: '安排重检',
      description: '质量管理员安排重新检测',
      operator: '质量管理员',
      operateTime: '2023-07-20 15:00:00',
      status: 'completed',
      remark: '已通知检测人员'
    })
  }
  
  if (data.revisionStatus === 'retesting' || data.revisionStatus === 'pending_entry') {
    records.push({
      step: '重新检测',
      description: '检测人员进行重新检测',
      operator: data.retester || '检测员',
      operateTime: '2023-07-21 09:00:00',
      status: data.revisionStatus === 'retesting' ? 'processing' : 'completed',
      remark: '重新检测中'
    })
  }
  
  revisionRecords.value = records
}

// 安排重检
const handleArrangeRetest = () => {
  if (!retestForm.tester) {
    ElMessage.warning('请选择检测人员')
    return
  }
  
  ElMessage.success('重检安排成功')
  
  // 更新状态
  formData.revisionStatus = 'retesting'
  
  // 添加记录
  revisionRecords.value.push({
    step: '安排重检',
    description: `安排${retestForm.tester}进行重新检测`,
    operator: '质量管理员',
    operateTime: new Date().toLocaleString(),
    status: 'completed',
    remark: retestForm.remark
  })
  
  // 发送成功事件
  emit('success')
}

// 查看最终结果
const handleViewResult = () => {
  ElMessage.info('查看最终结果功能')
}

// 获取当前状态标题
const getCurrentStatusTitle = () => {
  const titleMap = {
    'pending_retest': '等待安排重检',
    'retesting': '重新检测中',
    'pending_entry': '等待数据录入',
    'pending_audit': '等待重新审核',
    'completed': '修正完成'
  }
  return titleMap[formData.revisionStatus] || '未知状态'
}

// 获取当前状态描述
const getCurrentStatusDescription = () => {
  const descMap = {
    'pending_retest': '数据已被退回，需要安排重新检测',
    'retesting': '检测人员正在进行重新检测',
    'pending_entry': '重新检测完成，等待录入新的检测数据',
    'pending_audit': '新数据已录入，等待审核员重新审核',
    'completed': '修正流程已完成，数据审核通过'
  }
  return descMap[formData.revisionStatus] || '状态未知'
}

// 获取当前状态类型
const getCurrentStatusType = () => {
  const typeMap = {
    'pending_retest': 'warning',
    'retesting': 'info',
    'pending_entry': 'info',
    'pending_audit': 'warning',
    'completed': 'success'
  }
  return typeMap[formData.revisionStatus] || 'info'
}

// 获取步骤标签类型
const getStepTagType = (status: string) => {
  const typeMap = {
    'completed': 'success',
    'processing': 'warning',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签类型
const getStatusTagType = (status: string) => {
  const typeMap = {
    'completed': 'success',
    'processing': 'warning',
    'pending': 'info'
  }
  return typeMap[status] || 'info'
}

// 获取状态标签文本
const getStatusLabel = (status: string) => {
  const labelMap = {
    'completed': '已完成',
    'processing': '进行中',
    'pending': '待处理'
  }
  return labelMap[status] || '未知'
}

// 定义事件
const emit = defineEmits(['success'])

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.text-danger {
  color: #f56c6c;
}

.mt-4 {
  margin-top: 1rem;
}
</style>

/**
 * 企业档案相关类型定义
 */

// 企业档案基础信息接口
export interface EnterpriseInfo {
  id?: number
  name: string
  province: string
  city: string
  registeredCapital: number
  servicePopulation: number
  serviceArea: number
  employeeCount: number
  plantCount: number
  remark?: string
  createTime?: Date
  updateTime?: Date
}

// 企业处理能力接口
export interface EnterpriseCapacity {
  // 污水处理能力
  sewageTreatmentDesign: number
  sewageTreatmentProduction: number
  
  // 再生水处理能力
  recycledWaterDesign: number
  recycledWaterProduction: number
  
  // 供水能力
  waterSupplyDesign: number
  waterSupplyProduction: number
}

// 企业运营指标接口
export interface EnterpriseOperations {
  sludgeOutput: number
  renewableEnergyGeneration: number
  pipelineLength: number
  riskAssessment: RiskLevel
}

// 完整的企业档案接口
export interface Enterprise extends EnterpriseInfo, EnterpriseCapacity, EnterpriseOperations {}

// 风险等级枚举
export type RiskLevel = '低风险' | '中风险' | '高风险'

// 查询参数接口
export interface EnterpriseQueryParams {
  pageNo: number
  pageSize: number
  name?: string
  province?: string
  city?: string
  riskAssessment?: RiskLevel
}

// 统计信息接口
export interface EnterpriseStatistics {
  totalCount: number
  totalServicePopulation: number
  totalServiceArea: number
  totalEmployeeCount: number
  totalPlantCount: number
  averageUtilizationRate: number
  riskDistribution: {
    lowRisk: number
    mediumRisk: number
    highRisk: number
  }
}

// 表单验证规则类型
export interface FormRules {
  [key: string]: Array<{
    required?: boolean
    message: string
    trigger: string
    min?: number
    max?: number
    type?: string
  }>
}

// 表格列配置接口
export interface TableColumn {
  prop: string
  label: string
  width?: number | string
  minWidth?: number | string
  align?: 'left' | 'center' | 'right'
  fixed?: boolean | 'left' | 'right'
  sortable?: boolean
  formatter?: (row: any, column: any, cellValue: any, index: number) => string
  showOverflowTooltip?: boolean
}

// 操作按钮配置接口
export interface ActionButton {
  label: string
  type?: 'primary' | 'success' | 'warning' | 'danger' | 'info'
  icon?: string
  permission?: string
  handler: (row: Enterprise) => void
}

// 导出配置接口
export interface ExportConfig {
  filename: string
  sheetName: string
  columns: string[]
  data: Enterprise[]
}

// 导入配置接口
export interface ImportConfig {
  templateUrl: string
  maxFileSize: number
  allowedExtensions: string[]
  requiredColumns: string[]
}

// API响应接口
export interface ApiResponse<T = any> {
  code: number
  message: string
  data: T
}

// 分页响应接口
export interface PageResponse<T = any> {
  list: T[]
  total: number
  pageNo: number
  pageSize: number
}

// 企业档案页面状态接口
export interface EnterprisePageState {
  loading: boolean
  list: Enterprise[]
  total: number
  queryParams: EnterpriseQueryParams
  selectedRows: Enterprise[]
  exportLoading: boolean
  importLoading: boolean
}

// 表单状态接口
export interface FormState {
  visible: boolean
  loading: boolean
  type: 'create' | 'update'
  title: string
  data: Partial<Enterprise>
}

// 详情状态接口
export interface DetailState {
  visible: boolean
  loading: boolean
  data: Enterprise | null
}

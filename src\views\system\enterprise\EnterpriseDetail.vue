<template>
  <Dialog v-model="dialogVisible" title="企业档案详情" width="900px">
    <div v-loading="loading">
      <el-descriptions :column="2" border>
        <el-descriptions-item label="企业名称">
          <el-tag type="primary" size="large">{{ detailData.name }}</el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="所在地区">
          {{ detailData.province }}{{ detailData.city }}
        </el-descriptions-item>
        <el-descriptions-item label="注册资本">
          <el-text type="success" size="large">{{ detailData.registeredCapital }}万元</el-text>
        </el-descriptions-item>
        <el-descriptions-item label="风险评估">
          <el-tag 
            :type="getRiskTagType(detailData.riskAssessment)"
            effect="dark"
          >
            {{ detailData.riskAssessment }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="left">
        <el-icon><User /></el-icon>
        人员与规模
      </el-divider>
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon service-population">
                <el-icon size="24"><UserFilled /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.servicePopulation }}</div>
                <div class="stat-label">服务人口(万人)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon employee-count">
                <el-icon size="24"><Avatar /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.employeeCount }}</div>
                <div class="stat-label">职工人数(人)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon plant-count">
                <el-icon size="24"><OfficeBuilding /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.plantCount }}</div>
                <div class="stat-label">厂站数量(个)</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-divider content-position="left">
        <el-icon><MapLocation /></el-icon>
        服务范围
      </el-divider>
      <el-row :gutter="20" class="mb-4">
        <el-col :span="12">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon service-area">
                <el-icon size="24"><Location /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.serviceArea }}</div>
                <div class="stat-label">服务面积(km²)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="12">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon pipeline-length">
                <el-icon size="24"><Connection /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.pipelineLength }}</div>
                <div class="stat-label">配套管网长度(km)</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-divider content-position="left">
        <el-icon><Tools /></el-icon>
        处理能力
      </el-divider>
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>污水处理</span>
                <el-icon color="#409EFF"><Drizzling /></el-icon>
              </div>
            </template>
            <div class="capacity-item">
              <div class="capacity-row">
                <span>设计处理量:</span>
                <el-text type="primary">{{ detailData.sewageTreatmentDesign }}吨/日</el-text>
              </div>
              <div class="capacity-row">
                <span>投产处理量:</span>
                <el-text type="success">{{ detailData.sewageTreatmentProduction }}吨/日</el-text>
              </div>
              <el-progress 
                :percentage="getUtilizationRate(detailData.sewageTreatmentProduction, detailData.sewageTreatmentDesign)"
                :color="getProgressColor(getUtilizationRate(detailData.sewageTreatmentProduction, detailData.sewageTreatmentDesign))"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>再生水</span>
                <el-icon color="#67C23A"><Sunny /></el-icon>
              </div>
            </template>
            <div class="capacity-item">
              <div class="capacity-row">
                <span>设计处理量:</span>
                <el-text type="primary">{{ detailData.recycledWaterDesign }}吨/日</el-text>
              </div>
              <div class="capacity-row">
                <span>投产处理量:</span>
                <el-text type="success">{{ detailData.recycledWaterProduction }}吨/日</el-text>
              </div>
              <el-progress 
                :percentage="getUtilizationRate(detailData.recycledWaterProduction, detailData.recycledWaterDesign)"
                :color="getProgressColor(getUtilizationRate(detailData.recycledWaterProduction, detailData.recycledWaterDesign))"
              />
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover">
            <template #header>
              <div class="card-header">
                <span>供水能力</span>
                <el-icon color="#E6A23C"><Cloudy /></el-icon>
              </div>
            </template>
            <div class="capacity-item">
              <div class="capacity-row">
                <span>设计供水量:</span>
                <el-text type="primary">{{ detailData.waterSupplyDesign }}吨/日</el-text>
              </div>
              <div class="capacity-row">
                <span>投产供水量:</span>
                <el-text type="success">{{ detailData.waterSupplyProduction }}吨/日</el-text>
              </div>
              <el-progress 
                :percentage="getUtilizationRate(detailData.waterSupplyProduction, detailData.waterSupplyDesign)"
                :color="getProgressColor(getUtilizationRate(detailData.waterSupplyProduction, detailData.waterSupplyDesign))"
              />
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-divider content-position="left">
        <el-icon><Lightning /></el-icon>
        其他指标
      </el-divider>
      <el-row :gutter="20" class="mb-4">
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon sludge-output">
                <el-icon size="24"><Box /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.sludgeOutput }}</div>
                <div class="stat-label">泥饼产量(吨/日)</div>
              </div>
            </div>
          </el-card>
        </el-col>
        <el-col :span="8">
          <el-card shadow="hover" class="stat-card">
            <div class="stat-item">
              <div class="stat-icon renewable-energy">
                <el-icon size="24"><Sunny /></el-icon>
              </div>
              <div class="stat-content">
                <div class="stat-value">{{ detailData.renewableEnergyGeneration }}</div>
                <div class="stat-label">新能源发电量(kWh/年)</div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>

      <el-divider content-position="left">备注信息</el-divider>
      <el-card shadow="never">
        <el-text v-if="detailData.remark" size="large">{{ detailData.remark }}</el-text>
        <el-text v-else type="info">暂无备注信息</el-text>
      </el-card>
    </div>
    
    <template #footer>
      <el-button @click="dialogVisible = false">关 闭</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { 
  User, UserFilled, Avatar, OfficeBuilding, MapLocation, Location, 
  Connection, Tools, Drizzling, Sunny, Cloudy, Lightning, Box 
} from '@element-plus/icons-vue'

const dialogVisible = ref(false)
const loading = ref(false)
const detailData = ref({})

/** 打开弹窗 */
const open = (data: any) => {
  dialogVisible.value = true
  detailData.value = data
}

/** 获取风险标签类型 */
const getRiskTagType = (risk: string) => {
  switch (risk) {
    case '低风险':
      return 'success'
    case '中风险':
      return 'warning'
    case '高风险':
      return 'danger'
    default:
      return 'info'
  }
}

/** 计算利用率 */
const getUtilizationRate = (production: number, design: number) => {
  if (!design || design === 0) return 0
  return Math.round((production / design) * 100)
}

/** 获取进度条颜色 */
const getProgressColor = (percentage: number) => {
  if (percentage >= 90) return '#F56C6C'
  if (percentage >= 70) return '#E6A23C'
  return '#67C23A'
}

defineExpose({ open })
</script>

<style lang="scss" scoped>
.stat-card {
  height: 100px;
  
  .stat-item {
    display: flex;
    align-items: center;
    height: 100%;
    
    .stat-icon {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-right: 16px;
      
      &.service-population {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }
      
      &.employee-count {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
      }
      
      &.plant-count {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }
      
      &.service-area {
        background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        color: white;
      }
      
      &.pipeline-length {
        background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        color: white;
      }
      
      &.sludge-output {
        background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
        color: #666;
      }
      
      &.renewable-energy {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        color: #666;
      }
    }
    
    .stat-content {
      flex: 1;
      
      .stat-value {
        font-size: 24px;
        font-weight: bold;
        color: #303133;
        line-height: 1;
      }
      
      .stat-label {
        font-size: 14px;
        color: #909399;
        margin-top: 4px;
      }
    }
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: bold;
}

.capacity-item {
  .capacity-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    
    span {
      font-size: 14px;
      color: #606266;
    }
  }
}
</style>

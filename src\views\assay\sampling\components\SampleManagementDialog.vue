<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="800px"
    :before-close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="dialog-form"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品编号" prop="sampleCode">
            <el-input v-model="formData.sampleCode" placeholder="请输入样品编号" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="样品类型" prop="sampleType">
            <el-select v-model="formData.sampleType" placeholder="请选择样品类型" style="width: 100%">
              <el-option label="进水" value="inlet" />
              <el-option label="出水" value="outlet" />
              <el-option label="污泥" value="sludge" />
              <el-option label="其他" value="other" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="关联执行ID" prop="executionId">
            <el-input-number v-model="formData.executionId" placeholder="请输入执行ID" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="采样日期" prop="samplingDate">
            <el-date-picker
              v-model="formData.samplingDate"
              type="date"
              placeholder="选择采样日期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品体积(mL)" prop="volume">
            <el-input-number v-model="formData.volume" :min="1" :max="10000" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="样品外观" prop="appearance">
            <el-input v-model="formData.appearance" placeholder="请描述样品外观" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="采样人员ID" prop="samplingPersonId">
            <el-input-number v-model="formData.samplingPersonId" placeholder="请输入采样人员ID" style="width: 100%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="检测项目ID" prop="testItem">
            <el-input-number v-model="formData.testItem" placeholder="请输入检测项目ID" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="保存方法" prop="preservationMethod">
            <el-select v-model="formData.preservationMethod" placeholder="请选择保存方法" style="width: 100%">
              <el-option label="冷藏" value="冷藏" />
              <el-option label="常温" value="常温" />
              <el-option label="冷冻" value="冷冻" />
              <el-option label="酸化保存" value="酸化保存" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存储位置" prop="storageLocation">
            <el-input v-model="formData.storageLocation" placeholder="请输入存储位置" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="有效期至" prop="expiryDate">
            <el-date-picker
              v-model="formData.expiryDate"
              type="date"
              placeholder="选择有效期"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="存储温度(°C)" prop="storageTemperature">
            <el-input-number v-model="formData.storageTemperature" :min="-20" :max="50" style="width: 100%" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="样品状态" prop="status">
            <el-select v-model="formData.status" placeholder="请选择状态" style="width: 100%">
              <el-option label="已入库" value="stored" />
              <el-option label="检测中" value="testing" />
              <el-option label="已完成" value="completed" />
              <el-option label="已销毁" value="destroyed" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item label="描述">
        <el-input
          v-model="formData.description"
          type="textarea"
          :rows="3"
          placeholder="请输入样品描述"
        />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleConfirm" :loading="loading">
          {{ mode === 'create' ? '创建' : '更新' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance, FormRules } from 'element-plus'

// Props
interface Props {
  modelValue: boolean
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm', data: any): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const mode = ref<'create' | 'update'>('create')
const currentData = ref<any>(null)

// 表单数据
const formData = reactive({
  id: 0,
  factoryId: 1,     // ✅ v4.0新增：水厂ID
  sampleCode: '',
  sampleType: '',
  samplingPoint: '',
  samplingDate: '',
  volume: 500,
  appearance: '',
  testProgress: 0,
  preservationMethod: '',
  storageLocation: '',
  expiryDate: '',
  temperature: 4,
  status: 'stored',  // ✅ v4.0修改：默认状态改为stored（5状态生命周期）
  description: '',
  // ✅ v4.0新增字段
  samplingPersonId: 0,    // 采样人员ID
  samplingPersonName: '', // 采样人员姓名（用于显示）
  executionId: 0,         // 关联采样执行ID
  testItems: [] as number[] // 检测项目ID列表
})

// 表单验证规则
const formRules: FormRules = {
  sampleCode: [{ required: true, message: '请输入样品编号', trigger: 'blur' }],
  sampleType: [{ required: true, message: '请选择样品类型', trigger: 'change' }],
  samplingPoint: [{ required: true, message: '请输入采样点', trigger: 'blur' }],
  samplingDate: [{ required: true, message: '请选择采样日期', trigger: 'change' }],
  volume: [{ required: true, message: '请输入样品体积', trigger: 'blur' }],
  preservationMethod: [{ required: true, message: '请选择保存方法', trigger: 'change' }],
  storageLocation: [{ required: true, message: '请输入存储位置', trigger: 'blur' }],
  expiryDate: [{ required: true, message: '请选择有效期', trigger: 'change' }],
  status: [{ required: true, message: '请选择样品状态', trigger: 'change' }]
}

// 对话框显示状态
const dialogVisible = ref(false)

const dialogTitle = computed(() => {
  return mode.value === 'create' ? '新增样品' : '编辑样品'
})

// 方法
const open = (type: 'create' | 'update', data?: any) => {
  mode.value = type
  if (type === 'update' && data) {
    currentData.value = data
    Object.assign(formData, data)
  } else {
    resetForm()
  }
  dialogVisible.value = true
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const resetForm = () => {
  Object.assign(formData, {
    id: 0,
    factoryId: 1,     // ✅ v4.0新增：水厂ID
    sampleCode: '',
    sampleType: '',
    samplingPoint: '',
    samplingDate: '',
    volume: 500,
    appearance: '',
    testProgress: 0,
    preservationMethod: '',
    storageLocation: '',
    expiryDate: '',
    temperature: 4,
    status: 'stored',  // ✅ v4.0修改：默认状态改为stored
    description: '',
    // ✅ v4.0新增字段
    samplingPersonId: 0,
    samplingPersonName: '',
    executionId: 0,
    testItems: []
  })
}

const handleClose = () => {
  dialogVisible.value = false
  resetForm()
}

const handleConfirm = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    loading.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    emit('confirm', { ...formData })
    ElMessage.success(`${mode.value === 'create' ? '创建' : '更新'}成功`)
    handleClose()
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    loading.value = false
  }
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.dialog-form {
  padding: 0 20px;
}

.dialog-footer {
  text-align: right;
}
</style>

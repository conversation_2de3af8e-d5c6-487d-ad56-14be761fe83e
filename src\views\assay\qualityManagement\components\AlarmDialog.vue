<template>
  <Dialog v-model="dialogVisible" title="超标预警处理" width="70%">
    <el-descriptions :column="2" border>
      <el-descriptions-item label="样品编号">{{ formData.sampleCode }}</el-descriptions-item>
      <el-descriptions-item label="检测项目">{{ formData.testItem }}</el-descriptions-item>
      <el-descriptions-item label="采样点">{{ formData.samplingPoint }}</el-descriptions-item>
      <el-descriptions-item label="检测日期">{{ formData.testDate }}</el-descriptions-item>
      <el-descriptions-item label="检测值">
        <span class="text-danger">
          {{ formData.testValue }} {{ formData.unit }}
        </span>
      </el-descriptions-item>
      <el-descriptions-item label="标准限值">
        {{ formData.standardValue }} {{ formData.unit }}
      </el-descriptions-item>
      <el-descriptions-item label="超标倍数">
        <span class="text-danger">{{ formData.exceedRatio }}倍</span>
      </el-descriptions-item>
      <el-descriptions-item label="超标类型">
        <el-tag :type="getExceedTypeTagType(formData.exceedType)">
          {{ getExceedTypeLabel(formData.exceedType) }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    
    <el-divider content-position="center">预警信息</el-divider>
    
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="alarmForm"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="预警级别" prop="level">
        <el-radio-group v-model="alarmForm.level">
          <el-radio label="low">
            <el-tag type="info">低级预警</el-tag>
          </el-radio>
          <el-radio label="medium">
            <el-tag type="warning">中级预警</el-tag>
          </el-radio>
          <el-radio label="high">
            <el-tag type="danger">高级预警</el-tag>
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="预警原因" prop="reason">
        <el-input v-model="alarmForm.reason" type="textarea" :rows="3" placeholder="请描述预警原因" />
      </el-form-item>
      <el-form-item label="通知部门" prop="departments">
        <el-select v-model="alarmForm.departments" multiple placeholder="请选择需要通知的部门">
          <el-option v-for="dept in departmentOptions" :key="dept.id" :label="dept.name" :value="dept.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="通知人员" prop="users">
        <el-select v-model="alarmForm.users" multiple placeholder="请选择需要通知的人员">
          <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="预警方式" prop="methods">
        <el-checkbox-group v-model="alarmForm.methods">
          <el-checkbox label="sms">短信通知</el-checkbox>
          <el-checkbox label="email">邮件通知</el-checkbox>
          <el-checkbox label="app">APP推送</el-checkbox>
          <el-checkbox label="wechat">微信通知</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="应对措施" prop="measures">
        <el-input v-model="alarmForm.measures" type="textarea" :rows="3" placeholder="请描述应对措施" />
      </el-form-item>
      <el-form-item label="预警截止时间" prop="endTime">
        <el-date-picker
          v-model="alarmForm.endTime"
          type="datetime"
          placeholder="选择预警截止时间"
          :default-time="new Date(Date.now() + 24 * 60 * 60 * 1000)"
        />
      </el-form-item>
      <el-form-item label="附加文件">
        <el-upload
          action="#"
          :auto-upload="false"
          :limit="3"
          list-type="picture-card"
        >
          <el-icon><Plus /></el-icon>
        </el-upload>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="alarmForm.remark" type="textarea" :rows="2" placeholder="其他备注信息" />
      </el-form-item>
    </el-form>
    
    <el-divider content-position="center">预警处理流程</el-divider>
    
    <el-steps :active="1" align-center>
      <el-step title="发现超标" description="检测数据超过标准限值" />
      <el-step title="发起预警" description="当前步骤" />
      <el-step title="应急处理" description="执行应对措施" />
      <el-step title="跟踪复检" description="验证处理效果" />
      <el-step title="预警解除" description="恢复正常状态" />
    </el-steps>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">发起预警</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'

defineOptions({ name: 'AlarmDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)

// 部门选项
const departmentOptions = [
  { id: 1, name: '生产部' },
  { id: 2, name: '质量管理部' },
  { id: 3, name: '环保部' },
  { id: 4, name: '安全部' },
  { id: 5, name: '综合管理部' }
]

// 人员选项
const userOptions = [
  { id: 1, name: '张三 (生产部经理)' },
  { id: 2, name: '李四 (质量管理部主管)' },
  { id: 3, name: '王五 (环保专员)' },
  { id: 4, name: '赵六 (安全主管)' },
  { id: 5, name: '钱七 (总经理)' }
]

interface FormData {
  id?: number;
  sampleCode: string;
  testItem: string;
  samplingPoint: string;
  testDate: string;
  testValue: string;
  unit: string;
  standardValue: string;
  exceedRatio: string;
  exceedType: 'slight' | 'moderate' | 'severe';
}

interface AlarmForm {
  level: 'low' | 'medium' | 'high';
  reason: string;
  departments: number[];
  users: number[];
  methods: string[];
  measures: string;
  endTime: Date | null;
  remark: string;
}

// 表单数据
const formData = ref<FormData>({
  sampleCode: '',
  testItem: '',
  samplingPoint: '',
  testDate: '',
  testValue: '',
  unit: '',
  standardValue: '',
  exceedRatio: '',
  exceedType: 'moderate'
})

// 预警表单
const alarmForm = ref<AlarmForm>({
  level: 'medium',
  reason: '',
  departments: [],
  users: [],
  methods: ['sms', 'email'],
  measures: '',
  endTime: null,
  remark: ''
})

// 表单校验规则
const formRules = reactive<FormRules>({
  level: [{ required: true, message: '请选择预警级别', trigger: 'change' }],
  reason: [{ required: true, message: '请输入预警原因', trigger: 'blur' }],
  departments: [{ required: true, message: '请至少选择一个通知部门', trigger: 'change', type: 'array', min: 1 }],
  users: [{ required: true, message: '请至少选择一个通知人员', trigger: 'change', type: 'array', min: 1 }],
  methods: [{ required: true, message: '请至少选择一种预警方式', trigger: 'change', type: 'array', min: 1 }],
  measures: [{ required: true, message: '请输入应对措施', trigger: 'blur' }],
  endTime: [{ required: true, message: '请选择预警截止时间', trigger: 'change', type: 'date' }]
})

// 获取超标类型标签类型
const getExceedTypeTagType = (type: string): string => {
  switch (type) {
    case 'slight':
      return 'warning'
    case 'moderate':
      return 'orange'
    case 'severe':
      return 'danger'
    default:
      return 'info'
  }
}

// 获取超标类型标签文本
const getExceedTypeLabel = (type: string): string => {
  switch (type) {
    case 'slight':
      return '轻度超标'
    case 'moderate':
      return '中度超标'
    case 'severe':
      return '严重超标'
    default:
      return '未知'
  }
}

// 根据超标类型自动设置预警级别
const autoSetAlarmLevel = (exceedType: string) => {
  switch (exceedType) {
    case 'slight':
      alarmForm.value.level = 'low'
      break
    case 'moderate':
      alarmForm.value.level = 'medium'
      break
    case 'severe':
      alarmForm.value.level = 'high'
      break
    default:
      alarmForm.value.level = 'medium'
  }
}

// 自动生成预警原因
const generateAlarmReason = () => {
  return `${formData.value.testItem}检测结果(${formData.value.testValue}${formData.value.unit})超过标准限值(${formData.value.standardValue}${formData.value.unit})，超标${formData.value.exceedRatio}倍，属于${getExceedTypeLabel(formData.value.exceedType)}。`
}

// 根据预警级别推荐应对措施
const recommendMeasures = (level: string) => {
  switch (level) {
    case 'low':
      return '1. 加强监测频次，密切关注数据变化\n2. 检查相关工艺参数，排查可能原因\n3. 安排复检确认数据准确性'
    case 'medium':
      return '1. 立即安排复检，确认超标情况\n2. 排查超标原因，调整相关工艺参数\n3. 增加取样点位，扩大监测范围\n4. 制定应急处理方案'
    case 'high':
      return '1. 立即启动应急预案，采取必要措施\n2. 全面排查超标原因，查找污染源\n3. 组织应急处理小组，制定详细处理方案\n4. 增加监测频次，密切跟踪数据变化\n5. 必要时暂停相关生产工序'
    default:
      return ''
  }
}

const formRef = ref<FormInstance>()
const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any): Promise<void> => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value = {
    id: data.id,
    sampleCode: data.sampleCode,
    testItem: data.testItem,
    samplingPoint: data.samplingPoint || '',
    testDate: data.testDate || '',
    testValue: data.testValue || '',
    unit: data.unit || 'mg/L',
    standardValue: data.standardValue || '',
    exceedRatio: data.exceedRatio || '',
    exceedType: data.exceedType || 'moderate'
  }
  
  // 自动设置预警级别
  autoSetAlarmLevel(formData.value.exceedType)
  
  // 默认通知部门和人员
  let defaultDepartments: number[] = []
  let defaultUsers: number[] = []
  
  if (alarmForm.value.level === 'high') {
    defaultDepartments = [1, 2, 3, 4, 5] // 所有部门
    defaultUsers = [1, 2, 3, 5] // 关键人员
  } else if (alarmForm.value.level === 'medium') {
    defaultDepartments = [1, 2, 3]
    defaultUsers = [1, 2, 3]
  } else {
    defaultDepartments = [2]
    defaultUsers = [2]
  }
  
  // 设置预警截止时间（默认24小时后）
  const endTime = new Date()
  endTime.setHours(endTime.getHours() + 24)
  
  // 重置预警表单
  alarmForm.value = {
    level: alarmForm.value.level,
    reason: generateAlarmReason(),
    departments: defaultDepartments,
    users: defaultUsers,
    methods: ['sms', 'email'],
    measures: recommendMeasures(alarmForm.value.level),
    endTime,
    remark: ''
  }
}
defineExpose({ open })

// 提交表单
const submitForm = async (): Promise<void> => {
  // 表单校验
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('预警发起成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 监听预警级别变化，自动推荐应对措施
watch(
  () => alarmForm.value.level,
  (newLevel) => {
    if (!alarmForm.value.measures || alarmForm.value.measures === recommendMeasures('low') || alarmForm.value.measures === recommendMeasures('medium') || alarmForm.value.measures === recommendMeasures('high')) {
      alarmForm.value.measures = recommendMeasures(newLevel)
    }
  }
)
</script>

<style scoped>
.text-danger {
  color: #f56c6c;
  font-weight: bold;
}

.text-warning {
  color: #e6a23c;
  font-weight: bold;
}
</style> 
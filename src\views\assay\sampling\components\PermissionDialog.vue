<template>
  <Dialog v-model="dialogVisible" title="报告权限设置">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      label-width="100px"
    >
      <el-form-item label="报告编号">
        <el-input v-model="formData.code" disabled />
      </el-form-item>
      <el-form-item label="报告名称">
        <el-input v-model="formData.name" disabled />
      </el-form-item>

      <el-divider content-position="center">查看权限设置</el-divider>
      
      <el-form-item label="查看权限">
        <el-transfer
          v-model="formData.viewPermission"
          :titles="['可选用户', '已选用户']"
          :data="userOptions"
        />
      </el-form-item>
      
      <el-divider content-position="center">下载权限设置</el-divider>
      
      <el-form-item label="下载权限">
        <el-transfer
          v-model="formData.downloadPermission"
          :titles="['可选用户', '已选用户']"
          :data="userOptions"
        />
      </el-form-item>
      
      <el-form-item label="部门权限">
        <el-tree
          ref="deptTreeRef"
          :data="departmentOptions"
          show-checkbox
          node-key="id"
          :props="{ label: 'name', children: 'children' }"
        />
      </el-form-item>
      
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { Dialog } from '../../../../components/Dialog'

defineOptions({ name: 'PermissionDialog' })

const dialogVisible = ref(false)
const formLoading = ref(false)
const deptTreeRef = ref()

// 用户选项（模拟数据）
const userOptions = [
  { key: 1, label: '张三（化验员）' },
  { key: 2, label: '李四（化验员）' },
  { key: 3, label: '王五（化验员）' },
  { key: 4, label: '赵六（化验主管）' },
  { key: 5, label: '钱七（化验主管）' },
  { key: 6, label: '孙八（厂长）' },
  { key: 7, label: '周九（环保专员）' },
  { key: 8, label: '吴十（环保局）' }
]

// 部门选项（模拟数据）
const departmentOptions = [
  {
    id: 1,
    name: '污水厂',
    children: [
      {
        id: 11,
        name: '化验室',
        children: [
          { id: 111, name: '化验员组' },
          { id: 112, name: '化验主管组' }
        ]
      },
      { id: 12, name: '运行部' },
      { id: 13, name: '管理部' }
    ]
  },
  {
    id: 2,
    name: '外部单位',
    children: [
      { id: 21, name: '环保局' },
      { id: 22, name: '水务局' }
    ]
  }
]

// 表单数据
const formData = ref({
  id: undefined as number | undefined,
  code: '',
  name: '',
  viewPermission: [] as number[],
  downloadPermission: [] as number[],
  remark: ''
})

const emit = defineEmits(['success'])

// 打开对话框
const open = async (data: any) => {
  if (!data) return
  
  dialogVisible.value = true
  formData.value.id = data.id
  formData.value.code = data.code
  formData.value.name = data.name
  
  // 模拟已有权限数据
  formData.value.viewPermission = [1, 2, 4, 6]
  formData.value.downloadPermission = [4, 6]
  formData.value.remark = ''
  
  // 设置部门权限
  deptTreeRef.value?.setCheckedKeys([11, 13])
}
defineExpose({ open })

// 提交表单
const submitForm = async () => {
  formLoading.value = true
  try {
    // 获取选中的部门ID
    const checkedDepts = deptTreeRef.value?.getCheckedKeys() || []
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500))
    
    // 在实际环境中应发送checkedDepts到后端
    console.log('选中的部门权限:', checkedDepts)
    
    ElMessage.success('权限设置成功')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('设置失败:', error)
    ElMessage.error('权限设置失败，请重试')
  } finally {
    formLoading.value = false
  }
}
</script>

<style scoped>
.el-transfer {
  margin-bottom: 1rem;
}
</style> 
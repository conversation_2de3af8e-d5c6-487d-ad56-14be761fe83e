<template>
  <Dialog v-model="dialogVisible" title="确认采样 - 开始现场采样" width="900px">
    <div class="status-transition-info mb-6">
      <el-alert
        title="状态变更：待采样 → 采样中"
        type="info"
        :closable="false"
        show-icon
      >
        <template #default>
          <div class="status-flow">
            <span class="status-item current">待采样</span>
            <el-icon class="arrow"><ArrowRight /></el-icon>
            <span class="status-item next">采样中</span>
          </div>
          <p class="mt-2 text-sm text-gray-600">确认采样后，任务状态将变更为"采样中"，请填写现场采样的详细信息</p>
        </template>
      </el-alert>
    </div>

    <!-- 任务基本信息展示 -->
    <div class="task-info-section mb-6">
      <el-card shadow="never" class="info-card">
        <template #header>
          <div class="card-header">
            <span class="font-semibold">任务基本信息</span>
          </div>
        </template>
        <el-row :gutter="24">
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">任务编号：</label>
              <span class="info-value">{{ taskInfo.taskCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">任务名称：</label>
              <span class="info-value">{{ taskInfo.name }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">计划日期：</label>
              <span class="info-value">{{ taskInfo.planDate }}</span>
            </div>
          </el-col>
        </el-row>
        <el-row :gutter="24" class="mt-4">
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">采样点：</label>
              <span class="info-value">{{ taskInfo.samplingPointName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">检测项目：</label>
              <span class="info-value">{{ taskInfo.testItemName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="info-item">
              <label class="info-label">采样人员：</label>
              <span class="info-value">{{ taskInfo.samplerName }}</span>
            </div>
          </el-col>
        </el-row>
      </el-card>
    </div>

    <!-- 现场采样信息填写表单 -->
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="140px"
    >
      <el-card shadow="never">
        <template #header>
          <div class="card-header">
            <span class="font-semibold">现场采样信息填写</span>
            <span class="text-sm text-gray-500 ml-2">（以下信息将用于下一阶段的样品管理）</span>
          </div>
        </template>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="具体采样位置" prop="samplingLocation">
              <el-input
                v-model="formData.samplingLocation"
                placeholder="请输入具体的采样位置（如：进水口东侧2米处）"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="实际采样时间" prop="actualSamplingTime">
              <el-date-picker
                v-model="formData.actualSamplingTime"
                type="datetime"
                placeholder="请选择实际采样时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DDTHH:mm:ss"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="24">
          <el-col :span="12">
            <el-form-item label="实际样品量(mL)" prop="actualSampleQuantity">
              <el-input-number
                v-model="formData.actualSampleQuantity"
                :min="1"
                :max="10000"
                placeholder="请输入实际采集的样品量"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="样品外观描述" prop="actualSampleAppearance">
              <el-input
                v-model="formData.actualSampleAppearance"
                placeholder="请描述样品外观（如：无色透明、微黄色、有悬浮物等）"
                maxlength="500"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="现场采样情况" prop="samplingCondition">
          <el-input
            v-model="formData.samplingCondition"
            type="textarea"
            :rows="4"
            placeholder="请详细描述现场采样情况，包括：&#10;1. 天气情况（晴天、雨天、温度等）&#10;2. 水流状态（流速、水位等）&#10;3. 现场环境（是否有异味、颜色异常等）&#10;4. 其他需要记录的情况"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="样品状态评估" prop="sampleStatus">
          <el-radio-group v-model="formData.sampleStatus">
            <el-radio label="normal">正常</el-radio>
            <el-radio label="abnormal">异常</el-radio>
          </el-radio-group>
          <div class="text-sm text-gray-500 mt-1">
            正常：样品符合预期，可正常进行后续检测；异常：样品存在问题，需要特殊处理
          </div>
        </el-form-item>

        <el-form-item v-if="formData.sampleStatus === 'abnormal'" label="异常原因" prop="abnormalReason">
          <el-input
            v-model="formData.abnormalReason"
            type="textarea"
            :rows="3"
            placeholder="请详细描述样品异常的具体原因和情况"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>

        <el-form-item label="备注信息" prop="remark">
          <el-input
            v-model="formData.remark"
            type="textarea"
            :rows="2"
            placeholder="其他需要记录的信息（可选）"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :loading="formLoading" type="primary" @click="submitForm">
        确认采样
      </el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { ArrowRight } from '@element-plus/icons-vue'
import type { FormInstance, FormRules } from 'element-plus'
import { Dialog } from '@/components/Dialog'

defineOptions({ name: 'ConfirmSamplingDialog' })

const emit = defineEmits(['success'])

const dialogVisible = ref(false)
const formLoading = ref(false)
const formRef = ref<FormInstance>()

interface TaskInfo {
  id: number
  taskCode: string
  name: string
  planDate: string
  samplingPointName: string
  testItemName: string
  samplerName: string
}

const taskInfo = ref<TaskInfo>({
  id: 0,
  taskCode: '',
  name: '',
  planDate: '',
  samplingPointName: '',
  testItemName: '',
  samplerName: ''
})

const formData = reactive({
  taskId: 0,
  samplingLocation: '',
  actualSamplingTime: '',
  samplingCondition: '',
  actualSampleQuantity: undefined as number | undefined,
  actualSampleAppearance: '',
  sampleStatus: 'normal',
  abnormalReason: '',
  remark: ''
})

const formRules: FormRules = {
  samplingLocation: [
    { required: true, message: '请输入具体采样位置', trigger: 'blur' },
    { max: 200, message: '长度不能超过 200 个字符', trigger: 'blur' }
  ],
  actualSamplingTime: [
    { required: true, message: '请选择实际采样时间', trigger: 'change' }
  ],
  samplingCondition: [
    { required: true, message: '请描述现场采样情况', trigger: 'blur' },
    { max: 1000, message: '长度不能超过 1000 个字符', trigger: 'blur' }
  ],
  actualSampleQuantity: [
    { required: true, message: '请输入实际样品量', trigger: 'blur' },
    { type: 'number', min: 1, max: 10000, message: '样品量必须在1-10000mL之间', trigger: 'blur' }
  ],
  actualSampleAppearance: [
    { required: true, message: '请描述样品外观', trigger: 'blur' },
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ],
  sampleStatus: [
    { required: true, message: '请选择样品状态', trigger: 'change' }
  ],
  abnormalReason: [
    { required: true, message: '请输入异常原因', trigger: 'blur' },
    { max: 1000, message: '长度不能超过 1000 个字符', trigger: 'blur' }
  ]
}

// 打开对话框
const open = (task: any) => {
  taskInfo.value = {
    id: task.id,
    taskCode: task.taskCode || '',
    name: task.name,
    planDate: task.planDate || '',
    samplingPointName: task.samplingPointName || '未指定',
    testItemName: task.testItemName || '未指定',
    samplerName: task.samplerName || '未指定'
  }
  
  // 重置表单数据
  formData.taskId = task.id
  formData.samplingLocation = ''
  formData.actualSamplingTime = ''
  formData.samplingCondition = ''
  formData.actualSampleQuantity = undefined
  formData.actualSampleAppearance = ''
  formData.sampleStatus = 'normal'
  formData.abnormalReason = ''
  formData.remark = ''

  dialogVisible.value = true
}

// 提交表单
const submitForm = async () => {
  if (!formRef.value) return
  
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    // 调用确认采样API
    const requestData = {
      factoryId: 1, // 从用户信息获取
      taskId: formData.taskId,
      samplingLocation: formData.samplingLocation,
      samplingCondition: formData.samplingCondition,
      actualSamplingTime: formData.actualSamplingTime
    }
    
    // TODO: 替换为真实API调用
    // await confirmSamplingApi(requestData)
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    ElMessage.success('确认采样成功，任务状态已变更为"采样中"')
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('确认采样失败:', error)
    ElMessage.error('确认采样失败，请重试')
  } finally {
    formLoading.value = false
  }
}

defineExpose({
  open
})
</script>

<style scoped>
.status-transition-info {
  margin-bottom: 1rem;
}

.status-flow {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.status-item {
  padding: 0.25rem 0.75rem;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.status-item.current {
  background-color: #e1f5fe;
  color: #0277bd;
}

.status-item.next {
  background-color: #e8f5e8;
  color: #2e7d32;
}

.arrow {
  color: #666;
}

.task-info .info-card {
  background-color: #f5f5f5;
  padding: 1rem;
  border-radius: 0.5rem;
  border-left: 4px solid #409eff;
}

.task-info .info-card p {
  margin: 0.25rem 0;
  color: #606266;
}

.task-info-section .info-card {
  border: 1px solid #e4e7ed;
}

.info-item {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.info-label {
  font-weight: 500;
  color: #606266;
  min-width: 80px;
  margin-right: 8px;
}

.info-value {
  color: #303133;
  font-weight: 500;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #303133;
}

:deep(.el-card__header) {
  padding: 16px 20px;
  background-color: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-card__body) {
  padding: 24px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-input__wrapper) {
  border-radius: 6px;
}

:deep(.el-textarea__inner) {
  border-radius: 6px;
}

:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-radio-group) {
  display: flex;
  gap: 16px;
}

.text-gray-500 {
  color: #909399;
}

.text-gray-600 {
  color: #606266;
}

.font-semibold {
  font-weight: 600;
}

.ml-2 {
  margin-left: 8px;
}

.mt-1 {
  margin-top: 4px;
}

.mt-2 {
  margin-top: 8px;
}

.mt-4 {
  margin-top: 16px;
}

.mb-4 {
  margin-bottom: 16px;
}

.mb-6 {
  margin-bottom: 24px;
}
</style>

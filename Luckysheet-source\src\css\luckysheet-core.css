﻿/* body {
    margin: 0px;
    height: 100%;
    overflow: hidden;
}
 */

::-webkit-scrollbar-track {
  background-color: transparent;
}

.luckysheet-noselected-text {
  -moz-user-select: -moz-test;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.chart-moveable {
  cursor: move;
}

.luckysheet {
  position: absolute;
  /*width: 100%;
    height: 100%;*/
  font-size: 12px;
  font-family: "Helvetica Neue", Helvetica, Arial, "PingFang SC",
    "Hiragino Sans GB", "Heiti SC", "Microsoft YaHei", "WenQuanYi Micro Hei",
    sans-serif;
  border: 1px solid #e5e5e5;
  background: #fff;
}

.luckysheet * {
  box-sizing: initial;
  outline: none;
}

.luckysheetLoaderGif {
  /*换用GIF loading*/
  width: 8em;
  height: 8em;
  position: absolute;
  top: 50%;
  left: 50%;
  -ms-transform: translate(-50%, -100%);
  -moz-transform: translate(-50%, -100%);
  -o-transform: translate(-50%, -100%);
  transform: translate(-50%, -100%);

  background-image: url(loading.gif);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

.luckysheet-loading-mask {
  position: absolute;
  z-index: 1000000000;
  margin: 0;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #fff;
}

.luckysheet-loading-content {
  position: relative;
  top: 50%;
  transform: translateY(-50%);
  width: 100%;
  font-size: 14px;
  color: var(--luckysheet-main-color);
  text-align: center;
}

.luckysheet-loading-image {
  width: 8em;
  height: 8em;
  margin: 0 auto;
}

.luckysheet-loading-text {
  margin-top: 1em;
}

.luckysheet-loading-image .image-type {
  width: 100%;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

.luckysheet-loading-image .path-type {
  width: 100%;
  height: 100%;
}

.luckysheet-work-area {
  height: 90px;
  width: 100%;
  position: relative;
}

.luckysheet_info_detail {
  position: relative;
  left: 0px;
  top: 0px;
  margin: 0px;
  padding: 0 17px;
  /* width: 100%; */
  height: 56px;
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 1);
  box-shadow: 0px -1px 0px 0px rgba(230, 231, 232, 1);
  border-bottom: 1px solid #d4d4d4;
}

.luckysheet_info_detail .sheet-name {
  margin: auto;
}

.luckysheet_info_detail div.luckysheet-icon-menu-btn {
  height: 32px;
  line-height: 32px;
  font-size: 14px;
  padding: 0 8px;
  margin-right: 18px;
  border-radius: 3px;
  cursor: pointer;
}

.luckysheet_info_detail div.luckysheet-icon-menu-btn:hover {
  background: #eee;
}

.luckysheet_info_detail .luckysheet_info_detail_input {
  border: 1px solid transparent;
  border-radius: 3px !important;
  color: #000000;
  font-size: 16px;
  height: 26px;
  line-height: 22px;
  margin: 0;
  min-width: 1px;
  padding: 2px 7px;
  visibility: hidden;
}

.luckysheet_info_detail .luckysheet_info_detail_input:hover {
  border: 1px solid #e5e5e5;
}

.luckysheet_info_detail .luckysheet_info_detail_input:focus {
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 1px solid var(--luckysheet-main-color) !important;
  -webkit-box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.1);
  color: #000;
  outline: none;
}

.luckysheet_info_detail_update {
  color: #cbcbcb;
  font-size: 12px;
  margin-left: 15px;
}

.luckysheet_info_detail_user {
  /* right: 20px; */
  font-size: 12px;
  cursor: pointer;
  margin-left: 10px;
  /* position: absolute; */
}
#luckysheet_info_detail_user_img {
  vertical-align: middle;
  height: 20px;
  line-height: 20px;
  border-radius: 50%;
  object-fit: cover;
}
.luckysheet_info_detail_save {
  color: #828282;
  font-size: 12px;
  margin: 0 5px;
}

.luckysheet-share-logo {
  height: 32px;
  width: 152px;
  z-index: 1;
  background-image: url("data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAJgAAAAgCAYAAADuW7E5AAAACXBIWXMAAC4jAAAuIwF4pT92AAAGgWlUWHRYTUw6Y29tLmFkb2JlLnhtcAAAAAAAPD94cGFja2V0IGJlZ2luPSLvu78iIGlkPSJXNU0wTXBDZWhpSHpyZVN6TlRjemtjOWQiPz4gPHg6eG1wbWV0YSB4bWxuczp4PSJhZG9iZTpuczptZXRhLyIgeDp4bXB0az0iQWRvYmUgWE1QIENvcmUgNi4wLWMwMDIgNzkuMTY0NDg4LCAyMDIwLzA3LzEwLTIyOjA2OjUzICAgICAgICAiPiA8cmRmOlJERiB4bWxuczpyZGY9Imh0dHA6Ly93d3cudzMub3JnLzE5OTkvMDIvMjItcmRmLXN5bnRheC1ucyMiPiA8cmRmOkRlc2NyaXB0aW9uIHJkZjphYm91dD0iIiB4bWxuczp4bXA9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC8iIHhtbG5zOnhtcE1NPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAvbW0vIiB4bWxuczpzdEV2dD0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL3NUeXBlL1Jlc291cmNlRXZlbnQjIiB4bWxuczpwaG90b3Nob3A9Imh0dHA6Ly9ucy5hZG9iZS5jb20vcGhvdG9zaG9wLzEuMC8iIHhtbG5zOmRjPSJodHRwOi8vcHVybC5vcmcvZGMvZWxlbWVudHMvMS4xLyIgeG1wOkNyZWF0b3JUb29sPSJBZG9iZSBQaG90b3Nob3AgMjIuMCAoV2luZG93cykiIHhtcDpDcmVhdGVEYXRlPSIyMDI0LTEyLTE5VDE1OjAwOjUyKzA4OjAwIiB4bXA6TWV0YWRhdGFEYXRlPSIyMDI0LTEyLTE5VDE1OjAwOjUyKzA4OjAwIiB4bXA6TW9kaWZ5RGF0ZT0iMjAyNC0xMi0xOVQxNTowMDo1MiswODowMCIgeG1wTU06SW5zdGFuY2VJRD0ieG1wLmlpZDpkOWU2MWRlZi1kNmMzLTI5NDktYjY5NS04MTlmNjQwMTdiNTIiIHhtcE1NOkRvY3VtZW50SUQ9ImFkb2JlOmRvY2lkOnBob3Rvc2hvcDphMGRiNGE2Zi1kYzczLTJiNGQtYWVkZi04OWZhZWE5N2U4NGQiIHhtcE1NOk9yaWdpbmFsRG9jdW1lbnRJRD0ieG1wLmRpZDpiMDAyODdkMi1iMTBjLTZmNGQtYTUzZS1iZWNhMGQxZjgyYzciIHBob3Rvc2hvcDpDb2xvck1vZGU9IjMiIHBob3Rvc2hvcDpJQ0NQcm9maWxlPSJzUkdCIElFQzYxOTY2LTIuMSIgZGM6Zm9ybWF0PSJpbWFnZS9wbmciPiA8eG1wTU06SGlzdG9yeT4gPHJkZjpTZXE+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJjcmVhdGVkIiBzdEV2dDppbnN0YW5jZUlEPSJ4bXAuaWlkOmIwMDI4N2QyLWIxMGMtNmY0ZC1hNTNlLWJlY2EwZDFmODJjNyIgc3RFdnQ6d2hlbj0iMjAyNC0xMi0xOVQxNTowMDo1MiswODowMCIgc3RFdnQ6c29mdHdhcmVBZ2VudD0iQWRvYmUgUGhvdG9zaG9wIDIyLjAgKFdpbmRvd3MpIi8+IDxyZGY6bGkgc3RFdnQ6YWN0aW9uPSJzYXZlZCIgc3RFdnQ6aW5zdGFuY2VJRD0ieG1wLmlpZDpkOWU2MWRlZi1kNmMzLTI5NDktYjY5NS04MTlmNjQwMTdiNTIiIHN0RXZ0OndoZW49IjIwMjQtMTItMTlUMTU6MDA6NTIrMDg6MDAiIHN0RXZ0OnNvZnR3YXJlQWdlbnQ9IkFkb2JlIFBob3Rvc2hvcCAyMi4wIChXaW5kb3dzKSIgc3RFdnQ6Y2hhbmdlZD0iLyIvPiA8L3JkZjpTZXE+IDwveG1wTU06SGlzdG9yeT4gPHBob3Rvc2hvcDpUZXh0TGF5ZXJzPiA8cmRmOkJhZz4gPHJkZjpsaSBwaG90b3Nob3A6TGF5ZXJOYW1lPSJMdWNreXNoZWV0IiBwaG90b3Nob3A6TGF5ZXJUZXh0PSJMdWNreXNoZWV0Ii8+IDwvcmRmOkJhZz4gPC9waG90b3Nob3A6VGV4dExheWVycz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1ldGE+IDw/eHBhY2tldCBlbmQ9InIiPz76Z2J7AAAJL0lEQVR4nO2cbYhjVxnHfzOMbq2rm5YWlNaaxVKwBptRfAGFzdTGwFpxppJCkWwzVesiyMw4MfXlXmaHez9oyOxkqR9KtW5KtOJGSGxpJUbddBV8adGgUdAKGwXBD4tm0S66pV0/nOdsTu7cvE1nc7fb+UO45z7n5fnn3Oee5zlPzswUglKrNgUcAg4Ds8AexsSpM88903nh3HvG6HIe+C3wMFAsx7MvAVy4cGFc1bu4TDENUGrVrgaeAIrA+9mGcW0TrwXeBzwCPJWs566ekN5dTAjTcn0Y+EiQRIAEcDxgDrvYYUyXWrUY8ImgiQjuTtZzdwRNYhc7h2ngM0GT8OBw0AR2sXOYBg4ETcKDy43PLl4GZoA3BU3Cg+v8hLa7MQ/gWKtVUz41NXXpGQksJ38cSAOLrp0pTkxxL4c0KlYtuHZmJQgOJobt+KeBSTyhLwHvBr69nc62uxEFKkBFykEhLdd7A+SgdacD5DAyZiag41Q5nv0qQLKeuw+4Cxg3HRHqU341IxQ0AQ3b3QihVtW2Y632rKrTvj12Ftcm6zm9Su5D5b52cWVhGZgHlm13I2xWjGtgLeCvY/aJAJVkPfc5oMZkVs2Jw3LyUcvJh4PmcRkgbN6M87B/DsSAvcAfgBvG6Psx+UwElpOPobgWXDvTMeRhVOxSde1M06dfGjVBbaDh2pn2iPrSKBfRsZz8rIjTMkbDp31I6ptmvcijwl2jPWhDId9pHuUy+7aVdjG6BuDLzWgfM3i08cyHcF223Q3o3fnfa7sbMaDjWKuFcQxsJRVJvAicLbVqG8DRMfpOGhXUhN8GLBjyTdTDWAKu0ULLyUelT9gcxHLyRWDFNFIvDONCdIaAk3Jdspz8rI+hbiJButQ3LSe/iXI1fjrwMxzLyR8B1jyyJdfOzHpky6LTxJrl5NvAgvmyiSFWUIbu1WfuXI+j5tKLtC7Y7sbILvKxVCTxrHH/DeCfI/adKGSCQnIb8lRvkcubuMW4BGkG/HwlD86sX5SH1TH0bHr6xOjdAXZEttxPTx9u4DEuQdRy8vOGvrSXg2fcitE2RB/jEizLeKBWtWFoj2Jg54GvmIJUJPEf4OtD+j0L/HCE8XcaYaPcHKH9ptGnCcyhVj3dt+3XSXJi5oMzc2OLhnxeDEhjySgXZXUz64vCYU7KAI0B/Duib92QRYVjyMNxXcZdofsShI36ZbrG1UTNw5xHfxjAtTMrrp2Z8vBExp4DZh1rtTqKgT2YiiTaAKVW7ZZSq6b7PAg836fPE8AHyvHsQdRJiaBwdlClEZOBmvA51840XDtTFTcz1yeZGaN3FepJvEpsUzXq1wx984ZcG0XTkEX1GK6dWXTtzNSAWElzLuJvhGm6q/W6a2eOoAxkyZCb/bTxd1BG0kTl3WJGmx49jrXaoHfj13Ss1YZjrTZh+C7yX4Br3D8CfBwgFUmcAb7p0+ckcHc5nj1vkP7zED1BIW2Ut8Rag4JgD8I+MtMwY7KKmS5Nr164dqZKdxWIAictJ3/adHV9sO63WTFwm1F+q+XkT6NcuuZbRWJUT2jRlnan6X0BF8aYE2C4gbmpSKIDUGrV5oEPAl806o8CL+ibN77mqjZwZzme/a+WlePZ54F7zHbbQFQX5I3ZKZi7n+o2+nfkuuZxg4jxmG5rDf/VS7dfpNcdhYGKuGJfuHamMIRf2CinjfsiauVbMF4qs23U4NpGvSz75UUYC4MM7DQSZ5VatRngayJ/V6lV+zBAKpL4G/CYyH8f3feWT5fj2XMAyXru5mQ9dx1AOZ79DWCNS85AaNSGnjds38vQ2QNPjquNOvVrrlIViXlMFOgaYYzu96j6pUDELc7J2FURp2W3OA78Dgx0UIa1X1xvY8gYTZTr3+/amYJ3dR8Vg9IUX05FEtrN3Q/cYtQ9APxIyjnUqdQPpSKJswDJeu5G4CdCUue/8qhDhbdvh6gfbHcjKivHAdQDbKNWAY2YLkgqIuQZomm02cQIzmXcJeAHEuOEjX5tcU1Ny8kfoBvrVEz9rp3pWE7+GFt3e8fMG3GFUSQ3JWmLY3RXkVFOmLR9ZE/T/X5F4Jg2bNG5JPULrp2pWk7eHGtdr1hGrLqE+u49aZBB6GdgzwDfAyi1am8Ajnjqby+1au9NRRK/TkUSf5TyvwGS9dz1qDjsJuCmZD13uBzPPlSOZ19K1nOHgN8B145K0Avb3dA5pqhPdVQ+DdTERS0nP+jn/mN00wNpmfQmypjCIp+nd5fkxYqhN2Y5+bQnZ1VABcp6PL8Ep04VrBkP2UTbKMf8Grh2pm30Dcm1SDegX0alGfy6a24FaRdGrciD2o6Efi5yNRVJ6AfzAHC9T5uLsZhhXPuAnwI3G+2OJuu5WwHK8ezfgU+NQ1DQMcox+udpivLw1vvU90DeZjOlEKI32w29bnALH3Ed5hg9Jy18XIsft+IAmk0PBz1ew6dtW65R0d1GraheDiYaWr/smAdx6eA/H942F+FnYI+nIomfAZRatRuAz/cZaL7Uqr1d35RatdfvndnzKOq3RxOvA76brOf2AJTj2Qr+u89BKLI1CG+IbB21E7pGAmUdh+1HvZEN+RToPtyiHkRWm1mRdUTcphuvFIwxC4bOixB3uSh6eh6ArIphuW36xT7Ce0F0ar5FVAw06zHSOdFvGrWGdr3m92ui5mLRM34BFejPmeMLl1nRodtW6Qb6F8c2UEXNXVOnJzSmSq2a6UJeBN6RiiT+BFBq1b7V54tolFKRxKFSq3YV8NSpM8/tHfBna4VyPLsCkKzn3gb8pd+gJ+74wshn1CZ54HA7sJx8hW4sFdhBxUuFUQ4cmqgYxvVOhh9qS5VatSeBX9AbXPthOVnPJaR845C2VwQ8idXOlWZco2AGuID/qdZcH7kXB8fQ9/1kPXcCuHOMPq9kzBvlYkAcAsUM8A/gzXJ/V6lVc1C7vETfXtvHXuC+IW3OXAK9QcE8ovRoYCwCxDS9u5FpVEL0s4GwUXg6QN07DW1UvufPXg2YQf1V9z1BEzHw0DiNL/P/Y1GUD461GiiRoDCdiiQawHeCJiI4UY5nfxw0iV3sHPQu8n7gySCJoM7rD0qJ7OIViGmAVCRxDvgoKi3xS+B/E9J/HvgV8EngoP6hfBdXDv4PiXU6CqfsV/MAAAAASUVORK5CYII=");
  background-size: 100% 100%;
}

.luckysheet-wa-editor {
  height: 32px;
  /* background: #f7f7f7; */
  background: #fafafc;
  position: relative;
  /* padding-left: 15px; */
  /* padding: 0px 0px 10px 15px; */
  padding: 5px 0px 3px 15px;
  border-bottom: 1px solid #d4d4d4;
  white-space: nowrap;
  transition: all 0.2s;
}

/* 
.luckysheet-wa-editor>div.luckysheetfulltoolbar {
    display: inline-block;
    height: 34px;
    padding: 0px 5px;
    line-height: 34px;
    font-size: 13px;
    transition: all 0.2s;
    -moz-transition: all 0.2s;
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
    vertical-align: middle;
}

.luckysheet-wa-editor>div.luckysheetfulltoolbar:hover {
    background: var(--luckysheet-main-color);
    color: #FAFAFA;
    cursor: pointer;
}

.luckysheet-wa-editor>div.luckysheetfulltoolbar:active {
    background: #FB4747;
    color: #FFFFFF;
    -webkit-box-shadow: inset 0 0 2px #727272;
    -moz-box-shadow: inset 0 0 2px #727272;
    box-shadow: inset 0 0 2px #727272;
    cursor: pointer;
} */

/*toolbar菜单start*/

.luckysheet-toolbar-left-theme {
  width: 15px;
  /* background: var(--luckysheet-main-color);  */ /* 工具栏左边的蓝色背景 */
  position: absolute;
  left: 0px;
  top: 1px;
  bottom: 1px;
}

.luckysheet-inline-block {
  position: relative;
  display: -moz-inline-box;
  display: inline-block;
}

/*分隔符*/
.luckysheet-toolbar-separator {
  line-height: normal;
  list-style: none;
  outline: none;
  overflow: hidden;
  padding: 0;
  text-decoration: none;
  width: 0;
  /* height: 35px; */
  height: 20px;
  vertical-align: top;
  border-left: 1px solid #e0e0e0;
  /* margin: 0 1px; */
  margin: 5px 1px;
}

/*combo*/
.luckysheet-toolbar-combo-button {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  background: 0;
  border-color: transparent;
  border-style: solid;
  border-width: 1px;
  outline: none;
  padding: 0;
  color: #333;
  list-style: none;
  font-size: 11px;
  font-weight: bold;
  text-decoration: none;
  cursor: default;
  /* top: 2px; */
  height: 26px;
  line-height: 26px;
  vertical-align: inherit;
  margin: 0 1px;
}

.luckysheet-toolbar-combo-button:hover {
  -webkit-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  -moz-box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  box-shadow: 0 1px 1px rgba(0, 0, 0, 0.1);
  background-color: #f8f8f8;
  background-image: -webkit-linear-gradient(to bottom, #f8f8f8, #f1f1f1);
  background-image: -moz-linear-gradient(to bottom, #f8f8f8, #f1f1f1);
  background-image: -ms-linear-gradient(to bottom, #f8f8f8, #f1f1f1);
  background-image: -o-linear-gradient(to bottom, #f8f8f8, #f1f1f1);
  background-image: linear-gradient(to bottom, #f8f8f8, #f1f1f1);
  border-color: #c6c6c6 !important;
  color: #222;

  border-width: 1px;
  border-color: transparent !important;
  background-color: rgba(var(--luckysheet-main-color-rgb), 0.16);
  background-image: none;
  cursor: pointer;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  cursor: pointer;
}

.luckysheet-toolbar-combo-button:hover .luckysheet-toolbar-combo-button-input {
  border-right-color: rgba(0, 0, 0, 0.12);
}

.luckysheet-toolbar-combo-button-open {
  color: #222;
  border-width: 1px;
  border-color: transparent !important;
  background-color: rgba(0, 0, 0, 0.12);
  background-image: none;
  cursor: pointer;
}

.luckysheet-toolbar-combo-button-open .luckysheet-toolbar-combo-button-input {
  background: transparent;
  border-right: 1px solid transparent !important;
}

.luckysheet-toolbar-combo-button-outer-box,
.luckysheet-toolbar-combo-button-inner-box {
  border: 0;
  vertical-align: top;
  margin: 0;
  padding: 0;
}

/* .luckysheet-toolbar-combo-button-inner-box {
    padding: 0 2px;
    margin: 0 1px;
} */

.luckysheet-toolbar-zoom-combobox .luckysheet-toolbar-combo-button-caption {
  /* width: 48px !important; */
  width: 36px !important;
}

.luckysheet-toolbar-combo-button-caption {
  padding: 0;
  margin: 0 0 0 -3px;
}

.luckysheet-toolbar-combo-button-input {
  background: transparent;
  border: 1px solid transparent !important;
  color: #333;
  font-family: Arial, sans-serif !important;
  font-size: 11px !important;
  font-weight: bold !important;
  height: 20px !important;
  overflow: hidden !important;
  color: rgba(0, 0, 0, 0.7);
  height: 22px !important;
  width: 22px;
}

.luckysheet-toolbar-combo-button-input:focus {
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  background: #fff;
  outline: none;
  border: 1px solid var(--luckysheet-main-color) !important;
}

.luckysheet-toolbar-textinput {
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  border: 1px solid #d9d9d9;
  border-top: 1px solid #c0c0c0;
  font-size: 13px;
  height: 25px;
  /* padding: 1px 8px; */
  padding: 1px 0px 1px 8px;
}

.luckysheet-toolbar-combo-button-dropdown {
  /* background: url(arrow-down.png) center no-repeat; */
  float: right;
  margin: 9px 0 0 0px;
  padding: 0 0 0 1px;
  min-width: 7px;
  opacity: 0.8;
  vertical-align: middle;
  width: 5px;
  height: 7px;

  margin-top: 10px;
}

/*menubar*/
.luckysheet-toolbar-color-menu-button .luckysheet-toolbar-menu-button-caption {
  top: -2px;
}

.luckysheet-color-menu-button-indicator {
  position: relative;
  /* border-bottom: 4px solid #f0f0f0; */
  height: 20px;
  /* border-bottom-color: transparent; */
}

.luckysheet-color-menu-button-indicator .text-color-bar {
  position: absolute;
  bottom: 0px;
  background-color: #0081f9;
  height: 3px;
  width: 55%;
  left: 30%;
}

.luckysheet-toolbar-button-inner-box .luckysheet-icon,
.luckysheet-toolbar-menu-button-caption .luckysheet-icon,
.luckysheet-toolbar-menu-button-caption
  .luckysheet-color-menu-button-indicator
  .luckysheet-icon {
  margin-top: 0px;
}

/*menubutton*/
.luckysheet-toolbar-menu-button-caption {
  padding: 0;
  margin: 0;
}

.luckysheet-toolbar-menu-button-inner-box {
  margin: 0px 2px;
}

.luckysheet-toolbar-menu-button-dropdown {
  /* background: url(arrow-down.png) center no-repeat; */
  float: right;
  margin: 10px 2px 0 3px;
  padding: 0;
  opacity: 0.8;
  vertical-align: middle;
  width: 5px;
  height: 7px;

  margin-left: 4px;
  margin-right: 0;

  margin-top: 10px;
}

.luckysheet-toolbar-button-split-right
  .luckysheet-toolbar-menu-button-dropdown {
  padding: 0px 3px;
}

/* .luckysheet-toolbar-select {
    margin-left: 1px;
} */

/*split*/
.luckysheet-toolbar-button-split-left,
.luckysheet-toolbar-button-split-left * {
  border-top-right-radius: 0 !important;
  border-bottom-right-radius: 0 !important;
  margin-right: 0;
}

.luckysheet-toolbar-button-split-right,
.luckysheet-toolbar-button-split-right * {
  border-top-left-radius: 0 !important;
  border-bottom-left-radius: 0 !important;
  min-width: 5px !important;
  margin-left: 0;
}

#luckysheet-icon-merge-menu {
  margin-right: 1px;
}

/*button*/
.luckysheet-toolbar-button,
.luckysheet-toolbar-menu-button {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  background: 0;
  border: 1px solid transparent;
  outline: none;
  padding: 0;
  list-style: none;
  font-size: 11px;
  /* font-weight: bold; */
  text-decoration: none;
  vertical-align: middle;
  cursor: default;

  /* margin: 3px -3px 0px 2px; */
  height: 26px;
  line-height: 26px;
  /* color: rgba(0, 0, 0, .7); */
  color: #333;
}

.luckysheet-toolbar-button:hover,
.luckysheet-toolbar-menu-button:hover,
.luckysheet-toolbar-button-hover {
  border: 1px solid transparent;
  background-color: rgba(var(--luckysheet-main-color-rgb), 0.16);
  background-image: none;
  box-shadow: none;
  cursor: pointer;
}
/* 重新定义激活样式 */
.luckysheet-toolbar-button-hover {
  background-color: var(--luckysheet-main-color-a4);
}
.luckysheet-toolbar-button-hover:hover {
  background-color: var(--luckysheet-main-color-a4);
}

.luckysheet-toolbar-button-split-right.luckysheet-toolbar-menu-button:hover {
  border-left-color: rgba(0, 0, 0, 0.12) !important;
}

.luckysheet-toolbar-button-split-right-hover {
  border-width: 1px;
  border-color: transparent !important;
  background-color: rgba(var(--luckysheet-main-color-rgb), 0.16);
  cursor: pointer;
  box-shadow: none;
  border-left-color: rgba(0, 0, 0, 0.12) !important;
}

.luckysheet-toolbar-button:active,
.luckysheet-toolbar-menu-button:active {
  border: 1px solid transparent;
  background-color: rgba(0, 0, 0, 0.12);
  background-image: none;
  box-shadow: none;
  cursor: pointer;
}

.luckysheet-toolbar-button-outer-box,
.luckysheet-toolbar-menu-button-outer-box {
  border: 0;
  vertical-align: top;
  margin: 0;
  padding: 0;
}

.luckysheet-toolbar-button-inner-box,
.luckysheet-toolbar-menu-button-inner-box {
  padding: 0 2px;
  padding: 0;
  text-align: center;
  height: 26px;
  min-width: 26px;
}

.luckysheet-icon {
  direction: ltr;
  /* text-align: left; */
  text-align: center;
  overflow: hidden;
  vertical-align: middle;
  /* height: 18px;
    width: 18px; */
  height: 26px;
  width: 26px;
  /* margin: 1px 2px 2px 1px; */
  margin: 2px;
  /* opacity: .54; */
}

#luckysheet-icon-fmt-other .luckysheet-toolbar-menu-button-caption {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 55px;
  margin-left: 1px;
  text-align: center;
}

#luckysheet-icon-font-family .luckysheet-toolbar-menu-button-caption {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 55px;
  margin-left: 1px;
  /* text-align: left; */
  text-align: center;
}

#luckysheet-icon-function .luckysheet-toolbar-menu-button-caption {
  margin-right: 5px;
  margin-left: -5px;
}

#luckysheet-icon-function .luckysheet-icon {
  margin-right: 0px;
}

/* .luckysheet-icon-autofilter {
    left: -36px;
    top: -406px;
} */

#luckysheet-icon-pivotTable .luckysheet-toolbar-menu-button-caption {
  color: var(--luckysheet-main-color);
}

#luckysheet-icon-chart .luckysheet-toolbar-menu-button-caption {
  color: var(--luckysheet-main-color);
}

.luckysheet-rightgclick-menu-sub .sp-container {
  background-color: #ffffff;
  border: solid 1px #ffffff;
}

#luckysheet-icon-text-color-menu-menuButton .sp-palette-container,
#luckysheet-icon-cell-color-menu-menuButton .sp-palette-container {
  margin-bottom: -300px;
}

#luckysheet-icon-text-color-menu-menuButton .sp-palette,
#luckysheet-icon-cell-color-menu-menuButton .sp-palette {
  margin-top: -10px;
}

/*toolbar菜单end*/

.luckysheet-wa-calculate {
  height: 28px;
  /*padding: 10px 8px;*/
  background: #fff;
  position: relative;
  padding-right: 44px;
  border-bottom: 1px solid #d4d4d4;
}

.luckysheet-wa-calculate-help {
  height: 100%;
  width: 99px;
  border-right: 1px solid #d4d4d4;
}

.luckysheet-wa-calculate-help-box {
  height: 100%;
  width: 85px;
  position: absolute;
  top: 0px;
  left: 0px;
}

#luckysheet-helpbox {
  left: 0;
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  resize: none;
  /* border: 1px #b9b9b9 solid; */
  font-family: arial, sans, sans-serif;
  font-size: 14px;
  line-height: 14px;
  background-color: #ffffff;
  padding: 2px 5px;
}

.luckysheet-helpbox-cell-input {
  width: 100%;
  height: 100%;
  margin: 0;
  outline: none;
  cursor: text;
  -webkit-user-modify: read-write-plaintext-only;
  white-space: nowrap;
  overflow: hidden;
  -webkit-transform: translateZ(0);
  background-color: white;
  word-wrap: break-word;
  -webkit-nbsp-mode: space;
  -webkit-line-break: after-white-space;
}

.luckysheet-wa-calculate-help-tool {
  position: absolute;
  left: 85px;
  text-align: center;
  height: 100%;
  width: 13px;
  border-left: 1px solid transparent;
  border-right: 1px solid transparent;
}
.luckysheet-wa-calculate-help-tool .fa-caret-down {
  position: absolute;
  top: 50%;
  left: 3px;
  transform: translateY(-50%);
}

.luckysheet-wa-calculate-help-tool:hover {
  background: #efefef;
  cursor: pointer;
  border-left: 1px solid #e5e5e5;
  border-right: 1px solid #e5e5e5;
}

.luckysheet-wa-calculate-size {
  position: absolute;
  height: 3px;
  width: 100%;
  left: 0px;
  bottom: 0px;
  z-index: 1000;
}

.luckysheet-wa-calculate-size:hover {
  background: var(--luckysheet-main-color-7) !important;
  cursor: ns-resize;
}

#luckysheet-wa-functionbox-cancel {
  left: 104px;
  /* font-size: 18px; */
}

#luckysheet-wa-functionbox-confirm {
  left: 130px;
  /* font-size: 18px; */
}

.luckysheet-wa-functionbox {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  /*background-color: #fff;*/
  text-align: center;
  left: 156px;
  color: #d6d6d6;
}

.luckysheet-wa-functionbox span {
  vertical-align: middle;
  width: 30px;
  height: 30px;
}

.luckysheet-wa-functionbox i {
  font-size: 24px;
}

.luckysheet-wa-calculate-active {
  color: #585858;
  cursor: pointer;
}

.luckysheet-wa-calculate-active:hover {
  color: var(--luckysheet-main-color);
}

.luckysheet-grid-container {
  width: 100%;
  position: absolute;
  top: 90px;
  bottom: 0px;
}

.luckysheet-stat-area {
  position: absolute;
  height: 23px;
  bottom: 0px;
  background: #ff00dc;
  width: 100%;
}

.luckysheet-sta-c {
  height: 22px;
  background-color: #fff;
  border-top: 1px solid #e1e1e1;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.luckysheet-stat-area:hover .luckysheet-sta-c {
  -moz-user-select: -moz-all;
  -khtml-user-select: initial;
  -webkit-user-select: initial;
  -ms-user-select: initial;
  user-select: initial;
}

.luckysheet-sta-c .luckysheet-sta-content {
  /* position: absolute; */
  /* left: 0px; */
  /* right: 358px; */
  height: 22px;
  line-height: 22px;
  text-align: right;
  white-space: nowrap;
  overflow: hidden;
}

.luckysheet-sta-c .luckysheet-sta-content span {
  margin-right: 10px;
}

.luckysheet-grid-window {
  position: absolute;
  top: 0px;
  bottom: 23px;
  left: 0;
  right: 0px;
  overflow: hidden;
  background: #00ffff;
}

.luckysheet-sheet-area {
  width: 100%;
  box-sizing: border-box;
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  /* background-color: #f6f6f6; */
  background-color: #fafafc;
  color: #444;
  height: 31px;
  padding: 0 0 0 44px;
  margin: 0;
  -webkit-touch-callout: none;
  cursor: default;
  transition: 0.3s ease all;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
#luckysheet-sheet-content {
  width: 0;
  flex: 3;
  display: flex;
  align-items: center;
}
#luckysheet-sheet-content .lucky-button-custom {
}

#luckysheet-bottom-pager {
  width: 0;
  background-color: #fafafc;
  z-index: 1;
  flex: 2;
  text-align: right;
  white-space: nowrap;
}

.luckysheet-sheet-area > div,
.luckysheet-sheet-area .luckysheet-sheets-item {
  display: inline-block;
  /* margin-right: 6px;
    margin-top: 1px;
    padding: 1px 6px; */
  /* padding: 6px 10px; */
}

/* .luckysheet-sheet-area .luckysheet-sheets-add {
    margin-left: 47px;
} */

/* div.luckysheet-sheets-add,
div.luckysheet-sheets-m,
div.luckysheet-sheets-scroll {
    background: #F6F6F6;
    border: 1px solid #F6F6F6;
    color: #4E4E4E;
} */

div.luckysheet-sheets-scroll {
  display: none;
}

div.luckysheet-sheets-add:hover,
div.luckysheet-sheets-m:hover {
  color: #2a2a2a;
}

.docs-sheet-fade {
  position: absolute;
  display: block;
  top: 0;
  width: 6px;
  height: 100%;
  /* z-index: 1005; */
  z-index: 1; /*因为会覆盖右击菜单，改为1*/
}

.docs-sheet-fade div {
  background-color: #d7d7d7;
  width: 2px;
  float: right;
  position: relative;
  height: 100%;
}

.docs-sheet-fade-left {
  left: 0;
}

.docs-sheet-fade-right {
  right: 0;
}

.docs-sheet-fade1 {
  opacity: 0.82;
  filter: alpha(opacity=82);
}

.docs-sheet-fade2 {
  opacity: 0.62;
  filter: alpha(opacity=62);
}

.docs-sheet-fade3 {
  opacity: 0.4;
  filter: alpha(opacity=40);
}

.luckysheet-sheet-area div.luckysheet-sheet-container {
  padding: 0px 0px;
  margin-left: 0px;
  position: relative;
  max-width: 70%;
  vertical-align: bottom;
  display: inline-block;
}

.luckysheet-sheet-area
  div.luckysheet-sheet-container
  div.luckysheet-sheet-container-c {
  padding: 0px 0px;
  margin-left: 0px;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  max-width: 100%;
  vertical-align: bottom;
  display: inline-block;
}

.luckysheet-sheet-container-menu-hide .luckysheet-sheets-item {
  padding-right: 5px !important;
}

.luckysheet-sheet-container-menu-hide .luckysheet-sheets-item-menu {
  display: none !important;
}

.luckysheet-sheet-area div.luckysheet-sheets-item {
  padding: 2px 6px;
  height: 100%;
  line-height: 29px;
  /* box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.3) inset; */
  /* background-color: #f7f7f7; */
  background-color: #fafafc;
  /* border-color: #000; */
  /* border-top-color: #aaa; */
  /* border-width: 0 1px 1px 1px; */
  color: #676464;
  min-width: 30px;
  top: 0px;
  position: relative;
  /* border-radius: 0 0 2px 2px; */
  margin-right: -1px;
  cursor: pointer;
  /* border-bottom: 1px solid #cacaca; */
  transition: all 0.1s;
  /* padding-right: 20px; */
  font-size: 13px;

  padding: 2px 19px 0px 5px;
  box-sizing: border-box;
  border-left: 1px solid #e0e0e0;
  border-bottom: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
  /* display:flex;
    justify-content: center;
    align-items: center;
    height: 100%; */
}

.luckysheet-sheet-area div.luckysheet-sheets-item:last-child {
  margin-right: 1px;
}
.luckysheet-sheet-area div.luckysheet-sheets-item:hover {
  background-color: rgba(var(--luckysheet-main-color-rgb), 0.16);
  /* border-color: #a5a5a5; */
  color: #490500;
}

.luckysheet-sheet-area div.luckysheet-sheets-item .luckysheet-sheets-item-menu {
  margin-left: 2px;
  display: inline-block;
  top: -2px;
  position: relative;
  color: #a1a1a1;
  position: absolute;
  height: 100%;
  width: 15px;
  right: 0px;
  text-align: center;
}

.luckysheet-sheet-area
  div.luckysheet-sheets-item
  .luckysheet-sheets-item-menu:hover {
  color: #2a2a2a;
  cursor: pointer;
}

.luckysheet-sheet-area div.luckysheet-sheets-item .luckysheet-sheets-item-name {
  padding: 0px 3px;
}

.luckysheet-sheet-area
  div.luckysheet-sheets-item
  .luckysheet-sheets-item-name[contenteditable="true"] {
  border: 1px solid #d9d9d9;
  display: inline-block;
  height: 18px;
  line-height: 18px;
  min-width: 8px;
  margin: -4px -1px;
  -moz-user-modify: read-write-plaintext-only;
  -webkit-user-modify: read-write-plaintext-only;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  -webkit-user-select: text !important;
}

.luckysheet-sheet-area
  div.luckysheet-sheets-item
  .luckysheet-sheets-item-name[contenteditable="true"]:focus {
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  border: 1px solid #4d90fe;
  outline: none;
}

.luckysheet-sheet-area div.luckysheet-sheets-item-active {
  /* padding: 2px 8px; */
  height: 100%;
  line-height: 29px;
  background-color: var(--luckysheet-main-color-a4);
  /* border-color: #aaa; */
  border-top-color: #fff;
  color: #222;
  /* box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2); */
  cursor: default;
  /* top: -2px; */
  /* border-bottom: none; */
  /* padding-right: 20px; */
}

.luckysheet-sheet-area div.luckysheet-sheets-item-active:hover {
  background-color: var(--luckysheet-main-color-a4);
  /* border-color: #aaa; */
  color: #222;
}

.luckysheet-grid-window-1 {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 27px;
  left: 0;
  min-width: 200px;
  background-color: #ffffff !important;
  overflow: hidden;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.luckysheetTableContent {
  position: absolute;
  z-index: 2;
  left: 0px;
  top: 0px;
  width: 100%;
  height: 100%;
  pointer-events: none;
  -moz-user-select: -moz-none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.luckysheet-grid-window-2 {
  /*background-color: #fff !important;*/
  height: 100%;
  width: 100%;
  outline: none;
  border-collapse: collapse;
  display: table;
}

.luckysheet-paneswrapper {
  overflow: hidden;
  height: 1px;
}

.luckysheet-left-top {
  width: 44.5px;
  height: 18.5px;
  border: solid 0 #dfdfdf;
  position: relative;
  padding-top: 0;
  border-width: 0 1px 1px 0;
  margin: -1px 0 0 -1px;
  padding-left: 0;
  cursor: pointer;
}

.luckysheet-cols-h-c {
  color: #5e5e5e;
  overflow: hidden;
  padding: 0;
  cursor: default;
  /*width: 1548px;*/
  height: 19px;
  outline-style: none;
  position: relative;
  -webkit-user-select: none;
  background: #f3f3f2;
}

.luckysheet-rows-h {
  position: relative;
  outline-style: none;
  color: #5e5e5e;
  overflow: hidden;
  padding: 0;
  margin-top: -2px;
  padding-top: 2px;
  cursor: default;
  /*height: 472px;*/
  width: 38px;
  background: #f3f3f2;
}

.luckysheet-cols-menu-btn {
  color: #5e5e5e;
  cursor: pointer;
  position: absolute;
  z-index: 12;
  border: 1px solid #5e5e5e;
  border-radius: 1px;
  top: 3px;
  margin-left: 0px;
  display: none;
  padding: 0px 2px;
  font-size: 12px;
  height: 12px;
  opacity: 0.5;
  /*transition: all 0.1s;*/
}

.luckysheet-cols-menu-btn:hover {
  opacity: 1;
}

.luckysheet-cols-h-hover {
  color: #5e5e5e;
  cursor: default;
  position: absolute;
  z-index: 11;
  border: 0 none;
  bottom: 0;
  height: 100%;
  margin-left: 0px;
  display: none;
  /*transition: all 0.1s;*/
  background-color: var(--luckysheet-main-color-a4);
}

.luckysheet-cols-h-selected {
  color: var(--luckysheet-main-color);
  cursor: default;
  position: absolute;
  z-index: 10;
  background-color: var(--luckysheet-main-color-a1) !important;
  border-bottom: 1px solid var(--luckysheet-main-color);
  bottom: 0;
  height: 100%;
  margin-left: 0px;
  display: none;
  transition: all 0.1s;
  background-color: rgba(76, 76, 76, 0.1);
  /*left: 2489px;
             width: 863px;*/
}

.luckysheet-cols-h-cells {
  margin: 0;
  padding: 0;
  border: none 0;
  position: relative;
  height: inherit;
  /*width: 4721px;*/
}

.luckysheet-cols-h-cells-c {
  color: #5e5e5e;
  cursor: default;
  width: 5000000px;
  height: inherit;
}

.luckysheet-cols-h-cells-clip {
  color: #5e5e5e;
  cursor: default;
  margin: 0;
  padding: 0;
  border: none 0;
  position: relative;
  float: left;
  direction: ltr;
  height: inherit;
  width: 2561px;
  color: #5e5e5e;
  border-bottom: solid 1px #bbb;
  position: relative;
  top: -1px;
}

.luckysheet-cols-h-cell-nosel {
  position: absolute;
  cursor: pointer;
  border: 0 solid;
  border-color: #dfdfdf;
  display: inline-block;
  min-height: 19px;
  touch-action: manipulation;
  border-right-width: 1px;
  height: inherit;
}

.luckysheet-cols-h-cell-sel {
  direction: ltr;
  height: inherit;
  position: absolute;
  cursor: pointer;
  border: 0 solid;
  border-color: #bfbfbf;
  background-color: #e1e1e1;
  display: inline-block;
  min-height: 19px;
  color: var(--luckysheet-main-color);
  font-weight: bold;
  touch-action: manipulation;
  border-right-width: 1px;
}

.luckysheet-col-flow-h {
  float: left;
  direction: ltr;
  position: relative;
  margin: 0;
  padding: 0;
  border: none 0;
  height: inherit;
  overflow: hidden;
}

.luckysheet-col-flow-h-sheet {
  width: inherit;
  height: inherit;
  position: relative;
  float: left;
  direction: ltr;
}

body:not(.ewa-ipad) .luckysheet-cols-h-cell-nosel:hover,
body:not(.ewa-ipad) .luckysheet-cols-h-cell-sel:hover,
body:not(.ewa-ipad) .luckysheet-rows-h-cell-nosel:hover,
body:not(.ewa-ipad) .luckysheet-rows-h-cell-sel:hover {
  background-color: #fcc3c3;
}

.luckysheet-cols-h-cell-txt {
  cursor: pointer;
  height: inherit;
  position: relative;
  text-align: center;
  overflow: hidden;
  touch-action: manipulation;
  font-size: 14px;
  padding-top: 2px;
}

.luckysheet-rows-h {
  position: relative;
  outline-style: none;
  color: #5e5e5e;
  overflow: hidden;
  padding: 0;
  margin-top: -2px;
  padding-top: 2px;
  cursor: default;
  width: 45px;
}

.luckysheet-rows-h-hover {
  position: absolute;
  z-index: 11;
  border: 0 none;
  right: 0;
  width: 100%;
  margin-top: 2px;
  display: none;
  /*transition: all 0.1s;*/
  background-color: var(--luckysheet-main-color-a4);
}

.luckysheet-rows-h-selected {
  position: absolute;
  z-index: 10;
  background-color: var(--luckysheet-main-color-a1) !important;
  border-right: 1px solid var(--luckysheet-main-color);
  right: 0;
  width: 100%;
  margin-top: 2px;
  display: none;
  transition: all 0.1s;
  background-color: rgba(76, 76, 76, 0.1);
  /*top: 106px;
             height: 145px;*/
}

.luckysheet-rows-h-cells {
  margin: 0;
  padding: 0;
  border: none 0;
  position: relative;
  width: 100%;
}

.luckysheet-rows-h-cells-c {
  margin: 0;
  padding: 0;
  border: none 0;
  position: relative;
  float: left;
  direction: ltr;
  width: 100%;
}

.luckysheet-rows-h-cells-clip {
  cursor: default;
  color: #5e5e5e;
  direction: ltr;
  border-right: solid 1px #bbb;
  width: inherit;
  height: inherit;
  position: relative;
  left: -1px;
  height: inherit;
}

.luckysheet-rows-h-cell-nosel {
  direction: ltr;
  width: 100%;
  position: absolute;
  cursor: pointer;
  border: 0 solid;
  border-color: #dfdfdf;
  border-bottom-width: 1px;
  touch-action: manipulation;
}

.luckysheet-rows-h-cell-sel {
  direction: ltr;
  width: 100%;
  position: absolute;
  cursor: pointer;
  border: 0 solid;
  border-color: #bfbfbf;
  background-color: #e1e1e1;
  border-bottom-width: 1px;
  color: var(--luckysheet-main-color);
  font-weight: bold;
  touch-action: manipulation;
}

.luckysheet-rows-h-cell-txt {
  width: 100%;
  position: absolute;
  bottom: 0;
  text-align: center;
  padding-bottom: 1px;
  max-height: 100%;
  overflow: hidden;
  font-size: 14px;
}

.luckysheet-cell-loading {
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.3);
  position: absolute;
  overflow: hidden;
  outline-style: none;
  cursor: not-allowed;
  font-size: 28px;
  z-index: 2;
  display: none;
}

.luckysheet-cell-loading-inner {
  position: relative;
  top: 40%;
  width: 100%;
  margin: 0 auto;
  text-align: center;
}

.luckysheet-cell-loading-inner span {
  margin-left: 10px;
}

.luckysheet-cell-main {
  /*border: solid 1px #BEC1C4;*/
  background-color: white;
  width: 15px;
  height: 15px;
  background-color: #f3f3f2;
  /*#E7E7E7;*/
  border-collapse: collapse;
  position: relative;
  overflow: hidden;
  outline-style: none;
  cursor: default;
}

.luckysheet-scrollbars,
.luckysheet-menu {
  scrollbar-base-color: #ffffff;
  scrollbar-track-color: #e7e7e7;
  scrollbar-darkshadow-color: #ffffff;
  scrollbar-3dlight-color: #ffffff;
  scrollbar-arrow-color: #757778;
  scrollbar-shadow-color: #bec1c4;
  scrollbar-highlight-color: #bec1c4;
  -ms-scroll-chaining: none;
  overflow: auto;
}

.luckysheet-scrollbar-ltr {
  position: absolute;
  overflow: hidden;
  z-index: 1003;
}

.luckysheet-scrollbar-ltr div {
  height: 1px;
  width: 1px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button {
  height: 0;
  width: 0;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:start {
  display: none;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:end {
  display: block;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button {
  border: 1px solid #d9d9d9;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal {
  border-width: 1px 0 0 0;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical {
  border-width: 0 0 0 1px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical {
  border-width: 0 1px 0 0;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal:increment {
  background: no-repeat url("waffle_sprite.png") -663px -13px;
  width: 15px;
  padding-left: 1px;
  background-clip: border-box;
  border: 1px solid #d9d9d9;
  border-width: 1px 0 0 0;
  box-shadow: none;
  background-color: #f8f8f8;
  border-bottom: 1px solid #d9d9d9;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal:increment:hover {
  background: no-repeat url("waffle_sprite.png") -395px -62px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal:increment:active {
  background: no-repeat url("waffle_sprite.png") -679px -13px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal:decrement {
  border-left: 1px solid #d9d9d9;
  border-right: 1px solid #d9d9d9;
  background: no-repeat url("waffle_sprite.png") -283px -62px;
  width: 17px;
  border-bottom: 1px solid #d9d9d9;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal:hover {
  background: no-repeat url("waffle_sprite.png") -145px -70px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:horizontal:active {
  background: no-repeat url("waffle_sprite.png") -552px 0;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical:increment {
  padding-top: 1px;
  background: no-repeat url("waffle_sprite.png") -531px -24px;
  border-left: 1px solid #d9d9d9;
  height: 15px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical:increment:hover {
  background: no-repeat url("waffle_sprite.png") -570px -42px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical:increment:active {
  background: no-repeat url("waffle_sprite.png") -83px -46px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical:decrement {
  border-top: 1px solid #d9d9d9;
  border-bottom: 1px solid #d9d9d9;
  border-left: 1px solid #d9d9d9;
  background: no-repeat url("waffle_sprite.png") -631px -27px;
  height: 17px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical:decrement:hover {
  background: no-repeat url("waffle_sprite.png") -180px -58px;
}

.luckysheet-scrollbar-ltr::-webkit-scrollbar-button:vertical:decrement:active {
  background: no-repeat url("waffle_sprite.png") -776px -28px;
}

.luckysheet-scrollbar-x {
  bottom: 0px;
  left: 44px;
  overflow-x: scroll;
}

.luckysheet-scrollbar-y {
  right: 0px;
  top: 0px;
  overflow-y: scroll;
}

.luckysheet-cell-flow {
  margin: 0;
  padding: 0;
  border: none 0;
  position: relative;
  touch-action: manipulation;
  overflow: hidden;
  /*background: #fff;*/
}

.luckysheet-cell-flow-clip {
  border-collapse: collapse;
  cursor: default;
  width: 5000000px;
  touch-action: manipulation;
  overflow: hidden;
}

.luckysheet-cell-flow-col {
  margin: 0;
  padding: 0;
  border: none 0;
  position: relative;
  touch-action: manipulation;
  overflow: hidden;
  float: left;
  direction: ltr;
}

.luckysheet-cell-sheettable {
  position: relative;
  /*background-color: #fff;*/
  text-align: left;
  font-size: 11pt;
  color: #000000;
  text-decoration: none;
}

.luckysheet-bottom-controll-row {
  position: absolute;
  height: 30px;
  /*width: 400px;*/
  /* background: #000; */
  bottom: 38px;
  left: 0px;
  z-index: 1000;
}

#luckysheet-bottom-add-row {
  padding: 5px 20px;
  margin-right: 5px;
  margin-top: -2px;
}

#luckysheet-bottom-add-row-input {
  width: 40px;
  min-width: 40px;
}

#luckysheet-bottom-return-top {
  padding: 5px 6px;
  margin-left: 10px;
  margin-top: -2px;
}

.luckysheet-cell-flow-column {
  position: absolute;
  height: inherit;
  width: inherit;
  top: 0;
  left: 0;
  z-index: 1;
  touch-action: manipulation;
}

.luckysheet-cell-flow-column-line {
  position: absolute;
  border-right: 1px solid #d4d4d4;
  height: inherit;
}

.luckysheet-cell-flow-row {
  text-align: left;
  position: absolute;
  height: inherit;
  width: inherit;
  top: 0;
  left: 0;
  z-index: 1;
  touch-action: manipulation;
}

.luckysheet-cell-flow-row-line {
  position: absolute;
  border-bottom: 1px solid #d4d4d4;
  width: inherit;
}

.luckysheet-cell-selected-focus {
  position: absolute;
  pointer-events: none;
  z-index: 14;
  /*border:1px solid #fff;*/
  margin: 0px 0 0 0px;
  background: rgba(85, 187, 138, 0.15);
  display: none;
  /*transition: all 0.1s;*/
}

.luckysheet-selection-copy {
  position: absolute;
  pointer-events: none;
  z-index: 18;
  border: none;
  margin: 0px 0 0 0px;
  display: none;
}

.luckysheet-selection-copy .luckysheet-copy {
  position: absolute;
  z-index: 18;
  background-color: transparent;
}

.luckysheet-selection-copy-top {
  left: 0;
  right: 0;
  height: 2px;
  top: 0;
  background-position: bottom;
  background-image: url("EwaAntH-cover.gif");
}

.luckysheet-selection-copy-right {
  top: 0;
  bottom: 0;
  width: 2px;
  right: 0;
  background-image: url("EwaAntV-cover.gif");
}

.luckysheet-selection-copy-bottom {
  left: 0;
  right: 0;
  height: 2px;
  bottom: 0;
  background-image: url("EwaAntH-cover.gif");
}

.luckysheet-selection-copy-left {
  top: 0;
  bottom: 0;
  width: 2px;
  left: 0;
  background-position: right;
  background-image: url("EwaAntV-cover.gif");
}

.luckysheet-selection-copy-hc {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  border: 2px dashed var(--luckysheet-main-color-6);
  z-index: 8;
}

.luckysheet-selection-highlight {
  position: absolute;
  /*pointer-events: none;*/
  z-index: 14;
  border: none;
  margin: 0px 0 0 0px;
  display: none;
}

.luckysheet-formula-functionrange-highlight .luckysheet-copy {
  background-image: none;
  background: var(--luckysheet-main-color);
  position: absolute;
  z-index: 18;
  cursor: move;
  opacity: 0.9;
  /*border: 1px solid #fff;*/
}

.luckysheet-formula-functionrange-highlight .luckysheet-selection-copy-top {
  top: -2px;
  border-top: 2px solid #fff;
  border-bottom: 2px solid #fff;
}

.luckysheet-formula-functionrange-highlight .luckysheet-selection-copy-right {
  right: -2px;
  border-left: 2px solid #fff;
  border-right: 2px solid #fff;
}

.luckysheet-formula-functionrange-highlight .luckysheet-selection-copy-bottom {
  bottom: -2px;
  border-top: 2px solid #fff;
  border-bottom: 2px solid #fff;
}

.luckysheet-formula-functionrange-highlight .luckysheet-selection-copy-left {
  left: -2px;
  border-left: 2px solid #fff;
  border-right: 2px solid #fff;
}

.luckysheet-formula-functionrange-highlight .luckysheet-selection-copy-hc {
  border: 2px solid #5e5e5e;
  opacity: 0.03;
  z-index: initial;
}

.luckysheet-selection-highlight-topleft {
  left: -3px;
  top: -3px;
  cursor: se-resize;
}

.luckysheet-selection-highlight-topright {
  right: -3px;
  top: -3px;
  cursor: ne-resize;
}

.luckysheet-selection-highlight-bottomleft {
  left: -3px;
  bottom: -3px;
  cursor: ne-resize;
}

.luckysheet-selection-highlight-bottomright {
  right: -3px;
  bottom: -3px;
  cursor: se-resize;
}

.luckysheet-formula-functionrange-highlight .luckysheet-highlight {
  position: absolute;
  z-index: 19;
  border: 1px solid #fff;
  background: var(--luckysheet-main-color);
  width: 6px;
  height: 6px;
}

.luckysheet-cell-selected-extend {
  position: absolute;
  pointer-events: none;
  z-index: 16;
  border: 1px dashed var(--luckysheet-main-color);
  margin: -1px 0 0 -1px;
  display: none;
  /*transition: all 0.1s;*/
}

.luckysheet-cell-selected-move {
  position: absolute;
  pointer-events: none;
  z-index: 16;
  border: 2px solid var(--luckysheet-main-color);
  margin: -1px 0 0 -1px;
  display: none;
  /*transition: all 0.1s;*/
}

.luckysheet-cell-selected {
  position: absolute;
  pointer-events: none;
  z-index: 15;
  border: 1px solid var(--luckysheet-main-color) !important;
  margin: -1px 0 0 -1px;
  background: var(--luckysheet-main-color-a2);
  display: none;
  /*transition: all 0.1s;*/
}

.luckysheet-cs-inner-border {
  pointer-events: none;
  border: 1px solid #fff;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
}

.luckysheet-cs-fillhandle {
  position: absolute;
  width: 6px;
  height: 6px;
  bottom: -5px;
  cursor: crosshair;
  background-color: var(--luckysheet-main-color);
  border: solid 1px #fff;
  z-index: 16;
  pointer-events: auto;
  right: -5px;
}

.luckysheet-cs-draghandle {
  position: absolute;
  cursor: move;
  background-color: #fff;
  opacity: 0.01;
  z-index: 15;
  pointer-events: auto;
  border: 2px solid #fff;
}

.luckysheet-cs-draghandle-top {
  top: -4px;
  left: -2px;
  right: -2px;
  height: 2px;
}

.luckysheet-cs-draghandle-bottom {
  right: 0;
  left: -2px;
  bottom: -4px;
  height: 2px;
}

.luckysheet-cs-draghandle-left {
  top: 0;
  left: -4px;
  bottom: 0;
  width: 2px;
}

.luckysheet-cs-draghandle-right {
  top: 0;
  right: -4px;
  bottom: 0;
  width: 2px;
}

.luckysheet-cs-touchhandle {
  display: none;
  position: absolute;
  width: 16px;
  height: 16px;
  padding: 5px;
  z-index: 100;
  pointer-events: auto;
  touch-action: auto;
}

.luckysheet-cs-touchhandle:before {
  content: "";
  display: block;
  width: 16px;
  height: 16px;
  border: 0.5px solid rgba(0, 0, 0, 0.15);
  background-color: #ffffff;
  box-sizing: border-box;
  border-radius: 50%;
}

.luckysheet-cs-touchhandle-lt {
  left: -13px;
  top: -13px;
}

.luckysheet-cs-touchhandle-lb {
  left: -13px;
  bottom: -13px;
}

.luckysheet-cs-touchhandle-rt {
  right: -13px;
  top: -13px;
}

.luckysheet-cs-touchhandle-rb {
  right: -13px;
  bottom: -13px;
}

.luckysheet-cs-touchhandle .luckysheet-cs-touchhandle-btn {
  position: absolute;
  width: 10px;
  height: 10px;
  left: 8px;
  top: 8px;
  background-color: #018ffb;
  background-position: center;
  box-sizing: border-box;
  border-radius: 50%;
  z-index: 11;
}

#luckysheet-dynamicArray-hightShow {
  position: absolute;
  pointer-events: none;
  z-index: 15;
  border: 1px solid blue;
  margin: -1px 0 0 -1px;
  display: none;
}

.luckysheet-scrollbars::-webkit-scrollbar-track {
  background-color: #ffffff;
  border: 1px solid #d9d9d9;
}

.luckysheet-scrollbar-x::-webkit-scrollbar-track {
  border-left: 1px solid #d9d9d9;
  border-right: none;
}

.luckysheet-scrollbar-y::-webkit-scrollbar-track {
  border-top: none;
  border-bottom: none;
}

.luckysheet-scrollbars::-webkit-scrollbar {
  width: 12px;
  height: 12px;
  background-color: #ffffff;
  /*border:0 none;*/
}

.luckysheet-scrollbars::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  background-clip: padding-box;
  border: solid transparent;
  border-radius: 12px;
  border-width: 2px 1px 1px 2px;
  box-shadow: inset 1px 1px 0 rgba(0, 0, 0, 0.1),
    inset 0 -1px 0 rgba(0, 0, 0, 0.07);
}

.luckysheet-scrollbars::-webkit-scrollbar-thumb:hover {
  background-color: #969696;
  border: 1px solid #a0a0a0;
  border-radius: 12px;
}

.luckysheet-grdusedrange {
  position: absolute;
  visibility: hidden;
  width: 1px;
  height: 1px;
}

.luckysheet-grdblkflowpush {
  margin: 0;
  padding: 0;
  border: none 0;
  width: 1px;
}

.luckysheet-grdblkpush {
  margin: 0;
  padding: 0;
  border: none 0;
  height: 1px;
  float: left;
  direction: ltr;
}

.luckysheet-cell-flow-data {
  position: absolute;
  height: inherit;
  width: inherit;
  top: 0;
  left: 0;
  z-index: 1;
}

.luckysheet-cell-flow-data-row {
  position: absolute;
  width: inherit;
}

.luckysheet-cell-flow-data-cell {
  position: absolute;
  height: inherit;
}

.luckysheet-cell-flow-data-value {
  position: absolute !important;
  bottom: 0;
  letter-spacing: -0.02em;
  white-space: nowrap;
  padding-left: 2px;
  overflow: hidden;
}

.luckysheet canvas {
  position: absolute;
}

.luckysheetcolumeHeader {
  margin-left: -1px;
}

.luckysheetrowHeader {
  margin-top: 1px;
}

.luckysheetsheettable {
  margin-left: -1px;
  margin-top: -1px;
}

.luckysheet-cols-menu {
  padding: 10px 0;
  border-radius: var(--luckysheet-border-radius) !important;
  max-height: 100%;
  overflow-y: auto;
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  -webkit-transition: opacity 0.218s;
  -moz-transition: opacity 0.218s;
  -o-transition: opacity 0.218s;
  transition: opacity 0.218s;
  background: #fff;
  border: 1px solid #ccc;
  border: 1px solid rgba(0, 0, 0, 0.2);
  cursor: default;
  font-size: 13px;
  margin: 0;
  outline: none;
  position: absolute;
  z-index: 1004;
  box-sizing: border-box;
  user-select: none;
  display: none;
}

.luckysheet-cols-menu .luckysheet-cols-menuitem {
  position: relative;
  color: #333;
  cursor: pointer;
  list-style: none;
  margin: 0;
  /*padding: 6px 8em 6px 30px;*/
  padding: 1px 6em 1px 20px;
  white-space: nowrap;
  padding-left: 8px;
  vertical-align: middle;
  padding-right: 24px;
  user-select: none;
}

/* 右击菜单项目 hover背景色 */
.luckysheet-cols-menu .luckysheet-cols-menuitem:hover,
.luckysheet-cols-menu .luckysheet-cols-menuitem-hover {
  background: #efefef;
}

/* 处理激活样式 */
.luckysheet-cols-menu .luckysheet-cols-menuitem .icon {
  color: var(--luckysheet-main-color);
}

.luckysheet-cols-menu
  .luckysheet-cols-menuitem
  .luckysheet-cols-menuitem-content {
  position: relative;
  color: #333;
  cursor: pointer;
  list-style: none;
  margin: 0;
  padding: 6px 7em 6px 30px;
  white-space: nowrap;
  user-select: none;
}

.luckysheet-rightgclick-menu
  .luckysheet-cols-menuitem
  .luckysheet-cols-menuitem-content {
  position: relative;
  color: #333;
  cursor: pointer;
  list-style: none;
  margin: 0;
  padding: 6px 15px 6px 20px;
  white-space: nowrap;
  user-select: none;
}

#luckysheet-cols-menu .luckysheet-cols-menuitem,
.luckysheet-filter-menu .luckysheet-cols-menuitem,
#luckysheet-pivotTable-config-option .luckysheet-cols-menuitem {
  padding-right: 10px;
  padding-left: 12px;
}

#luckysheet-pivotTable-config-option-sumtype .luckysheet-cols-menuitem {
  padding-right: 15px;
  padding-left: 12px;
  padding-top: 1px;
  padding-bottom: 1px;
}

#luckysheet-cols-menu .luckysheet-cols-menuitem-content,
.luckysheet-filter-menu
  .luckysheet-cols-menuitem
  .luckysheet-cols-menuitem-content {
  padding-right: 10px;
  padding-left: 12px;
}
#luckysheet-sheet-list
  .luckysheet-cols-menuitem
  .luckysheet-cols-menuitem-content {
  padding-right: 0px;
  max-width: 430px;
  min-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
.luckysheet-filter-menu div.luckysheet-cols-menuitem {
  padding-top: 0px;
  padding-bottom: 0px;
}

.luckysheet-filter-submenu div.luckysheet-cols-menuitem {
  padding-top: 1px;
  padding-bottom: 1px;
}

.luckysheet-filter-menu .luckysheet-filter-byvalue .luckysheet-cols-menuitem,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-byvalue
  .luckysheet-cols-menuitem {
  padding-top: 2px;
  padding-bottom: 0px;
  cursor: default;
}

.luckysheet-filter-menu
  .luckysheet-filter-byvalue
  .luckysheet-cols-menuitem-content,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-byvalue
  .luckysheet-cols-menuitem-content {
  padding-top: 2px;
  padding-bottom: 0px;
  cursor: default;
}

.luckysheet-filter-menu
  .luckysheet-filter-byvalue
  .luckysheet-cols-menuitem-content
  input,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-byvalue
  .luckysheet-cols-menuitem-content
  input {
  height: 24px;
  width: 191px;
  padding-right: 25px;
  padding-left: 3px;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  border: 1px solid #d9d9d9;
  border-top: 1px solid #c0c0c0;
  font-size: 13px;
}

.luckysheet-filter-menu
  .luckysheet-filter-byvalue
  .luckysheet-cols-menuitem-content
  input:focus,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-byvalue
  .luckysheet-cols-menuitem-content
  input:focus {
  -webkit-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  -moz-box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.3);
  border: 1px solid #4d90fe;
  outline: none;
}

.luckysheet-filter-menu
  .luckysheet-filter-byvalue
  .luckysheet-cols-menuitem-content
  .luckysheet-filter-byvalue-input-icon,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-byvalue
  .luckysheet-cols-menuitem-content
  .luckysheet-pivotTableFilter-byvalue-input-icon {
  position: absolute;
  right: 17px;
  top: 7px;
}

.luckysheet-filter-menu
  .luckysheet-filter-byvalue
  .luckysheet-cols-menuitem:hover,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-byvalue
  .luckysheet-cols-menuitem:hover {
  background: #fff;
}

.luckysheet-filter-menu .luckysheet-cols-menuitem:last-child:hover {
  background: #fff;
}

.luckysheet-filter-menu .luckysheet-cols-menuitem:last-child,
.luckysheet-filter-menu
  .luckysheet-cols-menuitem:last-child
  .luckysheet-cols-menuitem-content {
  cursor: default;
}

#luckysheet-filter-byvalue-select,
#luckysheet-pivotTableFilter-byvalue-select {
  min-height: 100px;
  width: 200px;
}

.luckysheet-filter-menu .luckysheet-mousedown-filter-byvalue-btn span,
.luckysheet-filter-menu
  .luckysheet-mousedown-pivotTableFilter-byvalue-btn
  span {
  color: blue;
  cursor: pointer;
  text-decoration: underline;
}

.luckysheet-filter-menu .luckysheet-mousedown-filter-byvalue-btn div,
.luckysheet-filter-menu .luckysheet-mousedown-pivotTableFilter-byvalue-btn div {
  position: absolute;
  right: 14px;
  top: 0px;
  font-size: 18px;
}

.luckysheet-filter-menu
  .luckysheet-filter-bycondition
  .luckysheet-filter-selected-input,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-bycondition
  .luckysheet-pivotTableFilter-selected-input {
  padding-left: 8px;
  padding-right: 8px;
  margin-top: 3px;
  display: none;
}

.luckysheet-filter-menu
  .luckysheet-filter-bycondition
  .luckysheet-filter-selected-input
  input,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-bycondition
  .luckysheet-pivotTableFilter-selected-input
  input {
  height: 24px;
  width: 100%;
  padding-right: 3px;
  padding-left: 3px;
  margin-left: -3px;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  border: 1px solid #d9d9d9;
  border-top: 1px solid #c0c0c0;
  font-size: 13px;
}

.luckysheet-filter-menu
  .luckysheet-filter-bycondition
  .luckysheet-filter-selected-input2
  input,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-bycondition
  .luckysheet-pivotTableFilter-selected-input2
  input {
  height: 24px;
  width: 92px;
  padding-right: 3px;
  padding-left: 3px;
  margin-left: -3px;
  -webkit-border-radius: 1px;
  -moz-border-radius: 1px;
  border-radius: 1px;
  border: 1px solid #d9d9d9;
  border-top: 1px solid #c0c0c0;
  font-size: 13px;
}

.luckysheet-filter-menu
  .luckysheet-filter-bycondition
  .luckysheet-filter-selected-input2
  span,
.luckysheet-filter-menu
  .luckysheet-pivotTableFilter-bycondition
  .luckysheet-pivotTableFilter-selected-input2
  span {
  margin-left: 2px;
  margin-right: 5px;
}

.luckysheet-menuseparator {
  border-top: 1px solid #ebebeb;
  margin-top: 6px;
  margin-bottom: 6px;
}

.luckysheet-submenu-arrow {
  -webkit-transition: all 0.218s;
  -moz-transition: all 0.218s;
  -o-transition: all 0.218s;
  transition: all 0.218s;
  font-size: 12px;
  left: auto;
  right: -15px;
  padding-top: 1px;
  padding-right: 0;
  position: absolute;
  text-align: right;
  opacity: 0.5;
  filter: alpha(opacity=50);
  color: #000;
  user-select: none;
  font-family: Arial;
  line-height: 100%;
}

#luckysheet-pivotTable-config-option-sumtype .luckysheet-submenu-arrow {
  right: -5px;
  font-size: 16px;
  padding-top: 0px;
  color: blue;
}

#luckysheet-filter-byvalue-select table,
#luckysheet-pivotTableFilter-byvalue-select table {
  table-layout: fixed;
}

#luckysheet-filter-byvalue-select tr td,
#luckysheet-pivotTableFilter-byvalue-select tr td {
  padding: 2px 3px;
}

#luckysheet-filter-byvalue-select tr:hover td,
#luckysheet-pivotTableFilter-byvalue-select tr:hover td {
  background: #e1e1e1;
}

/*筛选改 -- pan*/
.luckysheet-cols-menu .cf:before,
.luckysheet-cols-menu .cf:after {
  content: "";
  display: table;
}

.luckysheet-cols-menu .cf:after {
  clear: both;
}

#luckysheet-filter-byvalue-select .yearBox .monthList,
#luckysheet-pivotTableFilter-byvalue-select .yearBox .monthList {
  padding-left: 20px;
}

#luckysheet-filter-byvalue-select .yearBox .dayList,
#luckysheet-pivotTableFilter-byvalue-select .yearBox .dayList {
  padding-left: 20px;
}

#luckysheet-filter-byvalue-select .yearBox .fa-caret-right,
#luckysheet-pivotTableFilter-byvalue-select .yearBox .fa-caret-right {
  padding: 0 2px;
  float: left;
  margin-top: 3px;
  cursor: pointer;
}

#luckysheet-filter-byvalue-select .count,
#luckysheet-pivotTableFilter-byvalue-select .count {
  color: gray;
  margin-left: 5px;
}

#luckysheet-filter-byvalue-select input[type="checkbox"],
#luckysheet-pivotTableFilter-byvalue-select input[type="checkbox"] {
  width: auto;
  height: auto;
  float: left;
}

/*颜色筛选 -- pan*/
#luckysheet-filter-orderby-color-submenu {
  font-size: 12px;
}

#luckysheet-filter-orderby-color-submenu .title {
  padding: 10px;
  font-weight: 600;
  color: #333;
  background-color: #f4f4f4;
  text-align: center;
}

#luckysheet-filter-orderby-color-submenu .item {
  padding: 5px 40px 5px 20px;
  cursor: pointer;
  position: relative;
}

#luckysheet-filter-orderby-color-submenu .item:hover {
  background-color: #d3d3d3;
}

#luckysheet-filter-orderby-color-submenu .item label {
  display: block;
  width: 70px;
  height: 20px;
  border: 1px solid #d1d1d1;
}

#luckysheet-filter-orderby-color-submenu .item input[type="checkbox"] {
  position: absolute;
  right: 10px;
  top: 6px;
}

#luckysheet-copy-content {
  position: fixed;
  height: 0px;
  width: 0px;
  left: -100px;
  /*overflow: hidden;*/
  /*text-indent: -999999px;*/
  padding-left: 999999px;
}

/* #luckysheet-copy-btn {
    position: absolute;
    visibility: hidden;
} */

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: normal;
  text-align: center;
  vertical-align: middle;
  -ms-touch-action: manipulation;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  white-space: nowrap;
  padding: 4px 8px;
  font-size: 14px;
  line-height: 1.42857143;
  border-radius: 2px;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.btn:focus,
.btn:active:focus,
.btn.active:focus,
.btn.focus,
.btn:active.focus,
.btn.active.focus {
  outline: thin dotted;
  outline: 5px auto -webkit-focus-ring-color;
  outline: none;
  outline-offset: -2px;
}

.btn:hover,
.btn:focus,
.btn.focus {
  color: #333333;
  text-decoration: none;
}

.btn:active,
.btn.active {
  outline: 0;
  background-image: none;
  -webkit-box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
  box-shadow: inset 0 3px 5px rgba(0, 0, 0, 0.125);
}

.btn.disabled,
.btn[disabled],
fieldset[disabled] .btn {
  cursor: not-allowed;
  opacity: 0.65;
  filter: alpha(opacity=65);
  -webkit-box-shadow: none;
  box-shadow: none;
}

a.btn.disabled,
fieldset[disabled] a.btn {
  pointer-events: none;
}

.btn-default {
  color: #333;
  background-color: #fff;
  border-color: #ccc;
}

.btn-default:focus,
.btn-default.focus {
  color: #333;
  background-color: #e6e6e6;
  border-color: #8c8c8c;
}

.btn-default:hover {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  color: #333;
  background-color: #e6e6e6;
  border-color: #adadad;
}

.btn-default:active:hover,
.btn-default.active:hover,
.open > .dropdown-toggle.btn-default:hover,
.btn-default:active:focus,
.btn-default.active:focus,
.open > .dropdown-toggle.btn-default:focus,
.btn-default:active.focus,
.btn-default.active.focus,
.open > .dropdown-toggle.btn-default.focus {
  color: #333;
  background-color: #d4d4d4;
  border-color: #8c8c8c;
}

.btn-default:active,
.btn-default.active,
.open > .dropdown-toggle.btn-default {
  background-image: none;
}

.btn-default.disabled:hover,
.btn-default[disabled]:hover,
fieldset[disabled] .btn-default:hover,
.btn-default.disabled:focus,
.btn-default[disabled]:focus,
fieldset[disabled] .btn-default:focus,
.btn-default.disabled.focus,
.btn-default[disabled].focus,
fieldset[disabled] .btn-default.focus {
  background-color: #fff;
  border-color: #ccc;
}

.btn-default .badge {
  color: #fff;
  background-color: #333;
}

.btn-primary {
  color: #ffffff;
  /* background-color: #337ab7; */
  background-color: #2d7ff9;
  /* border-color: #2e6da4; */
  border-color: transparent;
}

.btn-primary:focus,
.btn-primary.focus {
  color: #ffffff;
  background-color: #286090;
  /* border-color: #122b40; */
  border-color: transparent;
}

.btn-primary:hover {
  color: #ffffff;
  /* background-color: #286090; */
  background-color: #5391ff;
  /* border-color: #204d74; */
  border-color: transparent;
}

.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  color: #ffffff;
  /* background-color: #286090; */
  background-color: #5391ff;
  /* border-color: #204d74; */
}

.btn-primary:active:hover,
.btn-primary.active:hover,
.open > .dropdown-toggle.btn-primary:hover,
.btn-primary:active:focus,
.btn-primary.active:focus,
.open > .dropdown-toggle.btn-primary:focus,
.btn-primary:active.focus,
.btn-primary.active.focus,
.open > .dropdown-toggle.btn-primary.focus {
  color: #ffffff;
  background-color: #204d74;
  /* border-color: #122b40; */
  border-color: transparent;
}

.btn-primary:active,
.btn-primary.active,
.open > .dropdown-toggle.btn-primary {
  background-image: none;
}

.btn-primary.disabled,
.btn-primary[disabled],
fieldset[disabled] .btn-primary,
.btn-primary.disabled:hover,
.btn-primary[disabled]:hover,
fieldset[disabled] .btn-primary:hover,
.btn-primary.disabled:focus,
.btn-primary[disabled]:focus,
fieldset[disabled] .btn-primary:focus,
.btn-primary.disabled.focus,
.btn-primary[disabled].focus,
fieldset[disabled] .btn-primary.focus,
.btn-primary.disabled:active,
.btn-primary[disabled]:active,
fieldset[disabled] .btn-primary:active,
.btn-primary.disabled.active,
.btn-primary[disabled].active,
fieldset[disabled] .btn-primary.active {
  background-color: #337ab7;
  /* border-color: #2e6da4; */
  border-color: transparent;
}

.btn-primary .badge {
  color: #337ab7;
  background-color: #ffffff;
}

.btn-primary,
.label-default,
.list-group-item.active,
.list-group-item.active:hover,
.list-group-item.active:focus {
  background: var(--luckysheet-main-color);
  /* border-color: #726EFE; */
  border-color: transparent;
}

.btn-primary:hover,
.btn-primary:focus {
  /* background: #388cf5; */
  background: var(--luckysheet-main-color-7);
  /* border-color: #6864FE; */
  border-color: transparent;
}
.btn-primary:focus:active {
  outline: none !important;
}

.btn-danger {
  color: #fff;
  background-color: #d9534f;
  border-color: #d43f3a;
}

.btn-danger:focus,
.btn-danger.focus {
  color: #fff;
  background-color: #c9302c;
  border-color: #761c19;
}

.btn-danger:hover {
  color: #fff;
  background-color: #c9302c;
  border-color: #ac2925;
}

.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  color: #fff;
  background-color: #c9302c;
  border-color: #ac2925;
}

.btn-danger:active:hover,
.btn-danger.active:hover,
.open > .dropdown-toggle.btn-danger:hover,
.btn-danger:active:focus,
.btn-danger.active:focus,
.open > .dropdown-toggle.btn-danger:focus,
.btn-danger:active.focus,
.btn-danger.active.focus,
.open > .dropdown-toggle.btn-danger.focus {
  color: #fff;
  background-color: #ac2925;
  border-color: #761c19;
}

.btn-danger:active,
.btn-danger.active,
.open > .dropdown-toggle.btn-danger {
  background-image: none;
}

.btn-danger.disabled:hover,
.btn-danger[disabled]:hover,
fieldset[disabled] .btn-danger:hover,
.btn-danger.disabled:focus,
.btn-danger[disabled]:focus,
fieldset[disabled] .btn-danger:focus,
.btn-danger.disabled.focus,
.btn-danger[disabled].focus,
fieldset[disabled] .btn-danger.focus {
  background-color: #d9534f;
  border-color: #d43f3a;
}

.btn-danger .badge {
  color: #d9534f;
  background-color: #fff;
}

.luckysheet-cols-change-size,
.luckysheet-rows-change-size,
.luckysheet-change-size-line {
  /*display: none;*/
  position: absolute;
  z-index: 12;
}

.luckysheet-cols-change-size {
  width: 5px;
  height: 100%;
  background: var(--luckysheet-main-color);
  cursor: ew-resize;
  opacity: 0;
}

.luckysheet-rows-change-size {
  width: 100%;
  height: 5px;
  background: var(--luckysheet-main-color);
  cursor: ns-resize;
  opacity: 0;
}

.luckysheet-change-size-line {
  border-color: var(--luckysheet-main-color);
  border-style: solid;
  z-index: 15;
  display: none;
}

.luckysheet-count-show {
  position: absolute;
  z-index: 15;
  background: rgba(76, 76, 76, 0.8);
  color: #fff;
  padding: 2px 3px;
  border-radius: 3px;
  transition: all 0.3s;
  display: none;
  white-space: nowrap;
}

.luckysheet-row-count-show {
  text-align: center;
}

.luckysheet-row-count-show div {
  /*-webkit-writing-mode: vertical-rl;*/
  writing-mode: vertical-rl;
  /*-ms-writing-mode: tb-rl;*/
  writing-mode: vertical-rl;
  -ms-writing-mode: initial;
  *writing-mode: tb-rl;
  /* IE 写法 */
}

.luckysheet-row-count-show div:last-child {
  writing-mode: initial;
}

#luckysheet-sheet-list {
  max-height: 60%;
  overflow: auto;
}

#luckysheet-sheet-list .luckysheet-cols-menuitem {
  padding-left: 0px;
  padding-right: 10px;
}

#luckysheet-sheet-list
  .luckysheet-cols-menuitem
  .luckysheet-cols-menuitem-content {
  padding-left: 5px;
}
/* 
#luckysheet-sheet-list .luckysheet-cols-menuitem .luckysheet-cols-menuitem-content {
    max-width: 420px;
    min-width: 190px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
} */

#luckysheet-sheet-list .icon {
  color: var(--luckysheet-main-color);
  width: 15px;
  margin-left: 4px;
  display: inline-block;
}

.luckysheet-input-box {
  position: absolute;
  font: normal normal 400 13px arial, sans, sans-serif;
  text-align: left;
  top: -10000px;
  max-height: 9900px;
  max-width: 9900px;
  border: 2px #5292f7 solid;
  padding: 0 2px;
  margin: 0;
  z-index: 15;
  resize: none;
  overflow: auto;
  overflow: initial;
  white-space: pre-wrap;
  outline: none;
  -webkit-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
  -moz-box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.4);
  word-wrap: break-word;
}

.luckysheet-cell-input {
  width: 100%;
  height: 100%;
  margin: 0;
  outline: none;
  cursor: text;
  -webkit-user-modify: read-write-plaintext-only;
  white-space: pre-wrap;
  -webkit-transform: translateZ(0);
  /*    background-color: white;*/
}

#luckysheet-rich-text-editor {
  -webkit-user-modify: read-write;
  /*    background-color: rgb(255, 255, 255); 
    font-size: 13px; 
    color: rgb(0, 0, 0); 
    font-weight: 400; 
    font-family: Arial; 
    font-style: normal;*/
}

.luckysheet-input-box-index {
  display: none;
  position: absolute;
  /*top: -20px;
    left: -3px;*/
  height: 14px;
  line-height: 16px;
  font-size: 12px;
  padding: 1px 6px;
  background-color: #5292f7;
  border-radius: 2px;
  box-shadow: 0px 1px 2px rgba(0, 0, 0, 0.5);
  color: white;
}

.luckysheet-modal-dialog {
  border-radius: var(--luckysheet-border-radius);
  -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  background: #fff;
  background-clip: padding-box;
  outline: 0;
  position: absolute;
  color: #000;
  padding: 16px;
  z-index: 100002;
}

.luckysheet-modal-dialog-mask {
  position: absolute;
  height: 100%;
  width: 100%;
  background: #fff;
  opacity: 0.6;
  display: none;
  left: 0px;
  top: 0px;
  z-index: 1010;
}

.luckysheet-modal-dialog-title {
  background-color: #fff;
  color: #000;
  cursor: default;
  font-size: 16px;
  font-weight: normal;
  line-height: 24px;
  margin: 0 0 16px;
}

.luckysheet-modal-dialog-title-close {
  /* height: 11px; */
  opacity: 0.7;
  padding: 16px;
  position: absolute;
  right: 0px;
  top: 0px;
  /* width: 11px; */
  color: #909399;
  outline: 0;
}

.luckysheet-modal-dialog-chart {
  padding: 20px 10px;
  webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
}

/*         .luckysheet-modal-dialog-chart .luckysheet-modal-dialog-title{
             line-height:0px;
             margin: 0px;
             font-size: 12px;
         }

        .luckysheet-modal-dialog-chart .luckysheet-modal-dialog-title-close,.luckysheet-modal-dialog-chart .luckysheet-modal-dialog-title-max,.luckysheet-modal-dialog-chart .luckysheet-modal-dialog-title-min,.luckysheet-modal-dialog-chart .luckysheet-modal-dialog-title-restore, .luckysheet-modal-dialog-chart .luckysheet-modal-dialog-title-update {
             height: 5px;
             opacity: 0.7;
             padding: 12px;
         }

         .luckysheet-modal-dialog-title-update {
             right: 105px;
             color:var(--luckysheet-main-color);
             font-size:14px;
             padding-top: 9px;
         }

         .luckysheet-modal-dialog-title-min {
             right: 70px;
             padding-top: 9px;
         }

         .luckysheet-modal-dialog-title-max, .luckysheet-modal-dialog-title-restore {
             right: 35px;
         }

         .luckysheet-modal-dialog-title-restore{
             display: none;
         }*/

.luckysheet-modal-dialog-resize {
  position: absolute;
  border: 2px solid var(--luckysheet-main-color);
  margin: 0px;
  padding: 0px;
  top: -2px;
  left: -2px;
  bottom: -2px;
  right: -2px;
  pointer-events: none;
}

.luckysheet-modal-dialog-resize-item {
  position: absolute;
  height: 6px;
  width: 6px;
  background: #ffffff;
  border: 2px solid var(--luckysheet-main-color);
  pointer-events: all;
  border-radius: 6px;
}

.luckysheet-modal-dialog-resize-item-lt {
  left: -6px;
  top: -6px;
  cursor: se-resize;
}

.luckysheet-modal-dialog-resize-item-mt {
  left: 50%;
  top: -6px;
  margin-left: -4px;
  cursor: s-resize;
}

.luckysheet-modal-dialog-resize-item-rt {
  right: -6px;
  top: -6px;
  cursor: ne-resize;
}

.luckysheet-modal-dialog-resize-item-lm {
  top: 50%;
  left: -6px;
  margin-top: -4px;
  cursor: w-resize;
}

.luckysheet-modal-dialog-resize-item-rm {
  top: 50%;
  right: -6px;
  margin-top: -4px;
  cursor: w-resize;
}

.luckysheet-modal-dialog-resize-item-lb {
  left: -6px;
  bottom: -6px;
  cursor: ne-resize;
}

.luckysheet-modal-dialog-resize-item-mb {
  left: 50%;
  bottom: -6px;
  margin-left: -4px;
  cursor: s-resize;
}

.luckysheet-modal-dialog-resize-item-rb {
  right: -6px;
  bottom: -6px;
  cursor: se-resize;
}

.luckysheet-modal-dialog-controll {
  position: absolute;
  margin: 0px;
  padding: 0px;
  right: -35px;
  /* width: 60px; */
  font-size: 14px;
  top: 0px;
}

.luckysheet-modal-controll-btn {
  height: 13px;
  /* opacity: 0.7; */
  padding: 8px;
  /* position: relative; */
  /* right: 0px; */
  /* top: 0px; */
  width: 13px;
  color: #d4d4d4;
  outline: 0;
  border: 1px solid #b6b6b6;
  display: block;
  background: #fff;
  margin-bottom: 3px;
  cursor: pointer;
  transition: all 0.2s;
  -moz-transition: all 0.2s;
  -webkit-transition: all 0.2s;
  -o-transition: all 0.2s;
}

.luckysheet-modal-controll-btn:hover {
  border: 1px solid #a1a1a1;
  color: var(--luckysheet-main-color);
}

.luckysheet-modal-controll-btn:active {
  border: 1px solid #bbbbbb;
  background: #efefef;
  color: var(--luckysheet-main-color);
}

.luckysheet-modal-controll-del {
  font-size: 16px;
}

.luckysheet-modal-controll-max-close {
  font-size: 22px;
  width: 42px;
  height: 42px;
  line-height: 42px;
  background: #383838;
  opacity: 0.7;
  -moz-border-radius: 20px;
  -webkit-border-radius: 20px;
  border-radius: 20px;
  color: #fff;
  position: absolute;
  right: 0px;
  top: 0px;
  z-index: 100000;
  text-align: center;
}

.luckysheet-modal-controll-max-close:hover {
  background: var(--luckysheet-main-color);
  cursor: pointer;
}

.luckysheet-sort-item-close {
  margin-right: 3px;
  font-size: 14px;
  color: #bbbbbb;
  cursor: pointer;
}

.luckysheet-sort-item-close:hover {
  color: #494949;
}

.luckysheet-modal-dialog-title-close:hover {
  color: #5e5e5e;
  cursor: pointer;
}

.luckysheet-modal-dialog-content {
  background-color: #fff;
  line-height: 1.4em;
  word-wrap: break-word;
}

.luckysheet-modal-dialog-buttons {
  margin-top: 10px;
}

.luckysheet-modal-dialog-buttons button {
  margin-right: 10px;
}

.luckysheet-modal-dialog-title-text span {
  font-family: Arial;
}

.luckysheet-sort-modal {
  font-size: 12px;
}

.luckysheet-sort-modal label input,
.luckysheet-sort-modal label span {
  vertical-align: middle;
}

.luckysheet-sort-modal > div,
.luckysheet-sort-modal table {
  margin-bottom: 10px;
}

.luckysheet-sort-modal table tr {
  margin-bottom: 10px;
}

.luckysheet-sort-modal table tr td {
  padding: 5px;
  white-space: nowrap;
  border-top: 1px solid #ffc6c6;
}

.luckysheet-sort-modal table tr td > div:first-child {
  margin-bottom: 8px;
}

.luckysheet-sort-modal table tr td select {
  max-width: 180px;
  min-width: 50px;
}

.luckysheet-sort-modal table tr:first-child td {
  border-top: none;
}

.luckysheet-filter-options {
  color: #897bff;
  cursor: pointer;
  position: absolute;
  z-index: 20;
  border: 1px solid #897bff;
  border-radius: 3px;
  top: 3px;
  margin-left: 0px;
  display: none;
  padding: 0px 4px;
  font-size: 12px;
  height: 15px;
  background: #fff;
  /* transition: all 0.1s; */
}

.luckysheet-filter-options:hover {
  color: #fff;
  border: 1px solid #fff;
  background: #897bff;
}

.luckysheet-filter-options-active {
  color: #fff;
  border: 1px solid #897bff;
  background: #897bff;
}

.luckysheet-flat-menu-button {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  background-color: #f5f5f5;
  background-image: -webkit-linear-gradient(to bottom, #f5f5f5, #f1f1f1);
  background-image: -moz-linear-gradient(to bottom, #f5f5f5, #f1f1f1);
  background-image: -ms-linear-gradient(to bottom, #f5f5f5, #f1f1f1);
  background-image: -o-linear-gradient(to bottom, #f5f5f5, #f1f1f1);
  background-image: linear-gradient(to bottom, #f5f5f5, #f1f1f1);
  border: 1px solid #dcdcdc;
  color: #333;
  cursor: default;
  font-size: 11px;
  font-weight: bold;
  line-height: 27px;
  list-style: none;
  margin: 0 2px;
  min-width: 46px;
  outline: none;
  padding: 0 18px 0 6px;
  text-align: left;
  text-decoration: none;
  position: relative;
  padding-left: 15px;
}

.luckysheet-flat-menu-button:hover {
  -webkit-border-radius: 2px;
  -moz-border-radius: 2px;
  border-radius: 2px;
  background-color: #f5f5f5;
  background-image: -webkit-linear-gradient(to bottom, #f1f1f1, #f5f5f5);
  background-image: -moz-linear-gradient(to bottom, #f1f1f1, #f5f5f5);
  background-image: -ms-linear-gradient(to bottom, #f1f1f1, #f5f5f5);
  background-image: -o-linear-gradient(to bottom, #f1f1f1, #f5f5f5);
  background-image: linear-gradient(to bottom, #f1f1f1, #f5f5f5);
  border: 1px solid #d0d0d0;
  color: #000;
}

.luckysheet-flat-menu-button div {
  display: inline-block;
  vertical-align: middle;
  position: absolute;
  right: 15px;
}

/*图表生成CSS*/

.luckysheet-data-visualization {
  width: 60%;
  min-width: 860px;
}

.luckysheet-data-visualization-selection,
.luckysheet-data-pivotTable-selection {
  width: 30%;
  min-width: 200px;
  display: none;
}

.luckysheet-data-visualization-chart {
  width: 50%;
  /*min-width: 860px;*/
  height: 50%;
}

.luckysheet-data-visualization-chart .luckysheet-modal-dialog-content {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.luckysheet-datavisual-modal {
  font-size: 12px;
  /*height: 450px;*/
  height: 100%;
  width: 100%;
}

/*, .luckysheet-datavisual-right*/
.luckysheet-datavisual-left {
  display: inline-block;
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
}

.luckysheet-datavisual-left {
}

.luckysheet-datavisual-tabs {
  border-bottom: 1px solid #dedede;
  width: 80%;
  height: 26px;
  padding-left: 20px;
}

.luckysheet-datavisual-tabs .luckysheet-datavisual-tab {
  padding: 0px 5px;
  text-align: center;
  display: inline-block;
  cursor: pointer;
  border: 1px solid #fff;
  border-bottom: none;
  height: 24px;
  line-height: 24px;
  background: #fff;
  color: #777;
}

.luckysheet-datavisual-tabs .luckysheet-datavisual-tab:hover {
  color: #000;
}

.luckysheet-datavisual-tabs .luckysheet-datavisual-tab-active {
  border: 1px solid #dedede;
  border-bottom: none;
  cursor: default;
  height: 26px;
  color: #000;
}

.luckysheet-datavisual-tab-content {
  position: absolute;
  top: 28px;
  bottom: 0px;
  width: 100%;
  display: none;
}

.luckysheet-datavisual-quick {
}

.luckysheet-datavisual-quick-menu {
  width: 90px;
  overflow: auto;
  margin-top: 5px;
}

.luckysheet-datavisual-quick-menu::-webkit-scrollbar {
  display: none;
}

.luckysheet-datavisual-quick-menu > div {
  text-align: left;
  padding: 4px 4px;
  border-right: 3px solid #fff;
  color: #777;
  cursor: pointer;
  line-height: 1.4em;
  word-wrap: break-word;
  /*margin: 4px 0px;*/
}

.luckysheet-datavisual-quick-menu > div:hover {
  color: #000;
}

.luckysheet-datavisual-quick-menu > div i {
  width: 15px;
}

.luckysheet-datavisual-quick-menu > div:hover i {
  color: #ff7e7e;
}

.luckysheet-datavisual-quick-menu
  > div.luckysheet-datavisual-quick-menu-active {
  border-right: 3px solid #ff7e7e;
  color: #000;
  font-weight: bold;
}

.luckysheet-datavisual-quick-menu
  > div.luckysheet-datavisual-quick-menu-active:hover
  i {
  color: #000;
}

.luckysheet-datavisual-quick-range {
  padding: 5px 0px;
}

.luckysheet-datavisual-range-container {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-top: 1px solid #c0c0c0;
  min-width: 20px;
  width: 100%;
  max-width: 200px;
  display: inline-block;
}

.luckysheet-datavisual-range-container-focus {
  border: 1px solid #4d90fe;
  box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
  outline: none;
}

.luckysheet-datavisual-range-input,
.luckysheet-datavisual-range-input:focus {
  background: transparent !important;
  border: none !important;
  box-sizing: border-box;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  height: 25px;
  margin: 0;
  outline: none !important;
  padding: 1px 8px !important;
  width: 100%;
}

.luckysheet-datavisual-range-button-container {
  overflow: hidden;
  padding: 0 0 0 8px;
  text-align: right;
  width: 21px;
}

.luckysheet-datavisual-range-button-container div {
  padding: 2px 10px 0px 10px;
  font-size: 18px;
  cursor: pointer;
  color: #6598f3;
}

.luckysheet-datavisual-range-button-container div:hover {
  color: #ff7e7e;
}

.luckysheet-datavisual-range-config {
  /*display: inline-block;*/
}

.luckysheet-datavisual-quick-m {
  margin-top: 5px;
}

.luckysheet-datavisual-quick-list {
  left: 90px;
  right: 0px;
  bottom: 0px;
  top: 110px;
  position: absolute;
  overflow: auto;
  border-top: 1px solid #e5e5e5;
  padding: 5px 3px 35px 3px;
}

.luckysheet-datavisual-quick-list-title {
  padding: 4px 6px;
  background: #e5e5e5;
  margin-top: 10px;
}

.luckysheet-datavisual-quick-list-ul {
  overflow: hidden;
}

.luckysheet-datavisual-quick-list-item {
  display: inline-block;
  margin: 5px 8px;
  border: 1px solid #dadada;
  width: 100px;
  height: 80px;
}

.luckysheet-datavisual-quick-list-item:hover {
  border: 1px solid #ff7e7e;
  box-shadow: 0px 0px 20px #ff7e7e;
}

.luckysheet-datavisual-quick-list-item img {
  display: inline-block;
  width: 100px;
  height: 80px;
}

.luckysheet-datavisual-quick-list-item-active {
  border: 1px solid #6598f3;
  box-shadow: 0px 0px 20px #6598f3;
}

/*.luckysheet-datavisual-right {
    background: #fff;
}

.luckysheet-datavisual-right-chart {
    width: 100%;
    height: 90%;
    top: 5%;
    position: relative;
}*/

.jfk-tooltip {
  z-index: 300000;
}

.jfk-tooltip-hide {
  -webkit-transition: visibility 0.13s, opacity 0.13s ease-out,
    left 0 linear 0.13s, top 0 linear 0.13s;
  -moz-transition: visibility 0.13s, opacity 0.13s ease-out, left 0 linear 0.13s,
    top 0 linear 0.13s;
  -o-transition: visibility 0.13s, opacity 0.13s ease-out, left 0 linear 0.13s,
    top 0 linear 0.13s;
  transition: visibility 0.13s, opacity 0.13s ease-out, left 0 linear 0.13s,
    top 0 linear 0.13s;
  opacity: 0;
  left: 20px !important;
  top: 20px !important;
  visibility: hidden !important;
}

.jfk-tooltip {
  -webkit-border-radius: 0;
  -moz-border-radius: 0;
  border-radius: 0;
  -webkit-box-shadow: none;
  -moz-box-shadow: none;
  box-shadow: none;
  -webkit-transition: visibility 0, opacity 0.13s ease-in;
  -moz-transition: visibility 0, opacity 0.13s ease-in;
  -o-transition: visibility 0, opacity 0.13s ease-in;
  transition: visibility 0, opacity 0.13s ease-in;
  background-color: #2a2a2a;
  border: 1px solid #fff;
  color: #fff;
  cursor: default;
  display: block;
  font-size: 11px;
  font-weight: bold;
  margin-left: -1px;
  opacity: 1;
  padding: 7px 9px;
  position: absolute;
  visibility: visible;
  white-space: pre-wrap;
  word-break: break-all;
  word-break: break-word;
}

.jfk-tooltip-arrowup {
  top: -6px;
}

.jfk-tooltip-arrow {
  pointer-events: none;
  position: absolute;
}

.jfk-tooltip-arrow .jfk-tooltip-arrowimplafter {
  border: 5px solid;
}

.jfk-tooltip-arrow .jfk-tooltip-arrowimplbefore {
  border: 6px solid;
}

.jfk-tooltip-arrow .jfk-tooltip-arrowimplbefore,
.jfk-tooltip-arrow .jfk-tooltip-arrowimplafter {
  content: "";
  display: block;
  height: 0;
  position: absolute;
  width: 0;
}

.jfk-tooltip-arrowup .jfk-tooltip-arrowimplafter {
  border-top-width: 0;
  top: 1px;
}

.jfk-tooltip-arrowdown .jfk-tooltip-arrowimplafter,
.jfk-tooltip-arrowup .jfk-tooltip-arrowimplafter {
  border-color: #2a2a2a transparent;
  left: -5px;
}

.jfk-tooltip-arrowup .jfk-tooltip-arrowimplbefore {
  border-top-width: 0;
}

.jfk-tooltip-arrowdown .jfk-tooltip-arrowimplbefore,
.jfk-tooltip-arrowup .jfk-tooltip-arrowimplbefore {
  border-color: #fff transparent;
  left: -6px;
}

/*图表详细设置*/
.luckysheet-datavisual-config {
  position: relative;
  width: 100%;
  height: 97%;
  overflow: auto;
  top: 0px;
}

.luckysheet-datavisual-config input {
  outline: none;
}

.luckysheet-datavisual-config .luckysheet-datavisual-accordion-title {
  position: relative;
  width: 97%;
  height: 33px;
  background: #f5f5f5;
  border: 1px solid #e5e5e5;
  margin-top: 30px;
  line-height: 30px;
  font-weight: bold;
  color: #d14836;
  cursor: pointer;
}

.luckysheet-datavisual-config .luckysheet-datavisual-accordion-title:hover {
  background: #efefef;
  border: 1px solid #e0e0e0;
}

.luckysheet-datavisual-config .luckysheet-datavisual-accordion-content {
  position: relative;
  width: 97%;
  border: 1px solid #e5e5e5;
  border-top: 1px solid #fff;
  display: none;
  color: #505050;
  padding-bottom: 20px;
}

.luckysheet-datavisual-config-input,
.luckysheet-datavisual-config-input-no {
  background: #fff;
  border: 1px solid #d9d9d9;
  border-top: 1px solid #c0c0c0;
  min-width: 50px;
  width: 90%;
  display: inline-block;
  height: 24px;
  line-height: 24px;
  padding: 3px;
}

.luckysheet-datavisual-config-input:focus,
.luckysheet-datavisual-config-input-no:focus {
  border: 1px solid #4d90fe;
  box-shadow: inset 0px 1px 2px rgba(0, 0, 0, 0.3);
  outline: none;
}

.luckysheet-datavisual-content-row {
  margin-left: 15px;
  margin-bottom: 5px;
  margin-top: 15px;
  height: 30px;
  line-height: 30px;
}

.luckysheet-datavisual-content-column {
  display: inline-block;
  position: relative;
}

.luckysheet-datavisual-content-column-title {
  text-align: left;
  font-size: 14px;
}

.luckysheet-datavisual-content-column-right {
  text-align: right;
}

.luckysheet-datavisual-content-rowsplit {
  height: 5px;
  border-top: 1px solid #e5e5e5;
  width: 100%;
  margin-top: 25px;
}

.luckysheet-datavisual-content-rowsplit-sub {
  height: 2px;
  border-top: 1px dashed #e5e5e5;
  width: 90%;
  margin: 0 auto;
  margin-top: 18px;
  text-align: center;
}

.sp-replacer {
  padding: 2px;
  border: solid 1px #e5e5e5;
  background: #f5f5f5;
}

.ui-visual-focus {
  box-shadow: none;
}

.luckysheet-datavisual-config-slider,
.luckysheet-datavisual-config-slider-range {
  width: 70%;
  display: inline-block;
}

.luckysheet-datavisual-config-slider-range .luckysheet-slider-handle {
  width: 45px;
  height: 26px;
  top: 50%;
  margin-top: -13px;
  text-align: center;
  line-height: 26px;
}

.luckysheet-datavisual-content-row-subtitle {
  display: none;
}

.ui-selectmenu-button.ui-button {
  text-align: left;
  white-space: nowrap;
  width: 48%;
}

.luckysheet-datavisual-content-column-italic {
  font-style: italic;
  font-weight: bold;
  font-family: "Times New Roman", Times, serif;
}

.luckysheetChartAxisShow {
  display: none;
}

.luckysheet-datavisual-chart-axistitle-show {
  display: none;
}

.luckysheetChartseriesShow {
  display: none;
}

#luckysheetswichxy-button,
#piecutselect-button {
  width: 70%;
}

.ui-selectmenu-menu .ui-menu.customicons .ui-menu-item-wrapper {
  padding: 0.5em 0 0.5em 3em;
}

.ui-selectmenu-menu .ui-menu.customicons .ui-menu-item .ui-icon {
  height: 26px;
  width: 26px;
  top: 0.1em;
  background-image: none;
}

#luckysheetswichseries-menu .ui-state-active,
#pie0cutselect-menu .ui-state-active,
#pie1cutselect-menu .ui-state-active,
#pie2cutselect-menu .ui-state-active,
#luckysheetscatterselectshow-menu .ui-state-active {
  border: 1px solid #f5f5f5;
  background: #f5f5f5;
  color: #333;
}

.ui-front {
  z-index: 100003;
}

.luckysheet-datavisual-skin {
}

.luckysheet-datavisual-skin-menu {
  top: 5px;
  position: absolute;
  left: 0px;
  width: 90%;
  height: 30px;
}

#luckysheet-chart-theme-content {
  height: 21px;
  width: 120px;
}

.luckysheet-datavisual-skin-c {
  position: absolute;
  left: 0px;
  top: 38px;
  bottom: 0px;
  /*background: #000;*/
  width: 100%;
  overflow: auto;
}

.luckysheet-datavisual-skin-c .luckysheet-datavisual-skin-item {
  display: inline-block;
  width: 46%;
  height: 152px;
  /*background: red;*/
  margin-right: 5px;
  border: 4px solid #efefef;
  border-radius: 4px;
  position: relative;
  cursor: pointer;
}

.luckysheet-datavisual-skin-c .luckysheet-datavisual-skin-item-more {
  display: inline-block;
  width: 94%;
  height: 32px;
  position: relative;
  cursor: pointer;
  font-size: 20px;
  line-height: 32px;
  margin-bottom: 20px;
  text-align: center;
}

.luckysheet-datavisual-skin-item .luckysheet-datavisual-skin-canvas,
.luckysheet-datavisual-skin-item .luckysheet-datavisual-skin-cover {
  position: absolute;
  width: 100%;
  height: 100%;
  bottom: 0;
}

.luckysheet-datavisual-skin-item .luckysheet-datavisual-skin-canvas {
  /*z-index: 1;*/
}

.luckysheet-datavisual-skin-item .luckysheet-datavisual-skin-cover {
  background-color: rgba(0, 0, 0, 0.4);
  color: #ffffff;
  font-size: 14px;
  height: 30%;
  /*opacity: 0;*/
  transition: opacity 0.15s ease;
  -moz-transition: opacity 0.15s ease;
  -webkit-transition: opacity 0.15s ease;
  -o-transition: opacity 0.15s ease;
}

.luckysheet-datavisual-skin-item:hover .luckysheet-datavisual-skin-cover {
  opacity: 1;
  z-index: 2;
}

.luckysheet-datavisual-skin-cover .luckysheet-datavisual-skin-cover-txt {
  position: absolute;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  text-align: center;
}

/*图表点设置*/
.luckysheet-chart-point-config {
  position: relative;
  width: 100%;
  height: 100%;
  margin: 0px;
  font-size: 12px;
}

.luckysheet-chart-point-config-set {
  position: absolute;
  width: 60%;
  height: 100%;
  left: 0px;
  top: 0px;
  /*background:#ff0000;*/
}

.luckysheet-chart-point-config-left {
  position: absolute;
  height: 100%;
  width: 50%;
  left: 0px;
  top: 0px;
}

.luckysheet-chart-point-config-left-top {
  position: absolute;
  top: 0px;
  height: 120px;
  width: 100%;
}

.luckysheet-chart-point-searchcondition {
  position: absolute;
  top: 10px;
  bottom: 10px;
  left: 10px;
  right: 10px;
}

.luckysheet-chart-point-config-left-mid {
  position: absolute;
  top: 120px;
  height: 25px;
  width: 100%;
  text-align: left;
  margin-left: 20px;
  line-height: 35px;
}

.luckysheet-chart-point-config-left-mid span {
  color: blue;
  cursor: pointer;
  text-decoration: underline;
  font-size: 12px;
}

.luckysheet-chart-point-config-left-bottom {
  position: absolute;
  top: 0px;
  bottom: 0px;
  margin-top: 145px;
  width: 100%;
}

.luckysheet-chart-point-searchitem-c {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
  overflow-y: auto;
  cursor: default;
}

.luckysheet-chart-point-searchitem {
  display: inline-block;
  margin-left: 5px;
  margin-top: 5px;
  width: 90px;
  border: 2px solid #f5f5f5;
  background: #fff;
  text-align: center;
  padding: 5px 0px;
  user-select: none;
  cursor: default;
  position: relative;
}

.luckysheet-chart-point-searchitem-selected {
  position: absolute;
  color: #616161;
  left: -6px;
  top: -10px;
  font-size: 20px;
  display: none;
  font-weight: normal;
}

.luckysheet-chart-point-searchitem-active {
  box-shadow: 0px 0px 4px #656565;
}

.luckysheet-chart-point-searchitem-active
  .luckysheet-chart-point-searchitem-selected {
  display: block;
}

.luckysheet-chart-point-searchitem-name {
  font-size: 12px;
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.luckysheet-chart-point-searchitem-dim {
  font-size: 12px;
  opacity: 0.7;
  /*color: #C1C1C1;*/
  cursor: default;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

#luckysheet-chart-point-selectedhelp {
  pointer-events: none;
  position: absolute;
  border: 1px dotted #535353;
}

.luckysheet-chart-point-config-right {
  position: absolute;
  height: 100%;
  width: 50%;
  top: 0px;
  right: 0px;
}

.luckysheet-chart-point-itemconfig {
  position: absolute;
  top: 10px;
  bottom: 10px;
  left: 10px;
  right: 10px;
  overflow: auto;
}

.luckysheet-chart-point-config-chart {
  position: absolute;
  width: 40%;
  height: 100%;
  right: 0px;
  top: 0px;
}

.luckysheet-chart-point-config-chart-c {
  width: 100%;
  height: 80%;
  top: 10%;
  position: relative;
}

@media (max-width: 776px) {
  .luckysheet-chart-point-config-set {
    width: 90%;
  }

  .luckysheet-chart-point-config-chart {
    width: 10%;
  }
}

@media (min-width: 768px) {
  .luckysheet-chart-point-config-set {
    width: 80%;
  }

  .luckysheet-chart-point-config-chart {
    width: 20%;
  }
}

@media (min-width: 1024px) {
  .luckysheet-chart-point-config-set {
    width: 70%;
  }

  .luckysheet-chart-point-config-chart {
    width: 30%;
  }
}

@media (min-width: 1280px) {
  .luckysheet-chart-point-config-set {
    width: 60%;
  }

  .luckysheet-chart-point-config-chart {
    width: 40%;
  }
}

@media (min-width: 1680px) {
  .luckysheet-chart-point-config-set {
    width: 50%;
  }

  .luckysheet-chart-point-config-chart {
    width: 50%;
  }
}

/*边栏通用样式*/
.luckysheet-modal-dialog-slider {
  top: 1px;
  bottom: 1px;
  position: absolute;
  right: 0px;
  width: 260px;
  border: 1px solid #e5e5e5;
  z-index: 1004;
  box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
  -webkit-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
  -moz-box-shadow: 0px 2px 4px rgba(0, 0, 0, 0.2);
  -moz-user-select: none;
  -khtml-user-select: none;
  -webkit-user-select: none;
  -ms-user-select: none;
  user-select: none;
  /*display: none;*/
}

.luckysheet-modal-dialog-slider .luckysheet-modal-dialog-slider-title {
  background: var(--luckysheet-main-color-7);
  color: #fff;
  height: 39px;
  width: 100%;
  position: absolute;
  top: 0px;
  left: 0px;
  line-height: 39px;
  font-size: 13px;
}

.luckysheet-modal-dialog-slider
  .luckysheet-modal-dialog-slider-title
  > span:first-child {
  margin-left: 5px;
  font-weight: bold;
}

.luckysheet-modal-dialog-slider
  .luckysheet-modal-dialog-slider-title
  > span:last-child {
  position: relative;
  float: right;
  margin-right: 20px;
  cursor: pointer;
}

.luckysheet-modal-dialog-slider-content {
  background: #efefef;
  margin-top: 39px;
  width: 100%;
  position: absolute;
  top: 0px;
  bottom: 0px;
  font-size: 12px;
}

.luckysheet-modal-dialog-slider-range {
  background: #e1e1de;
  color: #1b1b19;
  height: 40px;
  font-size: 13px;
  line-height: 40px;
}

.luckysheet-modal-dialog-slider-range > div:first-child {
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
  float: left;
  max-width: 170px;
  margin-right: 10px;
  margin-left: 5px;
  white-space: nowrap;
}

.luckysheet-modal-dialog-slider-range > div:last-child {
  color: blue;
  cursor: pointer;
  float: left;
}

.luckysheet-modal-dialog-slider-list {
  width: 250px;
  height: 320px;
  overflow-y: scroll;
  margin: 5px 0px;
  margin-left: 5px;
  border-top: 1px solid #e5e5e5;
  border-bottom: 1px solid #e5e5e5;
  background: #fff;
}

.luckysheet-modal-dialog-slider-list-title {
  height: 20px;
  line-height: 25px;
  padding: 0px 5px;
}

.luckysheet-modal-dialog-slider-list .luckysheet-modal-dialog-slider-list-item {
  padding: 0px 4px;
  position: relative;
  width: 228px;
  height: 25px;
  user-select: none;
  border: 1px solid #fff;
}

.luckysheet-modal-dialog-slider-list
  .luckysheet-modal-dialog-slider-list-item:hover {
  background: #fff6cb;
  border: 1px solid #ffe463;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-name {
  cursor: pointer;
  height: 25px;
  line-height: 25px;
  cursor: move;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  position: absolute;
  left: 22px;
  right: 40px;
  top: 0px;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-selected {
  width: 20px;
  cursor: pointer;
  text-align: center;
  position: absolute;
  left: 0px;
  top: 0px;
  height: 25px;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-selected i {
  margin-top: 4px;
  font-size: 16px;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-selected div {
  border: 1px solid #9c9c9c;
  top: 4px;
  left: 1px;
  position: absolute;
  height: 14px;
  width: 14px;
  -moz-box-shadow: 1px 1px 1px #dbdbdb inset;
  /* For Firefox3.6+ */
  -webkit-box-shadow: 1px 1px 1px #dbdbdb inset;
  /* For Chrome5+, Safari5+ */
  box-shadow: 1px 1px 1px #dbdbdb inset;
  /* For Latest Opera */
}

.luckysheet-modal-dialog-slider-list
  .luckysheet-slider-list-item-selected
  div:hover {
  border: 1px solid #5e5e5e;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-filtered {
  width: 20px;
  cursor: pointer;
  text-align: center;
  position: absolute;
  right: 20px;
  top: 2px;
  height: 25px;
  display: none;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-filtered i {
  margin-top: 2px;
  font-size: 16px;
}

.luckysheet-modal-dialog-slider-list
  .luckysheet-slider-list-item-filtered:hover
  i {
  color: #fb8686;
}

.luckysheet-modal-dialog-slider-list
  .luckysheet-slider-list-item-filtered
  i.fa-times {
  right: 0px;
  bottom: 3px;
  color: red;
  font-size: 9px;
  position: absolute;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-filter {
  width: 20px;
  cursor: pointer;
  text-align: center;
  position: absolute;
  right: 0px;
  top: 0px;
  height: 25px;
}

.luckysheet-modal-dialog-slider-list .luckysheet-slider-list-item-filter i {
  margin-top: 2px;
  font-size: 16px;
}

.luckysheet-modal-dialog-slider-list
  .luckysheet-slider-list-item-filter:hover
  i {
  color: #fb8686;
}

.luckysheet-modal-dialog-slider-config-c {
  width: 100%;
  position: absolute;
  margin-top: 390px;
  top: 0px;
  bottom: 3px;
}

.luckysheet-modal-dialog-slider-config {
  height: 50%;
  width: 50%;
  position: absolute;
}

.luckysheet-modal-dialog-slider-config > div:first-child {
  color: #1b1b19;
  font-size: 13px;
  height: 20px;
  line-height: 20px;
  padding-left: 5px;
}

.luckysheet-modal-dialog-slider-config > div:first-child span {
  font-weight: bold;
  font-weight: bold;
  overflow: hidden;
}

.luckysheet-modal-dialog-slider-config
  .luckysheet-modal-dialog-slider-config-list {
  position: absolute;
  margin-top: 22px;
  margin-left: 5px;
  left: 0px;
  right: 5px;
  top: 0px;
  bottom: 3px;
  border: 1px solid #e5e5e5;
  user-select: none;
  overflow-y: auto;
  background: #fff;
}

.luckysheet-modal-dialog-slider-config-list
  .luckysheet-modal-dialog-slider-config-item {
  position: relative;
  height: 19px;
  line-height: 19px;
  font-size: 12px;
  border: 1px solid #88adfd;
  background: #aac1fe;
  margin: 2px;
}

.luckysheet-modal-dialog-slider-config-list
  .luckysheet-modal-dialog-slider-config-item:hover {
  border: 1px solid #0188fb;
  background: #5f9afc;
}

.luckysheet-modal-dialog-slider-config-item-txt {
  position: absolute;
  height: 100%;
  left: 5px;
  right: 25px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  cursor: move;
}

.luckysheet-modal-dialog-slider-config-item-icon {
  position: absolute;
  height: 100%;
  width: 15px;
  right: 0;
  top: -4px;
  cursor: pointer;
  font-size: 14px;
}

.luckysheet-modal-dialog-slider-config-item-icon:hover {
  color: #fa7272;
}

.luckysheet-modal-dialog-config-filter {
  top: 0;
  left: 0;
}

.luckysheet-modal-dialog-config-column {
  top: 0;
  left: 50%;
}

.luckysheet-modal-dialog-config-row {
  top: 50%;
  left: 0;
}

.luckysheet-modal-dialog-config-value {
  top: 50%;
  left: 50%;
}

#luckysheet-modal-dialog-slider-pivot-move {
  position: absolute;
  max-width: 100px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  background: #fff;
  border: 1px dotted #000;
  color: #000;
  font-size: 14px;
  opacity: 0.6;
  z-index: 1005;
  padding: 3px 8px;
  pointer-events: none;
  user-select: none;
}

.luckysheet-modal-dialog-slider-chart {
  width: 445px;
}

.luckysheet-modal-dialog-slider-chart .luckysheet-modal-dialog-slider-title {
  background: #b94045;
}

.luckysheet-modal-dialog-slider-chart .luckysheet-modal-dialog-slider-content {
  background: #fff;
}

#luckysheet-dialog-pivotTable-clearitem {
  color: blue;
  cursor: pointer;
  float: right;
  margin-right: 30px;
}

/*冻结窗口样式*/
.luckysheet-freezebar-handle,
.luckysheet-freezebar-drop {
  position: absolute;
  z-index: 999;
}

.luckysheet-freezebar-vertical-handle {
  width: 4px;
  display: none;
  /*background: url(//ssl.gstatic.com/docs/spreadsheets/ritz_luckysheet-freezebar_vertical.png) no-repeat;*/
}

.luckysheet-freezebar-vertical-drop {
  /*background: url(//ssl.gstatic.com/docs/spreadsheets/jfk_luckysheet-freezebar_active_vertical.png) no-repeat;*/
  width: 4px;
}

.luckysheet-freezebar-active .luckysheet-freezebar-vertical-handle {
  display: block;
  z-index: 1003;
}

.luckysheet-freezebar-vertical-handle-bar {
  width: 2px;
  background: #dbe5f7;
  border-color: #a5c6fe;
  border-style: solid;
  border-width: 0 1px;
  opacity: 0.45;
  filter: alpha(opacity=45);
  margin-top: 19px;
  top: 0px;
  bottom: 0px;
}

.luckysheet-freezebar-vertical-handle-title {
  width: 4px;
  background: #9dbefb;
  opacity: 0.8;
  filter: alpha(opacity=80);
  height: 19px;
  top: 0px;
}

.luckysheet-freezebar-handle-bar,
.luckysheet-freezebar-drop-bar {
  position: absolute;
  z-index: 7;
}

.luckysheet-freezebar-vertical-drop-bar {
  width: 2px;
  background: rgba(0, 0, 0, 0.45);
  border-width: 2px 1px 2px 2px;
  margin-top: 19px;
  top: 0px;
  bottom: 0px;
}

.luckysheet-freezebar-vertical-drop-title {
  width: 2px;
  background: #bcbdbc;
  /*            opacity: 0.80;
            filter: alpha(opacity=80);*/
  height: 19px;
  top: 0px;
}

.luckysheet-freezebar-hover .luckysheet-freezebar-vertical-drop-bar,
.luckysheet-freezebar-active .luckysheet-freezebar-vertical-drop-bar {
  background: #c1c1c1;
  width: 4px;
}

.luckysheet-freezebar-hover .luckysheet-freezebar-vertical-drop-title,
.luckysheet-freezebar-active .luckysheet-freezebar-vertical-drop-title {
  background: #5d88db;
  width: 4px;
}

.luckysheet-freezebar-horizontal-handle {
  height: 4px;
  display: none;
  /*background: url(//ssl.gstatic.com/docs/spreadsheets/ritz_luckysheet-freezebar_horizontal.png) no-repeat;*/
}

.luckysheet-freezebar-horizontal-drop {
  /*background: url(//ssl.gstatic.com/docs/spreadsheets/jfk_luckysheet-freezebar_active_horizontal.png) no-repeat;*/
  height: 4px;
}

.luckysheet-freezebar-active .luckysheet-freezebar-horizontal-handle {
  display: block;
  z-index: 1003;
}

.luckysheet-freezebar-horizontal-handle-bar {
  height: 2px;
  background: #dbe5f7;
  border-color: #a5c6fe;
  border-style: solid;
  border-width: 1px 0;
  opacity: 0.45;
  filter: alpha(opacity=45);
  margin-left: 45px;
  left: 0px;
  right: 0px;
}

.luckysheet-freezebar-horizontal-handle-title {
  height: 4px;
  background: #9dbefb;
  opacity: 0.8;
  filter: alpha(opacity=80);
  width: 45px;
  left: 0px;
}

.luckysheet-freezebar-horizontal-drop-bar {
  height: 2px;
  overflow: hidden;
  background: rgba(0, 0, 0, 0.45);
  border-width: 2px 2px 1px 2px;
  margin-left: 45px;
  left: 0px;
  right: 0px;
}

.luckysheet-freezebar-horizontal-drop-title {
  height: 2px;
  background: #bcbdbc;
  /*            opacity: 0.80;
            filter: alpha(opacity=80);*/
  width: 45px;
  left: 0px;
}

.luckysheet-freezebar-hover .luckysheet-freezebar-horizontal-drop-bar,
.luckysheet-freezebar-active .luckysheet-freezebar-horizontal-drop-bar {
  background: #c1c1c1;
  height: 4px;
}

.luckysheet-freezebar-hover .luckysheet-freezebar-horizontal-drop-title,
.luckysheet-freezebar-active .luckysheet-freezebar-horizontal-drop-title {
  background: #5d88db;
  height: 4px;
}

/*函数样式*/
#luckysheet-functionbox-container {
  height: 100%;
  padding-left: 10px;
  overflow: hidden;
  position: absolute;
  padding: 0;
  top: 0px;
  left: 185px;
  right: 10px;
  border-left: 1px solid #e5e5e5;
}

#luckysheet-functionbox-container > div {
  height: 100%;
  overflow-x: hidden;
  overflow-y: auto;
  position: relative;
}

#luckysheet-functionbox {
  bottom: 6px;
  left: 0;
  position: absolute;
  right: 0;
  top: 6px;
  resize: none;
  /*border: 1px #b9b9b9 solid;*/
  font-family: arial, sans, sans-serif;
  font-size: 14px;
  line-height: 14px;
  background-color: #ffffff;
  padding: 0px 5px;
}

#luckysheet-functionbox .luckysheet-functionbox-cell-input {
  word-wrap: break-word;
  -webkit-nbsp-mode: space;
  -webkit-line-break: after-white-space;
}

.luckysheet-functionbox-cell-input {
  width: 100%;
  height: 100%;
  margin: 0;
  outline: none;
  cursor: text;
  -webkit-user-modify: read-write-plaintext-only;
  white-space: pre-wrap;
  -webkit-transform: translateZ(0);
  background-color: white;
}

.luckysheet-formula-text-color {
  color: black;
}

.luckysheet-formula-text-string {
  color: forestgreen;
}

.luckysheet-formula-search-c {
  position: absolute;
  left: 50%;
  top: 50%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: #535353;
  font-size: 12px;
  background: #fff;
  z-index: 1003;
  width: 300px;
  display: none;
}

.luckysheet-formula-search-c .luckysheet-formula-search-item {
  background: #fff;
  padding: 5px 10px;
  cursor: pointer;
}

.luckysheet-formula-search-c
  .luckysheet-formula-search-item
  .luckysheet-formula-search-detail {
  display: none;
  color: #444;
}

.luckysheet-formula-search-c
  .luckysheet-formula-search-item
  .luckysheet-formula-search-func {
  color: #222;
  font-size: 14px;
}

.luckysheet-formula-search-c .luckysheet-formula-search-item-active {
  display: block;
  border-top: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
  background: #f5f5f5;
}

.luckysheet-formula-search-c
  .luckysheet-formula-search-item-active
  .luckysheet-formula-search-detail {
  display: block;
}

.luckysheet-formula-help-c {
  display: none;
  position: absolute;
  left: 20%;
  top: 20%;
  border: 1px solid rgba(0, 0, 0, 0.2);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  color: #535353;
  font-size: 12px;
  background: #fff;
  z-index: 1003;
  width: 300px;
}

.luckysheet-formula-help-c .luckysheet-formula-help-content {
  max-height: 300px;
  overflow-y: scroll;
}

.luckysheet-formula-help-content-example {
  margin-top: 5px;
}

.luckysheet-formula-help-title {
  display: block;
  border-top: 1px solid #ebebeb;
  border-bottom: 1px solid #ebebeb;
  background: #f5f5f5;
  padding: 2px 10px;
  font-size: 14px;
}

.luckysheet-formula-help-title-formula {
  width: 250px;
  word-break: break-word;
}

.luckysheet-arguments-help-section {
  margin-top: 5px;
  margin-bottom: 5px;
  color: #222;
}

.luckysheet-arguments-help-section-title {
  padding: 1px 10px;
  color: #666;
}

.luckysheet-arguments-help-parameter-content {
  padding: 1px 10px;
  display: inline-block;
  word-wrap: break-word;
}

.luckysheet-arguments-help-formula {
  padding: 1px 10px;
  font-size: 14px;
}

.luckysheet-arguments-help-parameter-active {
  background-color: #fff9b2;
}

.luckysheet-formula-help-collapse {
  position: absolute;
  top: 0px;
  right: 25px;
  font-size: 16px;
  cursor: pointer;
  color: #bbb;
}

.luckysheet-formula-help-close {
  position: absolute;
  top: 0px;
  right: 5px;
  font-size: 16px;
  cursor: pointer;
  color: #bbb;
}

.luckysheet-formula-help-close:hover,
.luckysheet-formula-help-collapse:hover {
  color: #555;
}

.luckysheetpopover {
  position: absolute;
  /*    left: 50%; 
    top:20px; */
  background: rgba(85, 187, 138, 0.65);
  color: rgba(0, 0, 0, 0.7);
  font-size: 18px;
  padding: 20px 100px;
  text-align: center;
  z-index: 10000;
  border-radius: 4px;
  user-select: none;
  display: none;
}

.luckysheetpopover .luckysheetpopover-content {
}

.luckysheetpopover .luckysheetpopover-btn {
  position: absolute;
  right: 10px;
  top: 50%;
  margin-top: -12px;
  border: 1px solid rgba(0, 0, 0, 0.7);
  border-radius: 4px;
  padding: 2px 4px;
  cursor: pointer;
  font-size: 14px;
}

.luckysheetpopover .luckysheetpopover-btn:hover {
  border: 1px solid var(--luckysheet-main-color-7);
  color: var(--luckysheet-main-color-7);
}

.luckysheetPaintCursor {
  cursor: url(paint_24px.ico), auto;
}

/*        input {
             -webkit-appearance: textfield;
             background-color: white;
             -webkit-rtl-ordering: logical;
             user-select: text;
             cursor: auto;
             padding: 1px;
             border-width: 2px;
             border-style: inset;
             border-color: initial;
             border-image: initial;
         }
         user agent stylesheet
         input, textarea, keygen, select, button {
             text-rendering: auto;
             color: initial;
             letter-spacing: normal;
             word-spacing: normal;
             text-transform: none;
             text-indent: 0px;
             text-shadow: none;
             display: inline-block;
             text-align: start;
             margin: 0em 0em 0em 0em;
             font: 13.3333px Arial;
         }*/

/*查找替换弹出框样式*/
#luckysheet-search-replace .tabBox {
  margin-top: 20px;
  font-size: 0;
}

#luckysheet-search-replace .tabBox span {
  cursor: pointer;
  user-select: none;
  display: inline-block;
  text-align: center;
  width: 100px;
  border: 1px solid #d4d4d4;
  font-size: 14px;
  line-height: 2;
}

#luckysheet-search-replace .tabBox span.on {
  background-color: var(--luckysheet-main-color);
  border-color: var(--luckysheet-main-color);
  color: #fff;
}

#luckysheet-search-replace .ctBox {
  margin-top: 10px;
  padding: 5px 10px;
  border: solid 1px #d4d4d4;
  font-size: 14px;
}
#luckysheet-search-replace .ctBox input {
  padding: 4px 5px;
  outline: none;
  border: solid 1px;
  border-radius: 4px;
}
#luckysheet-search-replace .ctBox input:focus-visible {
  outline: none;
  border: solid var(--luckysheet-main-color) 1px;
}

#luckysheet-search-replace .inputBox {
  height: 90px;
  position: relative;
}

#luckysheet-search-replace .inputBox .textboxs {
  height: 30px;
  line-height: 30px;
}

#luckysheet-search-replace .inputBox .checkboxs {
  height: 90px;
  position: absolute;
  right: 0;
  top: 0;
}

#luckysheet-search-replace .inputBox .checkboxs div {
  height: 30px;
  line-height: 30px;
}

#luckysheet-search-replace .inputBox .checkboxs input[type="checkbox"] {
  float: left;
  margin-top: 9px;
}

#luckysheet-search-replace .btnBox {
  margin-top: 10px;
}

#luckysheet-search-replace .btnBox button {
  margin: 0 2.5px;
}
#luckysheet-search-replace .btnBox button:focus,
#luckysheet-search-replace .luckysheet-model-close-btn:focus {
  outline: none;
}

#luckysheet-search-replace #searchAllbox {
  height: 210px;
  border: 1px solid #d4d4d4;
  margin-top: 10px;
  overflow-y: auto;
  position: relative;
}

#luckysheet-search-replace #searchAllbox .boxTitle {
  width: 100%;
  height: 30px;
  line-height: 29px;
  padding: 0 5px;
  background-color: #fff;
  border-bottom: 1px solid #d4d4d4;
  box-sizing: border-box;
  position: sticky;
  left: 0;
  top: 0;
}

#luckysheet-search-replace #searchAllbox .boxTitle span {
  display: inline-block;
  text-align: center;
}

#luckysheet-search-replace #searchAllbox .boxTitle span:nth-of-type(1) {
  width: 25%;
}

#luckysheet-search-replace #searchAllbox .boxTitle span:nth-of-type(2) {
  width: 25%;
}

#luckysheet-search-replace #searchAllbox .boxTitle span:nth-of-type(3) {
  width: 50%;
}

#luckysheet-search-replace #searchAllbox .boxMain .boxItem {
  height: 30px;
  line-height: 29px;
  border-bottom: 1px solid #d4d4d4;
  padding: 0 5px;
  box-sizing: border-box;
}

#luckysheet-search-replace #searchAllbox .boxMain .boxItem.on {
  background-color: #8c89fe;
  color: #fff;
}

#luckysheet-search-replace #searchAllbox .boxMain .boxItem span {
  display: block;
  text-align: center;
  float: left;
}

#luckysheet-search-replace #searchAllbox .boxMain .boxItem span:nth-of-type(1) {
  width: 25%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#luckysheet-search-replace #searchAllbox .boxMain .boxItem span:nth-of-type(2) {
  width: 25%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#luckysheet-search-replace #searchAllbox .boxMain .boxItem span:nth-of-type(3) {
  width: 50%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/*函数公式查找样式*/
#luckysheet-search-formula {
  font-size: 12px;
}

#luckysheet-search-formula .inpbox {
  margin-bottom: 5px;
}

#luckysheet-search-formula .inpbox label {
  display: block;
  margin-bottom: 5px;
}

#luckysheet-search-formula .inpbox input {
  width: 100%;
  height: 24px;
  line-height: 24px;
  border: 1px solid #d4d4d4;
  padding: 0 10px;
  box-sizing: border-box;
  font-size: 12px;
}

#luckysheet-search-formula .selbox {
  margin-bottom: 5px;
}

#luckysheet-search-formula .selbox select {
  width: 50%;
  height: 24px;
  line-height: 24px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
  font-size: 12px;
}

#luckysheet-search-formula .listbox label {
  display: block;
  margin-bottom: 5px;
}

#formulaTypeList {
  width: 300px;
  height: 170px;
  border: 1px solid #d4d4d4;
  overflow-y: scroll;
}

#formulaTypeList .listBox {
  cursor: pointer;
  padding: 5px;
  border-bottom: 1px solid #d4d4d4;
}
#formulaTypeList .listBox:hover{
  background-color: var(--luckysheet-main-color-a2);
  color: var(--luckysheet-main-color);
}

#formulaTypeList .listBox.on {
  background-color: var(--luckysheet-main-color);
  color: #fff;
}

#formulaTypeList .listBox span:nth-of-type(1) {
  display: block;
}

#formulaTypeList .listBox span:nth-of-type(2) {
  display: block;
}

#luckysheet-search-formula-parm {
  width: 502px;
  font-size: 12px;
}

#luckysheet-search-formula-parm .parmListBox {
  width: 500px;
  padding: 5px 0;
  border: 1px solid #d4d4d4;
}

#luckysheet-search-formula-parm .parmBox {
  height: 30px;
  line-height: 30px;
  margin-bottom: 5px;
}

#luckysheet-search-formula-parm .parmBox:last-child {
  margin-bottom: 0;
}

#luckysheet-search-formula-parm .parmBox .name {
  width: 90px;
  height: 30px;
  padding: 0 5px;
  float: left;
  text-align: right;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#luckysheet-search-formula-parm .parmBox .txt {
  width: 198px;
  height: 28px;
  border: 1px solid #d4d4d4;
  float: left;
}

#luckysheet-search-formula-parm .parmBox .txt input {
  width: 150px;
  height: 28px;
  padding: 0 10px;
  border: none;
  outline-style: none;
  float: left;
}

#luckysheet-search-formula-parm .parmBox .txt i {
  float: right;
  margin-top: 8px;
  margin-right: 5px;
}

#luckysheet-search-formula-parm .fa-table {
  cursor: pointer;
  color: #6598f3;
}

#luckysheet-search-formula-parm .fa-table:hover {
  color: #ff7e7e;
}

#luckysheet-search-formula-parm .parmBox .val {
  width: 190px;
  height: 30px;
  line-height: 30px;
  padding: 0 5px;
  float: left;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

#luckysheet-search-formula-parm .formulaDetails {
  padding: 5px;
}

#luckysheet-search-formula-parm .parmDetailsBox {
  max-height: 100px;
  padding: 5px 0 5px 20px;
  overflow-y: scroll;
}

#luckysheet-search-formula-parm .parmDetailsBox span {
  display: inline-block;
}

#luckysheet-search-formula-parm .result {
  padding: 5px;
  border-top: 1px solid #d4d4d4;
}

/*条件格式颜色选择器*/
#textCellColor {
  border: 1px solid #d4d4d4;
  padding: 5px 10px;
}

#textCellColor .colorbox {
  height: 30px;
  line-height: 30px;
  margin-bottom: 10px;
}

#textCellColor .colorbox input[type="checkbox"] {
  float: left;
  margin-top: 10px;
}

#textCellColor .colorbox label {
  display: inline-block;
  width: 80px;
}

/*条件格式单元格选择框*/
#luckysheet-singleRange-dialog input,
#luckysheet-multiRange-dialog input {
  border: 1px solid #d4d4d4;
  padding: 0 10px;
  height: 30px;
}

/*条件格式弹出框*/
#luckysheet-conditionformat-dialog {
  font-size: 12px;
}

#luckysheet-conditionformat-dialog .box .boxTitleOne {
  margin: 5px 0;
  font-weight: 600;
}

#luckysheet-conditionformat-dialog .box .inpbox {
  width: 198px;
  height: 28px;
  border: 1px solid #d4d4d4;
}

#luckysheet-conditionformat-dialog .box .inpbox input {
  width: 150px;
  height: 28px;
  padding: 0 10px;
  border: none;
  outline-style: none;
  float: left;
}

#luckysheet-conditionformat-dialog .box .inpbox2 {
  float: left;
  width: 108px;
  height: 28px;
  border: 1px solid #d4d4d4;
}

#luckysheet-conditionformat-dialog .box .inpbox2 input {
  width: 60px;
  height: 28px;
  padding: 0 10px;
  border: none;
  outline-style: none;
  float: left;
}

#luckysheet-conditionformat-dialog .box i.fa-table {
  float: right;
  margin-top: 8px;
  margin-right: 5px;
}

#luckysheet-conditionformat-dialog .box .fa-table {
  cursor: pointer;
  color: #6598f3;
}

#luckysheet-conditionformat-dialog .box .fa-table:hover {
  color: #ff7e7e;
}

#luckysheet-conditionformat-dialog .box #daterange-btn {
  width: 188px;
  height: 28px;
  padding: 0 5px;
  line-height: 28px;
  border: 1px solid #d4d4d4;
  cursor: pointer;
}

#luckysheet-conditionformat-dialog .box .selectbox {
  width: 150px;
  height: 30px;
}

#luckysheet-icon-dataBar-menuButton .bgImgBox {
  width: 28px;
  height: 26px;
  background: url(../plugins/images/CFdataBar.png) no-repeat;
}

#luckysheet-icon-colorGradation-menuButton .bgImgBox {
  width: 28px;
  height: 26px;
  background: url(../plugins/images/CFcolorGradation.png) no-repeat;
}

/*条件格式规则管理器*/
#luckysheet-administerRule-dialog {
  font-size: 12px;
}

#luckysheet-administerRule-dialog .chooseSheet {
  height: 24px;
  line-height: 24px;
  margin-bottom: 5px;
}

#luckysheet-administerRule-dialog .chooseSheet select {
  height: 24px;
  padding: 0 5px;
  box-sizing: border-box;
  font-size: 12px;
}

#luckysheet-administerRule-dialog .ruleBox {
  border: 1px solid #d4d4d4;
}

#luckysheet-administerRule-dialog .ruleBox .ruleBtn {
  padding: 2.5px 5px;
  border-bottom: 1px solid #d4d4d4;
}

#luckysheet-administerRule-dialog .ruleBox .ruleBtn button {
  margin-right: 10px;
  font-size: 12px;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listTitle {
  height: 30px;
  padding: 0 10px;
  border-bottom: 1px solid #d4d4d4;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listTitle span {
  display: block;
  height: 100%;
  line-height: 29px;
  float: left;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listTitle
  span:nth-of-type(1) {
  width: 30%;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listTitle
  span:nth-of-type(2) {
  width: 20%;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listTitle
  span:nth-of-type(3) {
  width: 45%;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listBox {
  height: 150px;
  overflow-y: scroll;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listBox .item {
  height: 24px;
  padding: 2.5px 10px;
  border-bottom: 1px solid #d4d4d4;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listBox .item.on {
  background-color: #8c89fe;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listBox .item .ruleName {
  width: 30%;
  height: 100%;
  line-height: 24px;
  padding-right: 10px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  float: left;
  box-sizing: border-box;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listBox
  .item.on
  .ruleName {
  color: #fff;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listBox .item .format {
  width: 20%;
  height: 100%;
  line-height: 24px;
  float: left;
  position: relative;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listBox
  .item
  .format
  .colorbox {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: solid 1px #d0d0d0;
  margin: 3px 5px;
  cursor: pointer;
}

#luckysheet-administerRule-dialog .ruleBox .ruleList .listBox .item .ruleRange {
  width: 45%;
  height: 100%;
  border: 1px solid #d4d4d4;
  float: left;
  margin-left: 10px;
  box-sizing: border-box;
  background-color: #fff;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listBox
  .item
  .ruleRange
  input {
  width: 130px;
  height: 22px;
  padding: 0 5px;
  border: none;
  outline-style: none;
  float: left;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listBox
  .item
  .ruleRange
  i.fa-table {
  float: right;
  cursor: pointer;
  color: #6598f3;
  margin-top: 6px;
  margin-right: 5px;
}

#luckysheet-administerRule-dialog
  .ruleBox
  .ruleList
  .listBox
  .item
  .ruleRange
  i.fa-table:hover {
  color: #ff7e7e;
}

/*新建、编辑条件格式规则*/
.luckysheet-newEditorRule-dialog {
  font-size: 12px;
}

.luckysheet-newEditorRule-dialog .boxTitle {
  margin-bottom: 5px;
}

.luckysheet-newEditorRule-dialog .ruleTypeBox {
  border: 1px solid #d4d4d4;
  margin-bottom: 10px;
}

.luckysheet-newEditorRule-dialog .ruleTypeBox .ruleTypeItem {
  padding: 3px 5px;
  cursor: pointer;
}

.luckysheet-newEditorRule-dialog .ruleTypeBox .ruleTypeItem.on {
  background-color: #7c79fe;
  color: #fff;
}

.luckysheet-newEditorRule-dialog .ruleTypeBox .ruleTypeItem .icon {
  font-family: Arial, Helvetica, sans-serif;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox {
  border: 1px solid #d4d4d4;
  padding: 10px;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox .title {
  display: block;
  font-weight: 600;
  margin-bottom: 5px;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox select {
  height: 30px;
  font-size: 12px;
  float: left;
  margin-right: 5px;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox .inpbox {
  width: 100px;
  height: 30px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
  font-size: 12px;
  float: left;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox .inpbox input {
  width: 70px;
  height: 100%;
  border: none;
  outline-style: none;
  padding: 0 5px;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox .txt {
  display: block;
  height: 100%;
  line-height: 30px;
  float: left;
  margin: 0 5px;
}

.luckysheet-newEditorRule-dialog .ruleExplainBox #isPercent {
  float: left;
  margin: 9px 0 8px 10px;
}

.luckysheet-newEditorRule-dialog i.fa-table {
  float: right;
  margin-top: 8px;
  margin-right: 5px;
}

.luckysheet-newEditorRule-dialog .fa-table {
  cursor: pointer;
  color: #6598f3;
}

.luckysheet-newEditorRule-dialog .fa-table:hover {
  color: #ff7e7e;
}

.luckysheet-newEditorRule-dialog .iconsBox {
  height: 30px;
  margin-bottom: 5px;
  position: relative;
}

.luckysheet-newEditorRule-dialog .iconsBox label {
  display: block;
  width: 80px;
  height: 30px;
  line-height: 30px;
  float: left;
}

.luckysheet-newEditorRule-dialog .iconsBox .showbox {
  width: 150px;
  height: 20px;
  padding: 4px 4px 4px 10px;
  border: 1px solid #e5e5e5;
  background-color: #f5f5f5;
  float: left;
  cursor: pointer;
}

.luckysheet-newEditorRule-dialog .iconsBox .showbox .model {
  width: 125px;
  height: 20px;
  background: url(../plugins/images/CFicons.png) no-repeat;
  background-size: 256px;
  float: left;
}

.luckysheet-newEditorRule-dialog .iconsBox ul {
  display: none;
  width: 164px;
  max-height: 150px;
  overflow-y: auto;
  background-color: #fff;
  border: 1px solid #e5e5e5;
  position: absolute;
  left: 80px;
  top: 30px;
  list-style: none;
  margin: 0;
  padding: 0;
}

.luckysheet-newEditorRule-dialog .iconsBox ul li {
  padding: 5px 10px;
  background-color: #fff;
  cursor: pointer;
}

.luckysheet-newEditorRule-dialog .iconsBox ul li:hover {
  background-color: #dfdfdf;
}

.luckysheet-newEditorRule-dialog .iconsBox ul li div {
  width: 125px;
  height: 20px;
  background: url(../plugins/images/CFicons.png) no-repeat;
  background-size: 256px;
}

/*条件格式 图标集弹框*/
#luckysheet-CFicons-dialog .box {
  padding: 10px;
  border: 1px solid #dfdfdf;
  font-size: 14px;
}

#luckysheet-CFicons-dialog .box .title {
  height: 20px;
  line-height: 20px;
  padding: 0 10px;
  background-color: #ebebeb;
}

#luckysheet-CFicons-dialog .box .list {
  width: 300px;
  padding: 5px 0;
}

#luckysheet-CFicons-dialog .box .list .left {
  width: 50%;
  float: left;
}

#luckysheet-CFicons-dialog .box .list .right {
  width: 50%;
  float: right;
}

#luckysheet-CFicons-dialog .box .list .item {
  width: 125px;
  height: 20px;
  padding: 2.5px 10px;
  background-color: #fff;
  cursor: pointer;
}

#luckysheet-CFicons-dialog .box .list .item:hover {
  background-color: #dfdfdf;
}

#luckysheet-CFicons-dialog .box .list .item div {
  width: 125px;
  height: 20px;
  background: url(../plugins/images/CFicons.png) no-repeat;
  background-size: 256px;
}

/*交替颜色*/
#luckysheet-modal-dialog-slider-alternateformat {
  width: 280px;
  font-size: 12px;
}

#luckysheet-modal-dialog-slider-alternateformat
  .luckysheet-modal-dialog-slider-content {
  background-color: #fff;
  overflow-y: scroll;
}

#luckysheet-modal-dialog-slider-alternateformat .textTitle {
  padding: 5px 10px;
  font-weight: 600;
}

#luckysheet-alternateformat-range {
  width: 198px;
  height: 28px;
  border: 1px solid #d4d4d4;
  margin-left: 10px;
}

#luckysheet-alternateformat-range input {
  width: 150px;
  height: 28px;
  padding: 0 10px;
  border: none;
  outline-style: none;
  float: left;
}

#luckysheet-alternateformat-range .fa-table {
  float: right;
  margin-top: 8px;
  margin-right: 5px;
  cursor: pointer;
  color: #6598f3;
}

#luckysheet-alternateformat-range .fa-table:hover {
  color: #ff7e7e;
}

#luckysheet-alternateformat-checkbox {
  padding: 5px 10px;
  border-top: 1px solid #d4d4d4;
  border-bottom: 1px solid #d4d4d4;
  margin: 10px 0;
}

#luckysheet-alternateformat-checkbox div {
  height: 20px;
  line-height: 20px;
}

#luckysheet-alternateformat-checkbox div:first-child {
  margin-bottom: 5px;
}

#luckysheet-alternateformat-checkbox input[type="checkbox"] {
  float: left;
  cursor: pointer;
  margin-top: 4px;
}

#luckysheet-alternateformat-modelList {
  padding: 0 10px;
  margin-bottom: 10px;
}

#luckysheet-alternateformat-modelCustom {
  padding: 0 10px;
  margin-bottom: 10px;
}

#luckysheet-modal-dialog-slider-alternateformat .modelbox {
  display: inline-block;
  width: 36px;
  padding: 2px;
  border: 2px solid #fff;
  box-sizing: border-box;
  margin-right: 4px;
  margin-bottom: 4px;
  cursor: pointer;
}

#luckysheet-modal-dialog-slider-alternateformat .modelbox.on {
  border-color: #726efe;
}

#luckysheet-modal-dialog-slider-alternateformat .modelbox .box {
  width: 100%;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
}

#luckysheet-modal-dialog-slider-alternateformat .modelbox .box span {
  display: block;
  width: 100%;
  height: 10px;
  line-height: 9px;
  text-align: center;
  border-bottom: 1px solid #d4d4d4;
  box-sizing: border-box;
}

#luckysheet-modal-dialog-slider-alternateformat .modelbox .box span:last-child {
  line-height: 10px;
  border-bottom: none;
}

#luckysheet-alternateformat-modelToning {
  padding: 10px;
}

#luckysheet-alternateformat-modelToning .toningbox {
  height: 25px;
  margin-bottom: 5px;
}

#luckysheet-alternateformat-modelToning .toningbox .toningShow {
  width: 150px;
  height: 100%;
  line-height: 23px;
  text-align: center;
  border: 1px solid #d4d4d4;
  float: left;
  margin-right: 10px;
}

#luckysheet-alternateformat-modelToning
  .toningbox
  .luckysheet-color-menu-button-indicator {
  width: 20px;
  float: left;
  user-select: none;
  cursor: pointer;
}

#luckysheet-alternateformat-modelToning
  .toningbox
  .luckysheet-color-menu-button-indicator
  .luckysheet-icon {
  user-select: none;
  margin-bottom: -6px;
}

#luckysheet-alternateformat-colorSelect-dialog .currenColor {
  font-size: 12px;
  margin-bottom: 5px;
}

#luckysheet-alternateformat-colorSelect-dialog .currenColor span {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: solid 1px #d0d0d0;
  margin-left: 5px;
  margin-bottom: -5px;
  cursor: pointer;
}

#luckysheet-alternateformat-rangeDialog input {
  border: 1px solid #d4d4d4;
  padding: 0 10px;
  height: 30px;
}

/*if公式生成器*/
#luckysheet-ifFormulaGenerator-dialog {
  font-size: 12px;
}

#luckysheet-ifFormulaGenerator-dialog .ifAttr .attrBox {
  height: 30px;
  margin-bottom: 10px;
}

#luckysheet-ifFormulaGenerator-dialog .ifAttr .attrBox label {
  display: block;
  width: 100px;
  height: 100%;
  line-height: 30px;
  padding: 0 5px;
  text-align: right;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog .ifAttr .attrBox .inpBox {
  width: 150px;
  height: 100%;
  padding: 0 10px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog .ifAttr .attrBox .inpBox input {
  width: 100px;
  height: 100%;
  padding: 0;
  border: none;
  outline-style: none;
  background: transparent;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog .ifAttr .attrBox .inpBox i.fa-table {
  font-size: 14px;
  color: #6598f3;
  float: right;
  margin-right: 0;
  margin-top: 8px;
  cursor: pointer;
}

#luckysheet-ifFormulaGenerator-dialog
  .ifAttr
  .attrBox
  .inpBox
  i.fa-table:hover {
  color: #ff7e7e;
}

#luckysheet-ifFormulaGenerator-dialog .ifAttr .attrBox span.text {
  height: 100%;
  line-height: 30px;
  padding: 0 5px;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog #smallRange,
#luckysheet-ifFormulaGenerator-dialog #largeRange {
  width: 100px;
  height: 100%;
  padding: 0 10px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog #rangeAssess {
  height: 100%;
  line-height: 30px;
  float: left;
  margin-left: 20px;
}

#luckysheet-ifFormulaGenerator-dialog #rangeAssess i.fa-table {
  color: #6598f3;
  cursor: pointer;
}

#luckysheet-ifFormulaGenerator-dialog #rangeAssess i.fa-table:hover {
  color: #ff7e7e;
}

#luckysheet-ifFormulaGenerator-dialog #DivisionMethod {
  width: 100px;
  height: 100%;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog #DivisionMethodVal {
  width: 120px;
  height: 100%;
  border: 1px solid #d4d4d4;
  padding: 0 10px;
  box-sizing: border-box;
  float: left;
  margin-left: 10px;
}

#luckysheet-ifFormulaGenerator-dialog #createBtn {
  width: 100px;
  height: 100%;
  line-height: 30px;
  border-radius: 5px;
  text-align: center;
  font-size: 14px;
  color: #fff;
  background-color: #8c89fe;
  float: right;
  cursor: pointer;
}

#luckysheet-ifFormulaGenerator-dialog .ifList {
  border-top: 1px solid #d4d4d4;
  height: 180px;
  padding: 10px;
  overflow-y: scroll;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item {
  height: 30px;
  margin-bottom: 10px;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item input {
  width: 80px;
  height: 100%;
  border: 1px solid #d4d4d4;
  padding: 0 5px;
  background: transparent;
  box-sizing: border-box;
  float: left;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item input.markText {
  width: 140px;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item select {
  width: 50px;
  height: 100%;
  padding: 0 5px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
  float: left;
  margin: 0 10px;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item span {
  height: 100%;
  line-height: 30px;
  float: left;
  margin: 0 10px;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item i.fa-remove {
  font-size: 16px;
  float: left;
  margin-left: 15px;
  margin-top: 7px;
  color: #d6d6d6;
  cursor: pointer;
}

#luckysheet-ifFormulaGenerator-dialog .ifList .item i.fa-remove:hover {
  color: #333;
}

#luckysheet-ifFormulaGenerator-singleRange-dialog input,
#luckysheet-ifFormulaGenerator-multiRange-dialog input {
  border: 1px solid #d4d4d4;
  padding: 0 10px;
  height: 30px;
}

.pictorialBarUploadImg:hover {
  border: 1px solid #ccc !important;
  background: #efefef;
}

/*下拉图标*/
#luckysheet-dropCell-icon #icon_dropCell {
  width: 25px;
  height: 15px;
  background-image: url(../plugins/images/icon_dropCell.png);
  background-repeat: no-repeat;
  background-position: center;
  background-size: 100% 100%;
}

/*定位*/
#luckysheet-locationCell-dialog .listbox {
  border: 1px solid #dfdfdf;
  padding: 10px;
  font-size: 14px;
  color: #000;
}

#luckysheet-locationCell-dialog .listbox .listItem {
  padding: 5px 0;
}

#luckysheet-locationCell-dialog .listbox .listItem input[type="radio"] {
  float: left;
  margin-top: 5px;
}

#luckysheet-locationCell-dialog .listbox .listItem .subbox {
  height: 30px;
  padding: 0 10px;
}

#luckysheet-locationCell-dialog .listbox .listItem .subbox .subItem {
  float: left;
  margin-right: 5px;
}

/*更多格式*/
#luckysheet-moreFormat-dialog {
  font-size: 14px;
  color: #000;
}

#luckysheet-moreFormat-dialog .decimal {
  margin-bottom: 5px;
  height: 30px;
  line-height: 30px;
}

#luckysheet-moreFormat-dialog .decimal input {
  width: 80px;
  height: 24px;
  padding: 0 5px;
}

#luckysheet-moreFormat-dialog .listbox {
  border: 1px solid #666;
  height: 240px;
  overflow-y: auto;
}

#luckysheet-moreFormat-dialog .listbox .listItem {
  height: 30px;
  padding: 0 20px 0 10px;
  border-bottom: 1px solid #dfdfdf;
}

#luckysheet-moreFormat-dialog .listbox .listItem.on {
  background-color: #7c79fe;
  color: #fff;
}

#luckysheet-moreFormat-dialog .listbox .listItem .name {
  line-height: 29px;
  float: left;
}

#luckysheet-moreFormat-dialog .listbox .listItem .value {
  line-height: 30px;
  float: right;
  color: gray;
}

#luckysheet-moreFormat-dialog .listbox .listItem.on .value {
  color: #fff;
}

/*分列*/
#luckysheet-splitColumn-dialog {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

#luckysheet-splitColumn-dialog .box {
  font-size: 14px;
}

#luckysheet-splitColumn-dialog .box .boxTitle {
  padding: 5px;
}

#luckysheet-splitColumn-dialog .box .boxMain {
  padding: 5px;
  border: 1px solid #dfdfdf;
}

#luckysheet-splitColumn-dialog .box input[type="checkbox"] {
  float: left;
  margin-top: 5px;
}

#luckysheet-splitColumn-dialog .box .boxMain input[type="text"] {
  margin-left: 5px;
  width: 50px;
  padding: 0 5px;
}

#luckysheet-splitColumn-dialog .box #splitColumnData {
  height: 100px;
  overflow-y: auto;
}

#luckysheet-splitColumn-dialog .box #splitColumnData table {
  border-collapse: collapse;
}

#luckysheet-splitColumn-dialog .box #splitColumnData td {
  border: 1px solid #333;
}

/*图表设置最下方下拉框优化*/
.luckysheet-datavisual-config
  .luckysheet-datavisual-accordion-content:last-child {
  padding-bottom: 100px;
}

/*批注聚焦框 移动 改变大小*/
.luckysheet-postil-dialog-move {
  position: absolute;
  margin: 0px;
  padding: 0px;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  pointer-events: none;
}

.luckysheet-postil-dialog-move .luckysheet-postil-dialog-move-item {
  /*background: #000;*/
  position: absolute;
  pointer-events: all;
  cursor: move;
}

.luckysheet-postil-dialog-move .luckysheet-postil-dialog-move-item-t {
  width: 100%;
  height: 3px;
  border-bottom: 1px solid #000;
  left: 0;
  top: -4px;
}

.luckysheet-postil-dialog-move .luckysheet-postil-dialog-move-item-r {
  width: 3px;
  height: 100%;
  border-left: 1px solid #000;
  right: -4px;
  top: 0;
}

.luckysheet-postil-dialog-move .luckysheet-postil-dialog-move-item-b {
  width: 100%;
  height: 3px;
  border-top: 1px solid #000;
  left: 0;
  bottom: -4px;
}

.luckysheet-postil-dialog-move .luckysheet-postil-dialog-move-item-l {
  width: 3px;
  height: 100%;
  border-right: 1px solid #000;
  left: -4px;
  top: 0;
}

.luckysheet-postil-show-active
  .luckysheet-postil-dialog-move
  .luckysheet-postil-dialog-move-item {
  border-color: var(--luckysheet-main-color);
}

.luckysheet-postil-dialog-resize {
  position: absolute;
  margin: 0px;
  padding: 0px;
  top: -2px;
  left: -2px;
  bottom: -2px;
  right: -2px;
  pointer-events: none;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item {
  position: absolute;
  height: 6px;
  width: 6px;
  border: 1px solid var(--luckysheet-main-color);
  pointer-events: all;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-lt {
  left: -6px;
  top: -6px;
  cursor: se-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-mt {
  left: 50%;
  top: -6px;
  margin-left: -4px;
  cursor: s-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-lm {
  top: 50%;
  left: -6px;
  margin-top: -4px;
  cursor: w-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-rm {
  top: 50%;
  right: -6px;
  margin-top: -4px;
  cursor: w-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-rt {
  right: -6px;
  top: -6px;
  cursor: ne-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-lb {
  left: -6px;
  bottom: -6px;
  cursor: ne-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-mb {
  left: 50%;
  bottom: -6px;
  margin-left: -4px;
  cursor: s-resize;
}

.luckysheet-postil-dialog-resize .luckysheet-postil-dialog-resize-item-rb {
  right: -6px;
  bottom: -6px;
  cursor: se-resize;
}

/*图表设置最下方下拉框优化*/
.luckysheet-datavisual-config
  .luckysheet-datavisual-accordion-content:last-child {
  padding-bottom: 100px;
}

/*图表样式新增样式: 设置界面overflow滚动*/
.luckysheet-datavisual-left .el-tabs__content {
  overflow: auto;
}

/* .luckysheet-modal-dialog {
    -webkit-box-shadow: 0 4px 16px rgba(0, 0, 0, .2);
    -moz-box-shadow: 0 4px 16px rgba(0, 0, 0, .2);
    box-shadow: 0 4px 16px rgba(0, 0, 0, .2);
    background: #fff;
    background-clip: padding-box;
    border: 1px solid #acacac;
    border: 1px solid rgba(0, 0, 0, .333);
    outline: 0;
    position: absolute;
    color: #000;
    padding: 30px 42px;
    z-index: 100002;
}

.luckysheet-modal-dialog-mask {
    position: absolute;
    height: 100%;
    width: 100%;
    background: #fff;
    opacity: 0.6;
    display: none;
    left: 0px;
    top: 0px;
    z-index: 1010;
}



.luckysheet-modal-dialog-title {
    background-color: #fff;
    color: #000;
    cursor: default;
    font-size: 16px;
    font-weight: normal;
    line-height: 24px;
    margin: 0 0 16px;
}

.luckysheet-modal-dialog-title-close {
    height: 11px;
    opacity: 0.7;
    padding: 17px;
    position: absolute;
    right: 0px;
    top: 0px;
    width: 11px;
    color: #d4d4d4;
    outline: 0;
}

.luckysheet-modal-dialog-chart {
    padding: 20px 10px;
    webkit-box-shadow: none;
    -moz-box-shadow: none;
    box-shadow: none;
}

.luckysheet-modal-dialog-resize {
    position: absolute;
    border: 2px solid #85c0fc;
    margin: 0px;
    padding: 0px;
    top: -2px;
    left: -2px;
    bottom: -2px;
    right: -2px;
    pointer-events: none;
}

.luckysheet-modal-dialog-resize-item {
    position: absolute;
    height: 6px;
    width: 6px;
    background: #ffffff;
    border: 2px solid #85c0fc;
    pointer-events: all;
    border-radius: 6px;
}

.luckysheet-modal-dialog-resize-item-lt {
    left: -6px;
    top: -6px;
    cursor: se-resize;
}

.luckysheet-modal-dialog-resize-item-mt {
    left: 50%;
    top: -6px;
    margin-left: -4px;
    cursor: s-resize;
}

.luckysheet-modal-dialog-resize-item-rt {
    right: -6px;
    top: -6px;
    cursor: ne-resize;
}

.luckysheet-modal-dialog-resize-item-lm {
    top: 50%;
    left: -6px;
    margin-top: -4px;
    cursor: w-resize;
}

.luckysheet-modal-dialog-resize-item-rm {
    top: 50%;
    right: -6px;
    margin-top: -4px;
    cursor: w-resize;
}

.luckysheet-modal-dialog-resize-item-lb {
    left: -6px;
    bottom: -6px;
    cursor: ne-resize;
}

.luckysheet-modal-dialog-resize-item-mb {
    left: 50%;
    bottom: -6px;
    margin-left: -4px;
    cursor: s-resize;
}

.luckysheet-modal-dialog-resize-item-rb {
    right: -6px;
    bottom: -6px;
    cursor: se-resize;
}


.luckysheet-modal-dialog-controll {
    position: absolute;
    margin: 0px;
    padding: 0px;
    right: -35px;
    font-size: 14px;
    top: 0px;
}

.luckysheet-modal-controll-btn {
    height: 13px;
    padding: 8px;
    width: 13px;
    color: #d4d4d4;
    outline: 0;
    border: 1px solid #B6B6B6;
    display: block;
    background: #fff;
    margin-bottom: 3px;
    cursor: pointer;
    transition: all 0.2s;
    -moz-transition: all 0.2s;
    -webkit-transition: all 0.2s;
    -o-transition: all 0.2s;
}

.luckysheet-modal-controll-btn:hover {
    border: 1px solid #A1A1A1;
    color: #85c0fc;
}

.luckysheet-modal-controll-btn:active {
    border: 1px solid #BBBBBB;
    background: #EFEFEF;
    color: #85c0fc;
}

.luckysheet-modal-controll-del {
    font-size: 16px;
}

.luckysheet-modal-controll-max-close {
    font-size: 22px;
    width: 42px;
    height: 42px;
    line-height: 42px;
    background: #383838;
    opacity: 0.7;
    -moz-border-radius: 20px;
    -webkit-border-radius: 20px;
    border-radius: 20px;
    color: #fff;
    position: absolute;
    right: 0px;
    top: 0px;
    z-index: 100000;
    text-align: center;
}

.luckysheet-modal-controll-max-close:hover {
    background: #85c0fc;
    cursor: pointer;
} */

/* 图片 */
#luckysheet-modal-dialog-activeImage .luckysheet-modal-dialog-content {
  width: 100%;
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  cursor: move;
  image-rendering: -moz-crisp-edges; /* Firefox */
  image-rendering: -o-crisp-edges; /* Opera */
  image-rendering: -webkit-optimize-contrast; /*Webkit (non-standard naming) */
  image-rendering: crisp-edges;
}

#luckysheet-modal-dialog-cropping::before {
  content: "";
  outline: 1px solid #fff;
  position: absolute;
  left: 33.3%;
  right: 33.3%;
  top: 0;
  bottom: 0;
  z-index: 1;
  pointer-events: none;
}
#luckysheet-modal-dialog-cropping::after {
  content: "";
  outline: 1px solid #fff;
  position: absolute;
  left: 0;
  right: 0;
  top: 33.3%;
  bottom: 33.3%;
  z-index: 1;
  pointer-events: none;
}
#luckysheet-modal-dialog-cropping .cropping-mask {
  filter: brightness(0.5);
  position: absolute;
  background-size: 100% 100%;
  left: 0;
  top: 0;
}
#luckysheet-modal-dialog-cropping .cropping-content {
  position: absolute;
  overflow: hidden;
  background-position: 0 0;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
#luckysheet-modal-dialog-cropping .luckysheet-modal-dialog-resize {
  border: none;
  position: absolute;
  margin: 0px;
  padding: 0px;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  pointer-events: all;
}
#luckysheet-modal-dialog-cropping .resize-item {
  width: 0;
  height: 0;
  background: none;
  border: none;
  position: absolute;
  z-index: 3;
}
#luckysheet-modal-dialog-cropping .resize-item::before {
  content: "";
  display: block;
  position: absolute;
  background: #000;
}
#luckysheet-modal-dialog-cropping .resize-item::after {
  content: "";
  display: block;
  position: absolute;
  background: #000;
}
#luckysheet-modal-dialog-cropping .lt {
  left: 0;
  top: 0;
  cursor: nwse-resize;
}
#luckysheet-modal-dialog-cropping .lt::before {
  width: 18px;
  height: 4px;
  left: 0;
  top: 0;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .lt::after {
  width: 4px;
  height: 14px;
  left: 0;
  top: 4px;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .mt {
  left: 50%;
  top: 0;
  cursor: ns-resize;
}
#luckysheet-modal-dialog-cropping .mt::before {
  width: 18px;
  height: 4px;
  left: -11px;
  top: 0;
  border-left: 2px solid #fff;
  border-right: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .rt {
  right: 0;
  top: 0;
  cursor: nesw-resize;
}
#luckysheet-modal-dialog-cropping .rt::before {
  width: 18px;
  height: 4px;
  right: 0;
  top: 0;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .rt::after {
  width: 4px;
  height: 14px;
  right: 0;
  top: 4px;
  border-left: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .lm {
  left: 0;
  top: 50%;
  cursor: ew-resize;
}
#luckysheet-modal-dialog-cropping .lm::before {
  width: 4px;
  height: 18px;
  left: 0;
  top: -11px;
  border-right: 2px solid #fff;
  border-top: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .rm {
  right: 0;
  top: 50%;
  cursor: ew-resize;
}
#luckysheet-modal-dialog-cropping .rm::before {
  width: 4px;
  height: 18px;
  right: 0;
  top: -11px;
  border-left: 2px solid #fff;
  border-top: 2px solid #fff;
  border-bottom: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .lb {
  left: 0;
  bottom: 0;
  cursor: nesw-resize;
}
#luckysheet-modal-dialog-cropping .lb::before {
  width: 18px;
  height: 4px;
  left: 0;
  bottom: 0;
  border-right: 2px solid #fff;
  border-top: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .lb::after {
  width: 4px;
  height: 14px;
  left: 0;
  bottom: 4px;
  border-right: 2px solid #fff;
  border-top: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .rb {
  right: 0;
  bottom: 0;
  cursor: nwse-resize;
}
#luckysheet-modal-dialog-cropping .rb::before {
  width: 18px;
  height: 4px;
  right: 0;
  bottom: 0;
  border-left: 2px solid #fff;
  border-top: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .rb::after {
  width: 4px;
  height: 14px;
  right: 0;
  bottom: 4px;
  border-left: 2px solid #fff;
  border-top: 2px solid #fff;
}
#luckysheet-modal-dialog-cropping .mb {
  left: 50%;
  bottom: 0;
  cursor: ns-resize;
}
#luckysheet-modal-dialog-cropping .mb::before {
  width: 18px;
  height: 4px;
  left: -11px;
  bottom: 0;
  border-left: 2px solid #fff;
  border-right: 2px solid #fff;
  border-top: 2px solid #fff;
}
#luckysheet-modal-dialog-slider-imageCtrl
  .luckysheet-modal-dialog-slider-content {
  background-color: #fff;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box {
  border-bottom: 1px solid #e1e4e8;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .slider-box-title {
  padding: 10px 20px;
  font-weight: 600;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .slider-box-radios {
  padding: 10px 30px;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .radio-item {
  margin-bottom: 10px;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .radio-item:last-child {
  margin-bottom: 0;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .radio-item input {
  vertical-align: sub;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .slider-box-checkbox {
  padding: 10px 30px;
  border-top: 1px solid #e1e4e8;
}
#luckysheet-modal-dialog-slider-imageCtrl
  .slider-box
  .slider-box-checkbox
  input {
  vertical-align: middle;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .slider-box-borderConfig {
  padding: 10px 30px;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .border-item {
  margin-bottom: 10px;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .border-item:last-child {
  margin-bottom: 0;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .border-item label {
  display: inline-block;
  width: 40px;
}
#luckysheet-modal-dialog-slider-imageCtrl .slider-box .border-item input {
  width: 130px;
  padding: 5px;
}
#luckysheet-modal-dialog-slider-imageCtrl
  .slider-box
  .border-item
  .imgItemBorderColor {
  display: inline-block;
  width: 20px;
  height: 20px;
  padding: 2px;
  border: 1px solid #e1e4e8;
  vertical-align: middle;
  cursor: pointer;
}
#luckysheet-modal-dialog-slider-imageCtrl
  .slider-box
  .border-item
  .imgItemBorderColor
  span {
  display: block;
  width: 100%;
  height: 100%;
}
#luckysheet-imageCtrl-colorSelect-dialog .currenColor {
  font-size: 12px;
  margin-bottom: 5px;
}
#luckysheet-imageCtrl-colorSelect-dialog .currenColor span {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: solid 1px #d0d0d0;
  margin-left: 5px;
  margin-bottom: -5px;
  cursor: pointer;
}
#luckysheet-modal-dialog-activeImage,
#luckysheet-modal-dialog-cropping {
  background: none;
  box-shadow: none;
}

.luckysheet-modal-dialog-image {
  border: none;
  box-shadow: none;
  background: none;
  box-shadow: none;
  image-rendering: -moz-crisp-edges; /* Firefox */
  image-rendering: -o-crisp-edges; /* Opera */
  image-rendering: -webkit-optimize-contrast; /*Webkit (non-standard naming) */
  image-rendering: crisp-edges;
}

.luckysheet-modal-dialog-image .luckysheet-modal-dialog-content,
#luckysheet-modal-dialog-activeImage .luckysheet-modal-dialog-content {
  background: none;
}
/* 单元格日期选择 */
.cell-date-picker {
  position: absolute;
  display: none;
}
/* 插入链接 */
#luckysheet-insertLink-dialog {
  user-select: none;
}
#luckysheet-insertLink-dialog .box {
  font-size: 12px;
}
#luckysheet-insertLink-dialog .box-item {
  height: 30px;
  line-height: 30px;
  margin-bottom: 10px;
}
#luckysheet-insertLink-dialog .box-item label {
  display: inline-block;
  width: 90px;
  text-align: right;
  margin-right: 10px;
}
#luckysheet-insertLink-dialog .box-item input {
  width: 200px;
  height: 30px;
  padding: 0 10px;
  border: 1px solid #d4d4d4;
  outline-style: none;
  box-sizing: border-box;
}
#luckysheet-insertLink-dialog .box-item select {
  width: 200px;
  height: 30px;
  padding: 0 5px;
  border: 1px solid #d4d4d4;
  outline-style: none;
  box-sizing: border-box;
}
/* 数据验证 */
#luckysheet-dataVerification-dialog {
  user-select: none;
}
#luckysheet-dataVerification-dialog .box {
  font-size: 12px;
}
#luckysheet-dataVerification-dialog .box select {
  width: 100%;
  height: 30px;
  border-color: #d4d4d4;
  outline-style: none;
}
#luckysheet-dataVerification-dialog .box input::-webkit-input-placeholder {
  color: #d4d4d4;
}
#luckysheet-dataVerification-dialog .box input:-moz-placeholder {
  color: #d4d4d4;
}
#luckysheet-dataVerification-dialog .box input::-moz-placeholder {
  color: #d4d4d4;
}
#luckysheet-dataVerification-dialog .box input:-ms-input-placeholder {
  color: #d4d4d4;
}
#luckysheet-dataVerification-dialog .box-item {
  padding: 10px;
  border-bottom: 1px solid #e1e4e8;
}
#luckysheet-dataVerification-dialog .box-item .box-item-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 10px;
}
#luckysheet-dataVerification-dialog .box-item .range {
  width: 100%;
  height: 30px;
  border: 1px solid #d4d4d4;
}
#luckysheet-dataVerification-dialog .box-item .range input {
  width: calc(100% - 30px);
  height: 30px;
  padding: 0 10px;
  float: left;
  border: none;
  outline-style: none;
  box-sizing: border-box;
}
#luckysheet-dataVerification-dialog .box-item .range i.fa-table {
  float: right;
  margin-top: 9px;
  margin-right: 5px;
  cursor: pointer;
  color: var(--luckysheet-main-color);
}
.fa-exclamation-triangle:before {
  color: var(--luckysheet-warn-color) !important;
}
#luckysheet-dataVerification-dialog .box-item .multi {
  margin-top: 10px;
  line-height: 30px;
  font-size: 12px;
}
#luckysheet-dataVerification-dialog .box-item .multi input {
  vertical-align: text-top;
}
#luckysheet-dataVerification-dialog .box-item .show-box {
  margin-top: 10px;
}
#luckysheet-dataVerification-dialog .box-item .check-box {
  height: 30px;
  line-height: 30px;
  margin-bottom: 10px;
}
#luckysheet-dataVerification-dialog .box-item .check-box:last-child {
  margin-bottom: 0;
}
#luckysheet-dataVerification-dialog .box-item .check-box input {
  height: 30px;
  padding: 0 10px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
}
#luckysheet-dataVerification-dialog .box-item .check {
  line-height: 30px;
}
#luckysheet-dataVerification-dialog .box-item .check input {
  vertical-align: text-top;
}
#luckysheet-dataVerification-dialog .box-item .input {
  height: 30px;
  line-height: 30px;
  margin-top: 10px;
}
#luckysheet-dataVerification-dialog .box-item .input input {
  height: 30px;
  padding: 4px 10px 4px 10px;
  border: 1px solid #d4d4d4;
  box-sizing: border-box;
}
#luckysheet-dataVerification-dialog .box-item .input1 input {
  width: 150px;
}
#luckysheet-dataVerification-dialog .box-item .input2 input {
  width: 100%;
}
#luckysheet-dataVerification-dialog .box-item .input span {
  display: inline-block;
  width: 30px;
  text-align: center;
}
#luckysheet-dataVerification-dialog .data-verification-hint-text {
  width: 100%;
  height: 30px;
  border: 1px solid #d4d4d4;
  margin-top: 10px;
}
#luckysheet-dataVerification-dialog .data-verification-hint-text input {
  display: block;
  width: 100%;
  height: 100%;
  padding: 0 10px;
  border: none;
  outline-style: none;
  box-sizing: border-box;
}
#luckysheet-dataVerification-dialog .show-box .show-box-item {
  display: none;
}
#luckysheet-dataVerificationRange-dialog input {
  height: 30px;
  padding: 0 10px;
  border: 1px solid #d4d4d4;
  outline-style: none;
}
#luckysheet-dataVerification-dropdown-btn {
  display: none;
  width: 20px;
  height: 20px;
  background-color: #fff;
  position: absolute;
  z-index: 10;
  overflow: hidden;
}
#luckysheet-dataVerification-dropdown-btn::after {
  content: "";
  width: 10px;
  height: 10px;
  background: url(arrow-down.png) center no-repeat;
  position: absolute;
  left: 50%;
  top: 50%;
  margin-left: -5px;
  margin-top: -5px;
}
#luckysheet-dataVerification-dropdown-List {
  display: none;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: absolute;
  z-index: 10000;
  box-sizing: border-box;
}
#luckysheet-dataVerification-dropdown-List .dropdown-List-item {
  padding: 5px 10px;
  box-sizing: border-box;
  cursor: pointer;
}

#luckysheet-dataVerification-dropdown-List .dropdown-List-item.multi {
  padding-left: 0;
}
#luckysheet-dataVerification-dropdown-List .dropdown-List-item.multi:before {
  content: "";
  width: 14px;
  font-family: "iconfont" !important;
  font-size: 12px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: inline-block;
  margin-right: 2px;
}
#luckysheet-dataVerification-dropdown-List
  .dropdown-List-item.multi.checked:before {
  content: "\e7c8";
}
#luckysheet-dataVerification-dropdown-List .dropdown-List-item:hover {
  background-color: #e1e1e1;
}
#luckysheet-dataVerification-showHintBox {
  display: none;
  padding: 10px;
  background-color: #fff;
  border: 1px solid #ccc;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: absolute;
  z-index: 1000;
  user-select: none;
  cursor: default;
  white-space: nowrap;
}

#luckysheet-icon-undo.disabled,
#luckysheet-icon-redo.disabled {
  cursor: default;
  opacity: 0.4;
}

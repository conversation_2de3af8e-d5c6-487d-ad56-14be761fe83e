/* 超级公式相关样式 */
#luckysheet-superformula-dialog {
	padding: 10px;
	position: absolute;
	overflow: hidden;
	right: 0;
	top: 0;
	width: 320px;
	height: calc(100% - 20px);
	box-shadow: 0 8px 10px -5px rgba(0, 0, 0, 0.2),
		0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12);
	z-index: 9999;
	background-color: #fff;
	color: #444;
	display: flex;
	flex-direction: column;
}

.luckysheet-superformula-dialog-header {
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: solid 1px #dadce0;
	padding-bottom: 10px;
}
.luckysheet-superformula-dialog-header .title {
	cursor: pointer;
	padding: 5px 10px;
	border-radius: 4px;
	transition: all 0.3s;
}
.luckysheet-superformula-dialog-header .title:hover {
	background-color: var(--luckysheet-main-color-a2);
}
.luckysheet-superformula-dialog-header .close {
	cursor: pointer;
	width: 20px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.luckysheet-superformula-dialog-content {
	height: calc(100% - 32px - 32px - 10px);
	padding: 10px;
	overflow: hidden;
	overflow-y: auto;
	display: flex;
	flex-direction: column;
}

/* 滚动条样式 */
.luckysheet-superformula-dialog-content::-webkit-scrollbar {
	width: 4px;
}
/* 滑块样式 */
.luckysheet-superformula-dialog-content::-webkit-scrollbar-thumb {
	background-color: var(--luckysheet-main-color);
	border-radius: 2px;
}
/* 滚动条轨道样式 */
.luckysheet-superformula-dialog-content::-webkit-scrollbar-track {
	background-color: #f2f2f2;
	border-radius: 2px;
}
.luckysheet-superformula-dialog-content .describe,
.luckysheet-superformula-dialog-content .example {
	display: flex;
	flex-direction: column;
	margin-bottom: 10px;
}
.luckysheet-superformula-dialog-content .describe .tips,
.luckysheet-superformula-dialog-content .example .tips {
	font-size: 12px;
	color: #909399;
}
.luckysheet-superformula-dialog-content .describe .text {
	text-indent: 2rem;
	font-size: 14px;
	margin: 8px 0;
}
.luckysheet-superformula-dialog-content .example img {
	width: 100%;
}

.luckysheet-superformula-dialog-content .content {
	margin-top: 10px;
	flex: auto;
}
.luckysheet-superformula-dialog-content .content .content-item {
	margin: 10px 0;
	display: flex;
	align-items: center;
}
.luckysheet-superformula-dialog-content .content .content-item .label {
	padding-right: 10px;
	font-size: 14px;
}
.luckysheet-superformula-dialog-content .content .content-item input {
	height: 22px;
	border-radius: 4px;
	outline: none;
	flex: auto;
	border: solid 1px #ccc;
}
.luckysheet-superformula-dialog-content
	.content
	.content-item
	input:focus-visible {
	border: solid 1px var(--luckysheet-main-color);
}

.luckysheet-superformula-dialog-footer {
	margin-top: 10px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: center;
}

.luckysheet-superformula-dialog-footer span {
	margin-left: 10px;
	width: 100%;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
	cursor: pointer;
	background: #fff;
	border: 1px solid #dcdfe6;
	color: #606266;
	text-align: center;
	box-sizing: border-box;
	outline: none;
	transition: 0.1s;
	font-weight: 500;
	font-size: 14px;
	border-radius: 4px;
}

.luckysheet-superformula-dialog-footer .cancel:hover {
	color: var(--luckysheet-main-color);
	background-color: var(--luckysheet-main-color-a2);
	border-color: transparent;
}
.luckysheet-superformula-dialog-footer .confirm {
	background-color: var(--luckysheet-main-color-a8);
	border-color: transparent;
	color: #fff;
}

.luckysheet-superformula-dialog-content #codeFlask {
	position: relative;
	height: 100%;
	width: 100%;
	min-height: calc(100% - 100px);
	border: solid #eee 1px;
}
.luckysheet-superformula-dialog-content
	#codeFlask
	.codeflask__pre.codeflask__flatten.language-js {
	width: auto !important;
}
.luckysheet-superformula-dialog-content #editor {
	padding: 10px;
	height: 100%;
}

/* 重构 UI */
:root {
  /* 麦苗绿 */
  --luckysheet-main-color: #55bb8a;
  --luckysheet-main-color-rgb: 85, 187, 138;
  --luckysheet-main-color-a1: rgba(85, 187, 138, 0.1);
  --luckysheet-main-color-a2: rgba(85, 187, 138, 0.2);
  --luckysheet-main-color-a3: rgba(85, 187, 138, 0.3);
  --luckysheet-main-color-a4: rgba(85, 187, 138, 0.4);
  --luckysheet-main-color-a5: rgba(85, 187, 138, 0.5);
  --luckysheet-main-color-a6: rgba(85, 187, 138, 0.6);
  --luckysheet-main-color-a7: rgba(85, 187, 138, 0.7);
  --luckysheet-main-color-a8: rgba(85, 187, 138, 0.8);
  --luckysheet-main-color-a9: rgba(85, 187, 138, 0.9);

  --luckysheet-main-color-1: #ebfaf0;
  --luckysheet-main-color-2: #dfede4;
  --luckysheet-main-color-3: #d1e0d7;
  --luckysheet-main-color-4: #a3d4b9;
  --luckysheet-main-color-5: #79c79f;
  --luckysheet-main-color-6: #55bb8a;
  --luckysheet-main-color-7: #3b946c;
  --luckysheet-main-color-8: #266e50;
  --luckysheet-main-color-9: #154734;
  --luckysheet-main-color-10: #0a2119;

  --luckysheet-border-radius: 10px;
  --luckysheet-error-color: #f56c6c;
  --luckysheet-warn-color: #E6A23C;
  --luckysheet-success-color: #67C23A
}

/* 自定义loading演示样式 */
@keyframes luckysheet-loading-rotate {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

@keyframes luckysheet-loading-dash {
  0% {
    stroke-dasharray: 1, 200;
    stroke-dashoffset: 0;
  }

  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -40px;
  }

  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -120px;
  }
}

.loadingAnimation {
  width: 3em;
  height: 3em;
  animation: luckysheet-loading-rotate 2s linear infinite;
}

.loadingAnimation circle {
  animation: luckysheet-loading-dash 1.5s ease-in-out infinite;
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  stroke-width: 2;
  stroke: currentColor;
  stroke-linecap: round;
}

/* ========== Select 下拉菜单 rem 样式适配（大屏兼容） ========== */

/* 下拉菜单本体 */
.el-select-dropdown {
  font-size: 1rem !important;
  padding: 0.5rem 0;
  min-width: 12rem;
  border-radius: 0.5rem;
}

/* 每一项：高度、字体、内边距 */
.el-select-dropdown__item {
  font-size: 1rem !important;
  padding: 0.75rem 2rem;
  min-height: 2.5rem;
  line-height: 1.5;
}

/* hover 效果（适配大屏） */
.el-select-dropdown__item:hover {
  background-color: #f5faff;
}

/* 选中项样式 */
.el-select-dropdown__item.selected {
  font-weight: 600;
  color: #409EFF;
}

/* 多选 tag 样式 */
.el-select .el-tag {
  font-size: 1rem;
  height: 2rem;
  line-height: 2rem;
  padding: 0 0.75rem;
}

/* 选择框本体字体 */
.el-select {
  font-size: 1rem;
}

/* 选择框 input 区域高度 */
.el-select .el-input__inner {
  height: 2.5rem;
  font-size: 1rem;
  line-height: 2.5rem;
}

/* 控制箭头尺寸（可选） */
.el-select .el-input__suffix {
  font-size: 1.2rem;
}
<template>
  <el-dialog v-model="visible" :title="dialogType === 'add' ? '新增指标' : '编辑指标'" width="56.25rem"
    :close-on-click-modal="false">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="7.5rem">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="指标名称" prop="name">
            <el-input v-model="form.name" placeholder="请输入指标名称" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="单位" prop="unit">
            <el-input v-model="form.unit" placeholder="请输入单位，如：%、mg/L、无" clearable />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="评估周期" prop="evaluationPeriod">
            <el-select v-model="form.evaluationPeriod" placeholder="请选择评估周期" clearable style="width: 100%">
              <el-option label="时" value="hour" />
              <el-option label="天" value="day" />
              <el-option label="周" value="week" />
              <el-option label="月度" value="month" />
              <el-option label="季度" value="quarter" />
              <el-option label="年度" value="year" />
            </el-select>
            <div class="mt-2 text-sm text-gray-600">
              <el-icon>
                <InfoFilled />
              </el-icon>
              选择该指标的评估和统计周期
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="指标分类" prop="category">
            <el-tree-select v-model="form.category" :data="categoryTreeData" placeholder="请选择指标分类" style="width: 100%"
              node-key="id" :props="{ label: 'label', children: 'children' }" check-strictly
              :render-after-expand="false" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="基础数据项" prop="baseData">
        <el-select v-model="form.baseData" multiple placeholder="请选择基础数据项（必填）" style="width: 100%"
          @change="handleBaseDataChange">
          <el-option label="电压(V)" value="电压" />
          <el-option label="电流(A)" value="电流" />
          <el-option label="功率(kW)" value="功率" />
          <el-option label="流量(m³/h)" value="流量" />
          <el-option label="温度(℃)" value="温度" />
          <el-option label="压力(MPa)" value="压力" />
          <el-option label="pH值" value="pH" />
          <el-option label="COD(mg/L)" value="COD" />
          <el-option label="氨氮(mg/L)" value="氨氮" />
          <el-option label="达标次数" value="达标次数" />
          <el-option label="总检测次数" value="总检测次数" />
          <el-option label="有功功率" value="有功功率" />
          <el-option label="视在功率" value="视在功率" />
          <el-option label="运行时间" value="运行时间" />
          <el-option label="总时间" value="总时间" />
        </el-select>
        <div class="mt-2 text-sm text-gray-600">
          <el-icon>
            <InfoFilled />
          </el-icon>
          选择的数据项将作为计算公式中的可用变量
        </div>
      </el-form-item>

      <el-form-item label="状态" prop="status">
        <el-radio-group v-model="form.status">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>



      <el-form-item label="指标定义" prop="definition">
        <el-input v-model="form.definition" type="textarea" :rows="3" placeholder="请输入指标定义和说明" show-word-limit
          maxlength="500" />
      </el-form-item>

      <el-form-item label="计算逻辑" prop="calculation">
        <div class="formula-builder">
          <!-- 使用说明 -->
          <div class="formula-help mb-3">
            <el-alert title="使用说明" type="info" :closable="false" show-icon>
              <template #default>
                <div class="help-content">
                  <p>1. 先选择基础数据项，然后点击数据项按钮添加到公式中</p>
                  <p>2. 使用运算符连接数据项，支持 +、-、×、÷、括号等</p>
                  <p>3. 构建完成后点击"公式校验"验证公式正确性</p>
                  <p>4. 示例公式：(流量 + 温度) / 压力 * 100、流量 * 温度、(A - B) / C</p>
                </div>
              </template>
            </el-alert>
          </div>

          <!-- 公式显示区域 -->
          <div class="formula-display">
            <el-input v-model="form.calculation" type="textarea" :rows="3" placeholder="点击下方按钮构建计算公式" readonly />


          </div>

          <!-- 数据项选择区域 -->
          <div class="data-items-section mt-3">
            <div class="section-title">数据项：</div>
            <div class="button-group">
              <el-button v-for="item in form.baseData" :key="item" size="small" type="primary" plain
                @click="insertToFormula(item)" :disabled="!item">
                {{ item }}
              </el-button>
              <el-text v-if="form.baseData.length === 0" type="info" size="small">
                请先选择基础数据项
              </el-text>
            </div>
          </div>

          <!-- 运算符选择区域 -->
          <div class="operators-section mt-2">
            <div class="section-title">运算符：</div>
            <div class="button-group">
              <el-button size="small" @click="insertToFormula(' + ')">+</el-button>
              <el-button size="small" @click="insertToFormula(' - ')">-</el-button>
              <el-button size="small" @click="insertToFormula(' * ')">×</el-button>
              <el-button size="small" @click="insertToFormula(' / ')">÷</el-button>
              <el-button size="small" @click="insertToFormula('(')">(</el-button>
              <el-button size="small" @click="insertToFormula(')')">)</el-button>
              <el-button size="small" @click="insertToFormula(' * 100')">×100</el-button>
            </div>
          </div>



          <!-- 操作按钮 -->
          <div class="formula-actions mt-3">
            <el-button size="small" @click="clearFormula">清空</el-button>
            <el-button size="small" @click="undoFormula" :disabled="formulaHistory.length === 0">撤销</el-button>
            <el-button type="primary" size="small" @click="handleFormulaValidation" :loading="formulaValidating">
              <el-icon>
                <DocumentChecked />
              </el-icon>
              公式校验
            </el-button>
            <span v-if="formulaValidationResult" class="ml-2">
              <el-tag :type="formulaValidationResult.valid ? 'success' : 'danger'" size="small">
                {{ formulaValidationResult.message }}
              </el-tag>
            </span>
          </div>
        </div>
      </el-form-item>

      <!-- 公式校验和模拟计算区域 -->
      <el-form-item v-if="showFormulaTest" label="模拟计算">
        <FormulaTest :formula="form.calculation" :formula-params="formulaParams" :calculation-result="calculationResult"
          :calculation-error="calculationError" :can-calculate="canCalculate" @simulate="handleSimulateCalculation"
          @reset="handleResetTestValues" @param-change="handleParamValueChange" @close="handleCloseFormulaTest" />
      </el-form-item>

      <el-form-item label="阈值设置" prop="threshold">
        <el-input v-model="form.threshold" placeholder="请输入阈值，如：≥95%、<100mg/L" clearable />
      </el-form-item>

      <el-form-item label="备注说明">
        <el-input v-model="form.remark" type="textarea" :rows="3" placeholder="请输入备注说明（可选）" show-word-limit
          maxlength="300" />
      </el-form-item>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ dialogType === 'add' ? '新增' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed, watch, withDefaults } from 'vue'
import type { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { DocumentChecked, InfoFilled } from '@element-plus/icons-vue'
import FormulaTest from './FormulaTest.vue'

// 定义接口类型
interface IndexItem {
  id: string
  name: string
  definition: string
  unit: string
  category: string
  threshold: string
  status: number
  evaluationPeriod: string
  baseData: string[]
  calculation: string
  remark: string
  createTime: string
  creator: string
}

// Props
interface Props {
  modelValue: boolean
  dialogType: 'add' | 'edit'
  editData?: IndexItem | null
  categoryTreeData?: Array<{ id: string, label: string, children?: Array<{ id: string, label: string }> }>
}

// Emits
interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'submit', data: any): void
}

const props = withDefaults(defineProps<Props>(), {
  categoryTreeData: () => [
    {
      id: '1',
      label: '电力系统',
      children: [
        { id: '1-1', label: '电能质量' },
        { id: '1-2', label: '设备效率' }
      ]
    },
    {
      id: '2',
      label: '水处理系统',
      children: [
        { id: '2-1', label: '水质监测' },
        { id: '2-2', label: '处理效果' }
      ]
    },
    {
      id: '3',
      label: '环境监测',
      children: [
        { id: '3-1', label: '噪声监测' },
        { id: '3-2', label: '温度监测' }
      ]
    },
    {
      id: '4',
      label: '设备运行'
    },
    {
      id: '5',
      label: '安全管理'
    }
  ]
})
const emit = defineEmits<Emits>()

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const formRef = ref<FormInstance>()
const submitting = ref(false)

// 表单数据
const form = reactive({
  name: '',
  definition: '',
  unit: '',
  category: '',
  status: 1,
  evaluationPeriod: '',
  threshold: '',
  baseData: [] as string[],
  calculation: '',
  remark: ''
})

// 表单验证规则
const rules = {
  name: [{ required: true, message: '请输入指标名称', trigger: 'blur' }],
  definition: [{ required: true, message: '请输入指标定义', trigger: 'blur' }],
  unit: [{ required: true, message: '请输入单位', trigger: 'blur' }],
  category: [{ required: true, message: '请选择指标分类', trigger: 'change' }],
  status: [{ required: true, message: '请选择状态', trigger: 'change' }],
  evaluationPeriod: [{ required: true, message: '请选择评估周期', trigger: 'change' }],
  baseData: [{ required: true, message: '请选择基础数据项', trigger: 'change' }],
  calculation: [{ required: true, message: '请输入计算逻辑', trigger: 'blur' }]
}

// 公式校验相关
const formulaValidating = ref(false)
const formulaValidationResult = ref<{ valid: boolean; message: string } | null>(null)
const showFormulaTest = ref(false)
const formulaParams = ref<{ name: string; value: number }[]>([])
const calculationResult = ref<number | null>(null)
const calculationError = ref<string>('')
const formulaVariables = ref<string[]>([])
const formulaHistory = ref<string[]>([])

// 计算相关计算属性
const canCalculate = computed(() => {
  return formulaParams.value.length > 0 &&
    formulaParams.value.every(param =>
      typeof param.value === 'number' &&
      !isNaN(param.value)
    )
})



// 公式构建相关方法
const insertToFormula = (text: string) => {
  // 保存历史记录用于撤销
  formulaHistory.value.push(form.calculation)

  // 插入文本到公式中
  form.calculation += text

  // 触发公式变化处理
  handleFormulaChange()
}

const clearFormula = () => {
  formulaHistory.value.push(form.calculation)
  form.calculation = ''
  handleFormulaChange()
}

const undoFormula = () => {
  if (formulaHistory.value.length > 0) {
    form.calculation = formulaHistory.value.pop() || ''
    handleFormulaChange()
  }
}



// 监听编辑数据变化
watch(() => props.editData, (newData) => {
  if (newData && props.dialogType === 'edit') {
    Object.assign(form, {
      name: newData.name,
      definition: newData.definition,
      unit: newData.unit,
      category: newData.category,
      status: newData.status,
      evaluationPeriod: newData.evaluationPeriod || '',
      threshold: newData.threshold,
      baseData: newData.baseData,
      calculation: newData.calculation,
      remark: newData.remark
    })
  }
}, { immediate: true })

// 重置表单
const resetForm = () => {
  Object.assign(form, {
    name: '',
    definition: '',
    unit: '',
    category: '',
    status: 1,
    evaluationPeriod: '',
    baseData: [],
    threshold: '',
    calculation: '',
    remark: ''
  })

  // 重置公式校验相关状态
  formulaValidationResult.value = null
  showFormulaTest.value = false
  formulaParams.value = []
  calculationResult.value = null
  calculationError.value = ''
  formulaVariables.value = []
  formulaHistory.value = []
}

// 基础数据项变化处理
const handleBaseDataChange = () => {
  // 重置校验状态
  calculationResult.value = null
  calculationError.value = ''
  showFormulaTest.value = false
  formulaValidationResult.value = null
}

// 公式变化处理
const handleFormulaChange = () => {
  // 当公式发生变化时，重置校验状态
  formulaValidationResult.value = null
  showFormulaTest.value = false
  formulaParams.value = []
  calculationResult.value = null
  calculationError.value = ''
}

// 从公式中提取使用的变量
const extractVariablesFromFormula = (formula: string): string[] => {
  if (!formula.trim()) return []

  const variables: string[] = []

  // 按照数据项长度从长到短排序，避免短名称匹配到长名称的一部分
  const sortedDataItems = [...form.baseData].sort((a, b) => b.length - a.length)

  // 遍历基础数据项，检查哪些在公式中被使用
  sortedDataItems.forEach(dataItem => {
    if (dataItem && formula.includes(dataItem)) {
      // 检查是否是完整的变量名（不是其他变量名的一部分）
      const regex = new RegExp(`\\b${dataItem.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}\\b|${dataItem.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}(?=[^\\u4e00-\\u9fa5a-zA-Z0-9]|$)`, 'g')
      if (regex.test(formula)) {
        variables.push(dataItem)
      }
    }
  })

  return [...new Set(variables)] // 去重
}

// 公式校验
const handleFormulaValidation = async () => {
  if (!form.calculation.trim()) {
    ElMessage.warning('请先输入计算公式')
    return
  }

  if (!form.baseData || form.baseData.length === 0) {
    ElMessage.warning('请先选择基础数据项')
    return
  }

  formulaValidating.value = true

  try {
    await new Promise(resolve => setTimeout(resolve, 800))

    // 从公式中提取实际使用的变量
    const usedVariables = extractVariablesFromFormula(form.calculation)

    if (usedVariables.length === 0) {
      formulaValidationResult.value = {
        valid: false,
        message: '公式中未使用任何数据项'
      }
      showFormulaTest.value = false
      ElMessage.warning('公式中未使用任何数据项，请检查公式内容')
      return
    }

    // 检查公式中是否使用了未选择的数据项
    const invalidVariables = usedVariables.filter(variable => !form.baseData.includes(variable))
    if (invalidVariables.length > 0) {
      formulaValidationResult.value = {
        valid: false,
        message: `公式中使用了未选择的数据项: ${invalidVariables.join(', ')}`
      }
      showFormulaTest.value = false
      ElMessage.error(`公式中使用了未选择的数据项: ${invalidVariables.join(', ')}`)
      return
    }

    // 验证公式语法
    try {
      // 创建测试变量映射
      const testVariables: { [key: string]: number } = {}
      usedVariables.forEach(variable => {
        testVariables[variable] = 1 // 使用1作为测试值
      })

      // 尝试计算公式以验证语法
      evaluateFormula(form.calculation, testVariables)

      // 语法校验通过
      formulaValidationResult.value = {
        valid: true,
        message: `公式校验通过，使用了 ${usedVariables.length} 个数据项: ${usedVariables.join(', ')}`
      }
    } catch (syntaxError) {
      formulaValidationResult.value = {
        valid: false,
        message: `公式语法错误: ${syntaxError instanceof Error ? syntaxError.message : '未知错误'}`
      }
      showFormulaTest.value = false
      ElMessage.error(`公式语法错误: ${syntaxError instanceof Error ? syntaxError.message : '未知错误'}`)
      return
    }

    // 设置公式参数用于测试（只包含公式中实际使用的变量）
    formulaParams.value = usedVariables.map(variable => ({
      name: variable,
      value: 0
    }))

    // 保存使用的变量
    formulaVariables.value = usedVariables

    showFormulaTest.value = true

  } catch (error) {
    formulaValidationResult.value = {
      valid: false,
      message: '校验过程中发生错误'
    }
    showFormulaTest.value = false
    ElMessage.error('公式校验失败')
  } finally {
    formulaValidating.value = false
  }
}

// 安全的数学表达式计算
const evaluateFormula = (formula: string, variables: { [key: string]: number }): number => {
  if (!formula || !formula.trim()) {
    throw new Error('公式不能为空')
  }

  // 替换公式中的变量为实际值
  let expression = formula.trim()

  // 按变量名长度从长到短排序，避免短名称被误替换
  const sortedVariables = Object.keys(variables).sort((a, b) => b.length - a.length)

  for (const variable of sortedVariables) {
    const value = variables[variable]
    if (typeof value !== 'number' || !isFinite(value)) {
      throw new Error(`变量 "${variable}" 的值无效: ${value}`)
    }

    // 使用正则表达式确保完整匹配变量名
    const escapedVariable = variable.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')
    const regex = new RegExp(`\\b${escapedVariable}\\b|${escapedVariable}(?=[^\\u4e00-\\u9fa5a-zA-Z0-9_]|$)`, 'g')
    expression = expression.replace(regex, `(${value})`)
  }

  // 替换数学符号
  expression = expression.replace(/×/g, '*').replace(/÷/g, '/')

  // 处理常用函数（虽然界面上已经去除，但保留兼容性）
  expression = expression.replace(/abs\(([^)]+)\)/g, 'Math.abs($1)')
  expression = expression.replace(/max\(([^)]+)\)/g, 'Math.max($1)')
  expression = expression.replace(/min\(([^)]+)\)/g, 'Math.min($1)')

  // 检查是否还有未替换的变量
  const remainingVariables = expression.match(/[a-zA-Z\u4e00-\u9fa5][a-zA-Z0-9\u4e00-\u9fa5_]*/g)
  if (remainingVariables && remainingVariables.length > 0) {
    // 过滤掉Math函数
    const unknownVars = remainingVariables.filter(v => !v.startsWith('Math'))
    if (unknownVars.length > 0) {
      throw new Error(`公式中包含未定义的变量: ${unknownVars.join(', ')}`)
    }
  }

  // 验证表达式安全性
  const safePattern = /^[0-9+\-*/().\s,Math.abs|Math.max|Math.min|Math.pow|Math.sqrt|Math.floor|Math.ceil|Math.round]+$/
  if (!safePattern.test(expression)) {
    throw new Error('公式包含不支持的字符或函数')
  }

  // 检查括号匹配
  let parenthesesCount = 0
  for (const char of expression) {
    if (char === '(') parenthesesCount++
    if (char === ')') parenthesesCount--
    if (parenthesesCount < 0) {
      throw new Error('括号不匹配：多余的右括号')
    }
  }
  if (parenthesesCount !== 0) {
    throw new Error('括号不匹配：缺少右括号')
  }

  // 计算结果
  try {
    // 使用Function构造器安全执行表达式
    const result = new Function('Math', `"use strict"; return (${expression})`)(Math)

    if (typeof result !== 'number') {
      throw new Error('计算结果不是数字')
    }

    if (!isFinite(result)) {
      if (isNaN(result)) {
        throw new Error('计算结果为NaN，请检查公式是否正确')
      } else {
        throw new Error('计算结果为无穷大，可能存在除零错误')
      }
    }

    return result
  } catch (error) {
    if (error instanceof Error) {
      throw new Error(`计算错误: ${error.message}`)
    } else {
      throw new Error('计算过程中发生未知错误')
    }
  }
}

// 模拟计算
const handleSimulateCalculation = () => {
  try {
    calculationError.value = ''

    if (!canCalculate.value) {
      calculationError.value = '请输入所有变量的测试值'
      ElMessage.warning('请输入所有变量的测试值')
      return
    }

    // 构建变量映射
    const variables: { [key: string]: number } = {}
    formulaParams.value.forEach(param => {
      variables[param.name] = param.value
    })

    // 执行公式计算
    const result = evaluateFormula(form.calculation, variables)
    calculationResult.value = Number(result.toFixed(6))
    calculationError.value = ''

    // 计算完成，结果已显示在界面上
  } catch (error) {
    calculationResult.value = null
    calculationError.value = error instanceof Error ? error.message : '计算过程中发生未知错误'
    ElMessage.error(`计算失败: ${calculationError.value}`)
  }
}

// 参数值变化处理
const handleParamValueChange = () => {
  calculationError.value = ''
}

// 重置测试值
const handleResetTestValues = () => {
  formulaParams.value.forEach(param => {
    param.value = 0
  })
  calculationResult.value = null
  calculationError.value = ''
  // 测试值已重置
}

// 关闭模拟计算
const handleCloseFormulaTest = () => {
  showFormulaTest.value = false
  formulaParams.value = []
  calculationResult.value = null
  calculationError.value = ''
  formulaValidationResult.value = null
  // 已关闭模拟计算
}

// 对话框操作
const handleCancel = () => {
  resetForm()
  visible.value = false
}

const handleSubmit = async () => {
  if (!formRef.value) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const submitData = {
      ...form
    }

    emit('submit', submitData)
    resetForm()
    visible.value = false
  } catch (error) {
    console.error('表单验证失败:', error)
  } finally {
    submitting.value = false
  }
}
</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
}

/* 公式构建器样式 */
.formula-builder {
  border: 1px solid #e4e7ed;
  border-radius: 6px;
  padding: 16px;
  background-color: #fafbfc;
}

.formula-help {
  margin-bottom: 16px;
}

.help-content p {
  margin: 4px 0;
  font-size: 13px;
  line-height: 1.4;
}

.formula-display {
  margin-bottom: 12px;
}

.formula-display :deep(.el-textarea__inner) {
  background-color: #fff;
  border: 2px solid #e4e7ed;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  line-height: 1.5;
}



.section-title {
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.section-title::before {
  content: '';
  width: 3px;
  height: 14px;
  background-color: #409eff;
  margin-right: 8px;
  border-radius: 2px;
}

.button-group {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  align-items: center;
}

.data-items-section .button-group .el-button {
  background-color: #e1f3d8;
  border-color: #67c23a;
  color: #67c23a;
}

.data-items-section .button-group .el-button:hover {
  background-color: #67c23a;
  color: #fff;
}

.operators-section .button-group .el-button {
  background-color: #e6f7ff;
  border-color: #409eff;
  color: #409eff;
  min-width: 40px;
}

.operators-section .button-group .el-button:hover {
  background-color: #409eff;
  color: #fff;
}



.formula-actions {
  display: flex;
  align-items: center;
  gap: 12px;
  padding-top: 12px;
  border-top: 1px solid #e4e7ed;
}

.formula-actions .el-button {
  transition: all 0.3s ease;
}

.formula-actions .el-button:disabled {
  opacity: 0.5;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .button-group {
    gap: 6px;
  }

  .button-group .el-button {
    font-size: 12px;
    padding: 4px 8px;
  }

  .formula-actions {
    flex-wrap: wrap;
    gap: 8px;
  }
}
</style>

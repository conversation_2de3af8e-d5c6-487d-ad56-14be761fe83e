<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle" width="1000px" class="factory-detail-dialog">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      class="factory-detail-form"
    >
      <!-- 基本信息 -->
      <el-divider content-position="left">基本信息</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="水厂简称" prop="stationName">
            <el-input v-model="formData.stationName" placeholder="请输入水厂简称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="站点编码" prop="stationCode">
            <el-input v-model="formData.stationCode" placeholder="请输入站点编码" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="厂站类型" prop="type">
            <el-select v-model="formData.type" placeholder="请选择厂站类型" class="w-full">
              <el-option
                v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FACTORY_DETAIL_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="规模等级" prop="stpScale">
            <el-select v-model="formData.stpScale" placeholder="请选择规模等级" class="w-full">
              <el-option
                v-for="dict in getIntDictOptions(DICT_TYPE.SYSTEM_FACTORY_DETAIL_SCALE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="处理工艺" prop="trmPro">
            <el-input
              v-model="formData.trmPro"
              type="textarea"
              :rows="3"
              placeholder="请详细描述处理工艺流程，如：预处理→生化处理→深度处理→消毒等"
              show-word-limit
              maxlength="500"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="出水标准" prop="rwWq">
            <el-input v-model="formData.rwWq" placeholder="请输入出水标准" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="建设项目" prop="blProject">
            <el-input v-model="formData.blProject" placeholder="请输入建设项目名称" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="设计能力" prop="desCap">
            <div class="flex items-center">
              <el-input-number
                v-model="formData.desCap"
                placeholder="请输入设计能力"
                :min="0"
                :precision="0"
                controls-position="right"
                class="flex-1"
              />
              <span class="ml-2 text-gray-500">m³/d</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="出水量" prop="rwOut">
            <div class="flex items-center">
              <el-input-number
                v-model="formData.rwOut"
                placeholder="请输入出水量"
                :min="0"
                :precision="0"
                controls-position="right"
                class="flex-1"
              />
              <span class="ml-2 text-gray-500">m³/d</span>
            </div>
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 位置信息 -->
      <el-divider content-position="left">位置信息</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="所属区域" prop="region">
            <el-input v-model="formData.region" placeholder="请输入所属区域，如：合肥市蜀山区" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="详细位置" prop="stpLoc">
            <el-input v-model="formData.stpLoc" placeholder="请输入详细地址，如：XX区XX街道XX号" />
          </el-form-item>
        </el-col>
      </el-row>
<!--      <el-row :gutter="16">-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="经度" prop="longitude">-->
<!--            <el-input-number-->
<!--              v-model="formData.longitude"-->
<!--              placeholder="请输入经度"-->
<!--              :precision="6"-->
<!--              controls-position="right"-->
<!--              class="w-full"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="12">-->
<!--          <el-form-item label="纬度" prop="latitude">-->
<!--            <el-input-number-->
<!--              v-model="formData.latitude"-->
<!--              placeholder="请输入纬度"-->
<!--              :precision="6"-->
<!--              controls-position="right"-->
<!--              class="w-full"-->
<!--            />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--      </el-row>-->

      <!-- 运营信息 -->
      <el-divider content-position="left">运营信息</el-divider>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="建设运营单位" prop="stpUnit">
            <el-input v-model="formData.stpUnit" placeholder="请输入建设运营单位" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="投运时间" prop="stpDate">
            <el-date-picker
              v-model="formData.stpDate"
              type="date"
              placeholder="请选择投运时间"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              class="w-full"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="服务范围" prop="stpRange">
            <el-input
              v-model="formData.stpRange"
              type="textarea"
              :rows="2"
              placeholder="请描述服务范围，如：服务人口、覆盖区域等"
              show-word-limit
              maxlength="300"
            />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="负荷信息" prop="stpLoad">
            <el-input v-model="formData.stpLoad" placeholder="请输入当前负荷率，如：85%" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="目标用途" prop="target">
            <el-input v-model="formData.target" placeholder="请输入目标用途" />
          </el-form-item>
        </el-col>
      </el-row>
      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="出水去向" prop="rwTrtm">
            <el-input
              v-model="formData.rwTrtm"
              type="textarea"
              :rows="2"
              placeholder="请详细描述出水去向，如：排入XX河、回用于XX等"
              show-word-limit
              maxlength="300"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 状态设置 -->
      <el-divider content-position="left">状态设置</el-divider>
      <el-row :gutter="16">
<!--        <el-col :span="8">-->
<!--          <el-form-item label="是否启用">-->
<!--            <el-switch v-model="formData.isUse" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
<!--        <el-col :span="8">-->
<!--          <el-form-item label="是否告警">-->
<!--            <el-switch v-model="formData.isWarning" />-->
<!--          </el-form-item>-->
<!--        </el-col>-->
        <el-col :span="8">
          <el-form-item label="重点项目">
            <el-switch v-model="formData.blStp" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="多个站点">
            <el-switch v-model="formData.isMoreStation" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="合并站">
            <el-switch v-model="formData.isMrg" />
          </el-form-item>
        </el-col>
      </el-row>
<!--      <el-row :gutter="16">-->
<!--&lt;!&ndash;        <el-col :span="8">&ndash;&gt;-->
<!--&lt;!&ndash;          <el-form-item label="Web展示">&ndash;&gt;-->
<!--&lt;!&ndash;            <el-switch v-model="formData.isWeb" />&ndash;&gt;-->
<!--&lt;!&ndash;          </el-form-item>&ndash;&gt;-->
<!--&lt;!&ndash;        </el-col>&ndash;&gt;-->

<!--      </el-row>-->
    </el-form>
    <template #footer>
      <el-button :disabled="formLoading" type="primary" @click="submitForm">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>

<script lang="ts" setup>
import * as FactoryDetailApi from '@/api/system/factorydetail'
import { useI18n } from '@/hooks/web/useI18n'
import { useMessage } from '@/hooks/web/useMessage'
import { DICT_TYPE, getStrDictOptions, getIntDictOptions } from '@/utils/dict'

defineOptions({ name: 'FactoryDetailForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  stationName: '',
  stationCode: '',
  type: '',
  level: undefined,
  longitude: undefined,
  latitude: undefined,
  region: '',
  stpLoc: '',
  stpUnit: '',
  stpDate: '',
  stpRange: '',
  trmPro: '',
  desCap: undefined,
  stpScale: undefined,
  rwOut: undefined,
  rwWq: '',
  blProject: '',
  stpLoad: '',
  rwTrtm: '',
  isUse: 1,
  isWarning: 0,
  target: '',
  isWeb: 1,
  blStp: 0,
  isMoreStation: 0,
  isMrg: 0,
})



// 表单校验规则
const formRules = reactive({
  stationName: [
    { required: true, message: '水厂简称不能为空', trigger: 'blur' },
    { min: 2, max: 50, message: '水厂简称长度应在2-50个字符之间', trigger: 'blur' }
  ],
  stationCode: [
    { required: true, message: '站点编码不能为空', trigger: 'blur' },
    { pattern: /^[A-Za-z0-9-]+$/, message: '站点编码只能包含字母、数字和连字符', trigger: 'blur' }
  ],
  type: [{ required: true, message: '厂站类型不能为空', trigger: 'change' }],
  // trmPro: [
  //   { required: true, message: '处理工艺不能为空', trigger: 'blur' },
  //   { min: 10, max: 500, message: '处理工艺描述应在10-500个字符之间', trigger: 'blur' }
  // ],
  // rwWq: [{ required: true, message: '出水标准不能为空', trigger: 'change' }],
  // desCap: [
  //   { required: true, message: '设计能力不能为空', trigger: 'blur' },
  //   { type: 'number', min: 1, message: '设计能力必须大于0', trigger: 'blur' }
  // ],
  // stpUnit: [{ required: true, message: '建设运营单位不能为空', trigger: 'blur' }],
  // longitude: [
  //   { type: 'number', min: -180, max: 180, message: '经度范围应在-180到180之间', trigger: 'blur' }
  // ],
  // latitude: [
  //   { type: 'number', min: -90, max: 90, message: '纬度范围应在-90到90之间', trigger: 'blur' }
  // ]
})

const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增水厂档案' : '修改水厂档案'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await FactoryDetailApi.getFactoryDetail(id)
    } finally {
      formLoading.value = false
    }
  }
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value?.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as FactoryDetailApi.FactoryDetailSaveReqVO
    if (formType.value === 'create') {
      await FactoryDetailApi.createFactoryDetail(data)
      message.success(t('common.createSuccess'))
    } else {
      await FactoryDetailApi.updateFactoryDetail(data)
      message.success(t('common.updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    stationName: '',
    stationCode: '',
    type: '',
    level: undefined,
    longitude: undefined,
    latitude: undefined,
    region: '',
    stpLoc: '',
    stpUnit: '',
    stpDate: '',
    stpRange: '',
    trmPro: '',
    desCap: undefined,
    stpScale: undefined,
    rwOut: undefined,
    rwWq: '',
    blProject: '',
    stpLoad: '',
    rwTrtm: '',
    isUse: true,
    isWarning: false,
    target: '',
    isWeb: true,
    blStp: false,
    isMoreStation: false,
    isMrg: false,
    isActive: true
  }
  formRef.value?.resetFields()
}

defineExpose({ open }) // 提供 open 方法，用于打开弹窗
</script>

<style scoped lang="scss">
// 对话框样式
.factory-detail-dialog {
  :deep(.el-dialog__header) {
    .el-dialog__title {
      font-size: 16px;
      font-weight: 600;
    }
  }
}

// 表单样式
.factory-detail-form {
  :deep(.el-divider) {
    margin: 20px 0 16px;

    .el-divider__text {
      color: #409eff;
      font-weight: 600;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 18px;

    .el-form-item__label {
      color: #606266;
      font-weight: 500;
    }
  }
}



// 响应式设计
@media (max-width: 768px) {
  .factory-detail-dialog {
    :deep(.el-dialog) {
      width: 95% !important;
      margin: 5vh auto;
    }
  }

  .factory-detail-form {
    :deep(.el-form) {
      label-width: 80px !important;
    }
  }
}
</style>

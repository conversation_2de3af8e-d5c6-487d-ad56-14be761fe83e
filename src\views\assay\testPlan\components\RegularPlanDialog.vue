<template>
  <Dialog v-model="dialogVisible" :title="dialogTitle">
    <el-form
      ref="formRef"
      v-loading="formLoading"
      :model="formData"
      :rules="formRules"
      label-width="100px"
    >
      <el-form-item label="计划编号" prop="planCode">
        <el-input v-model="formData.planCode" placeholder="请输入计划编号" />
      </el-form-item>

      <el-form-item label="计划名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入计划名称" />
      </el-form-item>

      <el-form-item label="采样频率" prop="frequency">
        <el-select v-model="formData.frequency" placeholder="请选择采样频率">
          <el-option label="每日采样" value="daily" />
          <el-option label="每周采样" value="weekly" />
          <el-option label="每月采样" value="monthly" />
          <el-option label="季度采样" value="quarterly" />
        </el-select>
      </el-form-item>

      <el-form-item label="计划描述" prop="description">
        <el-input v-model="formData.description" type="textarea" placeholder="请输入计划描述" />
      </el-form-item>
      
      <el-form-item label="检测项目" prop="testItem">
        <div class="test-items-container">
          <el-input
            v-model="selectedTestItemName"
            placeholder="请选择检测项目"
            readonly
            style="width: calc(100% - 120px); margin-right: 8px;"
          />
          <el-button type="primary" @click="showTestItemDialog">
            从项目库选择
          </el-button>
        </div>
      </el-form-item>

      <el-form-item label="采样点" prop="samplingPoint">
        <div class="sampling-points-container">
          <el-input
            v-model="selectedSamplingPointName"
            placeholder="请选择采样点"
            readonly
            style="width: calc(100% - 120px); margin-right: 8px;"
          />
          <el-button type="primary" @click="showSamplingPointDialog">
            从采样点库选择
          </el-button>
        </div>
      </el-form-item>
      
      <!-- 执行人员配置 - 分离三个角色 -->
      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="采样人员" prop="samplerId">
            <el-select
              v-model="formData.samplerId"
              filterable
              placeholder="请选择采样人员"
              style="width: 100%"
            >
              <el-option
                v-for="person in responsiblePersons"
                :key="person.id"
                :label="person.name"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="检测人员" prop="testerId">
            <el-select
              v-model="formData.testerId"
              filterable
              placeholder="请选择检测人员"
              style="width: 100%"
            >
              <el-option
                v-for="person in responsiblePersons"
                :key="person.id"
                :label="person.name"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="审核人员" prop="reviewerId">
            <el-select
              v-model="formData.reviewerId"
              filterable
              placeholder="请选择审核人员"
              style="width: 100%"
            >
              <el-option
                v-for="person in responsiblePersons"
                :key="person.id"
                :label="person.name"
                :value="person.id"
              />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-alert
        title="人员角色说明"
        type="info"
        :closable="false"
        show-icon
        class="mb-4"
      >
        <template #default>
          <p><strong>采样人员</strong>：负责现场采样工作</p>
          <p><strong>检测人员</strong>：负责实验室检测工作</p>
          <p><strong>审核人员</strong>：负责检测结果审核工作</p>
        </template>
      </el-alert>
      
      <el-form-item label="开始日期" prop="startDate">
        <el-date-picker v-model="formData.startDate" type="date" placeholder="请选择开始日期" />
      </el-form-item>
      <el-form-item label="结束日期" prop="endDate">
        <el-date-picker v-model="formData.endDate" type="date" placeholder="请选择结束日期" />
      </el-form-item>
      
      <el-form-item label="优先级" prop="priority">
        <el-select v-model="formData.priority" placeholder="请选择优先级">
          <el-option label="普通" value="normal" />
          <el-option label="高" value="high" />
          <el-option label="紧急" value="urgent" />
        </el-select>
      </el-form-item>

      <el-form-item label="启用状态" prop="isEnabled">
        <el-switch v-model="formData.isEnabled" />
      </el-form-item>
      
      <!-- 样品预期信息 - 确保数据流完整 -->
      <el-card class="form-card">
        <template #header>
          <div class="card-header">
            <el-icon><Flask /></el-icon>
            <span>样品预期信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预期样品数量" prop="expectedSampleQuantity">
              <el-input-number
                v-model="formData.expectedSampleQuantity"
                :min="1"
                :max="10"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预期样品性质" prop="expectedSampleNature">
              <el-select v-model="formData.expectedSampleNature" placeholder="请选择样品性质" style="width: 100%">
                <el-option label="液体" value="liquid" />
                <el-option label="固体" value="solid" />
                <el-option label="半固体" value="semi-solid" />
                <el-option label="气体" value="gas" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="预期样品外观" prop="expectedSampleAppearance">
              <el-input v-model="formData.expectedSampleAppearance" placeholder="如：无色透明、微黄等" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="预期上清液情况" prop="expectedSupernatant">
              <el-input v-model="formData.expectedSupernatant" placeholder="如：清澈、浑浊、有沉淀等" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="采样说明" prop="samplingInstructions">
          <el-input
            v-model="formData.samplingInstructions"
            type="textarea"
            :rows="2"
            placeholder="请输入采样注意事项和特殊要求"
          />
        </el-form-item>
      </el-card>

      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" type="textarea" placeholder="请输入备注信息" />
      </el-form-item>
    </el-form>
    
    <template #footer>
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button :disabled="formLoading" type="primary" @click="checkConflicts">检查冲突</el-button>
      <el-button :disabled="formLoading" type="success" @click="submitForm">确 定</el-button>
    </template>
    
    <!-- 检测项目选择弹窗 -->
    <el-dialog
      v-model="testItemDialogVisible"
      title="检测项目库"
      width="60%"
      append-to-body
    >

      
      <el-table
        :data="filteredTestItems"
        border
        style="width: 100%"
        class="dialog-table"
        @selection-change="handleTestItemSelectionChange"
      >
        <el-table-column type="selection" min-width="50" />
        <el-table-column prop="id" label="ID" min-width="80" align="center" />
        <el-table-column prop="code" label="项目编号" min-width="120" align="center" />
        <el-table-column prop="name" label="检测项目名称" min-width="150" />
        <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
        <el-table-column prop="isEnabled" label="状态" min-width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isEnabled ? 'success' : 'danger'" size="small">
              {{ row.isEnabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <el-button @click="testItemDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmTestItems">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 采样点选择弹窗 -->
    <el-dialog
      v-model="samplingPointDialogVisible"
      title="采样点库"
      width="60%"
      append-to-body
    >

      
      <el-table
        :data="filteredSamplingPoints"
        border
        style="width: 100%"
        class="dialog-table"
        @selection-change="handleSamplingPointSelectionChange"
      >
        <el-table-column type="selection" width="50" />
        <el-table-column prop="id" label="ID" width="80" align="center" />
        <el-table-column prop="code" label="采样点编号" min-width="120" align="center" />
        <el-table-column prop="name" label="采样点名称" min-width="150" />
        <el-table-column prop="type" label="类型" width="100" align="center" />
        <el-table-column prop="location" label="位置" min-width="180" />
        <el-table-column prop="remark" label="备注" min-width="200" show-overflow-tooltip />
        <el-table-column prop="isEnabled" label="状态" min-width="80" align="center">
          <template #default="{ row }">
            <el-tag :type="row.isEnabled ? 'success' : 'danger'" size="small">
              {{ row.isEnabled ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      
      <template #footer>
        <el-button @click="samplingPointDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSamplingPoints">确定</el-button>
      </template>
    </el-dialog>
    
    <!-- 冲突检查结果弹窗 -->
    <el-dialog
      v-model="conflictDialogVisible"
      title="计划冲突检查结果"
      width="50%"
      append-to-body
    >
      <div v-if="conflicts.length > 0">
        <el-alert type="warning" title="检测到以下冲突，请调整计划:" :closable="false" show-icon />
        <el-table :data="conflicts" border style="width: 100%; margin-top: 1rem;">
          <el-table-column prop="type" label="冲突类型" width="8rem" />
          <el-table-column prop="description" label="冲突描述" />
          <el-table-column prop="suggestion" label="建议操作" />
        </el-table>
      </div>
      <div v-else>
        <el-alert type="success" title="未检测到任何冲突，可以安排执行" :closable="false" show-icon />
      </div>
      <template #footer>
        <el-button @click="conflictDialogVisible = false">关闭</el-button>
        <el-button type="primary" @click="submitForm" v-if="conflicts.length === 0">确认计划</el-button>
      </template>
    </el-dialog>
  </Dialog>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormRules } from 'element-plus'
import { Search } from '@element-plus/icons-vue'
import { AssayTestPlanApi, type AssaySamplingPlanSaveReqVO } from '@/api/assay/testPlan'
import { AssayBaseInfoApi } from '@/api/assay/baseInfo'
import { useAppStore } from '@/store/modules/app'

defineOptions({ name: 'RegularPlanDialog' })

const emit = defineEmits(['success'])

const appStore = useAppStore()
const currentFactoryId = computed(() => appStore.currentStation?.id || 1)

const dialogVisible = ref(false)
const dialogTitle = ref('')
const formLoading = ref(false)
const formType = ref('')

// 检测项目弹窗
const testItemDialogVisible = ref(false)
const selectedTestItems = ref<number[]>([])
const selectedTestItemName = ref('')

// 采样点弹窗
const samplingPointDialogVisible = ref(false)
const selectedSamplingPoints = ref<number[]>([])
const selectedSamplingPointName = ref('')

// 冲突检查弹窗
const conflictDialogVisible = ref(false)
const conflicts = ref<{type: string; description: string; suggestion: string}[]>([])

// 检测项目数据
const testItems = ref<any[]>([])

// 采样点数据
const samplingPoints = ref<any[]>([])

// 人员分组数据
const staffGroups = [
  {
    role: 'sampler',
    label: '采样员',
    options: [
      { id: 1, name: '张三', role: 'sampler' },
      { id: 2, name: '李四', role: 'sampler' }
    ]
  },
  {
    role: 'tester',
    label: '检测员',
    options: [
      { id: 3, name: '王五', role: 'tester' },
      { id: 4, name: '赵六', role: 'tester' }
    ]
  },
  {
    role: 'auditor',
    label: '审核员',
    options: [
      { id: 5, name: '钱七', role: 'auditor' }
    ]
  }
]

// 表单数据 - v4.0版本字段映射
const formData = ref({
  id: undefined,
  factoryId: 1,     // ✅ 新增：水厂ID，多水厂支持
  planCode: '',     // 计划编号
  name: '',
  frequency: '',    // 采样频率
  description: '',
  testItem: undefined as number | undefined,    // 单个检测项目ID
  samplingPoint: undefined as number | undefined, // 单个采样点ID
  // ✅ v4.0修改：人员字段改为ID关联
  samplerId: undefined as number | undefined,      // 采样人员ID
  testerId: undefined as number | undefined,       // 检测人员ID
  reviewerId: undefined as number | undefined,     // 审核人员ID
  startDate: '',
  endDate: '',
  priority: 'normal',
  isEnabled: true,  // 启用状态
  remark: '',

  // ✅ 新增：样品预期信息字段，确保数据流完整
  expectedSampleQuantity: 1,
  expectedSampleNature: 'liquid',
  expectedSampleAppearance: '',
  expectedSupernatant: '',
  samplingInstructions: ''
})

// 表单校验规则 - v4.0版本
const formRules = reactive<FormRules>({
  name: [{ required: true, message: '计划名称不能为空', trigger: 'blur' }],
  frequency: [{ required: true, message: '采样频率不能为空', trigger: 'change' }],
  testItem: [{ required: true, message: '检测项目不能为空', trigger: 'change' }],
  samplingPoint: [{ required: true, message: '采样点不能为空', trigger: 'change' }],
  // ✅ v4.0修改：人员ID字段的验证规则
  samplerId: [{ required: true, message: '采样人员不能为空', trigger: 'change' }],
  testerId: [{ required: true, message: '检测人员不能为空', trigger: 'change' }],
  reviewerId: [{ required: true, message: '审核人员不能为空', trigger: 'change' }],
  startDate: [{ required: true, message: '开始日期不能为空', trigger: 'blur' }],
  endDate: [{ required: true, message: '结束日期不能为空', trigger: 'blur' }]
})

const formRef = ref()

// 所有人员列表（合并所有角色）
const responsiblePersons = computed(() => {
  return staffGroups.flatMap(group => group.options)
})

// 过滤后的检测项目列表（不再需要搜索过滤）
const filteredTestItems = computed(() => {
  return testItems.value
})

// 过滤后的采样点列表（不再需要搜索过滤）
const filteredSamplingPoints = computed(() => {
  return samplingPoints.value
})

// 加载检测项目数据
const loadTestItems = async () => {
  try {
    const res = await AssayBaseInfoApi.getTestProjectList(currentFactoryId.value)
    if (res.data) {
      testItems.value = res.data
    }
  } catch (error) {
    console.error('加载检测项目失败:', error)
    ElMessage.error('加载检测项目失败')
  }
}

// 加载采样点数据
const loadSamplingPoints = async () => {
  try {
    const res = await AssayBaseInfoApi.getSamplingPointSimpleList(currentFactoryId.value)
    if (res.data) {
      samplingPoints.value = res.data
    }
  } catch (error) {
    console.error('加载采样点失败:', error)
    ElMessage.error('加载采样点失败')
  }
}

// 打开对话框
const open = async (type: string, data?: any) => {
  dialogVisible.value = true
  dialogTitle.value = type === 'create' ? '新增常规采样计划' : '编辑常规采样计划'
  formType.value = type
  resetForm()
  
  // 如果是编辑模式，设置表单数据
  if (type === 'update' && data) {
    // 深拷贝，避免直接修改原始数据
    formData.value = {
      id: data.id,
      factoryId: data.factoryId || currentFactoryId.value,
      planCode: data.planCode || '',
      name: data.name || '',
      frequency: data.frequency || '',
      description: data.description || '',
      testItem: data.testItem,
      samplingPoint: data.samplingPoint,
      samplerId: data.samplerId,
      testerId: data.testerId,
      reviewerId: data.reviewerId,
      startDate: data.startDate || '',
      endDate: data.endDate || '',
      priority: data.priority || 'normal',
      isEnabled: data.isEnabled !== undefined ? data.isEnabled : true,
      remark: data.remark || '',
      expectedSampleQuantity: data.expectedSampleQuantity || 1,
      expectedSampleNature: data.expectedSampleNature || 'liquid',
      expectedSampleAppearance: data.expectedSampleAppearance || '',
      expectedSupernatant: data.expectedSupernatant || '',
      samplingInstructions: data.samplingInstructions || ''
    }
  }
}
defineExpose({ open })

// 显示检测项目选择弹窗
const showTestItemDialog = async () => {
  // 加载检测项目数据
  await loadTestItems()
  testItemDialogVisible.value = true
  selectedTestItems.value = formData.value.testItem ? [formData.value.testItem] : []
}

// 处理检测项目选择变更
const handleTestItemSelectionChange = (selection: any[]) => {
  selectedTestItems.value = selection.map(item => item.id)
}

// 确认检测项目选择
const confirmTestItems = () => {
  if (selectedTestItems.value.length > 0) {
    const selectedItem = testItems.value.find(item => item.id === selectedTestItems.value[0])
    formData.value.testItem = selectedTestItems.value[0]
    selectedTestItemName.value = selectedItem?.name || ''
  }
  testItemDialogVisible.value = false
}

// 显示采样点选择弹窗
const showSamplingPointDialog = async () => {
  // 加载采样点数据
  await loadSamplingPoints()
  samplingPointDialogVisible.value = true
  selectedSamplingPoints.value = formData.value.samplingPoint ? [formData.value.samplingPoint] : []
}

// 处理采样点选择变更
const handleSamplingPointSelectionChange = (selection: any[]) => {
  selectedSamplingPoints.value = selection.map(point => point.id)
}

// 确认采样点选择
const confirmSamplingPoints = () => {
  if (selectedSamplingPoints.value.length > 0) {
    const selectedPoint = samplingPoints.value.find(point => point.id === selectedSamplingPoints.value[0])
    formData.value.samplingPoint = selectedSamplingPoints.value[0]
    selectedSamplingPointName.value = selectedPoint?.name || ''
  }
  samplingPointDialogVisible.value = false
}

// 检查计划冲突
const checkConflicts = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return
  
  formLoading.value = true
  try {
    const conflictData = {
      planId: formData.value.id,
      factoryId: currentFactoryId.value,
      samplingPoint: formData.value.samplingPoint,
      dateRange: {
        startDate: formData.value.startDate,
        endDate: formData.value.endDate
      },
      samplerId: formData.value.samplerId,
      testerId: formData.value.testerId,
      reviewerId: formData.value.reviewerId
    }

    const res = await AssayTestPlanApi.checkPlanConflicts(conflictData)

    if (res.data?.hasConflicts) {
      conflicts.value = res.data.conflicts.map((conflict: any) => ({
        type: conflict.type === 'sampling_point' ? '采样点冲突' :
              conflict.type === 'sampler' ? '采样人员冲突' :
              conflict.type === 'tester' ? '检测人员冲突' : '其他冲突',
        description: conflict.message,
        suggestion: '请调整计划时间或人员安排'
      }))
    } else {
      conflicts.value = []
    }
    
    conflictDialogVisible.value = true
  } catch (error) {
    console.error('检查冲突失败:', error)
    ElMessage.error('检查冲突失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 提交表单
const submitForm = async () => {
  // 表单校验
  if (!formRef.value) return
  const valid = await formRef.value.validate().catch(() => false)
  if (!valid) return

  formLoading.value = true
  try {
    // 构建API请求数据
    const apiData: AssaySamplingPlanSaveReqVO = {
      id: formData.value.id,
      factoryId: currentFactoryId.value,
      planCode: formData.value.planCode || `PLAN-${Date.now()}`,
      name: formData.value.name,
      type: 'regular',
      description: formData.value.description,
      frequency: formData.value.frequency,
      startDate: formData.value.startDate,
      endDate: formData.value.endDate,
      priority: formData.value.priority || 'normal',
      testItem: formData.value.testItem!,
      samplingPoint: formData.value.samplingPoint!,
      samplerId: formData.value.samplerId!,
      testerId: formData.value.testerId!,
      reviewerId: formData.value.reviewerId!,
      expectedSampleQuantity: formData.value.expectedSampleQuantity,
      expectedSampleNature: formData.value.expectedSampleNature,
      expectedSampleAppearance: formData.value.expectedSampleAppearance,
      expectedSupernatant: formData.value.expectedSupernatant,
      samplingInstructions: formData.value.samplingInstructions,
      isEnabled: formData.value.isEnabled,
      remark: formData.value.remark
    }

    // 调用API
    if (formType.value === 'create') {
      await AssayTestPlanApi.createSamplingPlan(apiData)
    } else {
      await AssayTestPlanApi.updateSamplingPlan(apiData)
    }

    const message = formType.value === 'create' ? '新增成功' : '修改成功'
    ElMessage.success(message)
    dialogVisible.value = false
    emit('success')
  } catch (error) {
    console.error('提交失败:', error)
    ElMessage.error('操作失败，请重试')
  } finally {
    formLoading.value = false
  }
}

// 重置表单
const resetForm = () => {
  formData.value = {
    id: undefined,
    factoryId: 1,     // ✅ v4.0新增：水厂ID
    planCode: '',     // 计划编号
    name: '',
    frequency: '',    // 采样频率
    description: '',
    testItem: undefined,    // 单个检测项目ID
    samplingPoint: undefined, // 单个采样点ID
    // ✅ v4.0修改：人员字段改为ID关联
    samplerId: undefined,
    testerId: undefined,
    reviewerId: undefined,
    startDate: '',
    endDate: '',
    priority: 'normal',
    isEnabled: true,  // 启用状态
    remark: '',

    // ✅ 新增：样品预期信息字段重置
    expectedSampleQuantity: 1,
    expectedSampleNature: 'liquid',
    expectedSampleAppearance: '',
    expectedSupernatant: '',
    samplingInstructions: ''
  }
  formRef.value?.resetFields()
}
</script>

<style scoped>
.regular-plan-form {
  max-width: 100%;
}

.form-card {
  margin-bottom: 1.5rem;
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.03);
}

.form-card:last-child {
  margin-bottom: 0;
}

.card-header {
  display: flex;
  align-items: center;
  font-size: 1rem;
  font-weight: 500;
  color: #303133;
}

.test-items-container,
.sampling-points-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.date-row {
  display: flex;
  gap: 1rem;
}

.date-row .el-form-item {
  flex: 1;
  margin-bottom: 0;
}

.priority-select {
  width: 100%;
}

.priority-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.priority-desc {
  font-size: 0.875rem;
  color: #909399;
}

.scheduling-options {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 0.75rem;
  padding: 0.75rem;
  background-color: #f5f7fa;
  border-radius: 0.25rem;
}

.search-bar {
  margin-bottom: 1rem;
}

.link-button {
  align-self: flex-end;
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-card__header) {
  padding: 0.75rem 1.25rem;
  background-color: #f5f7fa;
  border-bottom: 1px solid #ebeef5;
}

:deep(.el-card__body) {
  padding: 1.25rem;
}

:deep(.el-select .el-select__tags) {
  padding-right: 1.5rem;
}

:deep(.el-dialog__body) {
  padding: 1.25rem;
}

:deep(.el-table th.el-table__cell) {
  background-color: #f5f7fa;
}

:deep(.el-radio) {
  margin-right: 1.5rem;
}

/* 弹窗表格优化样式 */
.dialog-table {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.dialog-table :deep(.el-table__header) {
  background-color: #f8fafc;
}

.dialog-table :deep(.el-table__header th) {
  background-color: #f8fafc !important;
  color: #374151;
  font-weight: 600;
  border-bottom: 2px solid #e5e7eb;
}

.dialog-table :deep(.el-table__body tr:hover) {
  background-color: #f9fafb;
}

.dialog-table :deep(.el-table__body td) {
  padding: 12px 8px;
  border-bottom: 1px solid #f3f4f6;
}
</style> 
<template>
  <el-dialog
    v-model="dialogVisible"
    title="样品送检报告"
    width="60%"
    class="submission-report-dialog"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
      class="submission-form"
    >
      <!-- 基本信息 -->
      <el-card shadow="never" class="form-section">
        <template #header>
          <span class="section-title">基本信息</span>
        </template>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="样品编号" prop="sampleCode">
              <el-input v-model="formData.sampleCode" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="送检日期" prop="reportDate">
              <el-date-picker
                v-model="formData.reportDate"
                type="date"
                placeholder="选择送检日期"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="送检人" prop="submitterId">
              <el-select v-model="formData.submitterId" placeholder="请选择送检人" filterable>
                <el-option v-for="user in userOptions" :key="user.id" :label="user.name" :value="user.id" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="紧急程度" prop="urgency">
              <el-select v-model="formData.urgency" placeholder="请选择紧急程度" style="width: 100%">
                <el-option label="常规" value="normal" />
                <el-option label="紧急" value="urgent" />
                <el-option label="特急" value="emergency" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 检测项目 -->
      <el-card shadow="never" class="form-section">
        <template #header>
          <span class="section-title">检测项目</span>
        </template>
        
        <el-form-item label="检测项目" prop="testItems">
          <el-checkbox-group v-model="formData.testItems">
            <el-checkbox label="COD">COD（化学需氧量）</el-checkbox>
            <el-checkbox label="BOD5">BOD5（生化需氧量）</el-checkbox>
            <el-checkbox label="NH3-N">NH3-N（氨氮）</el-checkbox>
            <el-checkbox label="TP">TP（总磷）</el-checkbox>
            <el-checkbox label="TN">TN（总氮）</el-checkbox>
            <el-checkbox label="SS">SS（悬浮物）</el-checkbox>
            <el-checkbox label="pH">pH值</el-checkbox>
            <el-checkbox label="色度">色度</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
      </el-card>

      <!-- 特殊要求 -->
      <el-card shadow="never" class="form-section">
        <template #header>
          <span class="section-title">特殊要求</span>
        </template>
        
        <el-form-item label="特殊要求">
          <el-input
            v-model="formData.specialRequirements"
            type="textarea"
            :rows="3"
            placeholder="请输入特殊检测要求或注意事项"
          />
        </el-form-item>
      </el-card>

      <!-- 文件上传 -->
      <el-card shadow="never" class="form-section">
        <template #header>
          <span class="section-title">送检报告文件</span>
        </template>
        
        <el-form-item label="报告文件">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :action="uploadAction"
            :on-success="handleUploadSuccess"
            :on-error="handleUploadError"
            :before-upload="beforeUpload"
            :file-list="fileList"
            :limit="1"
            accept=".pdf,.doc,.docx"
          >
            <el-button type="primary">
              <el-icon><Upload /></el-icon>
              上传送检报告
            </el-button>
            <template #tip>
              <div class="el-upload__tip">
                支持 PDF、Word 格式，文件大小不超过 10MB
              </div>
            </template>
          </el-upload>
        </el-form-item>
      </el-card>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          提交送检
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Upload, Download } from '@element-plus/icons-vue'

defineOptions({ name: 'SubmissionReportDialog' })

// 接口定义
interface Sample {
  id: number
  code: string
  taskName: string
  samplingPoint: string
  samplingDate: string
  samplingPerson: string
  status: string
  testItems?: string[]
}

interface SubmissionReport {
  id: number
  factoryId: number   // ✅ v4.0新增：水厂ID
  sampleCode: string
  reportDate: string
  submitterId: number // ✅ v4.0修改：送检人员ID
  submitterName: string // 送检人员姓名（用于显示）
  testItems: string[]
  urgency: 'normal' | 'urgent' | 'emergency'
  specialRequirements?: string
  fileUrl?: string
  fileName?: string
  createTime: string
}

// 响应式数据
const dialogVisible = ref(false)
const submitting = ref(false)
const formRef = ref()
const uploadRef = ref()
const currentSample = ref<Sample | null>(null)

// 表单数据
const formData = reactive({
  factoryId: 1,     // ✅ v4.0新增：水厂ID
  sampleCode: '',
  reportDate: '',
  submitterId: 0,   // ✅ v4.0修改：送检人员ID
  submitterName: '', // 送检人员姓名（用于显示）
  urgency: 'normal' as 'normal' | 'urgent' | 'emergency',
  testItems: [] as string[],
  specialRequirements: '',
  fileUrl: '',
  fileName: ''
})

// 文件列表
const fileList = ref([])

// 上传地址（实际项目中应该是真实的API地址）
const uploadAction = '/api/upload/submission-report'

// 表单验证规则
const formRules = {
  reportDate: [
    { required: true, message: '请选择送检日期', trigger: 'change' }
  ],
  submitter: [
    { required: true, message: '请输入送检人姓名', trigger: 'blur' }
  ],
  testItems: [
    { required: true, message: '请至少选择一个检测项目', trigger: 'change' }
  ]
}

// 事件定义
const emit = defineEmits<{
  success: [sample: Sample, reportData: SubmissionReport]
}>()

// 打开对话框
const open = (sample: Sample) => {
  currentSample.value = sample
  
  // 初始化表单数据
  formData.sampleCode = sample.code
  formData.reportDate = new Date().toISOString().slice(0, 10)
  formData.submitter = sample.samplingPerson || ''
  formData.urgency = 'normal'
  formData.testItems = sample.testItems || ['COD', 'BOD5', 'NH3-N']
  formData.specialRequirements = ''
  formData.fileUrl = ''
  formData.fileName = ''
  
  // 清空文件列表
  fileList.value = []
  
  dialogVisible.value = true
}

// 文件上传成功
const handleUploadSuccess = (response: any, file: any) => {
  formData.fileUrl = response.data.url
  formData.fileName = file.name
  ElMessage.success('文件上传成功')
}

// 文件上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败，请重试')
}

// 上传前检查
const beforeUpload = (file: File) => {
  const isValidType = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'].includes(file.type)
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isValidType) {
    ElMessage.error('只能上传 PDF 或 Word 格式的文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}



// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    submitting.value = true
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 创建送检报告数据
    const reportData: SubmissionReport = {
      id: Date.now(),
      sampleCode: formData.sampleCode,
      reportDate: formData.reportDate,
      submitter: formData.submitter,
      testItems: formData.testItems,
      urgency: formData.urgency,
      specialRequirements: formData.specialRequirements,
      fileUrl: formData.fileUrl,
      fileName: formData.fileName,
      createTime: new Date().toISOString()
    }
    
    // 触发成功事件
    emit('success', currentSample.value!, reportData)
    
    ElMessage.success('送检报告提交成功')
    dialogVisible.value = false
  } catch (error) {
    console.error('提交送检报告失败:', error)
  } finally {
    submitting.value = false
  }
}

// 取消
const handleCancel = () => {
  dialogVisible.value = false
}

// 暴露方法
defineExpose({
  open
})
</script>

<style lang="scss" scoped>
.submission-report-dialog {
  .submission-form {
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .form-section {
    margin-bottom: 20px;
    border: 1px solid #e4e7ed;
    
    .section-title {
      font-weight: 600;
      color: #303133;
    }
  }
  
  .upload-demo {
    .el-upload__tip {
      color: #909399;
      font-size: 12px;
      margin-top: 8px;
    }
  }
  
  .dialog-footer {
    display: flex;
    justify-content: flex-end;
    gap: 12px;
  }
}

:deep(.el-card__header) {
  background-color: #f5f7fa;
  padding: 12px 20px;
}

:deep(.el-checkbox-group) {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
}

:deep(.el-checkbox) {
  margin-right: 0;
}
</style>

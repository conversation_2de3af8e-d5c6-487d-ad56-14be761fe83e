/* 文件导入配置面板 */
.luckysheet-import-mask {
	z-index: 9999;
	position: absolute;
	left: 0;
	top: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 100%;
	height: 100%;
	background-color: rgba(0, 0, 0, 0.15);
	z-index: 9999;
}
.luckysheet-import-setting {
	width: 25%;
	background-color: #fff;
	border-radius: 10px;
	padding: 14px;
	box-shadow: 0px 14px 71px 14px rgba(0, 0, 0, 0.1);
}

.luckysheet-import-title {
	display: flex;
	align-items: center;
	justify-content: space-between;
}
.luckysheet-import-title .title {
	font-size: 18px;
	font-weight: 900;
}
.luckysheet-import-title .close {
	cursor: pointer;
}

.luckysheet-import-content {
	padding: 20px 0;
	cursor: pointer;
	margin: 10px auto;
	width: 90%;
	height: inherit;
	border: solid #eee 1px;
	border-radius: 8px;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}
.luckysheet-import-content i {
	font-size: 80px;
	color: #a8abb2;
}
.luckysheet-import-content p {
	font-size: 12px;
	color: #606266;
}
.luckysheet-import-content p span {
	color: #50a6ff;
}

.luckysheet-import-content-mode,
.luckysheet-import-content-result {
	font-size: 14px;
    margin: 10px 0;
}

.luckysheet-import-content-footer {
	margin-top: 10px;
	height: 32px;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}
.luckysheet-import-content-footer span {
	margin-left: 10px;
	width: 75px;
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	white-space: nowrap;
	cursor: pointer;
	background: #fff;
	border: 1px solid #dcdfe6;
	color: #606266;
	text-align: center;
	box-sizing: border-box;
	outline: none;
	transition: 0.1s;
	font-weight: 500;
	font-size: 14px;
	border-radius: 4px;
}

.luckysheet-import-content-footer .cancel:hover {
	color: var(--luckysheet-main-color);
	background-color: var(--luckysheet-main-color-a2);
	border-color: transparent;
}
.luckysheet-import-content-footer .confirm {
	background-color: var(--luckysheet-main-color-a8);
	border-color: transparent;
	color: #fff;
}
.luckysheet-import-content-result #file-result{
	margin-left: 10px;
}
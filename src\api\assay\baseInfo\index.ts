import request from '@/config/axios'

// ==================== 类型定义 ====================

// 检测项目类型相关类型
export interface AssayTestCategoryVO {
  id?: number
  factoryId: number
  code: string
  name: string
  description?: string
  isEnabled: boolean
  createTime?: string
  updateTime?: string
}

export interface AssayTestCategoryCreateReqVO {
  factoryId: number
  code: string
  name: string
  description?: string
  isEnabled: boolean
}

export interface AssayTestCategoryUpdateReqVO {
  id: number
  factoryId: number
  code: string
  name: string
  description?: string
  isEnabled: boolean
}

// 检测项目相关类型
export interface AssayTestProjectVO {
  id?: number
  factoryId: number
  code: string
  name: string
  categoryId: number
  description?: string
  isEnabled: boolean
  createTime?: string
  updateTime?: string
}

export interface AssayTestProjectCreateReqVO {
  factoryId: number
  code: string
  name: string
  categoryId: number
  description?: string
  isEnabled: boolean
}

export interface AssayTestProjectUpdateReqVO {
  id: number
  factoryId: number
  code: string
  name: string
  categoryId: number
  description?: string
  isEnabled: boolean
}

// 检测指标相关类型
export interface AssayTestIndicatorVO {
  id?: number
  factoryId: number
  code: string
  name: string
  projectId: number
  method: string
  unit: string
  standardMin?: number
  standardMax?: number
  equipment: string
  precisionLimit?: string
  sampleVolume?: number
  detectionTimeMinutes?: number
  isEnabled: boolean
  createTime?: string
  updateTime?: string
}

export interface AssayTestIndicatorCreateReqVO {
  factoryId: number
  code: string
  name: string
  projectId: number
  method: string
  unit: string
  standardMin?: number
  standardMax?: number
  equipment: string
  precisionLimit?: string
  sampleVolume?: number
  detectionTimeMinutes?: number
  isEnabled: boolean
}

export interface AssayTestIndicatorUpdateReqVO {
  id: number
  factoryId: number
  code: string
  name: string
  projectId: number
  method: string
  unit: string
  standardMin?: number
  standardMax?: number
  equipment: string
  precisionLimit?: string
  sampleVolume?: number
  detectionTimeMinutes?: number
  isEnabled: boolean
}

// 采样点相关类型
export interface AssaySamplingPointVO {
  id?: number
  factoryId: number
  code: string
  name: string
  type: string
  location?: string
  managerId?: number
  isEnabled: boolean
  remark?: string
  createTime?: string
  updateTime?: string
}

export interface AssaySamplingPointCreateReqVO {
  factoryId: number
  code: string
  name: string
  type: string
  location?: string
  managerId?: number
  isEnabled: boolean
  remark?: string
}

export interface AssaySamplingPointUpdateReqVO {
  id: number
  factoryId: number
  code: string
  name: string
  type: string
  location?: string
  managerId?: number
  isEnabled: boolean
  remark?: string
}

// 树形结构类型
export interface TestTreeNodeVO {
  id: number
  factoryId: number
  name: string
  code: string
  type: 'category' | 'project' | 'indicator'
  description?: string
  isEnabled: boolean
  hasChildren?: boolean
  children?: TestTreeNodeVO[]
  // 类型特有字段
  categoryId?: number // project 和 indicator 有
  projectId?: number // indicator 有
  method?: string // indicator 有
  unit?: string // indicator 有
  standardMin?: number // indicator 有
  standardMax?: number // indicator 有
  equipment?: string // indicator 有
  precisionLimit?: string // indicator 有
  sampleVolume?: number // indicator 有
  detectionTimeMinutes?: number // indicator 有
}

// ==================== API 接口 ====================

/**
 * 基础信息管理模块API
 */
export const AssayBaseInfoApi = {
  // ==================== 检测项目类型管理 ====================
  
  /**
   * 创建检测项目类型
   */
  createTestCategory: async (data: AssayTestCategoryCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-test-category/create',
      data
    })
  },

  /**
   * 更新检测项目类型
   */
  updateTestCategory: async (data: AssayTestCategoryUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-test-category/update',
      data
    })
  },

  /**
   * 删除检测项目类型
   */
  deleteTestCategory: async (id: number, factoryId: number) => {
    return await request.deleteOriginal({
      url: '/assay/assay-test-category/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测项目类型详情
   */
  getTestCategory: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-category/get',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测项目类型精简列表
   */
  getTestCategorySimpleList: async (factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-category/simple-list',
      params: { factoryId }
    })
  },

  /**
   * 获取检测项目类型分页列表
   */
  getTestCategoryPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-test-category/page',
      params
    })
  },

  // ==================== 检测项目管理 ====================
  
  /**
   * 创建检测项目
   */
  createTestProject: async (data: AssayTestProjectCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-test-project/create',
      data
    })
  },

  /**
   * 更新检测项目
   */
  updateTestProject: async (data: AssayTestProjectUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-test-project/update',
      data
    })
  },

  /**
   * 删除检测项目
   */
  deleteTestProject: async (id: number, factoryId: number) => {
    return await request.deleteOriginal({
      url: '/assay/assay-test-project/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测项目列表
   */
  getTestProjectList: async (factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-project/list',
      params: { factoryId }
    })
  },

  /**
   * 根据类型获取检测项目列表
   */
  getTestProjectListByCategory: async (categoryId: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-project/list-by-category',
      params: { categoryId, factoryId }
    })
  },

  /**
   * 获取检测项目详情
   */
  getTestProject: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-project/get',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测项目分页列表
   */
  getTestProjectPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-test-project/page',
      params
    })
  },

  // ==================== 检测指标管理 ====================

  /**
   * 创建检测指标
   */
  createTestIndicator: async (data: AssayTestIndicatorCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-test-indicator/create',
      data
    })
  },

  /**
   * 更新检测指标
   */
  updateTestIndicator: async (data: AssayTestIndicatorUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-test-indicator/update',
      data
    })
  },

  /**
   * 删除检测指标
   */
  deleteTestIndicator: async (id: number, factoryId: number) => {
    return await request.deleteOriginal({
      url: '/assay/assay-test-indicator/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 根据项目获取检测指标列表
   */
  getTestIndicatorListByProject: async (projectId: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-indicator/list-by-project',
      params: { projectId, factoryId }
    })
  },

  /**
   * 获取检测项目树形结构
   */
  getTestIndicatorTree: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-test-indicator/tree',
      params
    })
  },

  /**
   * 获取检测指标详情
   */
  getTestIndicator: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-test-indicator/get',
      params: { id, factoryId }
    })
  },

  /**
   * 获取检测指标分页列表
   */
  getTestIndicatorPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-test-indicator/page',
      params
    })
  },

  // ==================== 采样点管理 ====================

  /**
   * 创建采样点
   */
  createSamplingPoint: async (data: AssaySamplingPointCreateReqVO) => {
    return await request.postOriginal({
      url: '/assay/assay-sampling-point/create',
      data
    })
  },

  /**
   * 更新采样点
   */
  updateSamplingPoint: async (data: AssaySamplingPointUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/assay-sampling-point/update',
      data
    })
  },

  /**
   * 删除采样点
   */
  deleteSamplingPoint: async (id: number, factoryId: number) => {
    return await request.deleteOriginal({
      url: '/assay/assay-sampling-point/delete',
      params: { id, factoryId }
    })
  },

  /**
   * 获取采样点分页列表
   */
  getSamplingPointPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-point/page',
      params
    })
  },

  /**
   * 根据类型获取采样点列表
   */
  getSamplingPointListByType: async (type: string, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-point/list-by-type',
      params: { type, factoryId }
    })
  },

  /**
   * 获取采样点精简列表
   */
  getSamplingPointSimpleList: async (factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-point/simple-list',
      params: { factoryId }
    })
  },

  /**
   * 获取采样点详情
   */
  getSamplingPoint: async (id: number, factoryId: number) => {
    return await request.getOriginal({
      url: '/assay/assay-sampling-point/get',
      params: { id, factoryId }
    })
  }
}

import request from '@/config/axios'
import { useAppStore } from '@/store/modules/app'

// ==================== 类型定义 ====================

// 数据审核相关类型
export interface AssayDataAuditVO {
  id?: number
  factoryId: number
  testDataId: number
  dataCode: string
  indicatorId: number
  indicatorName: string
  testValue: number
  unit: string
  testDate: string
  testerId: number
  testerName: string
  isExceeded: boolean
  isWarning: boolean
  exceedRatio: number
  auditResult: 'pending' | 'passed' | 'rejected'
  auditorId?: number
  auditorName?: string
  auditComment?: string
  qualityLevel: 'excellent' | 'good' | 'fair' | 'poor'
  auditTime?: string
  status: string
  createTime?: string
  updateTime?: string
}

// 数据告警相关类型
export interface AssayDataAlarmVO {
  id?: number
  factoryId: number
  testDataId: number
  dataCode: string
  indicatorId: number
  indicatorName: string
  testValue: number
  unit: string
  testDate: string
  alarmType: 'exceed' | 'warning' | 'abnormal'
  alarmLevel: 'low' | 'medium' | 'high'
  alarmReason: string
  thresholdValue?: number
  exceedRatio?: number
  isHandled: boolean
  handlerId?: number
  handlerName?: string
  handleTime?: string
  handleResult?: string
  handleComment?: string
  status: string
  createTime?: string
  updateTime?: string
}

// 检验报告相关类型
export interface AssayTestReportVO {
  id?: number
  factoryId: number
  reportCode: string
  taskId?: number
  reportTitle: string
  reportType: 'standard' | 'special' | 'emergency'
  generateDate: string
  generatePersonId: number
  generatePersonName?: string
  totalIndicators: number
  qualifiedIndicators: number
  exceededIndicators: number
  reportContent?: string
  fileUrl?: string
  status: 'draft' | 'published' | 'archived'
  remark?: string
  createTime?: string
  updateTime?: string
}

// 请求参数类型
export interface AssayDataAuditSubmitReqVO {
  id: number
  auditResult: 'passed' | 'rejected'
  auditComment: string
  qualityLevel: 'excellent' | 'good' | 'fair' | 'poor'
}

export interface AssayDataAlarmHandleReqVO {
  id: number
  handleResult: string
  handleComment: string
}

export interface AssayTestReportSaveReqVO {
  id?: number
  factoryId: number
  reportCode?: string
  taskId?: number
  reportTitle: string
  reportType: 'standard' | 'special' | 'emergency'
  generateDate: string
  generatePersonId: number
  totalIndicators: number
  qualifiedIndicators: number
  exceededIndicators: number
  reportContent?: string
  remark?: string
}

export interface AssayTestReportUpdateReqVO extends AssayTestReportSaveReqVO {
  id: number
}

// ==================== API 接口 ====================

/**
 * 获取全局水厂ID
 */
const getFactoryId = (): number => {
  const appStore = useAppStore()
  return appStore.currentStation?.id || 1
}

/**
 * 检验质量管理模块API
 */
export const AssayQualityManagementApi = {
  // ==================== 数据审核管理 ====================
  
  /**
   * 获取待审核数据分页列表
   */
  getDataAuditPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/data-audit/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取数据审核详情
   */
  getDataAudit: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/data-audit/get',
      params: { 
        id, 
        factoryId: getFactoryId() 
      }
    })
  },

  /**
   * 提交审核结果
   */
  submitAudit: async (data: AssayDataAuditSubmitReqVO) => {
    return await request.postOriginal({
      url: '/assay/data-audit/submit',
      data,
      params: {
        auditorId: 1, // 当前用户ID，应从用户状态获取
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 触发复检
   */
  triggerRecheck: async (auditId: number, recheckReason: string, priority: string = 'normal') => {
    return await request.postOriginal({
      url: '/assay/data-audit/trigger-recheck',
      params: {
        auditId,
        recheckReason,
        priority,
        operatorId: 1, // 当前用户ID，应从用户状态获取
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 批量审核
   */
  batchAudit: async (auditIds: number[], auditResult: string, auditComment: string) => {
    return await request.postOriginal({
      url: '/assay/data-audit/batch-audit',
      data: auditIds,
      params: {
        auditResult,
        auditComment,
        auditorId: 1, // 当前用户ID，应从用户状态获取
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取审核员绩效统计
   */
  getAuditorPerformance: async () => {
    return await request.getOriginal({
      url: '/assay/data-audit/auditor-performance',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取质量趋势数据
   */
  getQualityTrend: async (startDate: string, endDate: string) => {
    return await request.getOriginal({
      url: '/assay/data-audit/quality-trend',
      params: {
        factoryId: getFactoryId(),
        startDate,
        endDate
      }
    })
  },

  /**
   * 获取超标数据列表
   */
  getExceededData: async () => {
    return await request.getOriginal({
      url: '/assay/data-audit/exceeded-data',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 导出审核数据Excel
   */
  exportAuditExcel: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/data-audit/export-excel',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 数据告警管理 ====================

  /**
   * 获取数据告警分页列表
   */
  getDataAlarmPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/data-alarm/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取数据告警详情
   */
  getDataAlarm: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/data-alarm/get',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 处理告警
   */
  handleAlarm: async (data: AssayDataAlarmHandleReqVO) => {
    return await request.putOriginal({
      url: '/assay/data-alarm/handle',
      data,
      params: {
        handlerId: 1, // 当前用户ID，应从用户状态获取
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 批量处理告警
   */
  batchHandleAlarm: async (alarmIds: number[], handleResult: string, handleComment: string) => {
    return await request.postOriginal({
      url: '/assay/data-alarm/batch-handle',
      data: alarmIds,
      params: {
        handleResult,
        handleComment,
        handlerId: 1, // 当前用户ID，应从用户状态获取
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取未处理告警列表
   */
  getUnhandledAlarms: async () => {
    return await request.getOriginal({
      url: '/assay/data-alarm/unhandled',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取告警统计信息
   */
  getAlarmStatistics: async () => {
    return await request.getOriginal({
      url: '/assay/data-alarm/statistics',
      params: {
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取告警趋势数据
   */
  getAlarmTrend: async (startDate: string, endDate: string) => {
    return await request.getOriginal({
      url: '/assay/data-alarm/trend',
      params: {
        factoryId: getFactoryId(),
        startDate,
        endDate
      }
    })
  },

  /**
   * 导出告警数据Excel
   */
  exportAlarmExcel: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/data-alarm/export-excel',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  // ==================== 检验报告管理 ====================

  /**
   * 获取检验报告分页列表
   */
  getTestReportPage: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/test-report/page',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取检验报告详情
   */
  getTestReport: async (id: number) => {
    return await request.getOriginal({
      url: '/assay/test-report/get',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 创建检验报告
   */
  createTestReport: async (data: AssayTestReportSaveReqVO) => {
    return await request.postOriginal({
      url: '/assay/test-report/create',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 更新检验报告
   */
  updateTestReport: async (data: AssayTestReportUpdateReqVO) => {
    return await request.putOriginal({
      url: '/assay/test-report/update',
      data: {
        ...data,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 删除检验报告
   */
  deleteTestReport: async (id: number) => {
    return await request.deleteOriginal({
      url: '/assay/test-report/delete',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 发布检验报告
   */
  publishTestReport: async (id: number) => {
    return await request.postOriginal({
      url: '/assay/test-report/publish',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 归档检验报告
   */
  archiveTestReport: async (id: number) => {
    return await request.postOriginal({
      url: '/assay/test-report/archive',
      params: {
        id,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 导出检验报告Excel
   */
  exportTestReportExcel: async (params: any) => {
    return await request.getOriginal({
      url: '/assay/test-report/export-excel',
      params: {
        ...params,
        factoryId: getFactoryId()
      }
    })
  },

  /**
   * 获取报告统计信息
   */
  getReportStatistics: async () => {
    return await request.getOriginal({
      url: '/assay/test-report/statistics',
      params: {
        factoryId: getFactoryId()
      }
    })
  }
}

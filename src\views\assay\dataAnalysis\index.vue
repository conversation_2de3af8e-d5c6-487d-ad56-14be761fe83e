<template>
  <ContentWrap title="数据分析展示">
    <el-tabs v-model="activeTab">
      <!-- 实时监控大屏 -->
      <el-tab-pane label="实时监控大屏" name="monitor">
        <el-card shadow="hover">
          <div class="dashboard-container">
            <MonitorDashboard />
          </div>
        </el-card>
      </el-tab-pane>

      <!-- 执行监控 -->
      <el-tab-pane label="执行监控" name="execution">
        <ExecutionMonitor />
      </el-tab-pane>

      <!-- 趋势分析 -->
      <el-tab-pane label="趋势分析" name="trend">
        <el-card shadow="hover">
          <!-- 搜索 -->
          <div class="mb-4">
            <el-form :inline="true" :model="searchForm.trend" class="search-form">
              <el-form-item label="检测项目">
                <el-select
                  style="min-width: 10rem;"  
                  v-model="searchForm.trend.project" 
                  placeholder="请选择检测项目" 
                  clearable
                  multiple
                  collapse-tags
                >
                  <el-option v-for="item in projectOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="采样点">
                <el-select 
                  style="min-width: 10rem;" 
                  v-model="searchForm.trend.samplingPoint" 
                  placeholder="请选择采样点" 
                  clearable
                >
                  <el-option v-for="item in samplingPointOptions" :key="item.value" :label="item.label" :value="item.value" />
                </el-select>
              </el-form-item>
              <el-form-item label="时间范围">
                <ShortcutDateRangePicker v-model="searchForm.trend.dateRange" />
              </el-form-item>
              <el-form-item>
                <el-button type="primary" @click="handleSearch('trend')">
                  <el-icon><Search /></el-icon>分析
                </el-button>
                <el-button @click="resetSearch('trend')">
                  <el-icon><Refresh /></el-icon>重置
                </el-button>
                <el-button
                  type="success"
                  @click="handleExportData"
                  :disabled="chartData.length === 0"
                  class="ml-2"
                >
                  <el-icon><Download /></el-icon>导出数据
                </el-button>
                <el-button
                  type="warning"
                  @click="handleCompareAnalysis"
                  :disabled="chartData.length === 0"
                  class="ml-2"
                >
                  <el-icon><TrendCharts /></el-icon>对比分析
                </el-button>
              </el-form-item>
            </el-form>
          </div>
          
          <!-- 趋势图 -->
          <div v-loading="loading.trend">
            <div v-if="chartData.length > 0">
              <div class="trend-charts-container">
                <div 
                  v-for="(chart, index) in chartData" 
                  :key="index" 
                  class="chart-item"
                >
                  <ChartTitle :title="chart.title" />
                  <div ref="chartRefs" class="chart-wrapper"></div>
                </div>
              </div>
            </div>
            <el-empty v-else description="请选择检测项目和时间范围进行分析" />
          </div>
        </el-card>
      </el-tab-pane>


    </el-tabs>

    <!-- 对比分析对话框 -->
    <el-dialog
      v-model="dialogVisible.compareAnalysis"
      title="数据对比分析"
      width="80%"
      destroy-on-close
    >
      <div class="compare-analysis-container">
        <el-form :inline="true" :model="compareForm" class="mb-4">
          <el-form-item label="对比时间段">
            <ShortcutDateRangePicker v-model="compareForm.dateRange" />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleCompareSearch">
              <el-icon><Search /></el-icon>对比
            </el-button>
          </el-form-item>
        </el-form>

        <div v-loading="loading.compare" class="compare-charts">
          <div v-if="compareChartData.length > 0">
            <div
              v-for="(chart, index) in compareChartData"
              :key="index"
              class="compare-chart-item"
            >
              <ChartTitle :title="`${chart.title} - 对比分析`" />
              <div :ref="el => setCompareChartRef(el, index)" class="compare-chart-wrapper"></div>
            </div>
          </div>
          <el-empty v-else description="请选择对比时间段进行分析" />
        </div>
      </div>
    </el-dialog>

  </ContentWrap>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick, onBeforeUnmount, watch, ComponentPublicInstance } from 'vue'
import { ElMessage } from 'element-plus'
import { Search, Refresh, Download, TrendCharts } from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { ContentWrap } from '@/components/ContentWrap'
import { ShortcutDateRangePicker, ChartTitle } from '@/components'
import MonitorDashboard from './components/MonitorDashboard.vue'
import ExecutionMonitor from './components/ExecutionMonitor.vue'

// 标签页
const activeTab = ref('monitor')

// 搜索表单
const searchForm = ref({
  trend: {
    project: ['pH', 'COD', 'NH3-N'] as string[],
    samplingPoint: 'outlet',
    dateRange: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()]
  }
})



// 加载状态
const loading = ref({
  trend: false,
  compare: false
})

// 对话框
const dialogVisible = ref({
  compareAnalysis: false
})

// 对比分析表单
const compareForm = ref({
  dateRange: [new Date(Date.now() - 60 * 24 * 60 * 60 * 1000), new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)]
})

// 对比图表数据
interface CompareChartDataItem {
  title: string
  xAxis: string[]
  currentData: number[]
  compareData: number[]
}
const compareChartData = ref<CompareChartDataItem[]>([])

// 对比图表引用
const compareChartRefs = ref<(HTMLElement | null)[]>([])

// 对比图表实例
const compareChartInstances = ref<echarts.ECharts[]>([])

// 设置对比图表ref
const setCompareChartRef = (el: Element | ComponentPublicInstance | null, index: number) => {
  if (el) {
    compareChartRefs.value[index] = el as HTMLElement
  }
}

// ✅ 优化后的检测项目选项 - 与基础信息模块保持一致
const projectOptions = ref([
  { label: 'pH值', value: 'pH', unit: '无量纲', standardValue: '6-9' },
  { label: 'COD', value: 'COD', unit: 'mg/L', standardValue: '≤50' },
  { label: 'BOD5', value: 'BOD5', unit: 'mg/L', standardValue: '≤10' },
  { label: 'TOC', value: 'TOC', unit: 'mg/L', standardValue: '≤30' },
  { label: '氨氮', value: 'NH3-N', unit: 'mg/L', standardValue: '≤5' },
  { label: '总磷', value: 'TP', unit: 'mg/L', standardValue: '≤0.5' },
  { label: '总氮', value: 'TN', unit: 'mg/L', standardValue: '≤15' },
  { label: '硝酸盐氮', value: 'NO3-N', unit: 'mg/L', standardValue: '≤10' },
  { label: '悬浮物', value: 'SS', unit: 'mg/L', standardValue: '≤10' },
  { label: '浊度', value: 'Turbidity', unit: 'NTU', standardValue: '≤5' },
  { label: '污泥浓度', value: 'MLSS', unit: 'mg/L', standardValue: '2000-4000' },
  { label: '污泥沉降比', value: 'SV30', unit: '%', standardValue: '15-30' },
  { label: '含水率', value: 'Moisture', unit: '%', standardValue: '≤80' },
  { label: '铜', value: 'Cu', unit: 'mg/L', standardValue: '≤0.5' },
  { label: '锌', value: 'Zn', unit: 'mg/L', standardValue: '≤1.0' }
])

// ✅ 优化后的采样点选项 - 与基础信息模块保持一致
const samplingPointOptions = ref([
  { label: '进水总口', value: 'inlet', code: 'SP001' },
  { label: '生化池进口', value: 'bio-inlet', code: 'SP002' },
  { label: '生化池出口', value: 'bio-outlet', code: 'SP003' },
  { label: '二沉池出水', value: 'secondary-outlet', code: 'SP004' },
  { label: '出水总口', value: 'outlet', code: 'SP005' },
  { label: '污泥浓缩池', value: 'sludge-concentration', code: 'SP006' },
  { label: '污泥脱水间', value: 'sludge-dewatering', code: 'SP007' },
  { label: '回流污泥', value: 'return-sludge', code: 'SP008' },
  { label: '中间水池', value: 'intermediate-tank', code: 'SP009' },
  { label: '应急排放口', value: 'emergency-outlet', code: 'SP010' }
])

// 图表数据
interface ChartDataItem {
  title: string
  xAxis: string[]
  data: number[]
}
const chartData = ref<ChartDataItem[]>([])

// 图表引用
const chartRefs = ref<HTMLElement[]>([])

// 图表实例数组
const chartInstances = ref<echarts.ECharts[]>([])

// 搜索
const handleSearch = (type: string) => {
  if (type === 'trend') {
    loadTrendData()
  }
}

// 重置搜索
const resetSearch = (type: string) => {
  if (type === 'trend') {
    searchForm.value.trend = {
      project: [],
      samplingPoint: '',
      dateRange: [new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), new Date()]
    }
  }
}

// ✅ 优化后的趋势数据加载 - 基于真实历史数据模式
const loadTrendData = async () => {
  if (searchForm.value.trend.project.length === 0) {
    ElMessage.warning('请至少选择一个检测项目')
    return
  }

  loading.value.trend = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // ✅ 基于真实业务数据生成趋势数据
    chartData.value = searchForm.value.trend.project.map(project => {
      const dates = generateDateRange(searchForm.value.trend.dateRange[0], searchForm.value.trend.dateRange[1])
      const projectInfo = projectOptions.value.find(p => p.value === project)

      return {
        title: projectInfo?.label || project,
        xAxis: dates,
        data: generateRealisticTrendData(project, dates, searchForm.value.trend.samplingPoint)
      }
    })

    nextTick(() => {
      renderCharts()
      // 强制触发一次resize以确保图表正确渲染
      setTimeout(() => {
        handleResize()
      }, 200)
    })
  } catch (error) {
    console.error('加载趋势数据失败', error)
    ElMessage.error('加载趋势数据失败')
  } finally {
    loading.value.trend = false
  }
}

// ✅ 生成符合实际业务的趋势数据
const generateRealisticTrendData = (project: string, dates: string[], samplingPoint: string) => {
  const data: number[] = []

  // 根据不同检测项目和采样点生成符合实际的数据范围和趋势
  const dataPatterns: Record<string, { base: number, range: number, trend: 'stable' | 'improving' | 'fluctuating' }> = {
    // 进水数据 - 通常较高且波动较大
    'COD-inlet': { base: 300, range: 100, trend: 'fluctuating' },
    'BOD5-inlet': { base: 150, range: 50, trend: 'fluctuating' },
    'NH3-N-inlet': { base: 25, range: 10, trend: 'fluctuating' },
    'TP-inlet': { base: 3, range: 1, trend: 'fluctuating' },
    'TN-inlet': { base: 35, range: 15, trend: 'fluctuating' },
    'pH-inlet': { base: 7.5, range: 1, trend: 'stable' },
    'SS-inlet': { base: 200, range: 80, trend: 'fluctuating' },

    // 出水数据 - 通常较低且相对稳定
    'COD-outlet': { base: 35, range: 15, trend: 'improving' },
    'BOD5-outlet': { base: 8, range: 3, trend: 'improving' },
    'NH3-N-outlet': { base: 3, range: 2, trend: 'stable' },
    'TP-outlet': { base: 0.3, range: 0.2, trend: 'stable' },
    'TN-outlet': { base: 12, range: 5, trend: 'improving' },
    'pH-outlet': { base: 7.2, range: 0.5, trend: 'stable' },
    'SS-outlet': { base: 6, range: 4, trend: 'stable' },
    'Turbidity-outlet': { base: 2.5, range: 1.5, trend: 'stable' },

    // 生化池数据 - 中间处理过程
    'MLSS-sludge-concentration': { base: 3000, range: 800, trend: 'stable' },
    'SV30-sludge-concentration': { base: 22, range: 8, trend: 'stable' },
    'Moisture-sludge-dewatering': { base: 75, range: 10, trend: 'improving' },

    // 重金属数据 - 通常较低
    'Cu-inlet': { base: 0.3, range: 0.4, trend: 'fluctuating' },
    'Zn-inlet': { base: 0.6, range: 0.5, trend: 'fluctuating' },
    'Cu-outlet': { base: 0.1, range: 0.1, trend: 'stable' },
    'Zn-outlet': { base: 0.2, range: 0.2, trend: 'stable' }
  }

  // 构建数据模式键
  const patternKey = `${project}-${samplingPoint}`
  const pattern = dataPatterns[patternKey] || { base: 50, range: 20, trend: 'stable' }

  // 生成数据点
  dates.forEach((date, index) => {
    let value = pattern.base

    // 根据趋势类型调整数据
    switch (pattern.trend) {
      case 'improving':
        // 逐渐改善的趋势
        value = pattern.base + (pattern.range * 0.5) - (index / dates.length) * pattern.range
        break
      case 'fluctuating':
        // 波动较大的数据
        value = pattern.base + (Math.random() - 0.5) * pattern.range * 2
        break
      case 'stable':
        // 相对稳定的数据
        value = pattern.base + (Math.random() - 0.5) * pattern.range * 0.6
        break
    }

    // 添加一些随机噪声使数据更真实
    value += (Math.random() - 0.5) * pattern.range * 0.2

    // 确保数据不为负数
    value = Math.max(0, value)

    // 根据项目类型进行数值修正
    if (project === 'pH') {
      value = Math.max(6, Math.min(9, value)) // pH值限制在6-9之间
    }

    data.push(Number(value.toFixed(2)))
  })

  return data
}

// 渲染图表
const renderCharts = () => {
  // 先销毁之前的图表实例
  chartInstances.value.forEach(chart => {
    chart.dispose()
  })
  chartInstances.value = []
  
  nextTick(() => {
    const chartElements = document.querySelectorAll<HTMLElement>('.chart-wrapper')
    
    chartData.value.forEach((item, index) => {
      const chartElement = chartElements[index]
      if (!chartElement) return
      
      // 确保容器有宽高
      chartElement.style.width = '100%'
      chartElement.style.height = '100%'
      
      const chart = echarts.init(chartElement)
      chartInstances.value.push(chart)
      
      const option = {
        tooltip: {
          trigger: 'axis'
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: item.xAxis,
          axisLabel: {
            rotate: 45,
            interval: item.xAxis.length > 15 ? Math.ceil(item.xAxis.length / 15) : 0,
            margin: 15,
            fontSize: 12,
            formatter: function(value: string) {
              // 如果日期太长，只保留月和日
              if (value.length > 5) {
                return value.substring(5); // 只显示月-日部分
              }
              return value;
            }
          }
        },
        yAxis: {
          type: 'value',
          name: item.title,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [{
          data: item.data,
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: 6,
          itemStyle: {
            borderWidth: 2
          },
          lineStyle: {
            width: 3
          }
        }]
      }
      
      chart.setOption(option)
    })
  })
}

// 生成日期范围
const generateDateRange = (start: Date, end: Date): string[] => {
  const dates: string[] = []
  const current = new Date(start)
  const dayDiff = Math.floor((end.getTime() - start.getTime()) / (24 * 60 * 60 * 1000))

  // 如果日期范围太大，进行抽样显示
  const step = dayDiff > 60 ? Math.floor(dayDiff / 30) : 1

  while (current <= end) {
    dates.push(current.toISOString().split('T')[0])
    current.setDate(current.getDate() + step)
  }

  return dates
}

// 导出数据
const handleExportData = () => {
  if (chartData.value.length === 0) {
    ElMessage.warning('没有可导出的数据')
    return
  }

  try {
    // 准备CSV数据
    const headers = ['日期', ...chartData.value.map(chart => chart.title)]
    const rows: string[][] = []

    // 获取所有日期
    const dates = chartData.value[0].xAxis

    // 构建数据行
    dates.forEach((date, dateIndex) => {
      const row = [date]
      chartData.value.forEach(chart => {
        row.push(chart.data[dateIndex].toFixed(2))
      })
      rows.push(row)
    })

    // 生成CSV内容
    let csvContent = headers.join(',') + '\n'
    rows.forEach(row => {
      csvContent += row.join(',') + '\n'
    })

    // 创建Blob对象
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })

    // 创建下载链接
    const link = document.createElement('a')
    const url = URL.createObjectURL(blob)

    // 设置下载属性
    const fileName = `趋势分析数据_${new Date().toISOString().split('T')[0]}.csv`
    link.setAttribute('href', url)
    link.setAttribute('download', fileName)
    link.style.visibility = 'hidden'

    // 添加到DOM并触发下载
    document.body.appendChild(link)
    link.click()

    // 清理
    document.body.removeChild(link)
    URL.revokeObjectURL(url)

    ElMessage.success(`数据已导出为 ${fileName}`)
  } catch (error) {
    console.error('导出数据失败', error)
    ElMessage.error('导出数据失败')
  }
}

// 对比分析
const handleCompareAnalysis = () => {
  if (chartData.value.length === 0) {
    ElMessage.warning('请先进行趋势分析')
    return
  }
  dialogVisible.value.compareAnalysis = true
}

// ✅ 优化后的对比搜索 - 基于真实数据对比
const handleCompareSearch = async () => {
  loading.value.compare = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))

    // 清空之前的引用数组
    compareChartRefs.value = []

    // ✅ 生成基于真实业务的对比数据
    compareChartData.value = searchForm.value.trend.project.map(project => {
      const currentDates = generateDateRange(searchForm.value.trend.dateRange[0], searchForm.value.trend.dateRange[1])
      const projectInfo = projectOptions.value.find(p => p.value === project)

      // 当前期间数据
      const currentData = generateRealisticTrendData(project, currentDates, searchForm.value.trend.samplingPoint)

      // 对比期间数据（通常是上一个相同时间段）
      const compareData = generateCompareData(project, currentDates, searchForm.value.trend.samplingPoint)

      return {
        title: projectInfo?.label || project,
        xAxis: currentDates,
        currentData: currentData,
        compareData: compareData
      }
    })

    nextTick(() => {
      renderCompareCharts()
    })
  } catch (error) {
    console.error('加载对比数据失败', error)
    ElMessage.error('加载对比数据失败')
  } finally {
    loading.value.compare = false
  }
}

// ✅ 生成对比期间的数据（模拟历史同期数据）
const generateCompareData = (project: string, dates: string[], samplingPoint: string) => {
  // 基于当前数据生成对比数据，通常历史数据会有一定差异
  const currentData = generateRealisticTrendData(project, dates, samplingPoint)

  return currentData.map(value => {
    // 历史数据通常有10-20%的差异
    const variation = (Math.random() - 0.5) * 0.3 // -15% 到 +15% 的变化
    let compareValue = value * (1 + variation)

    // 对于出水数据，历史数据可能稍差一些（处理效果改善）
    if (samplingPoint === 'outlet') {
      compareValue = value * (1 + Math.abs(variation) * 0.5) // 历史数据稍高一些
    }

    // 确保数据不为负数
    compareValue = Math.max(0, compareValue)

    // pH值特殊处理
    if (project === 'pH') {
      compareValue = Math.max(6, Math.min(9, compareValue))
    }

    return Number(compareValue.toFixed(2))
  })
}

// 渲染对比图表
const renderCompareCharts = () => {
  // 先销毁之前的图表实例
  compareChartInstances.value.forEach(chart => {
    chart.dispose()
  })
  compareChartInstances.value = []

  // 等待DOM更新
  setTimeout(() => {
    compareChartData.value.forEach((item, index) => {
      const chartElement = compareChartRefs.value[index]
      if (!chartElement) return

      // 确保容器有宽高
      chartElement.style.width = '100%'
      chartElement.style.height = '400px'

      const chart = echarts.init(chartElement)
      compareChartInstances.value.push(chart)

      const option = {
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['当前时段', '对比时段']
        },
        grid: {
          left: '5%',
          right: '5%',
          bottom: '15%',
          top: '15%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: item.xAxis,
          axisLabel: {
            rotate: 45,
            interval: item.xAxis.length > 15 ? Math.ceil(item.xAxis.length / 15) : 0,
            margin: 15,
            fontSize: 12
          }
        },
        yAxis: {
          type: 'value',
          name: item.title,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14
          },
          splitLine: {
            lineStyle: {
              type: 'dashed'
            }
          }
        },
        series: [
          {
            name: '当前时段',
            data: item.currentData,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#409EFF',
              borderWidth: 2
            },
            lineStyle: {
              width: 3
            }
          },
          {
            name: '对比时段',
            data: item.compareData,
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: 6,
            itemStyle: {
              color: '#E6A23C',
              borderWidth: 2
            },
            lineStyle: {
              width: 3,
              type: 'dashed'
            }
          }
        ]
      }

      chart.setOption(option)
    })
  }, 100) // 延迟100ms确保DOM完全渲染
}



// 窗口大小变化时重新渲染图表
const handleResize = () => {
  chartInstances.value.forEach(chart => {
    chart.resize()
  })
}

// 监听窗口大小变化
window.addEventListener('resize', handleResize)

// 监听标签页切换
watch(() => activeTab.value, (newVal) => {
  if (newVal === 'trend') {
    nextTick(() => {
      handleResize()
    })
  } else if (newVal === 'execution') {
    // 切换到执行监控时，延迟确保组件完全渲染
    nextTick(() => {
      setTimeout(() => {
        // 触发ExecutionMonitor组件的图表重新渲染
        window.dispatchEvent(new Event('resize'))
      }, 500) // 实时监控需要更长的延迟，因为有多个图表
    })
  } else if (newVal === 'monitor') {
    // 切换到实时监控时，延迟确保组件完全渲染
    nextTick(() => {
      setTimeout(() => {
        // 触发MonitorDashboard组件的图表重新渲染
        window.dispatchEvent(new Event('resize'))
      }, 500) // 实时监控需要更长的延迟，因为有多个图表
    })
  }
})

// 监听趋势数据变化
watch(() => chartData.value, () => {
  nextTick(() => {
    renderCharts()
  })
}, { deep: true })

// 组件销毁前清理事件监听
onBeforeUnmount(() => {
  window.removeEventListener('resize', handleResize)
  chartInstances.value.forEach(chart => {
    chart.dispose()
  })
})

// 页面加载
onMounted(() => {
  // 默认加载趋势分析数据
  if (searchForm.value.trend.project.length > 0) {
    nextTick(() => {
      loadTrendData()
    })
  }
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  height: 80rem;
}

.trend-charts-container {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
  
  @media (min-width: 1200px) {
    grid-template-columns: 1fr 1fr;
  }
}

.chart-item {
  position: relative;
  height: 40rem;
  border: 1px solid #ebeef5;
  border-radius: 0.25rem;
  padding: 1rem;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
}

.chart-wrapper {
  height: 36rem;
  width: 100%;
}



.search-form {
  ::v-deep(.el-form-item) {
    margin-bottom: 1rem;
  }
}

.compare-analysis-container {
  .compare-charts {
    max-height: 60rem;
    overflow-y: auto;
  }

  .compare-chart-item {
    margin-bottom: 2rem;
    border: 1px solid #ebeef5;
    border-radius: 0.25rem;
    padding: 1rem;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.05);
  }

  .compare-chart-wrapper {
    height: 25rem;
    width: 100%;
  }
}
</style> 